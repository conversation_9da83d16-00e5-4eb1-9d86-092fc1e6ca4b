################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

module CZMQ
  module FFI

    # work with file-system directories
    # @note This class is 100% generated using zproject.
    class Zdir
      # Raised when one tries to use an instance of {Zdir} after
      # the internal pointer to the native object has been nullified.
      class DestroyedError < RuntimeError; end

      # Boilerplate for self pointer, initializer, and finalizer
      class << self
        alias :__new :new
      end
      # Attaches the pointer _ptr_ to this instance and defines a finalizer for
      # it if necessary.
      # @param ptr [::FFI::Pointer]
      # @param finalize [Boolean]
      def initialize(ptr, finalize = true)
        @ptr = ptr
        if @ptr.null?
          @ptr = nil # Remove null pointers so we don't have to test for them.
        elsif finalize
          @finalizer = self.class.create_finalizer_for @ptr
          ObjectSpace.define_finalizer self, @finalizer
        end
      end
      # @param ptr [::FFI::Pointer]
      # @return [Proc]
      def self.create_finalizer_for(ptr)
        ptr_ptr = ::FFI::MemoryPointer.new :pointer

        Proc.new do
          ptr_ptr.write_pointer ptr
          ::CZMQ::FFI.zdir_destroy ptr_ptr
        end
      end
      # @return [Boolean]
      def null?
        !@ptr or @ptr.null?
      end
      # Return internal pointer
      # @return [::FFI::Pointer]
      def __ptr
        raise DestroyedError unless @ptr
        @ptr
      end
      # So external Libraries can just pass the Object to a FFI function which expects a :pointer
      alias_method :to_ptr, :__ptr
      # Nullify internal pointer and return pointer pointer.
      # @note This detaches the current instance from the native object
      #   and thus makes it unusable.
      # @return [::FFI::MemoryPointer] the pointer pointing to a pointer
      #   pointing to the native object
      def __ptr_give_ref
        raise DestroyedError unless @ptr
        ptr_ptr = ::FFI::MemoryPointer.new :pointer
        ptr_ptr.write_pointer @ptr
        __undef_finalizer if @finalizer
        @ptr = nil
        ptr_ptr
      end
      # Undefines the finalizer for this object.
      # @note Only use this if you need to and can guarantee that the native
      #   object will be freed by other means.
      # @return [void]
      def __undef_finalizer
        ObjectSpace.undefine_finalizer self
        @finalizer = nil
      end

      # Create a new directory item that loads in the full tree of the specified
      # path, optionally located under some parent path. If parent is "-", then
      # loads only the top-level directory, and does not use parent as a path.
      # @param path [String, #to_s, nil]
      # @param parent [String, #to_s, nil]
      # @return [CZMQ::Zdir]
      def self.new(path, parent)
        ptr = ::CZMQ::FFI.zdir_new(path, parent)
        __new ptr
      end

      # Destroy a directory tree and all children it contains.
      #
      # @return [void]
      def destroy()
        return unless @ptr
        self_p = __ptr_give_ref
        result = ::CZMQ::FFI.zdir_destroy(self_p)
        result
      end

      # Return directory path
      #
      # @return [String]
      def path()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zdir_path(self_p)
        result
      end

      # Return last modification time for directory.
      #
      # @return [::FFI::Pointer]
      def modified()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zdir_modified(self_p)
        result
      end

      # Return total hierarchy size, in bytes of data contained in all files
      # in the directory tree.
      #
      # @return [::FFI::Pointer]
      def cursize()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zdir_cursize(self_p)
        result
      end

      # Return directory count
      #
      # @return [Integer]
      def count()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zdir_count(self_p)
        result
      end

      # Returns a sorted list of zfile objects; Each entry in the list is a pointer
      # to a zfile_t item already allocated in the zdir tree. Do not destroy the
      # original zdir tree until you are done with this list.
      #
      # @return [Zlist]
      def list()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zdir_list(self_p)
        result = Zlist.__new result, true
        result
      end

      # Returns a sorted list of char*; Each entry in the list is a path of a file
      # or directory contained in self.
      #
      # @return [Zlist]
      def list_paths()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zdir_list_paths(self_p)
        result = Zlist.__new result, true
        result
      end

      # Remove directory, optionally including all files that it contains, at
      # all levels. If force is false, will only remove the directory if empty.
      # If force is true, will remove all files and all subdirectories.
      #
      # @param force [Boolean]
      # @return [void]
      def remove(force)
        raise DestroyedError unless @ptr
        self_p = @ptr
        force = !(0==force||!force) # boolean
        result = ::CZMQ::FFI.zdir_remove(self_p, force)
        result
      end

      # Calculate differences between two versions of a directory tree.
      # Returns a list of zdir_patch_t patches. Either older or newer may
      # be null, indicating the directory is empty/absent. If alias is set,
      # generates virtual filename (minus path, plus alias).
      #
      # @param older [Zdir, #__ptr]
      # @param newer [Zdir, #__ptr]
      # @param alias_ [String, #to_s, nil]
      # @return [Zlist]
      def self.diff(older, newer, alias_)
        older = older.__ptr if older
        newer = newer.__ptr if newer
        result = ::CZMQ::FFI.zdir_diff(older, newer, alias_)
        result = Zlist.__new result, true
        result
      end

      # Return full contents of directory as a zdir_patch list.
      #
      # @param alias_ [String, #to_s, nil]
      # @return [Zlist]
      def resync(alias_)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zdir_resync(self_p, alias_)
        result = Zlist.__new result, true
        result
      end

      # Load directory cache; returns a hash table containing the SHA-1 digests
      # of every file in the tree. The cache is saved between runs in .cache.
      #
      # @return [Zhash]
      def cache()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zdir_cache(self_p)
        result = Zhash.__new result, true
        result
      end

      # Print contents of directory to open stream
      #
      # @param file [::FFI::Pointer, #to_ptr]
      # @param indent [Integer, #to_int, #to_i]
      # @return [void]
      def fprint(file, indent)
        raise DestroyedError unless @ptr
        self_p = @ptr
        indent = Integer(indent)
        result = ::CZMQ::FFI.zdir_fprint(self_p, file, indent)
        result
      end

      # Print contents of directory to stdout
      #
      # @param indent [Integer, #to_int, #to_i]
      # @return [void]
      def print(indent)
        raise DestroyedError unless @ptr
        self_p = @ptr
        indent = Integer(indent)
        result = ::CZMQ::FFI.zdir_print(self_p, indent)
        result
      end

      # Create a new zdir_watch actor instance:
      #
      #     zactor_t *watch = zactor_new (zdir_watch, NULL);
      #
      # Destroy zdir_watch instance:
      #
      #     zactor_destroy (&watch);
      #
      # Enable verbose logging of commands and activity:
      #
      #     zstr_send (watch, "VERBOSE");
      #
      # Subscribe to changes to a directory path:
      #
      #     zsock_send (watch, "ss", "SUBSCRIBE", "directory_path");
      #
      # Unsubscribe from changes to a directory path:
      #
      #     zsock_send (watch, "ss", "UNSUBSCRIBE", "directory_path");
      #
      # Receive directory changes:
      #     zsock_recv (watch, "sp", &path, &patches);
      #
      #     // Delete the received data.
      #     free (path);
      #     zlist_destroy (&patches);
      #
      # @param pipe [Zsock, #__ptr]
      # @param unused [::FFI::Pointer, #to_ptr]
      # @return [void]
      def self.watch(pipe, unused)
        pipe = pipe.__ptr if pipe
        result = ::CZMQ::FFI.zdir_watch(pipe, unused)
        result
      end

      # Self test of this class.
      #
      # @param verbose [Boolean]
      # @return [void]
      def self.test(verbose)
        verbose = !(0==verbose||!verbose) # boolean
        result = ::CZMQ::FFI.zdir_test(verbose)
        result
      end
    end
  end
end

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
