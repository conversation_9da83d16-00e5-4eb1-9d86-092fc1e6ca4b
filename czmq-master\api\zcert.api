<class name = "zcert" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    work with CURVE security certificates

    <constructor>
        Create and initialize a new certificate in memory
    </constructor>

    <constructor name = "new from">
        Accepts public/secret key pair from caller
        <argument name = "public key" type = "buffer" />
        <argument name = "secret key" type = "buffer" />
    </constructor>

    <constructor name = "new from txt" state = "draft">
        Accepts public/secret key text pair from caller
        <argument name = "public txt" type = "string" />
        <argument name = "secret txt" type = "string" />
    </constructor>

    <constructor name = "load">
        Load certificate from file
        <argument name = "filename" type = "string" />
    </constructor>

    <destructor>
        Destroy a certificate in memory
    </destructor>

    <method name = "public key">
        Return public part of key pair as 32-byte binary string
        <return type = "buffer" size = "32" />
    </method>

    <method name = "secret key">
        Return secret part of key pair as 32-byte binary string
        <return type = "buffer" size = "32" />
    </method>

    <method name = "public txt">
        Return public part of key pair as Z85 armored string
        <return type = "string" />
    </method>

    <method name = "secret txt">
        Return secret part of key pair as Z85 armored string
        <return type = "string" />
    </method>

    <method name = "set meta">
        Set certificate metadata from formatted string.
        <argument name = "name" type = "string" />
        <argument name = "format" type = "format" />
    </method>

    <method name = "unset meta" state = "draft" >
        Unset certificate metadata.
        <argument name = "name" type = "string" />
    </method>

    <method name = "meta">
        Get metadata value from certificate; if the metadata value doesn't
        exist, returns NULL.
        <argument name = "name" type = "string" />
        <return type = "string" />
    </method>

    <method name = "meta keys">
        Get list of metadata fields from certificate. Caller is responsible for
        destroying list. Caller should not modify the values of list items.
        <return type = "zlist" />
    </method>

    <method name = "save">
        Save full certificate (public + secret) to file for persistent storage
        This creates one public file and one secret file (filename + "_secret").
        <argument name = "filename" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "save public">
        Save public certificate only to file for persistent storage
        <argument name = "filename" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "save secret">
        Save secret certificate only to file for persistent storage
        <argument name = "filename" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "apply">
        Apply certificate to socket, i.e. use for CURVE security on socket.
        If certificate was loaded from public file, the secret key will be
        undefined, and this certificate will not work successfully.
        <argument name = "socket" type = "sockish" />
    </method>

    <method name = "dup">
        Return copy of certificate; if certificate is NULL or we exhausted
        heap memory, returns NULL.
        <return type = "zcert" fresh = "1" />
    </method>

    <method name = "eq">
        Return true if two certificates have the same keys
        <argument name = "compare" type = "zcert" />
        <return type = "boolean" />
    </method>

    <method name = "print">
        Print certificate contents to stdout
    </method>

    <method name = "test" singleton = "1">
        Self test of this class
        <argument name = "verbose" type = "boolean" />
    </method>
</class>
