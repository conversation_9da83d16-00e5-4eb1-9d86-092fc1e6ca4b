/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZhash::QZhash (zhash_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Create a new, empty hash container
QZhash::QZhash (QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zhash_new ();
}

///
//  Unpack binary frame into a new hash table. Packed data must follow format
//  defined by zhash_pack. Hash table is set to autofree. An empty frame
//  unpacks to an empty hash table.
QZhash* QZhash::unpack (QZframe *frame, QObject *qObjParent)
{
    return new QZhash (zhash_unpack (frame->self), qObjParent);
}

///
//  Destroy a hash container and all items in it
QZhash::~QZhash ()
{
    zhash_destroy (&self);
}

///
//  Insert item into hash table with specified key and item.
//  If key is already present returns -1 and leaves existing item unchanged
//  Returns 0 on success.
int QZhash::insert (const QString &key, void *item)
{
    int rv = zhash_insert (self, key.toUtf8().data(), item);
    return rv;
}

///
//  Update item into hash table with specified key and item.
//  If key is already present, destroys old item and inserts new one.
//  Use free_fn method to ensure deallocator is properly called on item.
void QZhash::update (const QString &key, void *item)
{
    zhash_update (self, key.toUtf8().data(), item);

}

///
//  Remove an item specified by key from the hash table. If there was no such
//  item, this function does nothing.
void QZhash::deleteNoConflict (const QString &key)
{
    zhash_delete (self, key.toUtf8().data());

}

///
//  Return the item at the specified key, or null
void * QZhash::lookup (const QString &key)
{
    void * rv = zhash_lookup (self, key.toUtf8().data());
    return rv;
}

///
//  Reindexes an item from an old key to a new key. If there was no such
//  item, does nothing. Returns 0 if successful, else -1.
int QZhash::rename (const QString &oldKey, const QString &newKey)
{
    int rv = zhash_rename (self, oldKey.toUtf8().data(), newKey.toUtf8().data());
    return rv;
}

///
//  Set a free function for the specified hash table item. When the item is
//  destroyed, the free function, if any, is called on that item.
//  Use this when hash items are dynamically allocated, to ensure that
//  you don't have memory leaks. You can pass 'free' or NULL as a free_fn.
//  Returns the item, or NULL if there is no such item.
void * QZhash::freefn (const QString &key, zhash_free_fn freeFn)
{
    void * rv = zhash_freefn (self, key.toUtf8().data(), freeFn);
    return rv;
}

///
//  Return the number of keys/items in the hash table
size_t QZhash::size ()
{
    size_t rv = zhash_size (self);
    return rv;
}

///
//  Make copy of hash table; if supplied table is null, returns null.
//  Does not copy items themselves. Rebuilds new table so may be slow on
//  very large tables. NOTE: only works with item values that are strings
//  since there's no other way to know how to duplicate the item value.
QZhash * QZhash::dup ()
{
    QZhash *rv = new QZhash (zhash_dup (self));
    return rv;
}

///
//  Return keys for items in table
QZlist * QZhash::keys ()
{
    QZlist *rv = new QZlist (zhash_keys (self));
    return rv;
}

///
//  Simple iterator; returns first item in hash table, in no given order,
//  or NULL if the table is empty. This method is simpler to use than the
//  foreach() method, which is deprecated. To access the key for this item
//  use zhash_cursor(). NOTE: do NOT modify the table while iterating.
void * QZhash::first ()
{
    void * rv = zhash_first (self);
    return rv;
}

///
//  Simple iterator; returns next item in hash table, in no given order,
//  or NULL if the last item was already returned. Use this together with
//  zhash_first() to process all items in a hash table. If you need the
//  items in sorted order, use zhash_keys() and then zlist_sort(). To
//  access the key for this item use zhash_cursor(). NOTE: do NOT modify
//  the table while iterating.
void * QZhash::next ()
{
    void * rv = zhash_next (self);
    return rv;
}

///
//  After a successful first/next method, returns the key for the item that
//  was returned. This is a constant string that you may not modify or
//  deallocate, and which lasts as long as the item in the hash. After an
//  unsuccessful first/next, returns NULL.
const QString QZhash::cursor ()
{
    const QString rv = QString (zhash_cursor (self));
    return rv;
}

///
//  Add a comment to hash table before saving to disk. You can add as many
//  comment lines as you like. These comment lines are discarded when loading
//  the file. If you use a null format, all comments are deleted.
void QZhash::comment (const QString &param)
{
    zhash_comment (self, "%s", param.toUtf8().data());

}

///
//  Serialize hash table to a binary frame that can be sent in a message.
//  The packed format is compatible with the 'dictionary' type defined in
//  http://rfc.zeromq.org/spec:35/FILEMQ, and implemented by zproto:
//
//     ; A list of name/value pairs
//     dictionary      = dict-count *( dict-name dict-value )
//     dict-count      = number-4
//     dict-value      = longstr
//     dict-name       = string
//
//     ; Strings are always length + text contents
//     longstr         = number-4 *VCHAR
//     string          = number-1 *VCHAR
//
//     ; Numbers are unsigned integers in network byte order
//     number-1        = 1OCTET
//     number-4        = 4OCTET
//
//  Comments are not included in the packed data. Item values MUST be
//  strings.
QZframe * QZhash::pack ()
{
    QZframe *rv = new QZframe (zhash_pack (self));
    return rv;
}

///
//  Save hash table to a text file in name=value format. Hash values must be
//  printable strings; keys may not contain '=' character. Returns 0 if OK,
//  else -1 if a file error occurred.
int QZhash::save (const QString &filename)
{
    int rv = zhash_save (self, filename.toUtf8().data());
    return rv;
}

///
//  Load hash table from a text file in name=value format; hash table must
//  already exist. Hash values must printable strings; keys may not contain
//  '=' character. Returns 0 if OK, else -1 if a file was not readable.
int QZhash::load (const QString &filename)
{
    int rv = zhash_load (self, filename.toUtf8().data());
    return rv;
}

///
//  When a hash table was loaded from a file by zhash_load, this method will
//  reload the file if it has been modified since, and is "stable", i.e. not
//  still changing. Returns 0 if OK, -1 if there was an error reloading the
//  file.
int QZhash::refresh ()
{
    int rv = zhash_refresh (self);
    return rv;
}

///
//  Set hash for automatic value destruction. Note that this assumes that
//  values are NULL-terminated strings. Do not use with different types.
void QZhash::autofree ()
{
    zhash_autofree (self);

}

///
//  Self test of this class.
void QZhash::test (bool verbose)
{
    zhash_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
