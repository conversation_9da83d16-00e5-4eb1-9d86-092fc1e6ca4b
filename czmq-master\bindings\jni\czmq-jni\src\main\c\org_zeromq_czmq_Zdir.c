/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#include <stdio.h>
#include <stdlib.h>
#include <jni.h>
#include "czmq.h"
#include "org_zeromq_czmq_Zdir.h"

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zdir__1_1new (JNIEnv *env, jclass c, jstring path, jstring parent)
{
    char *path_ = (char *) (*env)->GetStringUTFChars (env, path, NULL);
    char *parent_ = (char *) (*env)->GetStringUTFChars (env, parent, NULL);
    //  Disable CZMQ signal handling; allow Java to deal with it
    zsys_handler_set (NULL);
    jlong new_ = (jlong) (intptr_t) zdir_new (path_, parent_);
    (*env)->ReleaseStringUTFChars (env, path, path_);
    (*env)->ReleaseStringUTFChars (env, parent, parent_);
    return new_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zdir__1_1destroy (JNIEnv *env, jclass c, jlong self)
{
    zdir_destroy ((zdir_t **) &self);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zdir__1_1path (JNIEnv *env, jclass c, jlong self)
{
    char *path_ = (char *) zdir_path ((zdir_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, path_);
    return return_string_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zdir__1_1modified (JNIEnv *env, jclass c, jlong self)
{
    jlong modified_ = (jlong) zdir_modified ((zdir_t *) (intptr_t) self);
    return modified_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zdir__1_1cursize (JNIEnv *env, jclass c, jlong self)
{
    jlong cursize_ = (jlong) zdir_cursize ((zdir_t *) (intptr_t) self);
    return cursize_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zdir__1_1count (JNIEnv *env, jclass c, jlong self)
{
    jlong count_ = (jlong) zdir_count ((zdir_t *) (intptr_t) self);
    return count_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zdir__1_1list (JNIEnv *env, jclass c, jlong self)
{
    jlong list_ = (jlong) (intptr_t) zdir_list ((zdir_t *) (intptr_t) self);
    return list_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zdir__1_1listPaths (JNIEnv *env, jclass c, jlong self)
{
    jlong list_paths_ = (jlong) (intptr_t) zdir_list_paths ((zdir_t *) (intptr_t) self);
    return list_paths_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zdir__1_1remove (JNIEnv *env, jclass c, jlong self, jboolean force)
{
    zdir_remove ((zdir_t *) (intptr_t) self, (bool) force);
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zdir__1_1diff (JNIEnv *env, jclass c, jlong older, jlong newer, jstring alias)
{
    char *alias_ = (char *) (*env)->GetStringUTFChars (env, alias, NULL);
    jlong diff_ = (jlong) (intptr_t) zdir_diff ((zdir_t *) (intptr_t) older, (zdir_t *) (intptr_t) newer, alias_);
    (*env)->ReleaseStringUTFChars (env, alias, alias_);
    return diff_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zdir__1_1resync (JNIEnv *env, jclass c, jlong self, jstring alias)
{
    char *alias_ = (char *) (*env)->GetStringUTFChars (env, alias, NULL);
    jlong resync_ = (jlong) (intptr_t) zdir_resync ((zdir_t *) (intptr_t) self, alias_);
    (*env)->ReleaseStringUTFChars (env, alias, alias_);
    return resync_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zdir__1_1cache (JNIEnv *env, jclass c, jlong self)
{
    jlong cache_ = (jlong) (intptr_t) zdir_cache ((zdir_t *) (intptr_t) self);
    return cache_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zdir__1_1print (JNIEnv *env, jclass c, jlong self, jint indent)
{
    zdir_print ((zdir_t *) (intptr_t) self, (int) indent);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zdir__1_1watch (JNIEnv *env, jclass c, jlong pipe, jlong unused)
{
    zdir_watch ((zsock_t *) (intptr_t) pipe, (void *) (intptr_t) unused);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zdir__1_1test (JNIEnv *env, jclass c, jboolean verbose)
{
    zdir_test ((bool) verbose);
}

