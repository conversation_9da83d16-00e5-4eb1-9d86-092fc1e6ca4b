<class name = "zosc" state = "draft">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    Create and decode Open Sound Control messages. (OSC)

    OSC is a serialisation format (and usually transported over UDP) which is 
    supported by many applications and appliances. It is a de facto protocol
    for networking sound synthesizers, computers, and other multimedia devices 
    for purposes such as musical performance or show control. It is also often
    used for rapid prototyping purposes due to the support by many applications
    and frameworks in this field. With ZeroMQ's DGRAM sockets it is possible
    to use ZeroMQ to send and receive OSC messages which can be understood by
    any device supporting OSC. 

    Example creating an OSC message:

        zosc_t* conm = zosc_create("/someaddress", "iihfdscF", 
                            1, 2, 3, 3.14, 6.283185307179586, "greetings", 'q');

    Decoding a message:

        int rc = zosc_retr(oscmsg, "iihfdscF", &intx, &inty, &intz, &floatz, 
                            &doublez, &strings, &charq, &someBool);

    See the class's test method for more examples how to use the class.

    <constructor>
        Create a new empty OSC message with the specified address string.
        <argument name = "address" type = "string" />
    </constructor>

    <constructor name = "fromframe">
        Create a new OSC message from the specified zframe. Takes ownership of
        the zframe. 
        <argument name = "frame" type = "zframe" />
    </constructor>

    <constructor name = "frommem" state = "draft">
        Create a new zosc message from memory. Take ownership of the memory 
        and calling free on the data after construction.
        <argument name = "data" type = "buffer" c_type = "char *" />
        <argument name = "size" type = "size" />
    </constructor>

    <constructor name = "fromstring" state = "draft">
        Create a new zosc message from a string. This the same syntax as
        zosc_create but written as a single line string.
        <argument name = "oscstring" type = "string" />
    </constructor>

    <constructor name = "create">
        Create a new zosc message from the given format and arguments.
        The format type tags are as follows:
          i - 32bit integer
          h - 64bit integer
          f - 32bit floating point number (IEEE)
          d - 64bit (double) floating point number
          s - string (NULL terminated)
          t = timetag: an OSC timetag in NTP format (uint64_t)
          S - symbol
          c - char
          m - 4 byte midi packet (8 digits hexadecimal)
          T - TRUE (no value required)
          F - FALSE (no value required)
          N - NIL (no value required)
          I - Impulse (for triggers) or INFINITUM (no value required)
          b - binary blob
        <argument name = "address" type = "string" />
        <argument name = "format" type = "string" variadic = "1" />
    </constructor>

    <destructor>
        Destroy an OSC message
    </destructor>

    <method name = "size">
        Return chunk data size
        <return type = "size" />
    </method>

    <method name = "data">
        Return OSC chunk data. Caller does not own the data!
        <return type = "buffer" mutable = "1" size = ".size" />
    </method>

    <method name = "address">
        Return the OSC address string
        <return type = "string" />
    </method>

    <method name = "format">
        Return the OSC format of the message.
          i - 32bit integer
          h - 64bit integer
          f - 32bit floating point number (IEEE)
          d - 64bit (double) floating point number
          s - string (NULL terminated)
          t = timetag: an OSC timetag in NTP format (uint64_t)
          S - symbol
          c - char
          m - 4 byte midi packet (8 digits hexadecimal)
          T - TRUE (no value required)
          F - FALSE (no value required)
          N - NIL (no value required)
          I - Impulse (for triggers) or INFINITUM (no value required)
          b - binary blob
        <return type = "string" />
    </method>

    <method name = "append">
        Append data to the osc message. The format describes the data that
        needs to be appended in the message. This essentially relocates all 
        data!
        The format type tags are as follows:
          i - 32bit integer
          h - 64bit integer
          f - 32bit floating point number (IEEE)
          d - 64bit (double) floating point number
          s - string (NULL terminated)
          t = timetag: an OSC timetag in NTP format (uint64_t)
          S - symbol
          c - char
          m - 4 byte midi packet (8 digits hexadecimal)
          T - TRUE (no value required)
          F - FALSE (no value required)
          N - NIL (no value required)
          I - Impulse (for triggers) or INFINITUM (no value required)
          b - binary blob
        <argument name = "format" type = "string" variadic = "1" />
        <return type = "integer" />
    </method>

    <method name = "retr">
        Retrieve the values provided by the given format. Note that zosc_retr 
        creates the objects and the caller must destroy them when finished. 
        The supplied pointers do not need to be initialized. Returns 0 if 
        successful, or -1 if it failed to retrieve a value in which case the
        pointers are not modified. If an argument pointer is NULL is skips the
        value. See the format method for a detailed list op type tags for the 
        format string.
        <argument name = "format" type = "string" variadic = "1" />
        <return type = "integer" />
    </method>

    <method name = "dup">
        Create copy of the message, as new chunk object. Returns a fresh zosc_t
        object, or null if there was not enough heap memory. If chunk is null,
        returns null.
        <return type = "zosc" fresh = "1" />
    </method>

    <method name = "pack">
        Transform zosc into a zframe that can be sent in a message.
        <return type = "zframe" fresh = "1" />
    </method>

    <method name = "packx" state = "draft" singleton = "1">
        Transform zosc into a zframe that can be sent in a message.
        Take ownership of the chunk.
        <argument name = "self_p" type = "zosc" by_reference = "1" />
        <return type = "zframe" fresh = "1" />
    </method>

    <method name = "unpack" singleton = "1">
        Transform a zframe into a zosc.
        <argument name = "frame" type = "zframe" mutable = "1" />
        <return type = "zosc" fresh = "1" />
    </method>

    <method name = "dump">
        Return a string describing the the OSC message. The returned string must be freed by the caller.
        <return type = "string" fresh="1" />
    </method>

    <method name = "print">
        Dump OSC message to stdout, for debugging and tracing.
    </method>

    <method name = "is" singleton = "1">
        Probe the supplied object, and report if it looks like a zosc_t.
        <argument name = "self" type = "anything" mutable = "1" />
        <return type = "boolean" />
    </method>

    <method name = "first">
        Return a pointer to the item at the head of the OSC data. 
        Sets the given char argument to the type tag of the data.
        If the message is empty, returns NULL and the sets the 
        given char to NULL.
        <argument name = "type" type = "char" by_reference = "1" />
        <return type = "anything" mutable = "0" />
    </method>

    <method name = "next">
        Return the next item of the OSC message. If the list is empty, returns 
        NULL. To move to the start of the OSC message call zosc_first (). 
        <argument name = "type" type = "char" by_reference = "1" />
        <return type = "anything" mutable = "0" />
    </method>

    <method name = "last">
        Return a pointer to the item at the tail of the OSC message. 
        Sets the given char argument to the type tag of the data. If 
        the message is empty, returns NULL. 
        <argument name = "type" type = "char" by_reference = "1" />
        <return type = "anything" mutable = "0" />
    </method>

    <method name = "pop int32">
        Set the provided 32 bit integer from value at the current cursor position in the message. 
        If the type tag at the current position does not correspond it will fail and 
        return -1. Returns 0 on success.
        <argument name = "val" type = "integer" by_reference = "1" />
        <return type = "integer" />
    </method>

    <method name = "pop int64">
        Set the provided 64 bit integer from the value at the current cursor position in the message. 
        If the type tag at the current position does not correspond it will fail and 
        return -1. Returns 0 on success.
        <argument name = "val" type = "msecs" by_reference = "1" />
        <return type = "integer" />
    </method>

    <method name = "pop float">
        Set the provided float from the value at the current cursor position in the message. 
        If the type tag at the current position does not correspond it will fail and 
        return -1. Returns 0 on success.
        <argument name = "val" type = "real" size = "4" by_reference = "1" />
        <return type = "integer" />
    </method>

    <method name = "pop double">
        Set the provided double from the value at the current cursor position in the message. 
        If the type tag at the current position does not correspond it will fail and 
        return -1. Returns 0 on success.
        <argument name = "val" type = "real" size = "8" by_reference = "1" />
        <return type = "integer" />
    </method>

    <method name = "pop string">
        Set the provided string from the value at the current cursor position in the message. 
        If the type tag at the current position does not correspond it will fail and 
        return -1. Returns 0 on success. Caller owns the string!
        <argument name = "val" type = "string" by_reference = "1" />
        <return type = "integer" />
    </method>

    <method name = "pop char">
        Set the provided char from the value at the current cursor position in the message. 
        If the type tag at the current position does not correspond it will fail and 
        return -1. Returns 0 on success.
        <argument name = "val" type = "char" by_reference = "1" />
        <return type = "integer" />
    </method>

    <method name = "pop bool">
        Set the provided boolean from the type tag in the message. Booleans are not represented
        in the data in the message, only in the type tag. If the type tag at the current 
        position does not correspond it will fail and return -1. Returns 0 on success.
        <argument name = "val" type = "boolean" by_reference = "1" />
        <return type = "integer" />
    </method>

    <method name = "pop midi">
        Set the provided 4 bytes (unsigned 32bit int) from the value at the current 
        cursor position in the message. If the type tag at the current position does 
        not correspond it will fail and return -1. Returns 0 on success.
        <argument name = "val" type = "number" size = "4" by_reference = "1" />
        <return type = "integer" />
    </method>

</class>
