<!--
    zgossip_msg - ZeroMQ Gossip Protocol

    Codec class for zgossip_msg.

    ** WARNING *************************************************************
    THIS SOURCE FILE IS 100% GENERATED. If you edit this file, you will lose
    your changes at the next build cycle. This is great for temporary printf
    statements. DO NOT MAKE ANY CHANGES YOU WISH TO KEEP. The correct places
    for commits are:

     * The XML model used for this code generation: zgossip_msg.xml, or
     * The code generation script that built this file: zproto_codec_c
    ************************************************************************
    Copyright (c) the Contributors as noted in the AUTHORS file.       
    This file is part of CZMQ, the high-level C binding for 0MQ:       
    http://czmq.zeromq.org.                                            
                                                                       
    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.           
 -->
<class name = "zgossip_msg">
    <constant name = "hello" value = "1" />
    <constant name = "publish" value = "2" />
    <constant name = "ping" value = "3" />
    <constant name = "pong" value = "4" />
    <constant name = "invalid" value = "5" />

<constructor>
    Create a new empty zgossip_msg
</constructor>

<destructor>
    Destroy a zgossip_msg instance
</destructor>

<method name = "recv">
    Receive a zgossip_msg from the socket. Returns 0 if OK, -1 if
    there was an error. Blocks if there is no message waiting.
    <argument name = "input" type = "zsock" />
    <return type = "integer" />
</method>

<method name = "send">
    Send the zgossip_msg to the output socket, does not destroy it
    <argument name = "output" type = "zsock" />
    <return type = "integer" />
</method>

<method name = "print">
    Print contents of message to stdout
</method>

<method name = "routing id">
    Get the message routing id, as a frame
    <return type = "zframe" />
</method>

<method name = "set routing id">
    Set the message routing id from a frame
    <argument name = "routing id" type = "zframe" />
</method>

<method name = "id">
    Get the zgossip_msg message id
    <return type = "integer" />
</method>

<method name = "set id">
    Set the zgossip_msg message id
    <argument name = "id" type = "integer" />
</method>

<method name = "command">
    Get the zgossip_msg message id as printable text
    <return type = "string" />
</method>

<method name = "key">
    Get the key field
    <return type = "string" />
</method>
<method name = "set key">
    Set the key field
    <argument name = "key" type = "string" />
</method>
<method name = "value">
    Get the value field
    <return type = "string" />
</method>
<method name = "set value">
    Set the value field
    <argument name = "value" type = "string" />
</method>
<method name = "ttl">
    Get the ttl field
    <return type = "number" size = "4" />
</method>
<method name = "set ttl">
    Set the ttl field
    <argument name = "ttl" type = "number" size = "4" />
</method>
</class>
