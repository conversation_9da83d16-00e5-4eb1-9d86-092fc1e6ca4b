// -*- C++ -*-

//=============================================================================
/**
 *  @file    os_cpio.h
 *
 *  cpio archive values
 *
 *  $Id: os_cpio.h 80826 2008-03-04 14:51:23Z wotte $
 *
 *  <AUTHOR> <<EMAIL>>
 *  <AUTHOR> code was originally in various places including ace/OS.h.
 */
//=============================================================================

#ifndef ACE_OS_INCLUDE_OS_CPIO_H
#define ACE_OS_INCLUDE_OS_CPIO_H

#include /**/ "ace/pre.h"

#include /**/ "ace/config-all.h"

#if !defined (ACE_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* ACE_LACKS_PRAGMA_ONCE */

#if !defined (ACE_LACKS_CPIO_H)
# include /**/ <cpio.h>
#endif /* !ACE_LACKS_CPIO_H */

// Place all additions (especially function declarations) within extern "C" {}
#ifdef __cplusplus
extern "C"
{
#endif /* __cplusplus */

#ifdef __cplusplus
}
#endif /* __cplusplus */

#include /**/ "ace/post.h"
#endif /* ACE_OS_INCLUDE_OS_CPIO_H */
