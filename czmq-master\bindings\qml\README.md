```
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
```
# qml_czmq

QML bindings for creating UI applications using the "czmq" C library.

## Setting up a Build Environment

The following is intended to be a complete guide to setting up a build
environment that can build for Android (as well as your desktop, although
that part is considerably easier). To that end, if you encounter parts
where steps are missing or unclear, please file an issue or pull request
about it so that others might learn from your research.

Eventually, we'd like to remove some of these steps where possible to make
this process simpler. If you have ideas about removing or simplifying steps,
please file an issue or pull request so that others might be saved some complexity.

### C Library Dependencies

If you are building for Android, you can skip this step, as the necessary
dependencies are automatically pulled down and built by the `vendor/build`
scripts. If you are developing and testing on your desktop, you will need
a local installation of the czmq library to link against and include.

### Ruby

You will need an installation of
[Ruby 1.9 or greater](https://www.ruby-lang.org/en/downloads/)
to run some of the build scripts. In the future, this requirement may be
eliminated and replaced by "pure" shell scripts with no Ruby dependency.

On Linux, you can get Ruby from your package manager. On OSX, use
[brew](http://brew.sh/). If you are already a Ruby developer and have an
existing system to manage your Rubies, use what you are comfortable with.

Once Ruby is installed, you will need the
[qt-commander](https://github.com/jemc/qt-commander) gem, a utility package
for parsing the Qt Creator IDE configuration files to pull out key information
for building projects and project dependencies from the command line without
the IDE. You will also need the [rake](https://github.com/jimweirich/rake)
gem, a task automation package with usage similiar to the `make` command.
You can install both using the `gem` command:
```
gem install rake
gem install qt-commander
```

### Qt 5

You will need [Qt 5](http://www.qt.io/download-open-source/) and a
fully-functioning environment for Qt that can build and deploy to Android.

Qt features [a guide for Android](http://qt-project.org/doc/qt-5/android-support.html)
but here are a few tips to get you started:

* You will need the Android [SDK](https://developer.android.com/sdk/index.html)
and [NDK](https://developer.android.com/tools/sdk/ndk/index.html).
Install them to any path you like, but you will eventually need to point
the Qt Creator IDE to them.
* You will need Java 7 - JRE and JDK, and 'ant'. You can usually get
these through your package manager.
* You don't need the Eclipse IDE or bundle - all work is done from the
command line or through the Qt Creator IDE.

At the end of this setup, you should be able to use the Qt Creator IDE to
build and deploy an out-of-the-box simple 'Hello World' app for QML.

To ready your device for deployment:

* Be sure your Android device has
[developer options enabled](http://developer.android.com/tools/device.html#developer-device-options).
* Run the adb server with a privileged user (`sudo adb start-server`).
If you previously tried to run adb with an unprivileged user, you'll need
to stop the old adb server first (`sudo adb kill-server`). You will need
the `adb` binary from the Android development kit in your `$PATH`.
* Connect your device via USB and set it to "USB Debugging Mode".
* Run `adb devices` to make sure your device is detected and ready.

To create the temporary project:

* Click `File`->`New File or Project...`
* Choose `Applications`/`Qt Quick Application`
* Enter a name and location for the temporary project
* Choose the latest "Qt Quick Component Set"
* Select the relevant kits you want to be able to deploy with (this should
include the Android kit(s) that you set up earlier)
* Choose a version control system to use (if you like)
* Finish the creation wizard

Once you have a project to deploy:

* In the lower-left corner of the IDE (above the build icons),
select from the drop-down menu the Android kit that matches the
architectureof your connected device.
* Click the "Run" button (just below the kit menu) to deploy.
* Watch the bottom output pane of the IDE; it will show output from
the build and packaging process, transfer the file, then show in a
different tab output from the program execution as it runs on your device.

## Build tasks

### Install Locally and Run Tests
```
rake test
```

Use this command when developing and testing the library. A copy of the
czmq library is installed locally where Qt can find it for
running desktop applications that use the library, and tests are run.

You will need to have the czmq C library built and installed
on your machine.

### Install Locally for Android
```
rake android
```

Use this command to install a copy of the library locally where Qt can find
it later for bundling into an application you are deploying to Android.
The build will repeat for each android kit you have configured in the Qt
Creator IDE so that the installed library is available for all kits.

In order to build for android, the qml_czmq needs access to
an Android build of the czmq library and all its dependencies.
This can be done one of two ways:

1. Clone the czmq library and all its dependencies from source
into the same folder. If all project repos are side-by-side in the same
"workspace" folder (as is typical for many users' workflows), they can
detect eachother automatically and build in a chain when you run
`rake android` in the qml_czmq folder.
2. Manually export the `CZMQ_ROOT` environment variable as the path
to the CZMQ source code root. Do the same for any other projects
that you are prompted for when you run `rake android`. When all environment
variables are resolved, they will build in a chain.

For all of the zeromq libraries in the dependencies, the source code will
be copied to a temporary directory for building, but installed to the
`$(XXX_ROOT)/builds/android/prefix/$(TOOLCHAIN_NAME)` directory within
the original source tree. If you need to run builds for individual projects,
use the `$(XXX_ROOT)/builds/android/prefix/build.sh` command. The build
script will skip itself if it was already installed to
`$(XXX_ROOT)/builds/android/prefix/$(TOOLCHAIN_NAME)`, so at times you
may need to delete that directory to trigger a clean build.

```
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
```
