################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
exists(config.pri):infile(config.pri, QCZMQ_LIBRARY, yes): CONFIG += qczmq-uselib
TEMPLATE += fakelib
QCZMQ_LIBNAME = qczmq
CONFIG(debug, debug|release) {
    mac:QCZMQ_LIBNAME = $$member(QCZMQ_LIBNAME, 0)_debug
    else:win32:QCZMQ_LIBNAME = $$member(QCZMQ_LIBNAME, 0)d
}
TEMPLATE -= fakelib
CONFIG += link_pkgconfig
PKGCONFIG += libczmq
QCZMQ_LIBDIR = $$PWD/lib
unix:qczmq-uselib:!qczmq-buildlib:QMAKE_RPATHDIR += $$QCZMQ_LIBDIR
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
