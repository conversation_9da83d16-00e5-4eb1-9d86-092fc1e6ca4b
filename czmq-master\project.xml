<project
    name = "CZMQ"
    description = "The high-level C binding for 0MQ"
    script = "zproject.gsl"
    email = "<EMAIL>"
    license = "MPL-2.0"
    url = "https://github.com/zeromq/czmq"
    repository = "https://github.com/zeromq/czmq"
    >
    <include filename = "license.xml" />
    <version major = "4" minor = "2" patch = "2" />
    <abi current = "6" revision = "2" age = "2" />
    <use project = "libzmq" />
    <use project = "uuid" optional = "1" implied = "1" />
    <use project = "systemd" optional = "1" implied = "1" min_major = "200" />
    <use project = "lz4" optional = "1" implied = "1" />
    <use project = "libcurl" optional = "1" implied = "1" min_major = "7" min_minor = "28">
      <add_config_opts>--with-secure-transport</add_config_opts> 
    </use>
    <use project = "nss" optional = "1" implied = "1" />
    <use project = "libmicrohttpd"
         tarball = "http://ftp.gnu.org/gnu/libmicrohttpd/libmicrohttpd-0.9.44.tar.gz"
         implied = "1" optional = "1"  />

    <check_symbol_exists symbol = "AI_V4MAPPED" include = "netdb.h" />

    <target name = "*" />

    <!-- These classes have an API model -->
    <class name = "zactor" />
    <class name = "zargs" />
    <class name = "zarmour" />
    <class name = "zcert" />
    <class name = "zcertstore" />
    <class name = "zchunk" />
    <class name = "zclock" />
    <class name = "zconfig" />
    <class name = "zdigest" />
    <class name = "zdir" />
    <class name = "zdir_patch" />
    <class name = "zfile" />
    <class name = "zframe" />
    <class name = "zhash" />
    <class name = "zhashx" />
    <class name = "ziflist" />
    <class name = "zlist" />
    <class name = "zlistx" />
    <class name = "zloop" />
    <class name = "zmsg" />
    <class name = "zpoller" />
    <class name = "zproc" />
    <class name = "zsock" />
    <class name = "zstr" />
    <class name = "zsys" />
    <class name = "ztimerset" />
    <class name = "ztrie" />
    <class name = "zuuid" />
    <class name = "zhttp_client" />
    <class name = "zhttp_server" />
    <class name = "zhttp_server_options" />
    <class name = "zhttp_request"  />
    <class name = "zhttp_response" />
    <class name = "zosc" />

    <!-- These classes have no API model -->
    <class name = "zauth" state = "stable" />
    <class name = "zbeacon" state = "stable" />
    <class name = "zgossip" state = "stable" />
    <class name = "zmonitor" state = "stable" />
    <class name = "zproxy" state = "stable" />
    <class name = "zrex" state = "stable" />

    <!-- Models that we build using GSL -->
    <model name = "sockopts" />
    <model name = "zgossip" />
    <model name = "zgossip_msg" />
    <class name = "zgossip_msg" private = "1" />

    <!-- Other source files in src that we need to package -->
    <extra name = "zsock_option.inc" />
    <extra name = "zgossip_engine.inc" />
    <extra name = "zhash_primes.inc" />
    <extra name = "foreign/sha1/sha1.inc_c" />
    <extra name = "foreign/sha1/sha1.h" />
    <extra name = "foreign/slre/slre.inc_c" />
    <extra name = "foreign/slre/slre.h" />
    <extra name = "foreign/slre/readme.txt" />

    <!-- Command-line utilities -->
    <main name = "zmakecert" />

    <!-- Private command-line utilities -->
    <main name = "zsp" private = "1" />
    <main name = "test_randof" private = "1" />

    <!-- target configuration -->
    <target name = "nuget">
        <option name = "id" value = "czmq_vc120" />
        <option name = "platformtoolset" value = "v120" />
        <option name = "dependency">
            <item name = "libzmq_vc120" value = "*******" />
        </option>
    </target>

    <target name = "travis" >
        <option name = "check_zproject" value = "1" />
        <option name = "check_abi_compliance" value = "1" />
    </target>

    <target name = "gh_actions" >
        <option name = "check_zproject" value = "1" />
        <option name = "check_abi_compliance" value = "1" />
    </target>

    <target name = "jenkins" >
        <!-- While Jenkinsfile does not build prerequisites,
             request via node label that they are expected
             present in that OS deployment / buildroot -->
        <option name = "agent_label" value = "libzmq4-dev && ( linux || macosx || bsd || solaris || posix || windows )" />
        <option name = "check_sequential" value = "1" />
        <option name = "use_test_timeout" value = "30" />
        <option name = "use_test_retry" value = "3" />
    </target>

</project>
