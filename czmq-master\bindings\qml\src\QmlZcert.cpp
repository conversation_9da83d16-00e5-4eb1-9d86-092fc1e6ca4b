/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZcert.h"


///
//  Return public part of key pair as 32-byte binary string
const byte *QmlZcert::publicKey () {
    return zcert_public_key (self);
};

///
//  Return secret part of key pair as 32-byte binary string
const byte *QmlZcert::secretKey () {
    return zcert_secret_key (self);
};

///
//  Return public part of key pair as Z85 armored string
const QString QmlZcert::publicTxt () {
    return QString (zcert_public_txt (self));
};

///
//  Return secret part of key pair as Z85 armored string
const QString QmlZcert::secretTxt () {
    return QString (zcert_secret_txt (self));
};

///
//  Set certificate metadata from formatted string.
void QmlZcert::setMeta (const QString &name, const QString &format) {
    zcert_set_meta (self, name.toUtf8().data(), "%s", format.toUtf8().data());
};

///
//  Unset certificate metadata.
void QmlZcert::unsetMeta (const QString &name) {
    zcert_unset_meta (self, name.toUtf8().data());
};

///
//  Get metadata value from certificate; if the metadata value doesn't
//  exist, returns NULL.
const QString QmlZcert::meta (const QString &name) {
    return QString (zcert_meta (self, name.toUtf8().data()));
};

///
//  Get list of metadata fields from certificate. Caller is responsible for
//  destroying list. Caller should not modify the values of list items.
QmlZlist *QmlZcert::metaKeys () {
    QmlZlist *retQ_ = new QmlZlist ();
    retQ_->self = zcert_meta_keys (self);
    return retQ_;
};

///
//  Save full certificate (public + secret) to file for persistent storage
//  This creates one public file and one secret file (filename + "_secret").
int QmlZcert::save (const QString &filename) {
    return zcert_save (self, filename.toUtf8().data());
};

///
//  Save public certificate only to file for persistent storage
int QmlZcert::savePublic (const QString &filename) {
    return zcert_save_public (self, filename.toUtf8().data());
};

///
//  Save secret certificate only to file for persistent storage
int QmlZcert::saveSecret (const QString &filename) {
    return zcert_save_secret (self, filename.toUtf8().data());
};

///
//  Apply certificate to socket, i.e. use for CURVE security on socket.
//  If certificate was loaded from public file, the secret key will be
//  undefined, and this certificate will not work successfully.
void QmlZcert::apply (void *socket) {
    zcert_apply (self, socket);
};

///
//  Return copy of certificate; if certificate is NULL or we exhausted
//  heap memory, returns NULL.
QmlZcert *QmlZcert::dup () {
    QmlZcert *retQ_ = new QmlZcert ();
    retQ_->self = zcert_dup (self);
    return retQ_;
};

///
//  Return true if two certificates have the same keys
bool QmlZcert::eq (QmlZcert *compare) {
    return zcert_eq (self, compare->self);
};

///
//  Print certificate contents to stdout
void QmlZcert::print () {
    zcert_print (self);
};


QObject* QmlZcert::qmlAttachedProperties(QObject* object) {
    return new QmlZcertAttached(object);
}


///
//  Self test of this class
void QmlZcertAttached::test (bool verbose) {
    zcert_test (verbose);
};

///
//  Create and initialize a new certificate in memory
QmlZcert *QmlZcertAttached::construct () {
    QmlZcert *qmlSelf = new QmlZcert ();
    qmlSelf->self = zcert_new ();
    return qmlSelf;
};

///
//  Accepts public/secret key pair from caller
QmlZcert *QmlZcertAttached::constructFrom (const byte *publicKey, const byte *secretKey) {
    QmlZcert *qmlSelf = new QmlZcert ();
    qmlSelf->self = zcert_new_from (publicKey, secretKey);
    return qmlSelf;
};

///
//  Accepts public/secret key text pair from caller
QmlZcert *QmlZcertAttached::constructFromTxt (const QString &publicTxt, const QString &secretTxt) {
    QmlZcert *qmlSelf = new QmlZcert ();
    qmlSelf->self = zcert_new_from_txt (publicTxt.toUtf8().data(), secretTxt.toUtf8().data());
    return qmlSelf;
};

///
//  Load certificate from file
QmlZcert *QmlZcertAttached::load (const QString &filename) {
    QmlZcert *qmlSelf = new QmlZcert ();
    qmlSelf->self = zcert_load (filename.toUtf8().data());
    return qmlSelf;
};

///
//  Destroy a certificate in memory
void QmlZcertAttached::destruct (QmlZcert *qmlSelf) {
    zcert_destroy (&qmlSelf->self);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
