/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZchunk::QZchunk (zchunk_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Create a new chunk of the specified size. If you specify the data, it
//  is copied into the chunk. If you do not specify the data, the chunk is
//  allocated and left empty, and you can then add data using zchunk_append.
QZchunk::QZchunk (const void *data, size_t size, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zchunk_new (data, size);
}

///
//  Create a new chunk from memory. Take ownership of the memory and calling the destructor
//  on destroy.
QZchunk* QZchunk::frommem (void *data, size_t size, zchunk_destructor_fn destructor, void *hint, QObject *qObjParent)
{
    return new QZchunk (zchunk_frommem (data, size, destructor, hint), qObjParent);
}

///
//  Destroy a chunk
QZchunk::~QZchunk ()
{
    zchunk_destroy (&self);
}

///
//  Resizes chunk max_size as requested; chunk_cur size is set to zero
void QZchunk::resize (size_t size)
{
    zchunk_resize (self, size);

}

///
//  Return chunk cur size
size_t QZchunk::size ()
{
    size_t rv = zchunk_size (self);
    return rv;
}

///
//  Return chunk max size
size_t QZchunk::maxSize ()
{
    size_t rv = zchunk_max_size (self);
    return rv;
}

///
//  Return chunk data
byte * QZchunk::data ()
{
    byte * rv = zchunk_data (self);
    return rv;
}

///
//  Set chunk data from user-supplied data; truncate if too large. Data may
//  be null. Returns actual size of chunk
size_t QZchunk::set (const void *data, size_t size)
{
    size_t rv = zchunk_set (self, data, size);
    return rv;
}

///
//  Fill chunk data from user-supplied octet
size_t QZchunk::fill (byte filler, size_t size)
{
    size_t rv = zchunk_fill (self, filler, size);
    return rv;
}

///
//  Append user-supplied data to chunk, return resulting chunk size. If the
//  data would exceeded the available space, it is truncated. If you want to
//  grow the chunk to accommodate new data, use the zchunk_extend method.
size_t QZchunk::append (const void *data, size_t size)
{
    size_t rv = zchunk_append (self, data, size);
    return rv;
}

///
//  Append user-supplied data to chunk, return resulting chunk size. If the
//  data would exceeded the available space, the chunk grows in size.
size_t QZchunk::extend (const void *data, size_t size)
{
    size_t rv = zchunk_extend (self, data, size);
    return rv;
}

///
//  Copy as much data from 'source' into the chunk as possible; returns the
//  new size of chunk. If all data from 'source' is used, returns exhausted
//  on the source chunk. Source can be consumed as many times as needed until
//  it is exhausted. If source was already exhausted, does not change chunk.
size_t QZchunk::consume (QZchunk *source)
{
    size_t rv = zchunk_consume (self, source->self);
    return rv;
}

///
//  Returns true if the chunk was exhausted by consume methods, or if the
//  chunk has a size of zero.
bool QZchunk::exhausted ()
{
    bool rv = zchunk_exhausted (self);
    return rv;
}

///
//  Read chunk from an open file descriptor
QZchunk * QZchunk::read (FILE *handle, size_t bytes)
{
    QZchunk *rv = new QZchunk (zchunk_read (handle, bytes));
    return rv;
}

///
//  Write chunk to an open file descriptor
int QZchunk::write (FILE *handle)
{
    int rv = zchunk_write (self, handle);
    return rv;
}

///
//  Try to slurp an entire file into a chunk. Will read up to maxsize of
//  the file. If maxsize is 0, will attempt to read the entire file and
//  fail with an assertion if that cannot fit into memory. Returns a new
//  chunk containing the file data, or NULL if the file could not be read.
QZchunk * QZchunk::slurp (const QString &filename, size_t maxsize)
{
    QZchunk *rv = new QZchunk (zchunk_slurp (filename.toUtf8().data(), maxsize));
    return rv;
}

///
//  Create copy of chunk, as new chunk object. Returns a fresh zchunk_t
//  object, or null if there was not enough heap memory. If chunk is null,
//  returns null.
QZchunk * QZchunk::dup ()
{
    QZchunk *rv = new QZchunk (zchunk_dup (self));
    return rv;
}

///
//  Return chunk data encoded as printable hex string. Caller must free
//  string when finished with it.
QString QZchunk::strhex ()
{
    char *retStr_ = zchunk_strhex (self);
    QString rv = QString (retStr_);
    zstr_free (&retStr_);
    return rv;
}

///
//  Return chunk data copied into freshly allocated string
//  Caller must free string when finished with it.
QString QZchunk::strdup ()
{
    char *retStr_ = zchunk_strdup (self);
    QString rv = QString (retStr_);
    zstr_free (&retStr_);
    return rv;
}

///
//  Return TRUE if chunk body is equal to string, excluding terminator
bool QZchunk::streqNoConflict (const QString &string)
{
    bool rv = zchunk_streq (self, string.toUtf8().data());
    return rv;
}

///
//  Transform zchunk into a zframe that can be sent in a message.
QZframe * QZchunk::pack ()
{
    QZframe *rv = new QZframe (zchunk_pack (self));
    return rv;
}

///
//  Transform zchunk into a zframe that can be sent in a message.
//  Take ownership of the chunk.
QZframe * QZchunk::packx ()
{
    QZframe *rv = new QZframe (zchunk_packx (&self));
    return rv;
}

///
//  Transform a zframe into a zchunk.
QZchunk * QZchunk::unpack (QZframe *frame)
{
    QZchunk *rv = new QZchunk (zchunk_unpack (frame->self));
    return rv;
}

///
//  Calculate SHA1 digest for chunk, using zdigest class.
const QString QZchunk::digest ()
{
    const QString rv = QString (zchunk_digest (self));
    return rv;
}

///
//  Dump chunk to FILE stream, for debugging and tracing.
void QZchunk::fprint (FILE *file)
{
    zchunk_fprint (self, file);

}

///
//  Dump message to stderr, for debugging and tracing.
//  See zchunk_fprint for details
void QZchunk::print ()
{
    zchunk_print (self);

}

///
//  Probe the supplied object, and report if it looks like a zchunk_t.
bool QZchunk::is (void *self)
{
    bool rv = zchunk_is (self);
    return rv;
}

///
//  Self test of this class.
void QZchunk::test (bool verbose)
{
    zchunk_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
