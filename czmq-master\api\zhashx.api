<class name = "zhashx" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    extended generic type-free hash container

    <callback_type name = "destructor_fn">
        Destroy an item
        <argument name = "item" type = "anything" by_reference = "1" />
    </callback_type>

    <callback_type name = "duplicator_fn">
        Duplicate an item
        <argument name = "item" type = "anything" mutable = "0" />
        <return type = "anything" />
    </callback_type>

    <callback_type name = "comparator_fn">
        Compare two items, for sorting
        <argument name = "item1" type = "anything" mutable = "0" />
        <argument name = "item2" type = "anything" mutable = "0" />
        <return type = "integer" />
    </callback_type>

    <callback_type name = "free_fn">
        Destroy an item.
        <argument name = "data" type = "anything" />
    </callback_type>

    <callback_type name = "hash_fn">
        Hash function for keys.
        <argument name = "key" type = "anything" mutable = "0"/>
        <return type = "size"/>
    </callback_type>

    <callback_type name = "serializer_fn">
        Serializes an item to a longstr.
        The caller takes ownership of the newly created object.
        <argument name = "item" type = "anything" mutable = "0"/>
        <return type = "string" fresh = "1" />
    </callback_type>

    <callback_type name = "deserializer_fn">
        Deserializes a longstr into an item.
        The caller takes ownership of the newly created object.
        <argument name = "item_str" type = "string" />
        <return type = "anything"/>
    </callback_type>

    <constructor>
        Create a new, empty hash container
    </constructor>

    <destructor>
        Destroy a hash container and all items in it
    </destructor>

    <method name = "insert">
        Insert item into hash table with specified key and item.
        If key is already present returns -1 and leaves existing item unchanged
        Returns 0 on success.
        <argument name = "key" type = "anything" mutable = "0"/>
        <argument name = "item" type = "anything" />
        <return type = "integer" />
    </method>

    <method name = "update">
        Update or insert item into hash table with specified key and item. If the
        key is already present, destroys old item and inserts new one. If you set
        a container item destructor, this is called on the old value. If the key
        was not already present, inserts a new item. Sets the hash cursor to the
        new item.
        <argument name = "key" type = "anything" mutable = "0"/>
        <argument name = "item" type = "anything" />
    </method>

    <method name = "delete">
        Remove an item specified by key from the hash table. If there was no such
        item, this function does nothing.
        <argument name = "key" type = "anything" mutable = "0"/>
    </method>

    <method name = "purge">
        Delete all items from the hash table. If the key destructor is
        set, calls it on every key. If the item destructor is set, calls
        it on every item.
    </method>

    <method name = "lookup">
        Return the item at the specified key, or null
        <argument name = "key" type = "anything" mutable = "0" />
        <return type = "anything" />
    </method>

    <method name = "rename">
        Reindexes an item from an old key to a new key. If there was no such
        item, does nothing. Returns 0 if successful, else -1.
        <argument name = "old key" type = "anything" mutable = "0"/>
        <argument name = "new key" type = "anything" mutable = "0"/>
        <return type = "integer" />
    </method>

    <method name = "freefn">
        Set a free function for the specified hash table item. When the item is
        destroyed, the free function, if any, is called on that item.
        Use this when hash items are dynamically allocated, to ensure that
        you don't have memory leaks. You can pass 'free' or NULL as a free_fn.
        Returns the item, or NULL if there is no such item.
        <argument name = "key" type = "anything" mutable = "0" />
        <argument name = "free_fn" type = "zhashx_free_fn" callback = "1" />
        <return type = "anything" />
    </method>

    <method name = "size">
        Return the number of keys/items in the hash table
        <return type = "size" />
    </method>

    <method name = "keys">
        Return a zlistx_t containing the keys for the items in the
        table. Uses the key_duplicator to duplicate all keys and sets the
        key_destructor as destructor for the list.
        <return type = "zlistx" fresh = "1" />
    </method>

    <method name = "values">
        Return a zlistx_t containing the values for the items in the
        table. Uses the duplicator to duplicate all items and sets the
        destructor as destructor for the list.
        <return type = "zlistx" fresh = "1" />
    </method>

    <method name = "first">
        Simple iterator; returns first item in hash table, in no given order,
        or NULL if the table is empty. This method is simpler to use than the
        foreach() method, which is deprecated. To access the key for this item
        use zhashx_cursor(). NOTE: do NOT modify the table while iterating.
        <return type = "anything" />
    </method>

    <method name = "next">
        Simple iterator; returns next item in hash table, in no given order,
        or NULL if the last item was already returned. Use this together with
        zhashx_first() to process all items in a hash table. If you need the
        items in sorted order, use zhashx_keys() and then zlistx_sort(). To
        access the key for this item use zhashx_cursor(). NOTE: do NOT modify
        the table while iterating.
        <return type = "anything" />
    </method>

    <method name = "cursor">
        After a successful first/next method, returns the key for the item that
        was returned. This is a constant string that you may not modify or
        deallocate, and which lasts as long as the item in the hash. After an
        unsuccessful first/next, returns NULL.
        <return type = "anything" mutable = "0"/>
    </method>

    <method name = "comment">
        Add a comment to hash table before saving to disk. You can add as many
        comment lines as you like. These comment lines are discarded when loading
        the file. If you use a null format, all comments are deleted.
        <argument name = "format" type = "format" />
    </method>

    <method name = "save">
        Save hash table to a text file in name=value format. Hash values must be
        printable strings; keys may not contain '=' character. Returns 0 if OK,
        else -1 if a file error occurred.
        <argument name = "filename" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "load">
        Load hash table from a text file in name=value format; hash table must
        already exist. Hash values must printable strings; keys may not contain
        '=' character. Returns 0 if OK, else -1 if a file was not readable.
        <argument name = "filename" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "refresh">
        When a hash table was loaded from a file by zhashx_load, this method will
        reload the file if it has been modified since, and is "stable", i.e. not
        still changing. Returns 0 if OK, -1 if there was an error reloading the
        file.
        <return type = "integer" />
    </method>

    <method name = "pack">
        Serialize hash table to a binary frame that can be sent in a message.
        The packed format is compatible with the 'dictionary' type defined in
        http://rfc.zeromq.org/spec:35/FILEMQ, and implemented by zproto:

           ; A list of name/value pairs
           dictionary      = dict-count *( dict-name dict-value )
           dict-count      = number-4
           dict-value      = longstr
           dict-name       = string

           ; Strings are always length + text contents
           longstr         = number-4 *VCHAR
           string          = number-1 *VCHAR

           ; Numbers are unsigned integers in network byte order
           number-1        = 1OCTET
           number-4        = 4OCTET

        Comments are not included in the packed data. Item values MUST be
        strings.
        <return type = "zframe" fresh = "1" />
    </method>

    <method name = "pack own" state = "draft">
        Same as pack but uses a user-defined serializer function to convert items
        into longstr.
        <argument name = "serializer" type = "zhashx_serializer_fn" callback = "1"/>
        <return type = "zframe" fresh = "1" />
    </method>

    <constructor name = "unpack">
        Unpack binary frame into a new hash table. Packed data must follow format
        defined by zhashx_pack. Hash table is set to autofree. An empty frame
        unpacks to an empty hash table.
        <argument name = "frame" type = "zframe" />
    </constructor>

    <constructor name = "unpack own" state = "draft">
        Same as unpack but uses a user-defined deserializer function to convert
        a longstr back into item format.
        <argument name = "frame" type = "zframe" />
        <argument name = "deserializer" type = "zhashx_deserializer_fn" callback = "1"/>
    </constructor>

    <method name = "dup">
        Make a copy of the list; items are duplicated if you set a duplicator
        for the list, otherwise not. Copying a null reference returns a null
        reference. Note that this method's behavior changed slightly for CZMQ
        v3.x, as it does not set nor respect autofree. It does however let you
        duplicate any hash table safely. The old behavior is in zhashx_dup_v2.
        <return type = "zhashx" fresh = "1"/>
    </method>

    <method name = "set_destructor">
        Set a user-defined deallocator for hash items; by default items are not
        freed when the hash is destroyed.
        <argument name = "destructor" type = "zhashx_destructor_fn" callback = "1"/>
    </method>

    <method name = "set_duplicator">
        Set a user-defined duplicator for hash items; by default items are not
        copied when the hash is duplicated.
        <argument name = "duplicator" type = "zhashx_duplicator_fn" callback = "1"/>
    </method>

    <method name = "set_key_destructor">
        Set a user-defined deallocator for keys; by default keys are freed
        when the hash is destroyed using free().
        <argument name = "destructor" type = "zhashx_destructor_fn" callback = "1"/>
    </method>

    <method name = "set_key_duplicator">
        Set a user-defined duplicator for keys; by default keys are duplicated
        using strdup.
        <argument name = "duplicator" type = "zhashx_duplicator_fn" callback = "1"/>
    </method>

    <method name = "set_key_comparator">
        Set a user-defined comparator for keys; by default keys are
        compared using strcmp. 
        The callback function should return zero (0) on matching
        items.
        <argument name = "comparator" type = "zhashx_comparator_fn" callback = "1"/>
    </method>

    <method name = "set_key_hasher">
        Set a user-defined hash function for keys; by default keys are
        hashed by a modified Bernstein hashing function.
        <argument name = "hasher" type = "zhashx_hash_fn" callback = "1"/>
    </method>

    <method name = "dup_v2">
        Make copy of hash table; if supplied table is null, returns null.
        Does not copy items themselves. Rebuilds new table so may be slow on
        very large tables. NOTE: only works with item values that are strings
        since there's no other way to know how to duplicate the item value.
        <return type = "zhashx"/>
    </method>
</class>
