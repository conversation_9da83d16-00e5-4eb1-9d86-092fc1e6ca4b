<!--
******************************************************************
*   GENERATED SOURCE CODE, DO NOT EDIT!!                         *
*   TO CHANGE THIS FILE:                                         *
*    - EDIT src/zsock_option.gsl and/or                          *
*    - EDIT src/sockopts.xml     and then                        *
*    - RUN 'make code'                                           *
******************************************************************
-->

<!-- The following socket options are available in libzmq from version 4.3.0 -->

<method name = "priority" polymorphic = "1">
    Get socket option `priority`.
    Available from libzmq 4.3.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set priority" polymorphic = "1">
    Set socket option `priority`.
    Available from libzmq 4.3.0.
    <argument name = "priority" type = "integer" />
</method>

<method name = "reconnect stop" polymorphic = "1">
    Get socket option `reconnect_stop`.
    Available from libzmq 4.3.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set reconnect stop" polymorphic = "1">
    Set socket option `reconnect_stop`.
    Available from libzmq 4.3.0.
    <argument name = "reconnect stop" type = "integer" />
</method>

<method name = "set only first subscribe" polymorphic = "1">
    Set socket option `only_first_subscribe`.
    Available from libzmq 4.3.0.
    <argument name = "only first subscribe" type = "integer" />
</method>

<method name = "set hello msg" polymorphic = "1">
    Set socket option `hello_msg`.
    Available from libzmq 4.3.0.
    <argument name = "hello msg" type = "zframe" />
</method>

<method name = "set disconnect msg" polymorphic = "1">
    Set socket option `disconnect_msg`.
    Available from libzmq 4.3.0.
    <argument name = "disconnect msg" type = "zframe" />
</method>

<method name = "set wss trust system" polymorphic = "1">
    Set socket option `wss_trust_system`.
    Available from libzmq 4.3.0.
    <argument name = "wss trust system" type = "integer" />
</method>

<method name = "set wss hostname" polymorphic = "1">
    Set socket option `wss_hostname`.
    Available from libzmq 4.3.0.
    <argument name = "wss hostname" type = "string" />
</method>

<method name = "set wss trust pem" polymorphic = "1">
    Set socket option `wss_trust_pem`.
    Available from libzmq 4.3.0.
    <argument name = "wss trust pem" type = "string" />
</method>

<method name = "set wss cert pem" polymorphic = "1">
    Set socket option `wss_cert_pem`.
    Available from libzmq 4.3.0.
    <argument name = "wss cert pem" type = "string" />
</method>

<method name = "set wss key pem" polymorphic = "1">
    Set socket option `wss_key_pem`.
    Available from libzmq 4.3.0.
    <argument name = "wss key pem" type = "string" />
</method>

<method name = "out batch size" polymorphic = "1">
    Get socket option `out_batch_size`.
    Available from libzmq 4.3.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set out batch size" polymorphic = "1">
    Set socket option `out_batch_size`.
    Available from libzmq 4.3.0.
    <argument name = "out batch size" type = "integer" />
</method>

<method name = "in batch size" polymorphic = "1">
    Get socket option `in_batch_size`.
    Available from libzmq 4.3.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set in batch size" polymorphic = "1">
    Set socket option `in_batch_size`.
    Available from libzmq 4.3.0.
    <argument name = "in batch size" type = "integer" />
</method>

<method name = "socks password" polymorphic = "1">
    Get socket option `socks_password`.
    Available from libzmq 4.3.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set socks password" polymorphic = "1">
    Set socket option `socks_password`.
    Available from libzmq 4.3.0.
    <argument name = "socks password" type = "string" />
</method>

<method name = "socks username" polymorphic = "1">
    Get socket option `socks_username`.
    Available from libzmq 4.3.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set socks username" polymorphic = "1">
    Set socket option `socks_username`.
    Available from libzmq 4.3.0.
    <argument name = "socks username" type = "string" />
</method>

<method name = "set xpub manual last value" polymorphic = "1">
    Set socket option `xpub_manual_last_value`.
    Available from libzmq 4.3.0.
    <argument name = "xpub manual last value" type = "integer" />
</method>

<method name = "router notify" polymorphic = "1">
    Get socket option `router_notify`.
    Available from libzmq 4.3.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set router notify" polymorphic = "1">
    Set socket option `router_notify`.
    Available from libzmq 4.3.0.
    <argument name = "router notify" type = "integer" />
</method>

<method name = "multicast loop" polymorphic = "1">
    Get socket option `multicast_loop`.
    Available from libzmq 4.3.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set multicast loop" polymorphic = "1">
    Set socket option `multicast_loop`.
    Available from libzmq 4.3.0.
    <argument name = "multicast loop" type = "integer" />
</method>

<method name = "metadata" polymorphic = "1">
    Get socket option `metadata`.
    Available from libzmq 4.3.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set metadata" polymorphic = "1">
    Set socket option `metadata`.
    Available from libzmq 4.3.0.
    <argument name = "metadata" type = "string" />
</method>

<method name = "loopback fastpath" polymorphic = "1">
    Get socket option `loopback_fastpath`.
    Available from libzmq 4.3.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set loopback fastpath" polymorphic = "1">
    Set socket option `loopback_fastpath`.
    Available from libzmq 4.3.0.
    <argument name = "loopback fastpath" type = "integer" />
</method>

<method name = "zap enforce domain" polymorphic = "1">
    Get socket option `zap_enforce_domain`.
    Available from libzmq 4.3.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set zap enforce domain" polymorphic = "1">
    Set socket option `zap_enforce_domain`.
    Available from libzmq 4.3.0.
    <argument name = "zap enforce domain" type = "integer" />
</method>

<method name = "gssapi principal nametype" polymorphic = "1">
    Get socket option `gssapi_principal_nametype`.
    Available from libzmq 4.3.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set gssapi principal nametype" polymorphic = "1">
    Set socket option `gssapi_principal_nametype`.
    Available from libzmq 4.3.0.
    <argument name = "gssapi principal nametype" type = "integer" />
</method>

<method name = "gssapi service principal nametype" polymorphic = "1">
    Get socket option `gssapi_service_principal_nametype`.
    Available from libzmq 4.3.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set gssapi service principal nametype" polymorphic = "1">
    Set socket option `gssapi_service_principal_nametype`.
    Available from libzmq 4.3.0.
    <argument name = "gssapi service principal nametype" type = "integer" />
</method>

<method name = "bindtodevice" polymorphic = "1">
    Get socket option `bindtodevice`.
    Available from libzmq 4.3.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set bindtodevice" polymorphic = "1">
    Set socket option `bindtodevice`.
    Available from libzmq 4.3.0.
    <argument name = "bindtodevice" type = "string" />
</method>

<!-- The following socket options are available in libzmq from version 4.2.0 -->

<method name = "heartbeat ivl" polymorphic = "1">
    Get socket option `heartbeat_ivl`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set heartbeat ivl" polymorphic = "1">
    Set socket option `heartbeat_ivl`.
    Available from libzmq 4.2.0.
    <argument name = "heartbeat ivl" type = "integer" />
</method>

<method name = "heartbeat ttl" polymorphic = "1">
    Get socket option `heartbeat_ttl`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set heartbeat ttl" polymorphic = "1">
    Set socket option `heartbeat_ttl`.
    Available from libzmq 4.2.0.
    <argument name = "heartbeat ttl" type = "integer" />
</method>

<method name = "heartbeat timeout" polymorphic = "1">
    Get socket option `heartbeat_timeout`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set heartbeat timeout" polymorphic = "1">
    Set socket option `heartbeat_timeout`.
    Available from libzmq 4.2.0.
    <argument name = "heartbeat timeout" type = "integer" />
</method>

<method name = "use fd" polymorphic = "1">
    Get socket option `use_fd`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set use fd" polymorphic = "1">
    Set socket option `use_fd`.
    Available from libzmq 4.2.0.
    <argument name = "use fd" type = "integer" />
</method>

<method name = "set xpub manual" polymorphic = "1">
    Set socket option `xpub_manual`.
    Available from libzmq 4.2.0.
    <argument name = "xpub manual" type = "integer" />
</method>

<method name = "set xpub welcome msg" polymorphic = "1">
    Set socket option `xpub_welcome_msg`.
    Available from libzmq 4.2.0.
    <argument name = "xpub welcome msg" type = "string" />
</method>

<method name = "set stream notify" polymorphic = "1">
    Set socket option `stream_notify`.
    Available from libzmq 4.2.0.
    <argument name = "stream notify" type = "integer" />
</method>

<method name = "invert matching" polymorphic = "1">
    Get socket option `invert_matching`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set invert matching" polymorphic = "1">
    Set socket option `invert_matching`.
    Available from libzmq 4.2.0.
    <argument name = "invert matching" type = "integer" />
</method>

<method name = "set xpub verboser" polymorphic = "1">
    Set socket option `xpub_verboser`.
    Available from libzmq 4.2.0.
    <argument name = "xpub verboser" type = "integer" />
</method>

<method name = "connect timeout" polymorphic = "1">
    Get socket option `connect_timeout`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set connect timeout" polymorphic = "1">
    Set socket option `connect_timeout`.
    Available from libzmq 4.2.0.
    <argument name = "connect timeout" type = "integer" />
</method>

<method name = "tcp maxrt" polymorphic = "1">
    Get socket option `tcp_maxrt`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set tcp maxrt" polymorphic = "1">
    Set socket option `tcp_maxrt`.
    Available from libzmq 4.2.0.
    <argument name = "tcp maxrt" type = "integer" />
</method>

<method name = "thread safe" polymorphic = "1">
    Get socket option `thread_safe`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "multicast maxtpdu" polymorphic = "1">
    Get socket option `multicast_maxtpdu`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set multicast maxtpdu" polymorphic = "1">
    Set socket option `multicast_maxtpdu`.
    Available from libzmq 4.2.0.
    <argument name = "multicast maxtpdu" type = "integer" />
</method>

<method name = "vmci buffer size" polymorphic = "1">
    Get socket option `vmci_buffer_size`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set vmci buffer size" polymorphic = "1">
    Set socket option `vmci_buffer_size`.
    Available from libzmq 4.2.0.
    <argument name = "vmci buffer size" type = "integer" />
</method>

<method name = "vmci buffer min size" polymorphic = "1">
    Get socket option `vmci_buffer_min_size`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set vmci buffer min size" polymorphic = "1">
    Set socket option `vmci_buffer_min_size`.
    Available from libzmq 4.2.0.
    <argument name = "vmci buffer min size" type = "integer" />
</method>

<method name = "vmci buffer max size" polymorphic = "1">
    Get socket option `vmci_buffer_max_size`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set vmci buffer max size" polymorphic = "1">
    Set socket option `vmci_buffer_max_size`.
    Available from libzmq 4.2.0.
    <argument name = "vmci buffer max size" type = "integer" />
</method>

<method name = "vmci connect timeout" polymorphic = "1">
    Get socket option `vmci_connect_timeout`.
    Available from libzmq 4.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set vmci connect timeout" polymorphic = "1">
    Set socket option `vmci_connect_timeout`.
    Available from libzmq 4.2.0.
    <argument name = "vmci connect timeout" type = "integer" />
</method>

<!-- The following socket options are available in libzmq from version 4.1.0 -->

<method name = "tos" polymorphic = "1">
    Get socket option `tos`.
    Available from libzmq 4.1.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set tos" polymorphic = "1">
    Set socket option `tos`.
    Available from libzmq 4.1.0.
    <argument name = "tos" type = "integer" />
</method>

<method name = "set router handover" polymorphic = "1">
    Set socket option `router_handover`.
    Available from libzmq 4.1.0.
    <argument name = "router handover" type = "integer" />
</method>

<method name = "set connect rid" polymorphic = "1">
    Set socket option `connect_rid`.
    Available from libzmq 4.1.0.
    <argument name = "connect rid" type = "string" />
</method>

<method name = "set connect rid bin" polymorphic = "1">
    Set socket option `connect_rid` from 32-octet binary
    Available from libzmq 4.1.0.
    <argument name = "connect rid" type = "buffer" />
</method>

<method name = "handshake ivl" polymorphic = "1">
    Get socket option `handshake_ivl`.
    Available from libzmq 4.1.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set handshake ivl" polymorphic = "1">
    Set socket option `handshake_ivl`.
    Available from libzmq 4.1.0.
    <argument name = "handshake ivl" type = "integer" />
</method>

<method name = "socks proxy" polymorphic = "1">
    Get socket option `socks_proxy`.
    Available from libzmq 4.1.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set socks proxy" polymorphic = "1">
    Set socket option `socks_proxy`.
    Available from libzmq 4.1.0.
    <argument name = "socks proxy" type = "string" />
</method>

<method name = "set xpub nodrop" polymorphic = "1">
    Set socket option `xpub_nodrop`.
    Available from libzmq 4.1.0.
    <argument name = "xpub nodrop" type = "integer" />
</method>

<!-- The following socket options are available in libzmq from version 4.0.0 -->

<method name = "set router mandatory" polymorphic = "1">
    Set socket option `router_mandatory`.
    Available from libzmq 4.0.0.
    <argument name = "router mandatory" type = "integer" />
</method>

<method name = "set probe router" polymorphic = "1">
    Set socket option `probe_router`.
    Available from libzmq 4.0.0.
    <argument name = "probe router" type = "integer" />
</method>

<method name = "set req relaxed" polymorphic = "1">
    Set socket option `req_relaxed`.
    Available from libzmq 4.0.0.
    <argument name = "req relaxed" type = "integer" />
</method>

<method name = "set req correlate" polymorphic = "1">
    Set socket option `req_correlate`.
    Available from libzmq 4.0.0.
    <argument name = "req correlate" type = "integer" />
</method>

<method name = "set conflate" polymorphic = "1">
    Set socket option `conflate`.
    Available from libzmq 4.0.0.
    <argument name = "conflate" type = "integer" />
</method>

<method name = "zap domain" polymorphic = "1">
    Get socket option `zap_domain`.
    Available from libzmq 4.0.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set zap domain" polymorphic = "1">
    Set socket option `zap_domain`.
    Available from libzmq 4.0.0.
    <argument name = "zap domain" type = "string" />
</method>

<method name = "mechanism" polymorphic = "1">
    Get socket option `mechanism`.
    Available from libzmq 4.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "plain server" polymorphic = "1">
    Get socket option `plain_server`.
    Available from libzmq 4.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set plain server" polymorphic = "1">
    Set socket option `plain_server`.
    Available from libzmq 4.0.0.
    <argument name = "plain server" type = "integer" />
</method>

<method name = "plain username" polymorphic = "1">
    Get socket option `plain_username`.
    Available from libzmq 4.0.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set plain username" polymorphic = "1">
    Set socket option `plain_username`.
    Available from libzmq 4.0.0.
    <argument name = "plain username" type = "string" />
</method>

<method name = "plain password" polymorphic = "1">
    Get socket option `plain_password`.
    Available from libzmq 4.0.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set plain password" polymorphic = "1">
    Set socket option `plain_password`.
    Available from libzmq 4.0.0.
    <argument name = "plain password" type = "string" />
</method>

<method name = "curve server" polymorphic = "1">
    Get socket option `curve_server`.
    Available from libzmq 4.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set curve server" polymorphic = "1">
    Set socket option `curve_server`.
    Available from libzmq 4.0.0.
    <argument name = "curve server" type = "integer" />
</method>

<method name = "curve publickey" polymorphic = "1">
    Get socket option `curve_publickey`.
    Available from libzmq 4.0.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set curve publickey" polymorphic = "1">
    Set socket option `curve_publickey`.
    Available from libzmq 4.0.0.
    <argument name = "curve publickey" type = "string" />
</method>

<method name = "set curve publickey bin" polymorphic = "1">
    Set socket option `curve_publickey` from 32-octet binary
    Available from libzmq 4.0.0.
    <argument name = "curve publickey" type = "buffer" />
</method>

<method name = "curve secretkey" polymorphic = "1">
    Get socket option `curve_secretkey`.
    Available from libzmq 4.0.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set curve secretkey" polymorphic = "1">
    Set socket option `curve_secretkey`.
    Available from libzmq 4.0.0.
    <argument name = "curve secretkey" type = "string" />
</method>

<method name = "set curve secretkey bin" polymorphic = "1">
    Set socket option `curve_secretkey` from 32-octet binary
    Available from libzmq 4.0.0.
    <argument name = "curve secretkey" type = "buffer" />
</method>

<method name = "curve serverkey" polymorphic = "1">
    Get socket option `curve_serverkey`.
    Available from libzmq 4.0.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set curve serverkey" polymorphic = "1">
    Set socket option `curve_serverkey`.
    Available from libzmq 4.0.0.
    <argument name = "curve serverkey" type = "string" />
</method>

<method name = "set curve serverkey bin" polymorphic = "1">
    Set socket option `curve_serverkey` from 32-octet binary
    Available from libzmq 4.0.0.
    <argument name = "curve serverkey" type = "buffer" />
</method>

<method name = "gssapi server" polymorphic = "1">
    Get socket option `gssapi_server`.
    Available from libzmq 4.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set gssapi server" polymorphic = "1">
    Set socket option `gssapi_server`.
    Available from libzmq 4.0.0.
    <argument name = "gssapi server" type = "integer" />
</method>

<method name = "gssapi plaintext" polymorphic = "1">
    Get socket option `gssapi_plaintext`.
    Available from libzmq 4.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set gssapi plaintext" polymorphic = "1">
    Set socket option `gssapi_plaintext`.
    Available from libzmq 4.0.0.
    <argument name = "gssapi plaintext" type = "integer" />
</method>

<method name = "gssapi principal" polymorphic = "1">
    Get socket option `gssapi_principal`.
    Available from libzmq 4.0.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set gssapi principal" polymorphic = "1">
    Set socket option `gssapi_principal`.
    Available from libzmq 4.0.0.
    <argument name = "gssapi principal" type = "string" />
</method>

<method name = "gssapi service principal" polymorphic = "1">
    Get socket option `gssapi_service_principal`.
    Available from libzmq 4.0.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set gssapi service principal" polymorphic = "1">
    Set socket option `gssapi_service_principal`.
    Available from libzmq 4.0.0.
    <argument name = "gssapi service principal" type = "string" />
</method>

<method name = "ipv6" polymorphic = "1">
    Get socket option `ipv6`.
    Available from libzmq 4.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set ipv6" polymorphic = "1">
    Set socket option `ipv6`.
    Available from libzmq 4.0.0.
    <argument name = "ipv6" type = "integer" />
</method>

<method name = "immediate" polymorphic = "1">
    Get socket option `immediate`.
    Available from libzmq 4.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set immediate" polymorphic = "1">
    Set socket option `immediate`.
    Available from libzmq 4.0.0.
    <argument name = "immediate" type = "integer" />
</method>

<!-- The following socket options are available in libzmq from version 3.0.0 -->

<method name = "sndhwm" polymorphic = "1">
    Get socket option `sndhwm`.
    Available from libzmq 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set sndhwm" polymorphic = "1">
    Set socket option `sndhwm`.
    Available from libzmq 3.0.0.
    <argument name = "sndhwm" type = "integer" />
</method>

<method name = "rcvhwm" polymorphic = "1">
    Get socket option `rcvhwm`.
    Available from libzmq 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set rcvhwm" polymorphic = "1">
    Set socket option `rcvhwm`.
    Available from libzmq 3.0.0.
    <argument name = "rcvhwm" type = "integer" />
</method>

<method name = "maxmsgsize" polymorphic = "1">
    Get socket option `maxmsgsize`.
    Available from libzmq 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set maxmsgsize" polymorphic = "1">
    Set socket option `maxmsgsize`.
    Available from libzmq 3.0.0.
    <argument name = "maxmsgsize" type = "integer" />
</method>

<method name = "multicast hops" polymorphic = "1">
    Get socket option `multicast_hops`.
    Available from libzmq 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set multicast hops" polymorphic = "1">
    Set socket option `multicast_hops`.
    Available from libzmq 3.0.0.
    <argument name = "multicast hops" type = "integer" />
</method>

<method name = "set xpub verbose" polymorphic = "1">
    Set socket option `xpub_verbose`.
    Available from libzmq 3.0.0.
    <argument name = "xpub verbose" type = "integer" />
</method>

<method name = "tcp keepalive" polymorphic = "1">
    Get socket option `tcp_keepalive`.
    Available from libzmq 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set tcp keepalive" polymorphic = "1">
    Set socket option `tcp_keepalive`.
    Available from libzmq 3.0.0.
    <argument name = "tcp keepalive" type = "integer" />
</method>

<method name = "tcp keepalive idle" polymorphic = "1">
    Get socket option `tcp_keepalive_idle`.
    Available from libzmq 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set tcp keepalive idle" polymorphic = "1">
    Set socket option `tcp_keepalive_idle`.
    Available from libzmq 3.0.0.
    <argument name = "tcp keepalive idle" type = "integer" />
</method>

<method name = "tcp keepalive cnt" polymorphic = "1">
    Get socket option `tcp_keepalive_cnt`.
    Available from libzmq 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set tcp keepalive cnt" polymorphic = "1">
    Set socket option `tcp_keepalive_cnt`.
    Available from libzmq 3.0.0.
    <argument name = "tcp keepalive cnt" type = "integer" />
</method>

<method name = "tcp keepalive intvl" polymorphic = "1">
    Get socket option `tcp_keepalive_intvl`.
    Available from libzmq 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set tcp keepalive intvl" polymorphic = "1">
    Set socket option `tcp_keepalive_intvl`.
    Available from libzmq 3.0.0.
    <argument name = "tcp keepalive intvl" type = "integer" />
</method>

<method name = "tcp accept filter" polymorphic = "1">
    Get socket option `tcp_accept_filter`.
    Available from libzmq 3.0.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set tcp accept filter" polymorphic = "1">
    Set socket option `tcp_accept_filter`.
    Available from libzmq 3.0.0.
    <argument name = "tcp accept filter" type = "string" />
</method>

<method name = "last endpoint" polymorphic = "1">
    Get socket option `last_endpoint`.
    Available from libzmq 3.0.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set router raw" polymorphic = "1">
    Set socket option `router_raw`.
    Available from libzmq 3.0.0.
    <argument name = "router raw" type = "integer" />
</method>

<method name = "ipv4only" polymorphic = "1">
    Get socket option `ipv4only`.
    Available from libzmq 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set ipv4only" polymorphic = "1">
    Set socket option `ipv4only`.
    Available from libzmq 3.0.0.
    <argument name = "ipv4only" type = "integer" />
</method>

<method name = "set delay attach on connect" polymorphic = "1">
    Set socket option `delay_attach_on_connect`.
    Available from libzmq 3.0.0.
    <argument name = "delay attach on connect" type = "integer" />
</method>

<!-- The following socket options are available in libzmq from version 2.0.0 -->

<method name = "hwm" polymorphic = "1">
    Get socket option `hwm`.
    Available from libzmq 2.0.0 to 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set hwm" polymorphic = "1">
    Set socket option `hwm`.
    Available from libzmq 2.0.0 to 3.0.0.
    <argument name = "hwm" type = "integer" />
</method>

<method name = "swap" polymorphic = "1">
    Get socket option `swap`.
    Available from libzmq 2.0.0 to 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set swap" polymorphic = "1">
    Set socket option `swap`.
    Available from libzmq 2.0.0 to 3.0.0.
    <argument name = "swap" type = "integer" />
</method>

<method name = "affinity" polymorphic = "1">
    Get socket option `affinity`.
    Available from libzmq 2.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set affinity" polymorphic = "1">
    Set socket option `affinity`.
    Available from libzmq 2.0.0.
    <argument name = "affinity" type = "integer" />
</method>

<method name = "identity" polymorphic = "1">
    Get socket option `identity`.
    Available from libzmq 2.0.0.
    <return type = "string" fresh = "1" />
</method>

<method name = "set identity" polymorphic = "1">
    Set socket option `identity`.
    Available from libzmq 2.0.0.
    <argument name = "identity" type = "string" />
</method>

<method name = "rate" polymorphic = "1">
    Get socket option `rate`.
    Available from libzmq 2.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set rate" polymorphic = "1">
    Set socket option `rate`.
    Available from libzmq 2.0.0.
    <argument name = "rate" type = "integer" />
</method>

<method name = "recovery ivl" polymorphic = "1">
    Get socket option `recovery_ivl`.
    Available from libzmq 2.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set recovery ivl" polymorphic = "1">
    Set socket option `recovery_ivl`.
    Available from libzmq 2.0.0.
    <argument name = "recovery ivl" type = "integer" />
</method>

<method name = "recovery ivl msec" polymorphic = "1">
    Get socket option `recovery_ivl_msec`.
    Available from libzmq 2.0.0 to 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set recovery ivl msec" polymorphic = "1">
    Set socket option `recovery_ivl_msec`.
    Available from libzmq 2.0.0 to 3.0.0.
    <argument name = "recovery ivl msec" type = "integer" />
</method>

<method name = "mcast loop" polymorphic = "1">
    Get socket option `mcast_loop`.
    Available from libzmq 2.0.0 to 3.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set mcast loop" polymorphic = "1">
    Set socket option `mcast_loop`.
    Available from libzmq 2.0.0 to 3.0.0.
    <argument name = "mcast loop" type = "integer" />
</method>

<method name = "rcvtimeo" polymorphic = "1">
    Get socket option `rcvtimeo`.
    Available from libzmq 2.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set rcvtimeo" polymorphic = "1">
    Set socket option `rcvtimeo`.
    Available from libzmq 2.2.0.
    <argument name = "rcvtimeo" type = "integer" />
</method>

<method name = "sndtimeo" polymorphic = "1">
    Get socket option `sndtimeo`.
    Available from libzmq 2.2.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set sndtimeo" polymorphic = "1">
    Set socket option `sndtimeo`.
    Available from libzmq 2.2.0.
    <argument name = "sndtimeo" type = "integer" />
</method>

<method name = "sndbuf" polymorphic = "1">
    Get socket option `sndbuf`.
    Available from libzmq 2.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set sndbuf" polymorphic = "1">
    Set socket option `sndbuf`.
    Available from libzmq 2.0.0.
    <argument name = "sndbuf" type = "integer" />
</method>

<method name = "rcvbuf" polymorphic = "1">
    Get socket option `rcvbuf`.
    Available from libzmq 2.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set rcvbuf" polymorphic = "1">
    Set socket option `rcvbuf`.
    Available from libzmq 2.0.0.
    <argument name = "rcvbuf" type = "integer" />
</method>

<method name = "linger" polymorphic = "1">
    Get socket option `linger`.
    Available from libzmq 2.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set linger" polymorphic = "1">
    Set socket option `linger`.
    Available from libzmq 2.0.0.
    <argument name = "linger" type = "integer" />
</method>

<method name = "reconnect ivl" polymorphic = "1">
    Get socket option `reconnect_ivl`.
    Available from libzmq 2.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set reconnect ivl" polymorphic = "1">
    Set socket option `reconnect_ivl`.
    Available from libzmq 2.0.0.
    <argument name = "reconnect ivl" type = "integer" />
</method>

<method name = "reconnect ivl max" polymorphic = "1">
    Get socket option `reconnect_ivl_max`.
    Available from libzmq 2.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set reconnect ivl max" polymorphic = "1">
    Set socket option `reconnect_ivl_max`.
    Available from libzmq 2.0.0.
    <argument name = "reconnect ivl max" type = "integer" />
</method>

<method name = "backlog" polymorphic = "1">
    Get socket option `backlog`.
    Available from libzmq 2.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "set backlog" polymorphic = "1">
    Set socket option `backlog`.
    Available from libzmq 2.0.0.
    <argument name = "backlog" type = "integer" />
</method>

<method name = "set subscribe" polymorphic = "1">
    Set socket option `subscribe`.
    Available from libzmq 2.0.0.
    <argument name = "subscribe" type = "string" />
</method>

<method name = "set unsubscribe" polymorphic = "1">
    Set socket option `unsubscribe`.
    Available from libzmq 2.0.0.
    <argument name = "unsubscribe" type = "string" />
</method>

<method name = "type" polymorphic = "1">
    Get socket option `type`.
    Available from libzmq 2.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "rcvmore" polymorphic = "1">
    Get socket option `rcvmore`.
    Available from libzmq 2.0.0.
    <return type = "integer" fresh = "1" />
</method>

<method name = "fd" polymorphic = "1">
    Get socket option `fd`.
    Available from libzmq 2.0.0.
    <return type = "socket" fresh = "1" />
</method>

<method name = "events" polymorphic = "1">
    Get socket option `events`.
    Available from libzmq 2.0.0.
    <return type = "integer" fresh = "1" />
</method>
