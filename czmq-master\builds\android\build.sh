#!/usr/bin/env bash
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
#
#   Exit if any step fails
set -e

# Use directory of current script as the working directory
cd "$( dirname "${BASH_SOURCE[0]}" )"
PROJECT_ROOT="$(cd ../.. && pwd)"

########################################################################
# Configuration & tuning options.
########################################################################
# Set default values used in ci builds
export NDK_VERSION="${NDK_VERSION:-android-ndk-r25}"

# Set default path to find Android NDK.
# Must be of the form <path>/${NDK_VERSION} !!
export ANDROID_NDK_ROOT="${ANDROID_NDK_ROOT:-/tmp/${NDK_VERSION}}"

# With NDK r22b, the minimum SDK version range is [16, 31].
# Since NDK r24, the minimum SDK version range is [19, 31].
# SDK version 21 is the minimum version for 64-bit builds.
export MIN_SDK_VERSION=${MIN_SDK_VERSION:-21}

# Where to download our dependencies: default to /tmp/tmp-deps
export ANDROID_DEPENDENCIES_DIR="${ANDROID_DEPENDENCIES_DIR:-/tmp/tmp-deps}"

# Use directory of current script as the build directory
# ${ANDROID_BUILD_DIR}/prefix/<build_arch>/lib will contain produced libraries
export ANDROID_BUILD_DIR="${ANDROID_BUILD_DIR:-${PWD}}"

# Clean before processing
export ANDROID_BUILD_CLEAN="${ANDROID_BUILD_CLEAN:-no}"

# Set this to 'no', to enable verbose ./configure
export CI_CONFIG_QUIET="${CI_CONFIG_QUIET:-no}"

# Set this to enable verbose profiling
export CI_TIME="${CI_TIME:-}"

# Set this to enable verbose tracing
export CI_TRACE="${CI_TRACE:-no}"

# By default, dependencies will be cloned to /tmp/tmp-deps.
# If you have your own source tree for XXX, uncomment its
# XXX_ROOT configuration line below, and provide its absolute tree:
#    export LIBZMQ_ROOT="<absolute_path_to_LIBZMQ_source_tree>"
#    export LIBCURL_ROOT="<absolute_path_to_LIBCURL_source_tree>"
#    export LIBMICROHTTPD_ROOT="<absolute_path_to_LIBMICROHTTPD_source_tree>"

########################################################################
# Utilities
########################################################################
# Get access to android_build functions and variables
# Perform some sanity checks and calculate some variables.
source "${PROJECT_ROOT}/builds/android/android_build_helper.sh"

function usage {
    echo "CZMQ - Usage:"
    echo "  export XXX=xxx"
    echo "  ./build.sh [ arm | arm64 | x86 | x86_64 ]"
    echo ""
    echo "See this file (configuration & tuning options) for details"
    echo "on variables XXX and their values xxx"
    exit 1
}

########################################################################
# Sanity checks
########################################################################
BUILD_ARCH="$1"
[ -z "${BUILD_ARCH}" ] && usage

# Initialize our dependency _ROOT variables:
android_init_dependency_root "libzmq"             # Check or initialize LIBZMQ_ROOT
android_init_dependency_root "libcurl"            # Check or initialize LIBCURL_ROOT
android_init_dependency_root "libmicrohttpd"      # Check or initialize LIBMICROHTTPD_ROOT

# Fetch required dependencies:
[ ! -d "${LIBZMQ_ROOT}" ]           && android_clone_library "LIBZMQ" "${LIBZMQ_ROOT}" "https://github.com/zeromq/libzmq.git" ""
[ ! -d "${LIBCURL_ROOT}" ]          && android_clone_library "LIBCURL" "${LIBCURL_ROOT}" "https://github.com/curl/curl.git" ""
[ ! -d "${LIBMICROHTTPD_ROOT}" ]    && android_download_library "LIBMICROHTTPD" "${LIBMICROHTTPD_ROOT}" "http://ftp.gnu.org/gnu/libmicrohttpd/libmicrohttpd-0.9.44.tar.gz"

case "$CI_TIME" in
    [Yy][Ee][Ss]|[Oo][Nn]|[Tt][Rr][Uu][Ee])
        CI_TIME="time -p " ;;
    [Nn][Oo]|[Oo][Ff][Ff]|[Ff][Aa][Ll][Ss][Ee])
        CI_TIME="" ;;
esac

case "$CI_TRACE" in
    [Nn][Oo]|[Oo][Ff][Ff]|[Ff][Aa][Ll][Ss][Ee])
        set +x ;;
    [Yy][Ee][Ss]|[Oo][Nn]|[Tt][Rr][Uu][Ee])
        set -x ;;
esac

########################################################################
# Compilation
########################################################################
# Choose a C++ standard library implementation from the ndk
export ANDROID_BUILD_CXXSTL="gnustl_shared_49"

# Additional flags for LIBTOOL, for LIBZMQ and other dependencies.
export LIBTOOL_EXTRA_LDFLAGS='-avoid-version'

# Set up android build environment and set ANDROID_BUILD_OPTS array
android_build_set_env "${BUILD_ARCH}"
android_download_ndk
android_build_env
android_build_opts

# Check for environment variable to clear the prefix and do a clean build
if [ "${ANDROID_BUILD_CLEAN}" = "yes" ]; then
    android_build_trace "Doing a clean build (removing previous build and dependencies)..."
    rm -rf "${ANDROID_BUILD_PREFIX}"/*

    # Called shells MUST not clean after ourselves !
    export ANDROID_BUILD_CLEAN="no"
fi

DEPENDENCIES=()

########################################################################
# Make sure LIBZMQ is built and copy the prefix

DEPENDENCIES+=("libzmq.so")
(android_build_verify_so "libzmq.so" &> /dev/null) || {
    if [ -f "${LIBZMQ_ROOT}/builds/android/build.sh" ] ; then
        (
            bash "${LIBZMQ_ROOT}/builds/android/build.sh" "${BUILD_ARCH}"
        ) || exit 1
    else
        (
            CONFIG_OPTS=()
            [ "${CI_CONFIG_QUIET}" = "yes" ] && CONFIG_OPTS+=("--quiet")
            CONFIG_OPTS+=("${ANDROID_BUILD_OPTS[@]}")
            CONFIG_OPTS+=("--without-docs")

            android_build_library "LIBZMQ" "${LIBZMQ_ROOT}"
        ) || exit 1
    fi

    UPSTREAM_PREFIX="${LIBZMQ_ROOT}/builds/android/prefix/${TOOLCHAIN_ARCH}"
    cp -rn "${UPSTREAM_PREFIX}"/* "${ANDROID_BUILD_PREFIX}" || :
}

########################################################################
[ -z "$CI_TIME" ] || echo "`date`: Build czmq from local source"

(android_build_verify_so "libczmq.so" "${DEPENDENCIES[@]}" &> /dev/null) || {
    (
        CONFIG_OPTS=()
        [ "${CI_CONFIG_QUIET}" = "yes" ] && CONFIG_OPTS+=("--quiet")
        CONFIG_OPTS+=("${ANDROID_BUILD_OPTS[@]}")
        CONFIG_OPTS+=("--without-docs")

        android_build_library "LIBCZMQ" "${PROJECT_ROOT}"
    ) || exit 1
}

##
# Verify shared libraries in prefix
for library in "libczmq.so" "${DEPENDENCIES[@]}" ; do
    android_build_verify_so "${library}"
done

android_build_verify_so "libczmq.so" "${DEPENDENCIES[@]}"
android_build_trace "Android build successful"

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
