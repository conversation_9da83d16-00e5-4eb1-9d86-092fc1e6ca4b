/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#ifndef QML_ZCLOCK_H
#define QML_ZCLOCK_H

#include <QtQml>

#include <czmq.h>
#include "qml_czmq_plugin.h"


class QmlZclock : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isNULL READ isNULL)

public:
    zclock_t *self;

    QmlZclock() { self = NULL; }
    bool isNULL() { return self == NULL; }

    static QObject* qmlAttachedProperties(QObject* object); // defined in QmlZclock.cpp

public slots:};

class QmlZclockAttached : public QObject
{
    Q_OBJECT
    QObject* m_attached;

public:
    QmlZclockAttached (QObject* attached) {
        Q_UNUSED (attached);
    };

public slots:
    //  Sleep for a number of milliseconds
    void sleep (int msecs);

    //  Return current system clock as milliseconds. Note that this clock can
    //  jump backwards (if the system clock is changed) so is unsafe to use for
    //  timers and time offsets. Use zclock_mono for that instead.
    int64_t time ();

    //  Return current monotonic clock in milliseconds. Use this when you compute
    //  time offsets. The monotonic clock is not affected by system changes and
    //  so will never be reset backwards, unlike a system clock.
    int64_t mono ();

    //  Return current monotonic clock in microseconds. Use this when you compute
    //  time offsets. The monotonic clock is not affected by system changes and
    //  so will never be reset backwards, unlike a system clock.
    int64_t usecs ();

    //  Return formatted date/time as fresh string. Free using zstr_free().
    QString timestr ();

    //  Self test of this class.
    void test (bool verbose);
};


QML_DECLARE_TYPEINFO(QmlZclock, QML_HAS_ATTACHED_PROPERTIES)

#endif
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
