/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#ifndef Q_ZTIMERSET_H
#define Q_ZTIMERSET_H

#include "qczmq.h"

class QT_CZMQ_EXPORT QZtimerset : public QObject
{
    Q_OBJECT
public:

    //  Copy-construct to return the proper wrapped c types
    QZtimerset (ztimerset_t *self, QObject *qObjParent = 0);

    //  Create new timer set.
    explicit QZtimerset (QObject *qObjParent = 0);

    //  Destroy a timer set
    ~QZtimerset ();

    //  Add a timer to the set. Returns timer id if OK, -1 on failure.
    int add (size_t interval, ztimerset_fn handler, void *arg);

    //  Cancel a timer. Returns 0 if OK, -1 on failure.
    int cancel (int timerId);

    //  Set timer interval. Returns 0 if OK, -1 on failure.
    //  This method is slow, canceling the timer and adding a new one yield better performance.
    int setInterval (int timerId, size_t interval);

    //  Reset timer to start interval counting from current time. Returns 0 if OK, -1 on failure.
    //  This method is slow, canceling the timer and adding a new one yield better performance.
    int reset (int timerId);

    //  Return the time until the next interval.
    //  Should be used as timeout parameter for the zpoller wait method.
    //  The timeout is in msec.
    int timeout ();

    //  Invoke callback function of all timers which their interval has elapsed.
    //  Should be call after zpoller wait method.
    //  Returns 0 if OK, -1 on failure.
    int execute ();

    //  Self test of this class.
    static void test (bool verbose);

    ztimerset_t *self;
};
#endif //  Q_ZTIMERSET_H
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
