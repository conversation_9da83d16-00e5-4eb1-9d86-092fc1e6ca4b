#!/usr/bin/env bash
set -ex

# NOTE: This script is not standalone, it is included from project root
# ci_build.sh script, which sets some envvars (like REPO_DIR below).
[ -n "${REPO_DIR-}" ] || exit 1

docker run -e GSL_BUILD_DIR=/code/src -e BUILD_DIR=/code/src \
    -v "$REPO_DIR":/code zeromqorg/zproto -zproject:1 -q sockopts.xml
docker run -e GSL_BUILD_DIR=/code/src -e BUILD_DIR=/code/src \
    -v "$REPO_DIR":/code zeromqorg/zproto -zproject:1 -q zgossip.xml
docker run -e GSL_BUILD_DIR=/code/src -e BUILD_DIR=/code/src \
    -v "$REPO_DIR":/code zeromqorg/zproto -zproject:1 -q zgossip_msg.xml

# keep an eye on git version used by CI
git --version
if [[ $(git --no-pager diff -w api/*) ]]; then
    git --no-pager diff -w api/*
    echo "There are diffs between current code and code generated by zproto!"
    exit 1
fi
if [[ $(git status -s api) ]]; then
    git status -s api
    echo "zproto generated new files!"
    exit 1
fi
