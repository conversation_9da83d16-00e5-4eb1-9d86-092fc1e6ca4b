################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

from __future__ import print_function
import os, sys
from ctypes import *
from ctypes.util import find_library

# load libc to access free, etc.
libcpath = find_library("c")
if not libcpath:
    raise ImportError("Unable to find libc")
libc = cdll.LoadLibrary(libcpath)
libc.free.argtypes = [c_void_p]
libc.free.restype = None

def return_fresh_string(char_p):
    s = string_at(char_p)
    libc.free(char_p)
    return s

# czmq
lib = None
# check to see if the shared object was embedded locally, attempt to load it
# if not, try to load it using the default system paths...
# we need to use os.chdir instead of trying to modify $LD_LIBRARY_PATH and reloading the interpreter
t = os.getcwd()
p = os.path.join(os.path.dirname(__file__), '..')  # find the path to our $project_ctypes.py
os.chdir(p)  # change directories briefly

try:
    from czmq import libczmq                        # attempt to import the shared lib if it exists
    lib = CDLL(libczmq.__file__)             # if it exists try to load the shared lib
except ImportError:
    pass
finally:
    os.chdir(t)  # switch back to orig dir

if not lib:
    try:
        # If LD_LIBRARY_PATH or your OSs equivalent is set, this is the only way to
        # load the library.  If we use find_library below, we get the wrong result.
        if os.name == 'posix':
            if sys.platform == 'darwin':
                lib = cdll.LoadLibrary('libczmq.4.dylib')
            else:
                lib = cdll.LoadLibrary("libczmq.so.4")
        elif os.name == 'nt':
            lib = cdll.LoadLibrary('libczmq.dll')
    except OSError:
        libpath = find_library("czmq")
        if not libpath:
            raise ImportError("Unable to find libczmq")
        lib = cdll.LoadLibrary(libpath)

class zsock_t(Structure):
    pass # Empty - only for type checking
zsock_p = POINTER(zsock_t)

class zactor_t(Structure):
    pass # Empty - only for type checking
zactor_p = POINTER(zactor_t)

class zmsg_t(Structure):
    pass # Empty - only for type checking
zmsg_p = POINTER(zmsg_t)

class zargs_t(Structure):
    pass # Empty - only for type checking
zargs_p = POINTER(zargs_t)

class zarmour_t(Structure):
    pass # Empty - only for type checking
zarmour_p = POINTER(zarmour_t)

class zchunk_t(Structure):
    pass # Empty - only for type checking
zchunk_p = POINTER(zchunk_t)

class char_t(Structure):
    pass # Empty - only for type checking
char_p = POINTER(char_t)

class zcert_t(Structure):
    pass # Empty - only for type checking
zcert_p = POINTER(zcert_t)

class zlist_t(Structure):
    pass # Empty - only for type checking
zlist_p = POINTER(zlist_t)

class zcertstore_t(Structure):
    pass # Empty - only for type checking
zcertstore_p = POINTER(zcertstore_t)

class zlistx_t(Structure):
    pass # Empty - only for type checking
zlistx_p = POINTER(zlistx_t)

class FILE(Structure):
    pass # Empty - only for type checking
FILE_p = POINTER(FILE)

class zframe_t(Structure):
    pass # Empty - only for type checking
zframe_p = POINTER(zframe_t)

class msecs_t(Structure):
    pass # Empty - only for type checking
msecs_p = POINTER(msecs_t)

class zconfig_t(Structure):
    pass # Empty - only for type checking
zconfig_p = POINTER(zconfig_t)

class zdigest_t(Structure):
    pass # Empty - only for type checking
zdigest_p = POINTER(zdigest_t)

class zdir_t(Structure):
    pass # Empty - only for type checking
zdir_p = POINTER(zdir_t)

class zhash_t(Structure):
    pass # Empty - only for type checking
zhash_p = POINTER(zhash_t)

class zfile_t(Structure):
    pass # Empty - only for type checking
zfile_p = POINTER(zfile_t)

class zdir_patch_t(Structure):
    pass # Empty - only for type checking
zdir_patch_p = POINTER(zdir_patch_t)

class zhashx_t(Structure):
    pass # Empty - only for type checking
zhashx_p = POINTER(zhashx_t)

class ziflist_t(Structure):
    pass # Empty - only for type checking
ziflist_p = POINTER(ziflist_t)

class zloop_t(Structure):
    pass # Empty - only for type checking
zloop_p = POINTER(zloop_t)

class zmq_pollitem_t(Structure):
    pass # Empty - only for type checking
zmq_pollitem_p = POINTER(zmq_pollitem_t)

class zpoller_t(Structure):
    pass # Empty - only for type checking
zpoller_p = POINTER(zpoller_t)

class zproc_t(Structure):
    pass # Empty - only for type checking
zproc_p = POINTER(zproc_t)

class va_list_t(Structure):
    pass # Empty - only for type checking
va_list_p = POINTER(va_list_t)

class socket_t(Structure):
    pass # Empty - only for type checking
socket_p = POINTER(socket_t)

class ztimerset_t(Structure):
    pass # Empty - only for type checking
ztimerset_p = POINTER(ztimerset_t)

class ztrie_t(Structure):
    pass # Empty - only for type checking
ztrie_p = POINTER(ztrie_t)

class zuuid_t(Structure):
    pass # Empty - only for type checking
zuuid_p = POINTER(zuuid_t)

class zhttp_client_t(Structure):
    pass # Empty - only for type checking
zhttp_client_p = POINTER(zhttp_client_t)

class zhttp_server_options_t(Structure):
    pass # Empty - only for type checking
zhttp_server_options_p = POINTER(zhttp_server_options_t)

class zhttp_server_t(Structure):
    pass # Empty - only for type checking
zhttp_server_p = POINTER(zhttp_server_t)

class zhttp_request_t(Structure):
    pass # Empty - only for type checking
zhttp_request_p = POINTER(zhttp_request_t)

class zhttp_response_t(Structure):
    pass # Empty - only for type checking
zhttp_response_p = POINTER(zhttp_response_t)

class zosc_t(Structure):
    pass # Empty - only for type checking
zosc_p = POINTER(zosc_t)

def return_py_file(c_file):
    if not sys.version_info > (3,):
        PyFile_FromFile_close_cb = CFUNCTYPE(c_int, FILE_p)
        PyFile_FromFile = pythonapi.PyFile_FromFile
        PyFile_FromFile.restype = py_object
        PyFile_FromFile.argtypes = [FILE_p,
                                    c_char_p,
                                    c_char_p,
                                    PyFile_FromFile_close_cb]
        return PyFile_FromFile(c_file, "", "r+", PyFile_FromFile_close_cb())

    else:
        fileno = libc.fileno
        fileno.restype = c_int
        fileno.argtypes = [c_void_p]

        return os.fdopen(fileno(c_file), r'r+b')

def coerce_py_file(obj):
    if not sys.version_info > (3,):
        PyFile_AsFile = pythonapi.PyFile_AsFile
        PyFile_AsFile.restype = FILE_p
        PyFile_AsFile.argtypes = [py_object]

        if isinstance(obj, FILE_p):
            return obj
        else:
            return PyFile_AsFile(obj)

    # Python 3 does not provide a low level buffered I/O (FILE*) API. Had to
    # resort to direct Standard C library calls.
    #
    #   https://docs.python.org/3/c-api/file.html.
    #
    else:
        fdopen = libc.fdopen
        fdopen.restype = FILE_p
        fdopen.argtypes = [c_int, c_char_p]

        setbuf = libc.setbuf
        setbuf.restype = None
        setbuf.argtypes = [FILE_p, c_char_p]

        if isinstance(obj, FILE_p):
            return obj
        else:
            fd = obj.fileno()
            fp = fdopen(fd, obj.mode.encode())

            # Make sure the file is opened in unbuffered mode. The test case
            # "test_zmsg" of the CZMQ Python fails if this mode is not set.
            setbuf(fp, None)

            return fp


# zactor
zactor_fn = CFUNCTYPE(None, zsock_p, c_void_p)
zactor_destructor_fn = CFUNCTYPE(None, zactor_p)
lib.zactor_new.restype = zactor_p
lib.zactor_new.argtypes = [zactor_fn, c_void_p]
lib.zactor_destroy.restype = None
lib.zactor_destroy.argtypes = [POINTER(zactor_p)]
lib.zactor_send.restype = c_int
lib.zactor_send.argtypes = [zactor_p, POINTER(zmsg_p)]
lib.zactor_recv.restype = zmsg_p
lib.zactor_recv.argtypes = [zactor_p]
lib.zactor_is.restype = c_bool
lib.zactor_is.argtypes = [c_void_p]
lib.zactor_resolve.restype = c_void_p
lib.zactor_resolve.argtypes = [c_void_p]
lib.zactor_sock.restype = zsock_p
lib.zactor_sock.argtypes = [zactor_p]
lib.zactor_set_destructor.restype = None
lib.zactor_set_destructor.argtypes = [zactor_p, zactor_destructor_fn]
lib.zactor_test.restype = None
lib.zactor_test.argtypes = [c_bool]

class Zactor(object):
    """
    provides a simple actor framework
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new actor passing arbitrary arguments reference.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zactor_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zactor_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 2)
            self._as_parameter_ = lib.zactor_new(args[0], args[1]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy an actor.
        """
        if self.allow_destruct:
            lib.zactor_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def send(self, msg_p):
        """
        Send a zmsg message to the actor, take ownership of the message
and destroy when it has been sent.
        """
        return lib.zactor_send(self._as_parameter_, byref(zmsg_p.from_param(msg_p)))

    def recv(self):
        """
        Receive a zmsg message from the actor. Returns NULL if the actor
was interrupted before the message could be received, or if there
was a timeout on the actor.
        """
        return Zmsg(lib.zactor_recv(self._as_parameter_), True)

    @staticmethod
    def is_(self):
        """
        Probe the supplied object, and report if it looks like a zactor_t.
        """
        return lib.zactor_is(self)

    @staticmethod
    def resolve(self):
        """
        Probe the supplied reference. If it looks like a zactor_t instance,
return the underlying libzmq actor handle; else if it looks like
a libzmq actor handle, return the supplied value.
        """
        return c_void_p(lib.zactor_resolve(self))

    def sock(self):
        """
        Return the actor's zsock handle. Use this when you absolutely need
to work with the zsock instance rather than the actor.
        """
        return Zsock(lib.zactor_sock(self._as_parameter_), False)

    def set_destructor(self, destructor):
        """
        Change default destructor by custom function. Actor MUST be able to handle new message instead of default $TERM.
        """
        return lib.zactor_set_destructor(self._as_parameter_, destructor)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zactor_test(verbose)


# zargs
lib.zargs_new.restype = zargs_p
lib.zargs_new.argtypes = [c_int, POINTER(c_char_p)]
lib.zargs_destroy.restype = None
lib.zargs_destroy.argtypes = [POINTER(zargs_p)]
lib.zargs_progname.restype = c_char_p
lib.zargs_progname.argtypes = [zargs_p]
lib.zargs_arguments.restype = c_size_t
lib.zargs_arguments.argtypes = [zargs_p]
lib.zargs_first.restype = c_char_p
lib.zargs_first.argtypes = [zargs_p]
lib.zargs_next.restype = c_char_p
lib.zargs_next.argtypes = [zargs_p]
lib.zargs_param_first.restype = c_char_p
lib.zargs_param_first.argtypes = [zargs_p]
lib.zargs_param_next.restype = c_char_p
lib.zargs_param_next.argtypes = [zargs_p]
lib.zargs_param_name.restype = c_char_p
lib.zargs_param_name.argtypes = [zargs_p]
lib.zargs_get.restype = c_char_p
lib.zargs_get.argtypes = [zargs_p, c_char_p]
lib.zargs_getx.restype = c_char_p
lib.zargs_getx.argtypes = [zargs_p, c_char_p]
lib.zargs_has.restype = c_bool
lib.zargs_has.argtypes = [zargs_p, c_char_p]
lib.zargs_hasx.restype = c_bool
lib.zargs_hasx.argtypes = [zargs_p, c_char_p]
lib.zargs_print.restype = None
lib.zargs_print.argtypes = [zargs_p]
lib.zargs_test.restype = None
lib.zargs_test.argtypes = [c_bool]

class Zargs(object):
    """
    Platform independent command line argument parsing helpers

There are two kind of elements provided by this class
Named parameters, accessed by param_get and param_has methods
  * --named-parameter
  * --parameter with_value
  * -a val
Positional arguments, accessed by zargs_first, zargs_next

It DOES:
* provide easy to use CLASS compatible API for accessing argv
* is platform independent
* provide getopt_long style -- argument, which delimits parameters from arguments
* makes parameters position independent

It does NOT
* change argv
* provide a "declarative" way to define command line interface

In future it SHALL
* hide several formats of command line to one (-Idir, --include=dir,
  --include dir are the same from API pov)
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new zargs from command line arguments.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zargs_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zargs_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 2)
            self._as_parameter_ = lib.zargs_new(args[0], byref(c_char_p.from_param(args[1]))) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy zargs instance.
        """
        if self.allow_destruct:
            lib.zargs_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def progname(self):
        """
        Return program name (argv[0])
        """
        return lib.zargs_progname(self._as_parameter_)

    def arguments(self):
        """
        Return number of positional arguments
        """
        return lib.zargs_arguments(self._as_parameter_)

    def first(self):
        """
        Return first positional argument or NULL
        """
        return lib.zargs_first(self._as_parameter_)

    def next(self):
        """
        Return next positional argument or NULL
        """
        return lib.zargs_next(self._as_parameter_)

    def param_first(self):
        """
        Return first named parameter value, or NULL if there are no named
parameters, or value for which zargs_param_empty (arg) returns true.
        """
        return lib.zargs_param_first(self._as_parameter_)

    def param_next(self):
        """
        Return next named parameter value, or NULL if there are no named
parameters, or value for which zargs_param_empty (arg) returns true.
        """
        return lib.zargs_param_next(self._as_parameter_)

    def param_name(self):
        """
        Return current parameter name, or NULL if there are no named parameters.
        """
        return lib.zargs_param_name(self._as_parameter_)

    def get(self, name):
        """
        Return value of named parameter or NULL is it has no value (or was not specified)
        """
        return lib.zargs_get(self._as_parameter_, name)

    def getx(self, name, *args):
        """
        Return value of one of parameter(s) or NULL is it has no value (or was not specified)
        """
        return lib.zargs_getx(self._as_parameter_, name, *args)

    def has(self, name):
        """
        Returns true if named parameter was specified on command line
        """
        return lib.zargs_has(self._as_parameter_, name)

    def hasx(self, name, *args):
        """
        Returns true if named parameter(s) was specified on command line
        """
        return lib.zargs_hasx(self._as_parameter_, name, *args)

    def print(self):
        """
        Print an instance of zargs.
        """
        return lib.zargs_print(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zargs_test(verbose)


# zarmour
lib.zarmour_new.restype = zarmour_p
lib.zarmour_new.argtypes = []
lib.zarmour_destroy.restype = None
lib.zarmour_destroy.argtypes = [POINTER(zarmour_p)]
lib.zarmour_encode.restype = POINTER(c_char)
lib.zarmour_encode.argtypes = [zarmour_p, c_void_p, c_size_t]
lib.zarmour_decode.restype = zchunk_p
lib.zarmour_decode.argtypes = [zarmour_p, c_char_p]
lib.zarmour_mode.restype = c_int
lib.zarmour_mode.argtypes = [zarmour_p]
lib.zarmour_mode_str.restype = c_char_p
lib.zarmour_mode_str.argtypes = [zarmour_p]
lib.zarmour_set_mode.restype = None
lib.zarmour_set_mode.argtypes = [zarmour_p, c_int]
lib.zarmour_pad.restype = c_bool
lib.zarmour_pad.argtypes = [zarmour_p]
lib.zarmour_set_pad.restype = None
lib.zarmour_set_pad.argtypes = [zarmour_p, c_bool]
lib.zarmour_pad_char.restype = char_p
lib.zarmour_pad_char.argtypes = [zarmour_p]
lib.zarmour_set_pad_char.restype = None
lib.zarmour_set_pad_char.argtypes = [zarmour_p, char_p]
lib.zarmour_line_breaks.restype = c_bool
lib.zarmour_line_breaks.argtypes = [zarmour_p]
lib.zarmour_set_line_breaks.restype = None
lib.zarmour_set_line_breaks.argtypes = [zarmour_p, c_bool]
lib.zarmour_line_length.restype = c_size_t
lib.zarmour_line_length.argtypes = [zarmour_p]
lib.zarmour_set_line_length.restype = None
lib.zarmour_set_line_length.argtypes = [zarmour_p, c_size_t]
lib.zarmour_print.restype = None
lib.zarmour_print.argtypes = [zarmour_p]
lib.zarmour_test.restype = None
lib.zarmour_test.argtypes = [c_bool]

class Zarmour(object):
    """
    armoured text encoding and decoding
    """

    MODE_BASE64_STD = 0 # Standard base 64
    MODE_BASE64_URL = 1 # URL and filename friendly base 64
    MODE_BASE32_STD = 2 # Standard base 32
    MODE_BASE32_HEX = 3 # Extended hex base 32
    MODE_BASE16 = 4 # Standard base 16
    MODE_Z85 = 5 # Z85 from ZeroMQ RFC 32
    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new zarmour
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zarmour_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zarmour_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zarmour_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy the zarmour
        """
        if self.allow_destruct:
            lib.zarmour_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def encode(self, data, size):
        """
        Encode a stream of bytes into an armoured string. Returns the armoured
string, or NULL if there was insufficient memory available to allocate
a new string.
        """
        return return_fresh_string(lib.zarmour_encode(self._as_parameter_, data, size))

    def decode(self, data):
        """
        Decode an armoured string into a chunk. The decoded output is
null-terminated, so it may be treated as a string, if that's what
it was prior to encoding.
        """
        return Zchunk(lib.zarmour_decode(self._as_parameter_, data), True)

    def mode(self):
        """
        Get the mode property.
        """
        return lib.zarmour_mode(self._as_parameter_)

    def mode_str(self):
        """
        Get printable string for mode.
        """
        return lib.zarmour_mode_str(self._as_parameter_)

    def set_mode(self, mode):
        """
        Set the mode property.
        """
        return lib.zarmour_set_mode(self._as_parameter_, mode)

    def pad(self):
        """
        Return true if padding is turned on.
        """
        return lib.zarmour_pad(self._as_parameter_)

    def set_pad(self, pad):
        """
        Turn padding on or off. Default is on.
        """
        return lib.zarmour_set_pad(self._as_parameter_, pad)

    def pad_char(self):
        """
        Get the padding character.
        """
        return lib.zarmour_pad_char(self._as_parameter_)

    def set_pad_char(self, pad_char):
        """
        Set the padding character.
        """
        return lib.zarmour_set_pad_char(self._as_parameter_, pad_char)

    def line_breaks(self):
        """
        Return if splitting output into lines is turned on. Default is off.
        """
        return lib.zarmour_line_breaks(self._as_parameter_)

    def set_line_breaks(self, line_breaks):
        """
        Turn splitting output into lines on or off.
        """
        return lib.zarmour_set_line_breaks(self._as_parameter_, line_breaks)

    def line_length(self):
        """
        Get the line length used for splitting lines.
        """
        return lib.zarmour_line_length(self._as_parameter_)

    def set_line_length(self, line_length):
        """
        Set the line length used for splitting lines.
        """
        return lib.zarmour_set_line_length(self._as_parameter_, line_length)

    def print(self):
        """
        Print properties of object
        """
        return lib.zarmour_print(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zarmour_test(verbose)


# zcert
lib.zcert_new.restype = zcert_p
lib.zcert_new.argtypes = []
lib.zcert_destroy.restype = None
lib.zcert_destroy.argtypes = [POINTER(zcert_p)]
lib.zcert_new_from.restype = zcert_p
lib.zcert_new_from.argtypes = [c_void_p, c_void_p]
lib.zcert_new_from_txt.restype = zcert_p
lib.zcert_new_from_txt.argtypes = [c_char_p, c_char_p]
lib.zcert_load.restype = zcert_p
lib.zcert_load.argtypes = [c_char_p]
lib.zcert_public_key.restype = c_void_p
lib.zcert_public_key.argtypes = [zcert_p]
lib.zcert_secret_key.restype = c_void_p
lib.zcert_secret_key.argtypes = [zcert_p]
lib.zcert_public_txt.restype = c_char_p
lib.zcert_public_txt.argtypes = [zcert_p]
lib.zcert_secret_txt.restype = c_char_p
lib.zcert_secret_txt.argtypes = [zcert_p]
lib.zcert_set_meta.restype = None
lib.zcert_set_meta.argtypes = [zcert_p, c_char_p, c_char_p]
lib.zcert_unset_meta.restype = None
lib.zcert_unset_meta.argtypes = [zcert_p, c_char_p]
lib.zcert_meta.restype = c_char_p
lib.zcert_meta.argtypes = [zcert_p, c_char_p]
lib.zcert_meta_keys.restype = zlist_p
lib.zcert_meta_keys.argtypes = [zcert_p]
lib.zcert_save.restype = c_int
lib.zcert_save.argtypes = [zcert_p, c_char_p]
lib.zcert_save_public.restype = c_int
lib.zcert_save_public.argtypes = [zcert_p, c_char_p]
lib.zcert_save_secret.restype = c_int
lib.zcert_save_secret.argtypes = [zcert_p, c_char_p]
lib.zcert_apply.restype = None
lib.zcert_apply.argtypes = [zcert_p, c_void_p]
lib.zcert_dup.restype = zcert_p
lib.zcert_dup.argtypes = [zcert_p]
lib.zcert_eq.restype = c_bool
lib.zcert_eq.argtypes = [zcert_p, zcert_p]
lib.zcert_print.restype = None
lib.zcert_print.argtypes = [zcert_p]
lib.zcert_test.restype = None
lib.zcert_test.argtypes = [c_bool]

class Zcert(object):
    """
    work with CURVE security certificates
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create and initialize a new certificate in memory
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zcert_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zcert_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zcert_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a certificate in memory
        """
        if self.allow_destruct:
            lib.zcert_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def new_from(public_key, secret_key):
        """
        Accepts public/secret key pair from caller
        """
        return Zcert(lib.zcert_new_from(public_key, secret_key), True)

    @staticmethod
    def new_from_txt(public_txt, secret_txt):
        """
        Accepts public/secret key text pair from caller
        """
        return Zcert(lib.zcert_new_from_txt(public_txt, secret_txt), True)

    @staticmethod
    def load(filename):
        """
        Load certificate from file
        """
        return Zcert(lib.zcert_load(filename), True)

    def public_key(self):
        """
        Return public part of key pair as 32-byte binary string
        """
        return lib.zcert_public_key(self._as_parameter_)

    def secret_key(self):
        """
        Return secret part of key pair as 32-byte binary string
        """
        return lib.zcert_secret_key(self._as_parameter_)

    def public_txt(self):
        """
        Return public part of key pair as Z85 armored string
        """
        return lib.zcert_public_txt(self._as_parameter_)

    def secret_txt(self):
        """
        Return secret part of key pair as Z85 armored string
        """
        return lib.zcert_secret_txt(self._as_parameter_)

    def set_meta(self, name, format, *args):
        """
        Set certificate metadata from formatted string.
        """
        return lib.zcert_set_meta(self._as_parameter_, name, format, *args)

    def unset_meta(self, name):
        """
        Unset certificate metadata.
        """
        return lib.zcert_unset_meta(self._as_parameter_, name)

    def meta(self, name):
        """
        Get metadata value from certificate; if the metadata value doesn't
exist, returns NULL.
        """
        return lib.zcert_meta(self._as_parameter_, name)

    def meta_keys(self):
        """
        Get list of metadata fields from certificate. Caller is responsible for
destroying list. Caller should not modify the values of list items.
        """
        return Zlist(lib.zcert_meta_keys(self._as_parameter_), False)

    def save(self, filename):
        """
        Save full certificate (public + secret) to file for persistent storage
This creates one public file and one secret file (filename + "_secret").
        """
        return lib.zcert_save(self._as_parameter_, filename)

    def save_public(self, filename):
        """
        Save public certificate only to file for persistent storage
        """
        return lib.zcert_save_public(self._as_parameter_, filename)

    def save_secret(self, filename):
        """
        Save secret certificate only to file for persistent storage
        """
        return lib.zcert_save_secret(self._as_parameter_, filename)

    def apply(self, socket):
        """
        Apply certificate to socket, i.e. use for CURVE security on socket.
If certificate was loaded from public file, the secret key will be
undefined, and this certificate will not work successfully.
        """
        return lib.zcert_apply(self._as_parameter_, socket)

    def dup(self):
        """
        Return copy of certificate; if certificate is NULL or we exhausted
heap memory, returns NULL.
        """
        return Zcert(lib.zcert_dup(self._as_parameter_), True)

    def eq(self, compare):
        """
        Return true if two certificates have the same keys
        """
        return lib.zcert_eq(self._as_parameter_, compare)

    def print(self):
        """
        Print certificate contents to stdout
        """
        return lib.zcert_print(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class
        """
        return lib.zcert_test(verbose)


# zcertstore
zcertstore_loader = CFUNCTYPE(None, zcertstore_p)
zcertstore_destructor = CFUNCTYPE(None, c_void_p)
lib.zcertstore_new.restype = zcertstore_p
lib.zcertstore_new.argtypes = [c_char_p]
lib.zcertstore_destroy.restype = None
lib.zcertstore_destroy.argtypes = [POINTER(zcertstore_p)]
lib.zcertstore_set_loader.restype = None
lib.zcertstore_set_loader.argtypes = [zcertstore_p, zcertstore_loader, zcertstore_destructor, c_void_p]
lib.zcertstore_lookup.restype = zcert_p
lib.zcertstore_lookup.argtypes = [zcertstore_p, c_char_p]
lib.zcertstore_insert.restype = None
lib.zcertstore_insert.argtypes = [zcertstore_p, POINTER(zcert_p)]
lib.zcertstore_empty.restype = None
lib.zcertstore_empty.argtypes = [zcertstore_p]
lib.zcertstore_print.restype = None
lib.zcertstore_print.argtypes = [zcertstore_p]
lib.zcertstore_certs.restype = zlistx_p
lib.zcertstore_certs.argtypes = [zcertstore_p]
lib.zcertstore_state.restype = c_void_p
lib.zcertstore_state.argtypes = [zcertstore_p]
lib.zcertstore_test.restype = None
lib.zcertstore_test.argtypes = [c_bool]

class Zcertstore(object):
    """
    work with CURVE security certificate stores
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new certificate store from a disk directory, loading and
indexing all certificates in that location. The directory itself may be
absent, and created later, or modified at any time. The certificate store
is automatically refreshed on any zcertstore_lookup() call. If the
location is specified as NULL, creates a pure-memory store, which you
can work with by inserting certificates at runtime.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zcertstore_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zcertstore_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 1)
            self._as_parameter_ = lib.zcertstore_new(args[0]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a certificate store object in memory. Does not affect anything
stored on disk.
        """
        if self.allow_destruct:
            lib.zcertstore_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def set_loader(self, loader, destructor, state):
        """
        Override the default disk loader with a custom loader fn.
        """
        return lib.zcertstore_set_loader(self._as_parameter_, loader, destructor, state)

    def lookup(self, public_key):
        """
        Look up certificate by public key, returns zcert_t object if found,
else returns NULL. The public key is provided in Z85 text format.
        """
        return Zcert(lib.zcertstore_lookup(self._as_parameter_, public_key), False)

    def insert(self, cert_p):
        """
        Insert certificate into certificate store in memory. Note that this
does not save the certificate to disk. To do that, use zcert_save()
directly on the certificate. Takes ownership of zcert_t object.
        """
        return lib.zcertstore_insert(self._as_parameter_, byref(zcert_p.from_param(cert_p)))

    def empty(self):
        """
        Empty certificate hashtable. This wrapper exists to be friendly to bindings,
which don't usually have access to struct internals.
        """
        return lib.zcertstore_empty(self._as_parameter_)

    def print(self):
        """
        Print list of certificates in store to logging facility
        """
        return lib.zcertstore_print(self._as_parameter_)

    def certs(self):
        """
        Return a list of all the certificates in the store.
The caller takes ownership of the zlistx_t object and is responsible
for destroying it.  The caller does not take ownership of the zcert_t
objects.
        """
        return Zlistx(lib.zcertstore_certs(self._as_parameter_), True)

    def state(self):
        """
        Return the state stored in certstore
        """
        return c_void_p(lib.zcertstore_state(self._as_parameter_))

    @staticmethod
    def test(verbose):
        """
        Self test of this class
        """
        return lib.zcertstore_test(verbose)


# zchunk
zchunk_destructor_fn = CFUNCTYPE(None, POINTER(c_void_p))
lib.zchunk_new.restype = zchunk_p
lib.zchunk_new.argtypes = [c_void_p, c_size_t]
lib.zchunk_destroy.restype = None
lib.zchunk_destroy.argtypes = [POINTER(zchunk_p)]
lib.zchunk_frommem.restype = zchunk_p
lib.zchunk_frommem.argtypes = [c_void_p, c_size_t, zchunk_destructor_fn, c_void_p]
lib.zchunk_resize.restype = None
lib.zchunk_resize.argtypes = [zchunk_p, c_size_t]
lib.zchunk_size.restype = c_size_t
lib.zchunk_size.argtypes = [zchunk_p]
lib.zchunk_max_size.restype = c_size_t
lib.zchunk_max_size.argtypes = [zchunk_p]
lib.zchunk_data.restype = c_void_p
lib.zchunk_data.argtypes = [zchunk_p]
lib.zchunk_set.restype = c_size_t
lib.zchunk_set.argtypes = [zchunk_p, c_void_p, c_size_t]
lib.zchunk_fill.restype = c_size_t
lib.zchunk_fill.argtypes = [zchunk_p, c_ubyte, c_size_t]
lib.zchunk_append.restype = c_size_t
lib.zchunk_append.argtypes = [zchunk_p, c_void_p, c_size_t]
lib.zchunk_extend.restype = c_size_t
lib.zchunk_extend.argtypes = [zchunk_p, c_void_p, c_size_t]
lib.zchunk_consume.restype = c_size_t
lib.zchunk_consume.argtypes = [zchunk_p, zchunk_p]
lib.zchunk_exhausted.restype = c_bool
lib.zchunk_exhausted.argtypes = [zchunk_p]
lib.zchunk_read.restype = zchunk_p
lib.zchunk_read.argtypes = [FILE_p, c_size_t]
lib.zchunk_write.restype = c_int
lib.zchunk_write.argtypes = [zchunk_p, FILE_p]
lib.zchunk_slurp.restype = zchunk_p
lib.zchunk_slurp.argtypes = [c_char_p, c_size_t]
lib.zchunk_dup.restype = zchunk_p
lib.zchunk_dup.argtypes = [zchunk_p]
lib.zchunk_strhex.restype = POINTER(c_char)
lib.zchunk_strhex.argtypes = [zchunk_p]
lib.zchunk_strdup.restype = POINTER(c_char)
lib.zchunk_strdup.argtypes = [zchunk_p]
lib.zchunk_streq.restype = c_bool
lib.zchunk_streq.argtypes = [zchunk_p, c_char_p]
lib.zchunk_pack.restype = zframe_p
lib.zchunk_pack.argtypes = [zchunk_p]
lib.zchunk_packx.restype = zframe_p
lib.zchunk_packx.argtypes = [POINTER(zchunk_p)]
lib.zchunk_unpack.restype = zchunk_p
lib.zchunk_unpack.argtypes = [zframe_p]
lib.zchunk_digest.restype = c_char_p
lib.zchunk_digest.argtypes = [zchunk_p]
lib.zchunk_fprint.restype = None
lib.zchunk_fprint.argtypes = [zchunk_p, FILE_p]
lib.zchunk_print.restype = None
lib.zchunk_print.argtypes = [zchunk_p]
lib.zchunk_is.restype = c_bool
lib.zchunk_is.argtypes = [c_void_p]
lib.zchunk_test.restype = None
lib.zchunk_test.argtypes = [c_bool]

class Zchunk(object):
    """
    work with memory chunks
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new chunk of the specified size. If you specify the data, it
is copied into the chunk. If you do not specify the data, the chunk is
allocated and left empty, and you can then add data using zchunk_append.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zchunk_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zchunk_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 2)
            self._as_parameter_ = lib.zchunk_new(args[0], args[1]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a chunk
        """
        if self.allow_destruct:
            lib.zchunk_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def frommem(data, size, destructor, hint):
        """
        Create a new chunk from memory. Take ownership of the memory and calling the destructor
on destroy.
        """
        return Zchunk(lib.zchunk_frommem(data, size, destructor, hint), True)

    def resize(self, size):
        """
        Resizes chunk max_size as requested; chunk_cur size is set to zero
        """
        return lib.zchunk_resize(self._as_parameter_, size)

    def size(self):
        """
        Return chunk cur size
        """
        return lib.zchunk_size(self._as_parameter_)

    def max_size(self):
        """
        Return chunk max size
        """
        return lib.zchunk_max_size(self._as_parameter_)

    def data(self):
        """
        Return chunk data
        """
        return lib.zchunk_data(self._as_parameter_)

    def set(self, data, size):
        """
        Set chunk data from user-supplied data; truncate if too large. Data may
be null. Returns actual size of chunk
        """
        return lib.zchunk_set(self._as_parameter_, data, size)

    def fill(self, filler, size):
        """
        Fill chunk data from user-supplied octet
        """
        return lib.zchunk_fill(self._as_parameter_, filler, size)

    def append(self, data, size):
        """
        Append user-supplied data to chunk, return resulting chunk size. If the
data would exceeded the available space, it is truncated. If you want to
grow the chunk to accommodate new data, use the zchunk_extend method.
        """
        return lib.zchunk_append(self._as_parameter_, data, size)

    def extend(self, data, size):
        """
        Append user-supplied data to chunk, return resulting chunk size. If the
data would exceeded the available space, the chunk grows in size.
        """
        return lib.zchunk_extend(self._as_parameter_, data, size)

    def consume(self, source):
        """
        Copy as much data from 'source' into the chunk as possible; returns the
new size of chunk. If all data from 'source' is used, returns exhausted
on the source chunk. Source can be consumed as many times as needed until
it is exhausted. If source was already exhausted, does not change chunk.
        """
        return lib.zchunk_consume(self._as_parameter_, source)

    def exhausted(self):
        """
        Returns true if the chunk was exhausted by consume methods, or if the
chunk has a size of zero.
        """
        return lib.zchunk_exhausted(self._as_parameter_)

    @staticmethod
    def read(handle, bytes):
        """
        Read chunk from an open file descriptor
        """
        return Zchunk(lib.zchunk_read(coerce_py_file(handle), bytes), True)

    def write(self, handle):
        """
        Write chunk to an open file descriptor
        """
        return lib.zchunk_write(self._as_parameter_, coerce_py_file(handle))

    @staticmethod
    def slurp(filename, maxsize):
        """
        Try to slurp an entire file into a chunk. Will read up to maxsize of
the file. If maxsize is 0, will attempt to read the entire file and
fail with an assertion if that cannot fit into memory. Returns a new
chunk containing the file data, or NULL if the file could not be read.
        """
        return Zchunk(lib.zchunk_slurp(filename, maxsize), True)

    def dup(self):
        """
        Create copy of chunk, as new chunk object. Returns a fresh zchunk_t
object, or null if there was not enough heap memory. If chunk is null,
returns null.
        """
        return Zchunk(lib.zchunk_dup(self._as_parameter_), True)

    def strhex(self):
        """
        Return chunk data encoded as printable hex string. Caller must free
string when finished with it.
        """
        return return_fresh_string(lib.zchunk_strhex(self._as_parameter_))

    def strdup(self):
        """
        Return chunk data copied into freshly allocated string
Caller must free string when finished with it.
        """
        return return_fresh_string(lib.zchunk_strdup(self._as_parameter_))

    def streq(self, string):
        """
        Return TRUE if chunk body is equal to string, excluding terminator
        """
        return lib.zchunk_streq(self._as_parameter_, string)

    def pack(self):
        """
        Transform zchunk into a zframe that can be sent in a message.
        """
        return Zframe(lib.zchunk_pack(self._as_parameter_), True)

    @staticmethod
    def packx(self_p):
        """
        Transform zchunk into a zframe that can be sent in a message.
Take ownership of the chunk.
        """
        return Zframe(lib.zchunk_packx(byref(zchunk_p.from_param(self_p))), True)

    @staticmethod
    def unpack(frame):
        """
        Transform a zframe into a zchunk.
        """
        return Zchunk(lib.zchunk_unpack(frame), True)

    def digest(self):
        """
        Calculate SHA1 digest for chunk, using zdigest class.
        """
        return lib.zchunk_digest(self._as_parameter_)

    def fprint(self, file):
        """
        Dump chunk to FILE stream, for debugging and tracing.
        """
        return lib.zchunk_fprint(self._as_parameter_, coerce_py_file(file))

    def print(self):
        """
        Dump message to stderr, for debugging and tracing.
See zchunk_fprint for details
        """
        return lib.zchunk_print(self._as_parameter_)

    @staticmethod
    def is_(self):
        """
        Probe the supplied object, and report if it looks like a zchunk_t.
        """
        return lib.zchunk_is(self)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zchunk_test(verbose)


# zclock
lib.zclock_sleep.restype = None
lib.zclock_sleep.argtypes = [c_int]
lib.zclock_time.restype = msecs_p
lib.zclock_time.argtypes = []
lib.zclock_mono.restype = msecs_p
lib.zclock_mono.argtypes = []
lib.zclock_usecs.restype = msecs_p
lib.zclock_usecs.argtypes = []
lib.zclock_timestr.restype = POINTER(c_char)
lib.zclock_timestr.argtypes = []
lib.zclock_test.restype = None
lib.zclock_test.argtypes = [c_bool]

class Zclock(object):
    """
    millisecond clocks and delays
    """

    allow_destruct = False
    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def sleep(msecs):
        """
        Sleep for a number of milliseconds
        """
        return lib.zclock_sleep(msecs)

    @staticmethod
    def time():
        """
        Return current system clock as milliseconds. Note that this clock can
jump backwards (if the system clock is changed) so is unsafe to use for
timers and time offsets. Use zclock_mono for that instead.
        """
        return lib.zclock_time()

    @staticmethod
    def mono():
        """
        Return current monotonic clock in milliseconds. Use this when you compute
time offsets. The monotonic clock is not affected by system changes and
so will never be reset backwards, unlike a system clock.
        """
        return lib.zclock_mono()

    @staticmethod
    def usecs():
        """
        Return current monotonic clock in microseconds. Use this when you compute
time offsets. The monotonic clock is not affected by system changes and
so will never be reset backwards, unlike a system clock.
        """
        return lib.zclock_usecs()

    @staticmethod
    def timestr():
        """
        Return formatted date/time as fresh string. Free using zstr_free().
        """
        return return_fresh_string(lib.zclock_timestr())

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zclock_test(verbose)


# zconfig
zconfig_fct = CFUNCTYPE(c_int, zconfig_p, c_void_p, c_int)
lib.zconfig_new.restype = zconfig_p
lib.zconfig_new.argtypes = [c_char_p, zconfig_p]
lib.zconfig_destroy.restype = None
lib.zconfig_destroy.argtypes = [POINTER(zconfig_p)]
lib.zconfig_load.restype = zconfig_p
lib.zconfig_load.argtypes = [c_char_p]
lib.zconfig_loadf.restype = zconfig_p
lib.zconfig_loadf.argtypes = [c_char_p]
lib.zconfig_dup.restype = zconfig_p
lib.zconfig_dup.argtypes = [zconfig_p]
lib.zconfig_name.restype = c_char_p
lib.zconfig_name.argtypes = [zconfig_p]
lib.zconfig_value.restype = c_char_p
lib.zconfig_value.argtypes = [zconfig_p]
lib.zconfig_put.restype = None
lib.zconfig_put.argtypes = [zconfig_p, c_char_p, c_char_p]
lib.zconfig_putf.restype = None
lib.zconfig_putf.argtypes = [zconfig_p, c_char_p, c_char_p]
lib.zconfig_get.restype = c_char_p
lib.zconfig_get.argtypes = [zconfig_p, c_char_p, c_char_p]
lib.zconfig_set_name.restype = None
lib.zconfig_set_name.argtypes = [zconfig_p, c_char_p]
lib.zconfig_set_value.restype = None
lib.zconfig_set_value.argtypes = [zconfig_p, c_char_p]
lib.zconfig_child.restype = zconfig_p
lib.zconfig_child.argtypes = [zconfig_p]
lib.zconfig_next.restype = zconfig_p
lib.zconfig_next.argtypes = [zconfig_p]
lib.zconfig_locate.restype = zconfig_p
lib.zconfig_locate.argtypes = [zconfig_p, c_char_p]
lib.zconfig_at_depth.restype = zconfig_p
lib.zconfig_at_depth.argtypes = [zconfig_p, c_int]
lib.zconfig_execute.restype = c_int
lib.zconfig_execute.argtypes = [zconfig_p, zconfig_fct, c_void_p]
lib.zconfig_set_comment.restype = None
lib.zconfig_set_comment.argtypes = [zconfig_p, c_char_p]
lib.zconfig_comments.restype = zlist_p
lib.zconfig_comments.argtypes = [zconfig_p]
lib.zconfig_save.restype = c_int
lib.zconfig_save.argtypes = [zconfig_p, c_char_p]
lib.zconfig_savef.restype = c_int
lib.zconfig_savef.argtypes = [zconfig_p, c_char_p]
lib.zconfig_filename.restype = c_char_p
lib.zconfig_filename.argtypes = [zconfig_p]
lib.zconfig_reload.restype = c_int
lib.zconfig_reload.argtypes = [POINTER(zconfig_p)]
lib.zconfig_chunk_load.restype = zconfig_p
lib.zconfig_chunk_load.argtypes = [zchunk_p]
lib.zconfig_chunk_save.restype = zchunk_p
lib.zconfig_chunk_save.argtypes = [zconfig_p]
lib.zconfig_str_load.restype = zconfig_p
lib.zconfig_str_load.argtypes = [c_char_p]
lib.zconfig_str_save.restype = POINTER(c_char)
lib.zconfig_str_save.argtypes = [zconfig_p]
lib.zconfig_has_changed.restype = c_bool
lib.zconfig_has_changed.argtypes = [zconfig_p]
lib.zconfig_remove_subtree.restype = None
lib.zconfig_remove_subtree.argtypes = [zconfig_p]
lib.zconfig_remove.restype = None
lib.zconfig_remove.argtypes = [POINTER(zconfig_p)]
lib.zconfig_fprint.restype = None
lib.zconfig_fprint.argtypes = [zconfig_p, FILE_p]
lib.zconfig_print.restype = None
lib.zconfig_print.argtypes = [zconfig_p]
lib.zconfig_test.restype = None
lib.zconfig_test.argtypes = [c_bool]

class Zconfig(object):
    """
    work with config files written in rfc.zeromq.org/spec:4/ZPL.
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create new config item
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zconfig_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zconfig_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 2)
            self._as_parameter_ = lib.zconfig_new(args[0], args[1]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a config item and all its children
        """
        if self.allow_destruct:
            lib.zconfig_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def load(filename):
        """
        Load a config tree from a specified ZPL text file; returns a zconfig_t
reference for the root, if the file exists and is readable. Returns NULL
if the file does not exist.
        """
        return Zconfig(lib.zconfig_load(filename), True)

    @staticmethod
    def loadf(format, *args):
        """
        Equivalent to zconfig_load, taking a format string instead of a fixed
filename.
        """
        return Zconfig(lib.zconfig_loadf(format, *args), True)

    def dup(self):
        """
        Create copy of zconfig, caller MUST free the value
Create copy of config, as new zconfig object. Returns a fresh zconfig_t
object. If config is null, or memory was exhausted, returns null.
        """
        return Zconfig(lib.zconfig_dup(self._as_parameter_), True)

    def name(self):
        """
        Return name of config item
        """
        return lib.zconfig_name(self._as_parameter_)

    def value(self):
        """
        Return value of config item
        """
        return lib.zconfig_value(self._as_parameter_)

    def put(self, path, value):
        """
        Insert or update configuration key with value
        """
        return lib.zconfig_put(self._as_parameter_, path, value)

    def putf(self, path, format, *args):
        """
        Equivalent to zconfig_put, accepting a format specifier and variable
argument list, instead of a single string value.
        """
        return lib.zconfig_putf(self._as_parameter_, path, format, *args)

    def get(self, path, default_value):
        """
        Get value for config item into a string value; leading slash is optional
and ignored.
        """
        return lib.zconfig_get(self._as_parameter_, path, default_value)

    def set_name(self, name):
        """
        Set config item name, name may be NULL
        """
        return lib.zconfig_set_name(self._as_parameter_, name)

    def set_value(self, format, *args):
        """
        Set new value for config item. The new value may be a string, a printf
format, or NULL. Note that if string may possibly contain '%', or if it
comes from an insecure source, you must use '%s' as the format, followed
by the string.
        """
        return lib.zconfig_set_value(self._as_parameter_, format, *args)

    def child(self):
        """
        Find our first child, if any
        """
        return Zconfig(lib.zconfig_child(self._as_parameter_), False)

    def next(self):
        """
        Find our first sibling, if any
        """
        return Zconfig(lib.zconfig_next(self._as_parameter_), False)

    def locate(self, path):
        """
        Find a config item along a path; leading slash is optional and ignored.
        """
        return Zconfig(lib.zconfig_locate(self._as_parameter_, path), False)

    def at_depth(self, level):
        """
        Locate the last config item at a specified depth
        """
        return Zconfig(lib.zconfig_at_depth(self._as_parameter_, level), False)

    def execute(self, handler, arg):
        """
        Execute a callback for each config item in the tree; returns zero if
successful, else -1.
        """
        return lib.zconfig_execute(self._as_parameter_, handler, arg)

    def set_comment(self, format, *args):
        """
        Add comment to config item before saving to disk. You can add as many
comment lines as you like. If you use a null format, all comments are
deleted.
        """
        return lib.zconfig_set_comment(self._as_parameter_, format, *args)

    def comments(self):
        """
        Return comments of config item, as zlist.
        """
        return Zlist(lib.zconfig_comments(self._as_parameter_), False)

    def save(self, filename):
        """
        Save a config tree to a specified ZPL text file, where a filename
"-" means dump to standard output.
        """
        return lib.zconfig_save(self._as_parameter_, filename)

    def savef(self, format, *args):
        """
        Equivalent to zconfig_save, taking a format string instead of a fixed
filename.
        """
        return lib.zconfig_savef(self._as_parameter_, format, *args)

    def filename(self):
        """
        Report filename used during zconfig_load, or NULL if none
        """
        return lib.zconfig_filename(self._as_parameter_)

    @staticmethod
    def reload(self_p):
        """
        Reload config tree from same file that it was previously loaded from.
Returns 0 if OK, -1 if there was an error (and then does not change
existing data).
        """
        return lib.zconfig_reload(byref(zconfig_p.from_param(self_p)))

    @staticmethod
    def chunk_load(chunk):
        """
        Load a config tree from a memory chunk
        """
        return Zconfig(lib.zconfig_chunk_load(chunk), False)

    def chunk_save(self):
        """
        Save a config tree to a new memory chunk
        """
        return Zchunk(lib.zconfig_chunk_save(self._as_parameter_), False)

    @staticmethod
    def str_load(string):
        """
        Load a config tree from a null-terminated string
        """
        return Zconfig(lib.zconfig_str_load(string), True)

    def str_save(self):
        """
        Save a config tree to a new null terminated string
        """
        return return_fresh_string(lib.zconfig_str_save(self._as_parameter_))

    def has_changed(self):
        """
        Return true if a configuration tree was loaded from a file and that
file has changed in since the tree was loaded.
        """
        return lib.zconfig_has_changed(self._as_parameter_)

    def remove_subtree(self):
        """
        Destroy subtree (all children)
        """
        return lib.zconfig_remove_subtree(self._as_parameter_)

    @staticmethod
    def remove(self_p):
        """
        Destroy node and subtree (all children)
        """
        return lib.zconfig_remove(byref(zconfig_p.from_param(self_p)))

    def fprint(self, file):
        """
        Print the config file to open stream
        """
        return lib.zconfig_fprint(self._as_parameter_, coerce_py_file(file))

    def print(self):
        """
        Print properties of object
        """
        return lib.zconfig_print(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class
        """
        return lib.zconfig_test(verbose)


# zdigest
lib.zdigest_new.restype = zdigest_p
lib.zdigest_new.argtypes = []
lib.zdigest_destroy.restype = None
lib.zdigest_destroy.argtypes = [POINTER(zdigest_p)]
lib.zdigest_update.restype = None
lib.zdigest_update.argtypes = [zdigest_p, c_void_p, c_size_t]
lib.zdigest_data.restype = c_void_p
lib.zdigest_data.argtypes = [zdigest_p]
lib.zdigest_size.restype = c_size_t
lib.zdigest_size.argtypes = [zdigest_p]
lib.zdigest_string.restype = c_char_p
lib.zdigest_string.argtypes = [zdigest_p]
lib.zdigest_test.restype = None
lib.zdigest_test.argtypes = [c_bool]

class Zdigest(object):
    """
    provides hashing functions (SHA-1 at present)
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Constructor - creates new digest object, which you use to build up a
digest by repeatedly calling zdigest_update() on chunks of data.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zdigest_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zdigest_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zdigest_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a digest object
        """
        if self.allow_destruct:
            lib.zdigest_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def update(self, buffer, length):
        """
        Add buffer into digest calculation
        """
        return lib.zdigest_update(self._as_parameter_, buffer, length)

    def data(self):
        """
        Return final digest hash data. If built without crypto support,
returns NULL.
        """
        return lib.zdigest_data(self._as_parameter_)

    def size(self):
        """
        Return final digest hash size
        """
        return lib.zdigest_size(self._as_parameter_)

    def string(self):
        """
        Return digest as printable hex string; caller should not modify nor
free this string. After calling this, you may not use zdigest_update()
on the same digest. If built without crypto support, returns NULL.
        """
        return lib.zdigest_string(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zdigest_test(verbose)


# zdir
lib.zdir_new.restype = zdir_p
lib.zdir_new.argtypes = [c_char_p, c_char_p]
lib.zdir_destroy.restype = None
lib.zdir_destroy.argtypes = [POINTER(zdir_p)]
lib.zdir_path.restype = c_char_p
lib.zdir_path.argtypes = [zdir_p]
lib.zdir_modified.restype = c_int
lib.zdir_modified.argtypes = [zdir_p]
lib.zdir_cursize.restype = c_int
lib.zdir_cursize.argtypes = [zdir_p]
lib.zdir_count.restype = c_size_t
lib.zdir_count.argtypes = [zdir_p]
lib.zdir_list.restype = zlist_p
lib.zdir_list.argtypes = [zdir_p]
lib.zdir_list_paths.restype = zlist_p
lib.zdir_list_paths.argtypes = [zdir_p]
lib.zdir_remove.restype = None
lib.zdir_remove.argtypes = [zdir_p, c_bool]
lib.zdir_diff.restype = zlist_p
lib.zdir_diff.argtypes = [zdir_p, zdir_p, c_char_p]
lib.zdir_resync.restype = zlist_p
lib.zdir_resync.argtypes = [zdir_p, c_char_p]
lib.zdir_cache.restype = zhash_p
lib.zdir_cache.argtypes = [zdir_p]
lib.zdir_fprint.restype = None
lib.zdir_fprint.argtypes = [zdir_p, FILE_p, c_int]
lib.zdir_print.restype = None
lib.zdir_print.argtypes = [zdir_p, c_int]
lib.zdir_watch.restype = None
lib.zdir_watch.argtypes = [zsock_p, c_void_p]
lib.zdir_test.restype = None
lib.zdir_test.argtypes = [c_bool]

class Zdir(object):
    """
    work with file-system directories
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new directory item that loads in the full tree of the specified
path, optionally located under some parent path. If parent is "-", then
loads only the top-level directory, and does not use parent as a path.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zdir_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zdir_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 2)
            self._as_parameter_ = lib.zdir_new(args[0], args[1]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a directory tree and all children it contains.
        """
        if self.allow_destruct:
            lib.zdir_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def path(self):
        """
        Return directory path
        """
        return lib.zdir_path(self._as_parameter_)

    def modified(self):
        """
        Return last modification time for directory.
        """
        return lib.zdir_modified(self._as_parameter_)

    def cursize(self):
        """
        Return total hierarchy size, in bytes of data contained in all files
in the directory tree.
        """
        return lib.zdir_cursize(self._as_parameter_)

    def count(self):
        """
        Return directory count
        """
        return lib.zdir_count(self._as_parameter_)

    def list(self):
        """
        Returns a sorted list of zfile objects; Each entry in the list is a pointer
to a zfile_t item already allocated in the zdir tree. Do not destroy the
original zdir tree until you are done with this list.
        """
        return Zlist(lib.zdir_list(self._as_parameter_), True)

    def list_paths(self):
        """
        Returns a sorted list of char*; Each entry in the list is a path of a file
or directory contained in self.
        """
        return Zlist(lib.zdir_list_paths(self._as_parameter_), True)

    def remove(self, force):
        """
        Remove directory, optionally including all files that it contains, at
all levels. If force is false, will only remove the directory if empty.
If force is true, will remove all files and all subdirectories.
        """
        return lib.zdir_remove(self._as_parameter_, force)

    @staticmethod
    def diff(older, newer, alias):
        """
        Calculate differences between two versions of a directory tree.
Returns a list of zdir_patch_t patches. Either older or newer may
be null, indicating the directory is empty/absent. If alias is set,
generates virtual filename (minus path, plus alias).
        """
        return Zlist(lib.zdir_diff(older, newer, alias), True)

    def resync(self, alias):
        """
        Return full contents of directory as a zdir_patch list.
        """
        return Zlist(lib.zdir_resync(self._as_parameter_, alias), True)

    def cache(self):
        """
        Load directory cache; returns a hash table containing the SHA-1 digests
of every file in the tree. The cache is saved between runs in .cache.
        """
        return Zhash(lib.zdir_cache(self._as_parameter_), True)

    def fprint(self, file, indent):
        """
        Print contents of directory to open stream
        """
        return lib.zdir_fprint(self._as_parameter_, coerce_py_file(file), indent)

    def print(self, indent):
        """
        Print contents of directory to stdout
        """
        return lib.zdir_print(self._as_parameter_, indent)

    @staticmethod
    def watch(pipe, unused):
        """
        Create a new zdir_watch actor instance:

    zactor_t *watch = zactor_new (zdir_watch, NULL);

Destroy zdir_watch instance:

    zactor_destroy (&watch);

Enable verbose logging of commands and activity:

    zstr_send (watch, "VERBOSE");

Subscribe to changes to a directory path:

    zsock_send (watch, "ss", "SUBSCRIBE", "directory_path");

Unsubscribe from changes to a directory path:

    zsock_send (watch, "ss", "UNSUBSCRIBE", "directory_path");

Receive directory changes:
    zsock_recv (watch, "sp", &path, &patches);

    // Delete the received data.
    free (path);
    zlist_destroy (&patches);
        """
        return lib.zdir_watch(pipe, unused)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zdir_test(verbose)


# zdir_patch
lib.zdir_patch_new.restype = zdir_patch_p
lib.zdir_patch_new.argtypes = [c_char_p, zfile_p, c_int, c_char_p]
lib.zdir_patch_destroy.restype = None
lib.zdir_patch_destroy.argtypes = [POINTER(zdir_patch_p)]
lib.zdir_patch_dup.restype = zdir_patch_p
lib.zdir_patch_dup.argtypes = [zdir_patch_p]
lib.zdir_patch_path.restype = c_char_p
lib.zdir_patch_path.argtypes = [zdir_patch_p]
lib.zdir_patch_file.restype = zfile_p
lib.zdir_patch_file.argtypes = [zdir_patch_p]
lib.zdir_patch_op.restype = c_int
lib.zdir_patch_op.argtypes = [zdir_patch_p]
lib.zdir_patch_vpath.restype = c_char_p
lib.zdir_patch_vpath.argtypes = [zdir_patch_p]
lib.zdir_patch_digest_set.restype = None
lib.zdir_patch_digest_set.argtypes = [zdir_patch_p]
lib.zdir_patch_digest.restype = c_char_p
lib.zdir_patch_digest.argtypes = [zdir_patch_p]
lib.zdir_patch_test.restype = None
lib.zdir_patch_test.argtypes = [c_bool]

class ZdirPatch(object):
    """
    work with directory patches
    """

    CREATE = 1 # Creates a new file
    DELETE = 2 # Delete a file
    allow_destruct = False
    def __init__(self, *args):
        """
        Create new patch
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zdir_patch_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zdir_patch_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 4)
            self._as_parameter_ = lib.zdir_patch_new(args[0], args[1], args[2], args[3]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a patch
        """
        if self.allow_destruct:
            lib.zdir_patch_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def dup(self):
        """
        Create copy of a patch. If the patch is null, or memory was exhausted,
returns null.
        """
        return ZdirPatch(lib.zdir_patch_dup(self._as_parameter_), True)

    def path(self):
        """
        Return patch file directory path
        """
        return lib.zdir_patch_path(self._as_parameter_)

    def file(self):
        """
        Return patch file item
        """
        return Zfile(lib.zdir_patch_file(self._as_parameter_), False)

    def op(self):
        """
        Return operation
        """
        return lib.zdir_patch_op(self._as_parameter_)

    def vpath(self):
        """
        Return patch virtual file path
        """
        return lib.zdir_patch_vpath(self._as_parameter_)

    def digest_set(self):
        """
        Calculate hash digest for file (create only)
        """
        return lib.zdir_patch_digest_set(self._as_parameter_)

    def digest(self):
        """
        Return hash digest for patch file
        """
        return lib.zdir_patch_digest(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zdir_patch_test(verbose)


# zfile
lib.zfile_new.restype = zfile_p
lib.zfile_new.argtypes = [c_char_p, c_char_p]
lib.zfile_destroy.restype = None
lib.zfile_destroy.argtypes = [POINTER(zfile_p)]
lib.zfile_tmp.restype = zfile_p
lib.zfile_tmp.argtypes = []
lib.zfile_dup.restype = zfile_p
lib.zfile_dup.argtypes = [zfile_p]
lib.zfile_filename.restype = c_char_p
lib.zfile_filename.argtypes = [zfile_p, c_char_p]
lib.zfile_restat.restype = None
lib.zfile_restat.argtypes = [zfile_p]
lib.zfile_modified.restype = c_int
lib.zfile_modified.argtypes = [zfile_p]
lib.zfile_cursize.restype = c_int
lib.zfile_cursize.argtypes = [zfile_p]
lib.zfile_is_directory.restype = c_bool
lib.zfile_is_directory.argtypes = [zfile_p]
lib.zfile_is_regular.restype = c_bool
lib.zfile_is_regular.argtypes = [zfile_p]
lib.zfile_is_readable.restype = c_bool
lib.zfile_is_readable.argtypes = [zfile_p]
lib.zfile_is_writeable.restype = c_bool
lib.zfile_is_writeable.argtypes = [zfile_p]
lib.zfile_is_stable.restype = c_bool
lib.zfile_is_stable.argtypes = [zfile_p]
lib.zfile_has_changed.restype = c_bool
lib.zfile_has_changed.argtypes = [zfile_p]
lib.zfile_remove.restype = None
lib.zfile_remove.argtypes = [zfile_p]
lib.zfile_input.restype = c_int
lib.zfile_input.argtypes = [zfile_p]
lib.zfile_output.restype = c_int
lib.zfile_output.argtypes = [zfile_p]
lib.zfile_read.restype = zchunk_p
lib.zfile_read.argtypes = [zfile_p, c_size_t, c_int]
lib.zfile_eof.restype = c_bool
lib.zfile_eof.argtypes = [zfile_p]
lib.zfile_write.restype = c_int
lib.zfile_write.argtypes = [zfile_p, zchunk_p, c_int]
lib.zfile_readln.restype = c_char_p
lib.zfile_readln.argtypes = [zfile_p]
lib.zfile_close.restype = None
lib.zfile_close.argtypes = [zfile_p]
lib.zfile_handle.restype = FILE_p
lib.zfile_handle.argtypes = [zfile_p]
lib.zfile_digest.restype = c_char_p
lib.zfile_digest.argtypes = [zfile_p]
lib.zfile_test.restype = None
lib.zfile_test.argtypes = [c_bool]

class Zfile(object):
    """
    helper functions for working with files.
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        If file exists, populates properties. CZMQ supports portable symbolic
links, which are files with the extension ".ln". A symbolic link is a
text file containing one line, the filename of a target file. Reading
data from the symbolic link actually reads from the target file. Path
may be NULL, in which case it is not used.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zfile_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zfile_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 2)
            self._as_parameter_ = lib.zfile_new(args[0], args[1]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a file item
        """
        if self.allow_destruct:
            lib.zfile_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def tmp():
        """
        Create new temporary file for writing via tmpfile. File is automatically
deleted on destroy
        """
        return Zfile(lib.zfile_tmp(), True)

    def dup(self):
        """
        Duplicate a file item, returns a newly constructed item. If the file
is null, or memory was exhausted, returns null.
        """
        return Zfile(lib.zfile_dup(self._as_parameter_), True)

    def filename(self, path):
        """
        Return file name, remove path if provided
        """
        return lib.zfile_filename(self._as_parameter_, path)

    def restat(self):
        """
        Refresh file properties from disk; this is not done automatically
on access methods, otherwise it is not possible to compare directory
snapshots.
        """
        return lib.zfile_restat(self._as_parameter_)

    def modified(self):
        """
        Return when the file was last modified. If you want this to reflect the
current situation, call zfile_restat before checking this property.
        """
        return lib.zfile_modified(self._as_parameter_)

    def cursize(self):
        """
        Return the last-known size of the file. If you want this to reflect the
current situation, call zfile_restat before checking this property.
        """
        return lib.zfile_cursize(self._as_parameter_)

    def is_directory(self):
        """
        Return true if the file is a directory. If you want this to reflect
any external changes, call zfile_restat before checking this property.
        """
        return lib.zfile_is_directory(self._as_parameter_)

    def is_regular(self):
        """
        Return true if the file is a regular file. If you want this to reflect
any external changes, call zfile_restat before checking this property.
        """
        return lib.zfile_is_regular(self._as_parameter_)

    def is_readable(self):
        """
        Return true if the file is readable by this process. If you want this to
reflect any external changes, call zfile_restat before checking this
property.
        """
        return lib.zfile_is_readable(self._as_parameter_)

    def is_writeable(self):
        """
        Return true if the file is writeable by this process. If you want this
to reflect any external changes, call zfile_restat before checking this
property.
        """
        return lib.zfile_is_writeable(self._as_parameter_)

    def is_stable(self):
        """
        Check if file has stopped changing and can be safely processed.
Updates the file statistics from disk at every call.
        """
        return lib.zfile_is_stable(self._as_parameter_)

    def has_changed(self):
        """
        Return true if the file was changed on disk since the zfile_t object
was created, or the last zfile_restat() call made on it.
        """
        return lib.zfile_has_changed(self._as_parameter_)

    def remove(self):
        """
        Remove the file from disk
        """
        return lib.zfile_remove(self._as_parameter_)

    def input(self):
        """
        Open file for reading
Returns 0 if OK, -1 if not found or not accessible
        """
        return lib.zfile_input(self._as_parameter_)

    def output(self):
        """
        Open file for writing, creating directory if needed
File is created if necessary; chunks can be written to file at any
location. Returns 0 if OK, -1 if error.
        """
        return lib.zfile_output(self._as_parameter_)

    def read(self, bytes, offset):
        """
        Read chunk from file at specified position. If this was the last chunk,
sets the eof property. Returns a null chunk in case of error.
        """
        return Zchunk(lib.zfile_read(self._as_parameter_, bytes, offset), True)

    def eof(self):
        """
        Returns true if zfile_read() just read the last chunk in the file.
        """
        return lib.zfile_eof(self._as_parameter_)

    def write(self, chunk, offset):
        """
        Write chunk to file at specified position
Return 0 if OK, else -1
        """
        return lib.zfile_write(self._as_parameter_, chunk, offset)

    def readln(self):
        """
        Read next line of text from file. Returns a pointer to the text line,
or NULL if there was nothing more to read from the file.
        """
        return lib.zfile_readln(self._as_parameter_)

    def close(self):
        """
        Close file, if open
        """
        return lib.zfile_close(self._as_parameter_)

    def handle(self):
        """
        Return file handle, if opened
        """
        return return_py_file(lib.zfile_handle(self._as_parameter_))

    def digest(self):
        """
        Calculate SHA1 digest for file, using zdigest class.
        """
        return lib.zfile_digest(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zfile_test(verbose)


# zframe
zframe_destructor_fn = CFUNCTYPE(None, POINTER(c_void_p))
lib.zframe_new.restype = zframe_p
lib.zframe_new.argtypes = [c_void_p, c_size_t]
lib.zframe_destroy.restype = None
lib.zframe_destroy.argtypes = [POINTER(zframe_p)]
lib.zframe_new_empty.restype = zframe_p
lib.zframe_new_empty.argtypes = []
lib.zframe_from.restype = zframe_p
lib.zframe_from.argtypes = [c_char_p]
lib.zframe_frommem.restype = zframe_p
lib.zframe_frommem.argtypes = [c_void_p, c_size_t, zframe_destructor_fn, c_void_p]
lib.zframe_recv.restype = zframe_p
lib.zframe_recv.argtypes = [c_void_p]
lib.zframe_send.restype = c_int
lib.zframe_send.argtypes = [POINTER(zframe_p), c_void_p, c_int]
lib.zframe_size.restype = c_size_t
lib.zframe_size.argtypes = [zframe_p]
lib.zframe_data.restype = c_void_p
lib.zframe_data.argtypes = [zframe_p]
lib.zframe_meta.restype = c_char_p
lib.zframe_meta.argtypes = [zframe_p, c_char_p]
lib.zframe_dup.restype = zframe_p
lib.zframe_dup.argtypes = [zframe_p]
lib.zframe_strhex.restype = POINTER(c_char)
lib.zframe_strhex.argtypes = [zframe_p]
lib.zframe_strdup.restype = POINTER(c_char)
lib.zframe_strdup.argtypes = [zframe_p]
lib.zframe_streq.restype = c_bool
lib.zframe_streq.argtypes = [zframe_p, c_char_p]
lib.zframe_more.restype = c_int
lib.zframe_more.argtypes = [zframe_p]
lib.zframe_set_more.restype = None
lib.zframe_set_more.argtypes = [zframe_p, c_int]
lib.zframe_routing_id.restype = c_int
lib.zframe_routing_id.argtypes = [zframe_p]
lib.zframe_set_routing_id.restype = None
lib.zframe_set_routing_id.argtypes = [zframe_p, c_int]
lib.zframe_group.restype = c_char_p
lib.zframe_group.argtypes = [zframe_p]
lib.zframe_set_group.restype = c_int
lib.zframe_set_group.argtypes = [zframe_p, c_char_p]
lib.zframe_eq.restype = c_bool
lib.zframe_eq.argtypes = [zframe_p, zframe_p]
lib.zframe_reset.restype = None
lib.zframe_reset.argtypes = [zframe_p, c_void_p, c_size_t]
lib.zframe_print.restype = None
lib.zframe_print.argtypes = [zframe_p, c_char_p]
lib.zframe_print_n.restype = None
lib.zframe_print_n.argtypes = [zframe_p, c_char_p, c_size_t]
lib.zframe_is.restype = c_bool
lib.zframe_is.argtypes = [c_void_p]
lib.zframe_test.restype = None
lib.zframe_test.argtypes = [c_bool]

class Zframe(object):
    """
    working with single message frames
    """

    MORE = 1 #
    REUSE = 2 #
    DONTWAIT = 4 #
    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new frame. If size is not null, allocates the frame data
to the specified size. If additionally, data is not null, copies
size octets from the specified data into the frame body.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zframe_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zframe_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 2)
            self._as_parameter_ = lib.zframe_new(args[0], args[1]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a frame
        """
        if self.allow_destruct:
            lib.zframe_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def new_empty():
        """
        Create an empty (zero-sized) frame
        """
        return Zframe(lib.zframe_new_empty(), True)

    @staticmethod
    def from_(string):
        """
        Create a frame with a specified string content.
        """
        return Zframe(lib.zframe_from(string), True)

    @staticmethod
    def frommem(data, size, destructor, hint):
        """
        Create a new frame from memory. Take ownership of the memory and calling the destructor
on destroy.
        """
        return Zframe(lib.zframe_frommem(data, size, destructor, hint), True)

    @staticmethod
    def recv(source):
        """
        Receive frame from socket, returns zframe_t object or NULL if the recv
was interrupted. Does a blocking recv, if you want to not block then use
zpoller or zloop.
        """
        return Zframe(lib.zframe_recv(source), True)

    @staticmethod
    def send(self_p, dest, flags):
        """
        Send a frame to a socket, destroy frame after sending.
Return -1 on error, 0 on success.
        """
        return lib.zframe_send(byref(zframe_p.from_param(self_p)), dest, flags)

    def size(self):
        """
        Return number of bytes in frame data
        """
        return lib.zframe_size(self._as_parameter_)

    def data(self):
        """
        Return address of frame data
        """
        return lib.zframe_data(self._as_parameter_)

    def meta(self, property):
        """
        Return meta data property for frame
The caller shall not modify or free the returned value, which shall be
owned by the message.
        """
        return lib.zframe_meta(self._as_parameter_, property)

    def dup(self):
        """
        Create a new frame that duplicates an existing frame. If frame is null,
or memory was exhausted, returns null.
        """
        return Zframe(lib.zframe_dup(self._as_parameter_), True)

    def strhex(self):
        """
        Return frame data encoded as printable hex string, useful for 0MQ UUIDs.
Caller must free string when finished with it.
        """
        return return_fresh_string(lib.zframe_strhex(self._as_parameter_))

    def strdup(self):
        """
        Return frame data copied into freshly allocated string
Caller must free string when finished with it.
        """
        return return_fresh_string(lib.zframe_strdup(self._as_parameter_))

    def streq(self, string):
        """
        Return TRUE if frame body is equal to string, excluding terminator
        """
        return lib.zframe_streq(self._as_parameter_, string)

    def more(self):
        """
        Return frame MORE indicator (1 or 0), set when reading frame from socket
or by the zframe_set_more() method
        """
        return lib.zframe_more(self._as_parameter_)

    def set_more(self, more):
        """
        Set frame MORE indicator (1 or 0). Note this is NOT used when sending
frame to socket, you have to specify flag explicitly.
        """
        return lib.zframe_set_more(self._as_parameter_, more)

    def routing_id(self):
        """
        Return frame routing ID, if the frame came from a ZMQ_SERVER socket.
Else returns zero.
        """
        return lib.zframe_routing_id(self._as_parameter_)

    def set_routing_id(self, routing_id):
        """
        Set routing ID on frame. This is used if/when the frame is sent to a
ZMQ_SERVER socket.
        """
        return lib.zframe_set_routing_id(self._as_parameter_, routing_id)

    def group(self):
        """
        Return frame group of radio-dish pattern.
        """
        return lib.zframe_group(self._as_parameter_)

    def set_group(self, group):
        """
        Set group on frame. This is used if/when the frame is sent to a
ZMQ_RADIO socket.
Return -1 on error, 0 on success.
        """
        return lib.zframe_set_group(self._as_parameter_, group)

    def eq(self, other):
        """
        Return TRUE if two frames have identical size and data
If either frame is NULL, equality is always false.
        """
        return lib.zframe_eq(self._as_parameter_, other)

    def reset(self, data, size):
        """
        Set new contents for frame
        """
        return lib.zframe_reset(self._as_parameter_, data, size)

    def print(self, prefix):
        """
        Send message to zsys log sink (may be stdout, or system facility as
configured by zsys_set_logstream). Prefix shows before frame, if not null.
Long messages are truncated.
        """
        return lib.zframe_print(self._as_parameter_, prefix)

    def print_n(self, prefix, length):
        """
        Send message to zsys log sink (may be stdout, or system facility as
configured by zsys_set_logstream). Prefix shows before frame, if not null.
Message length is specified; no truncation unless length is zero.
Backwards compatible with zframe_print when length is zero.
        """
        return lib.zframe_print_n(self._as_parameter_, prefix, length)

    @staticmethod
    def is_(self):
        """
        Probe the supplied object, and report if it looks like a zframe_t.
        """
        return lib.zframe_is(self)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zframe_test(verbose)


# zhash
zhash_free_fn = CFUNCTYPE(None, c_void_p)
lib.zhash_new.restype = zhash_p
lib.zhash_new.argtypes = []
lib.zhash_destroy.restype = None
lib.zhash_destroy.argtypes = [POINTER(zhash_p)]
lib.zhash_unpack.restype = zhash_p
lib.zhash_unpack.argtypes = [zframe_p]
lib.zhash_insert.restype = c_int
lib.zhash_insert.argtypes = [zhash_p, c_char_p, c_void_p]
lib.zhash_update.restype = None
lib.zhash_update.argtypes = [zhash_p, c_char_p, c_void_p]
lib.zhash_delete.restype = None
lib.zhash_delete.argtypes = [zhash_p, c_char_p]
lib.zhash_lookup.restype = c_void_p
lib.zhash_lookup.argtypes = [zhash_p, c_char_p]
lib.zhash_rename.restype = c_int
lib.zhash_rename.argtypes = [zhash_p, c_char_p, c_char_p]
lib.zhash_freefn.restype = c_void_p
lib.zhash_freefn.argtypes = [zhash_p, c_char_p, zhash_free_fn]
lib.zhash_size.restype = c_size_t
lib.zhash_size.argtypes = [zhash_p]
lib.zhash_dup.restype = zhash_p
lib.zhash_dup.argtypes = [zhash_p]
lib.zhash_keys.restype = zlist_p
lib.zhash_keys.argtypes = [zhash_p]
lib.zhash_first.restype = c_void_p
lib.zhash_first.argtypes = [zhash_p]
lib.zhash_next.restype = c_void_p
lib.zhash_next.argtypes = [zhash_p]
lib.zhash_cursor.restype = c_char_p
lib.zhash_cursor.argtypes = [zhash_p]
lib.zhash_comment.restype = None
lib.zhash_comment.argtypes = [zhash_p, c_char_p]
lib.zhash_pack.restype = zframe_p
lib.zhash_pack.argtypes = [zhash_p]
lib.zhash_save.restype = c_int
lib.zhash_save.argtypes = [zhash_p, c_char_p]
lib.zhash_load.restype = c_int
lib.zhash_load.argtypes = [zhash_p, c_char_p]
lib.zhash_refresh.restype = c_int
lib.zhash_refresh.argtypes = [zhash_p]
lib.zhash_autofree.restype = None
lib.zhash_autofree.argtypes = [zhash_p]
lib.zhash_test.restype = None
lib.zhash_test.argtypes = [c_bool]

class Zhash(object):
    """
    generic type-free hash container (simple)
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new, empty hash container
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zhash_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zhash_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zhash_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a hash container and all items in it
        """
        if self.allow_destruct:
            lib.zhash_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def unpack(frame):
        """
        Unpack binary frame into a new hash table. Packed data must follow format
defined by zhash_pack. Hash table is set to autofree. An empty frame
unpacks to an empty hash table.
        """
        return Zhash(lib.zhash_unpack(frame), True)

    def insert(self, key, item):
        """
        Insert item into hash table with specified key and item.
If key is already present returns -1 and leaves existing item unchanged
Returns 0 on success.
        """
        return lib.zhash_insert(self._as_parameter_, key, item)

    def update(self, key, item):
        """
        Update item into hash table with specified key and item.
If key is already present, destroys old item and inserts new one.
Use free_fn method to ensure deallocator is properly called on item.
        """
        return lib.zhash_update(self._as_parameter_, key, item)

    def delete(self, key):
        """
        Remove an item specified by key from the hash table. If there was no such
item, this function does nothing.
        """
        return lib.zhash_delete(self._as_parameter_, key)

    def lookup(self, key):
        """
        Return the item at the specified key, or null
        """
        return c_void_p(lib.zhash_lookup(self._as_parameter_, key))

    def rename(self, old_key, new_key):
        """
        Reindexes an item from an old key to a new key. If there was no such
item, does nothing. Returns 0 if successful, else -1.
        """
        return lib.zhash_rename(self._as_parameter_, old_key, new_key)

    def freefn(self, key, free_fn):
        """
        Set a free function for the specified hash table item. When the item is
destroyed, the free function, if any, is called on that item.
Use this when hash items are dynamically allocated, to ensure that
you don't have memory leaks. You can pass 'free' or NULL as a free_fn.
Returns the item, or NULL if there is no such item.
        """
        return c_void_p(lib.zhash_freefn(self._as_parameter_, key, free_fn))

    def size(self):
        """
        Return the number of keys/items in the hash table
        """
        return lib.zhash_size(self._as_parameter_)

    def dup(self):
        """
        Make copy of hash table; if supplied table is null, returns null.
Does not copy items themselves. Rebuilds new table so may be slow on
very large tables. NOTE: only works with item values that are strings
since there's no other way to know how to duplicate the item value.
        """
        return Zhash(lib.zhash_dup(self._as_parameter_), True)

    def keys(self):
        """
        Return keys for items in table
        """
        return Zlist(lib.zhash_keys(self._as_parameter_), True)

    def first(self):
        """
        Simple iterator; returns first item in hash table, in no given order,
or NULL if the table is empty. This method is simpler to use than the
foreach() method, which is deprecated. To access the key for this item
use zhash_cursor(). NOTE: do NOT modify the table while iterating.
        """
        return c_void_p(lib.zhash_first(self._as_parameter_))

    def next(self):
        """
        Simple iterator; returns next item in hash table, in no given order,
or NULL if the last item was already returned. Use this together with
zhash_first() to process all items in a hash table. If you need the
items in sorted order, use zhash_keys() and then zlist_sort(). To
access the key for this item use zhash_cursor(). NOTE: do NOT modify
the table while iterating.
        """
        return c_void_p(lib.zhash_next(self._as_parameter_))

    def cursor(self):
        """
        After a successful first/next method, returns the key for the item that
was returned. This is a constant string that you may not modify or
deallocate, and which lasts as long as the item in the hash. After an
unsuccessful first/next, returns NULL.
        """
        return lib.zhash_cursor(self._as_parameter_)

    def comment(self, format, *args):
        """
        Add a comment to hash table before saving to disk. You can add as many
comment lines as you like. These comment lines are discarded when loading
the file. If you use a null format, all comments are deleted.
        """
        return lib.zhash_comment(self._as_parameter_, format, *args)

    def pack(self):
        """
        Serialize hash table to a binary frame that can be sent in a message.
The packed format is compatible with the 'dictionary' type defined in
http://rfc.zeromq.org/spec:35/FILEMQ, and implemented by zproto:

   ; A list of name/value pairs
   dictionary      = dict-count *( dict-name dict-value )
   dict-count      = number-4
   dict-value      = longstr
   dict-name       = string

   ; Strings are always length + text contents
   longstr         = number-4 *VCHAR
   string          = number-1 *VCHAR

   ; Numbers are unsigned integers in network byte order
   number-1        = 1OCTET
   number-4        = 4OCTET

Comments are not included in the packed data. Item values MUST be
strings.
        """
        return Zframe(lib.zhash_pack(self._as_parameter_), True)

    def save(self, filename):
        """
        Save hash table to a text file in name=value format. Hash values must be
printable strings; keys may not contain '=' character. Returns 0 if OK,
else -1 if a file error occurred.
        """
        return lib.zhash_save(self._as_parameter_, filename)

    def load(self, filename):
        """
        Load hash table from a text file in name=value format; hash table must
already exist. Hash values must printable strings; keys may not contain
'=' character. Returns 0 if OK, else -1 if a file was not readable.
        """
        return lib.zhash_load(self._as_parameter_, filename)

    def refresh(self):
        """
        When a hash table was loaded from a file by zhash_load, this method will
reload the file if it has been modified since, and is "stable", i.e. not
still changing. Returns 0 if OK, -1 if there was an error reloading the
file.
        """
        return lib.zhash_refresh(self._as_parameter_)

    def autofree(self):
        """
        Set hash for automatic value destruction. Note that this assumes that
values are NULL-terminated strings. Do not use with different types.
        """
        return lib.zhash_autofree(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zhash_test(verbose)


# zhashx
zhashx_destructor_fn = CFUNCTYPE(None, POINTER(c_void_p))
zhashx_duplicator_fn = CFUNCTYPE(c_void_p, c_void_p)
zhashx_comparator_fn = CFUNCTYPE(c_int, c_void_p, c_void_p)
zhashx_free_fn = CFUNCTYPE(None, c_void_p)
zhashx_hash_fn = CFUNCTYPE(c_size_t, c_void_p)
zhashx_serializer_fn = CFUNCTYPE(POINTER(c_char), c_void_p)
zhashx_deserializer_fn = CFUNCTYPE(c_void_p, c_char_p)
lib.zhashx_new.restype = zhashx_p
lib.zhashx_new.argtypes = []
lib.zhashx_destroy.restype = None
lib.zhashx_destroy.argtypes = [POINTER(zhashx_p)]
lib.zhashx_unpack.restype = zhashx_p
lib.zhashx_unpack.argtypes = [zframe_p]
lib.zhashx_unpack_own.restype = zhashx_p
lib.zhashx_unpack_own.argtypes = [zframe_p, zhashx_deserializer_fn]
lib.zhashx_insert.restype = c_int
lib.zhashx_insert.argtypes = [zhashx_p, c_void_p, c_void_p]
lib.zhashx_update.restype = None
lib.zhashx_update.argtypes = [zhashx_p, c_void_p, c_void_p]
lib.zhashx_delete.restype = None
lib.zhashx_delete.argtypes = [zhashx_p, c_void_p]
lib.zhashx_purge.restype = None
lib.zhashx_purge.argtypes = [zhashx_p]
lib.zhashx_lookup.restype = c_void_p
lib.zhashx_lookup.argtypes = [zhashx_p, c_void_p]
lib.zhashx_rename.restype = c_int
lib.zhashx_rename.argtypes = [zhashx_p, c_void_p, c_void_p]
lib.zhashx_freefn.restype = c_void_p
lib.zhashx_freefn.argtypes = [zhashx_p, c_void_p, zhashx_free_fn]
lib.zhashx_size.restype = c_size_t
lib.zhashx_size.argtypes = [zhashx_p]
lib.zhashx_keys.restype = zlistx_p
lib.zhashx_keys.argtypes = [zhashx_p]
lib.zhashx_values.restype = zlistx_p
lib.zhashx_values.argtypes = [zhashx_p]
lib.zhashx_first.restype = c_void_p
lib.zhashx_first.argtypes = [zhashx_p]
lib.zhashx_next.restype = c_void_p
lib.zhashx_next.argtypes = [zhashx_p]
lib.zhashx_cursor.restype = c_void_p
lib.zhashx_cursor.argtypes = [zhashx_p]
lib.zhashx_comment.restype = None
lib.zhashx_comment.argtypes = [zhashx_p, c_char_p]
lib.zhashx_save.restype = c_int
lib.zhashx_save.argtypes = [zhashx_p, c_char_p]
lib.zhashx_load.restype = c_int
lib.zhashx_load.argtypes = [zhashx_p, c_char_p]
lib.zhashx_refresh.restype = c_int
lib.zhashx_refresh.argtypes = [zhashx_p]
lib.zhashx_pack.restype = zframe_p
lib.zhashx_pack.argtypes = [zhashx_p]
lib.zhashx_pack_own.restype = zframe_p
lib.zhashx_pack_own.argtypes = [zhashx_p, zhashx_serializer_fn]
lib.zhashx_dup.restype = zhashx_p
lib.zhashx_dup.argtypes = [zhashx_p]
lib.zhashx_set_destructor.restype = None
lib.zhashx_set_destructor.argtypes = [zhashx_p, zhashx_destructor_fn]
lib.zhashx_set_duplicator.restype = None
lib.zhashx_set_duplicator.argtypes = [zhashx_p, zhashx_duplicator_fn]
lib.zhashx_set_key_destructor.restype = None
lib.zhashx_set_key_destructor.argtypes = [zhashx_p, zhashx_destructor_fn]
lib.zhashx_set_key_duplicator.restype = None
lib.zhashx_set_key_duplicator.argtypes = [zhashx_p, zhashx_duplicator_fn]
lib.zhashx_set_key_comparator.restype = None
lib.zhashx_set_key_comparator.argtypes = [zhashx_p, zhashx_comparator_fn]
lib.zhashx_set_key_hasher.restype = None
lib.zhashx_set_key_hasher.argtypes = [zhashx_p, zhashx_hash_fn]
lib.zhashx_dup_v2.restype = zhashx_p
lib.zhashx_dup_v2.argtypes = [zhashx_p]
lib.zhashx_test.restype = None
lib.zhashx_test.argtypes = [c_bool]

class Zhashx(object):
    """
    extended generic type-free hash container
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new, empty hash container
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zhashx_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zhashx_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zhashx_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a hash container and all items in it
        """
        if self.allow_destruct:
            lib.zhashx_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def unpack(frame):
        """
        Unpack binary frame into a new hash table. Packed data must follow format
defined by zhashx_pack. Hash table is set to autofree. An empty frame
unpacks to an empty hash table.
        """
        return Zhashx(lib.zhashx_unpack(frame), True)

    @staticmethod
    def unpack_own(frame, deserializer):
        """
        Same as unpack but uses a user-defined deserializer function to convert
a longstr back into item format.
        """
        return Zhashx(lib.zhashx_unpack_own(frame, deserializer), True)

    def insert(self, key, item):
        """
        Insert item into hash table with specified key and item.
If key is already present returns -1 and leaves existing item unchanged
Returns 0 on success.
        """
        return lib.zhashx_insert(self._as_parameter_, key, item)

    def update(self, key, item):
        """
        Update or insert item into hash table with specified key and item. If the
key is already present, destroys old item and inserts new one. If you set
a container item destructor, this is called on the old value. If the key
was not already present, inserts a new item. Sets the hash cursor to the
new item.
        """
        return lib.zhashx_update(self._as_parameter_, key, item)

    def delete(self, key):
        """
        Remove an item specified by key from the hash table. If there was no such
item, this function does nothing.
        """
        return lib.zhashx_delete(self._as_parameter_, key)

    def purge(self):
        """
        Delete all items from the hash table. If the key destructor is
set, calls it on every key. If the item destructor is set, calls
it on every item.
        """
        return lib.zhashx_purge(self._as_parameter_)

    def lookup(self, key):
        """
        Return the item at the specified key, or null
        """
        return c_void_p(lib.zhashx_lookup(self._as_parameter_, key))

    def rename(self, old_key, new_key):
        """
        Reindexes an item from an old key to a new key. If there was no such
item, does nothing. Returns 0 if successful, else -1.
        """
        return lib.zhashx_rename(self._as_parameter_, old_key, new_key)

    def freefn(self, key, free_fn):
        """
        Set a free function for the specified hash table item. When the item is
destroyed, the free function, if any, is called on that item.
Use this when hash items are dynamically allocated, to ensure that
you don't have memory leaks. You can pass 'free' or NULL as a free_fn.
Returns the item, or NULL if there is no such item.
        """
        return c_void_p(lib.zhashx_freefn(self._as_parameter_, key, free_fn))

    def size(self):
        """
        Return the number of keys/items in the hash table
        """
        return lib.zhashx_size(self._as_parameter_)

    def keys(self):
        """
        Return a zlistx_t containing the keys for the items in the
table. Uses the key_duplicator to duplicate all keys and sets the
key_destructor as destructor for the list.
        """
        return Zlistx(lib.zhashx_keys(self._as_parameter_), True)

    def values(self):
        """
        Return a zlistx_t containing the values for the items in the
table. Uses the duplicator to duplicate all items and sets the
destructor as destructor for the list.
        """
        return Zlistx(lib.zhashx_values(self._as_parameter_), True)

    def first(self):
        """
        Simple iterator; returns first item in hash table, in no given order,
or NULL if the table is empty. This method is simpler to use than the
foreach() method, which is deprecated. To access the key for this item
use zhashx_cursor(). NOTE: do NOT modify the table while iterating.
        """
        return c_void_p(lib.zhashx_first(self._as_parameter_))

    def next(self):
        """
        Simple iterator; returns next item in hash table, in no given order,
or NULL if the last item was already returned. Use this together with
zhashx_first() to process all items in a hash table. If you need the
items in sorted order, use zhashx_keys() and then zlistx_sort(). To
access the key for this item use zhashx_cursor(). NOTE: do NOT modify
the table while iterating.
        """
        return c_void_p(lib.zhashx_next(self._as_parameter_))

    def cursor(self):
        """
        After a successful first/next method, returns the key for the item that
was returned. This is a constant string that you may not modify or
deallocate, and which lasts as long as the item in the hash. After an
unsuccessful first/next, returns NULL.
        """
        return c_void_p(lib.zhashx_cursor(self._as_parameter_))

    def comment(self, format, *args):
        """
        Add a comment to hash table before saving to disk. You can add as many
comment lines as you like. These comment lines are discarded when loading
the file. If you use a null format, all comments are deleted.
        """
        return lib.zhashx_comment(self._as_parameter_, format, *args)

    def save(self, filename):
        """
        Save hash table to a text file in name=value format. Hash values must be
printable strings; keys may not contain '=' character. Returns 0 if OK,
else -1 if a file error occurred.
        """
        return lib.zhashx_save(self._as_parameter_, filename)

    def load(self, filename):
        """
        Load hash table from a text file in name=value format; hash table must
already exist. Hash values must printable strings; keys may not contain
'=' character. Returns 0 if OK, else -1 if a file was not readable.
        """
        return lib.zhashx_load(self._as_parameter_, filename)

    def refresh(self):
        """
        When a hash table was loaded from a file by zhashx_load, this method will
reload the file if it has been modified since, and is "stable", i.e. not
still changing. Returns 0 if OK, -1 if there was an error reloading the
file.
        """
        return lib.zhashx_refresh(self._as_parameter_)

    def pack(self):
        """
        Serialize hash table to a binary frame that can be sent in a message.
The packed format is compatible with the 'dictionary' type defined in
http://rfc.zeromq.org/spec:35/FILEMQ, and implemented by zproto:

   ; A list of name/value pairs
   dictionary      = dict-count *( dict-name dict-value )
   dict-count      = number-4
   dict-value      = longstr
   dict-name       = string

   ; Strings are always length + text contents
   longstr         = number-4 *VCHAR
   string          = number-1 *VCHAR

   ; Numbers are unsigned integers in network byte order
   number-1        = 1OCTET
   number-4        = 4OCTET

Comments are not included in the packed data. Item values MUST be
strings.
        """
        return Zframe(lib.zhashx_pack(self._as_parameter_), True)

    def pack_own(self, serializer):
        """
        Same as pack but uses a user-defined serializer function to convert items
into longstr.
        """
        return Zframe(lib.zhashx_pack_own(self._as_parameter_, serializer), True)

    def dup(self):
        """
        Make a copy of the list; items are duplicated if you set a duplicator
for the list, otherwise not. Copying a null reference returns a null
reference. Note that this method's behavior changed slightly for CZMQ
v3.x, as it does not set nor respect autofree. It does however let you
duplicate any hash table safely. The old behavior is in zhashx_dup_v2.
        """
        return Zhashx(lib.zhashx_dup(self._as_parameter_), True)

    def set_destructor(self, destructor):
        """
        Set a user-defined deallocator for hash items; by default items are not
freed when the hash is destroyed.
        """
        return lib.zhashx_set_destructor(self._as_parameter_, destructor)

    def set_duplicator(self, duplicator):
        """
        Set a user-defined duplicator for hash items; by default items are not
copied when the hash is duplicated.
        """
        return lib.zhashx_set_duplicator(self._as_parameter_, duplicator)

    def set_key_destructor(self, destructor):
        """
        Set a user-defined deallocator for keys; by default keys are freed
when the hash is destroyed using free().
        """
        return lib.zhashx_set_key_destructor(self._as_parameter_, destructor)

    def set_key_duplicator(self, duplicator):
        """
        Set a user-defined duplicator for keys; by default keys are duplicated
using strdup.
        """
        return lib.zhashx_set_key_duplicator(self._as_parameter_, duplicator)

    def set_key_comparator(self, comparator):
        """
        Set a user-defined comparator for keys; by default keys are
compared using strcmp.
The callback function should return zero (0) on matching
items.
        """
        return lib.zhashx_set_key_comparator(self._as_parameter_, comparator)

    def set_key_hasher(self, hasher):
        """
        Set a user-defined hash function for keys; by default keys are
hashed by a modified Bernstein hashing function.
        """
        return lib.zhashx_set_key_hasher(self._as_parameter_, hasher)

    def dup_v2(self):
        """
        Make copy of hash table; if supplied table is null, returns null.
Does not copy items themselves. Rebuilds new table so may be slow on
very large tables. NOTE: only works with item values that are strings
since there's no other way to know how to duplicate the item value.
        """
        return Zhashx(lib.zhashx_dup_v2(self._as_parameter_), False)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zhashx_test(verbose)


# ziflist
lib.ziflist_new.restype = ziflist_p
lib.ziflist_new.argtypes = []
lib.ziflist_destroy.restype = None
lib.ziflist_destroy.argtypes = [POINTER(ziflist_p)]
lib.ziflist_reload.restype = None
lib.ziflist_reload.argtypes = [ziflist_p]
lib.ziflist_size.restype = c_size_t
lib.ziflist_size.argtypes = [ziflist_p]
lib.ziflist_first.restype = c_char_p
lib.ziflist_first.argtypes = [ziflist_p]
lib.ziflist_next.restype = c_char_p
lib.ziflist_next.argtypes = [ziflist_p]
lib.ziflist_address.restype = c_char_p
lib.ziflist_address.argtypes = [ziflist_p]
lib.ziflist_broadcast.restype = c_char_p
lib.ziflist_broadcast.argtypes = [ziflist_p]
lib.ziflist_netmask.restype = c_char_p
lib.ziflist_netmask.argtypes = [ziflist_p]
lib.ziflist_mac.restype = c_char_p
lib.ziflist_mac.argtypes = [ziflist_p]
lib.ziflist_print.restype = None
lib.ziflist_print.argtypes = [ziflist_p]
lib.ziflist_new_ipv6.restype = ziflist_p
lib.ziflist_new_ipv6.argtypes = []
lib.ziflist_reload_ipv6.restype = None
lib.ziflist_reload_ipv6.argtypes = [ziflist_p]
lib.ziflist_is_ipv6.restype = c_bool
lib.ziflist_is_ipv6.argtypes = [ziflist_p]
lib.ziflist_test.restype = None
lib.ziflist_test.argtypes = [c_bool]

class Ziflist(object):
    """
    List of network interfaces available on system
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Get a list of network interfaces currently defined on the system
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], ziflist_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is ziflist_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.ziflist_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a ziflist instance
        """
        if self.allow_destruct:
            lib.ziflist_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def reload(self):
        """
        Reload network interfaces from system
        """
        return lib.ziflist_reload(self._as_parameter_)

    def size(self):
        """
        Return the number of network interfaces on system
        """
        return lib.ziflist_size(self._as_parameter_)

    def first(self):
        """
        Get first network interface, return NULL if there are none
        """
        return lib.ziflist_first(self._as_parameter_)

    def next(self):
        """
        Get next network interface, return NULL if we hit the last one
        """
        return lib.ziflist_next(self._as_parameter_)

    def address(self):
        """
        Return the current interface IP address as a printable string
        """
        return lib.ziflist_address(self._as_parameter_)

    def broadcast(self):
        """
        Return the current interface broadcast address as a printable string
        """
        return lib.ziflist_broadcast(self._as_parameter_)

    def netmask(self):
        """
        Return the current interface network mask as a printable string
        """
        return lib.ziflist_netmask(self._as_parameter_)

    def mac(self):
        """
        Return the current interface MAC address as a printable string
        """
        return lib.ziflist_mac(self._as_parameter_)

    def print(self):
        """
        Return the list of interfaces.
        """
        return lib.ziflist_print(self._as_parameter_)

    @staticmethod
    def new_ipv6():
        """
        Get a list of network interfaces currently defined on the system
Includes IPv6 interfaces
        """
        return Ziflist(lib.ziflist_new_ipv6(), True)

    def reload_ipv6(self):
        """
        Reload network interfaces from system, including IPv6
        """
        return lib.ziflist_reload_ipv6(self._as_parameter_)

    def is_ipv6(self):
        """
        Return true if the current interface uses IPv6
        """
        return lib.ziflist_is_ipv6(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.ziflist_test(verbose)


# zlist
zlist_compare_fn = CFUNCTYPE(c_int, c_void_p, c_void_p)
zlist_free_fn = CFUNCTYPE(None, c_void_p)
lib.zlist_new.restype = zlist_p
lib.zlist_new.argtypes = []
lib.zlist_destroy.restype = None
lib.zlist_destroy.argtypes = [POINTER(zlist_p)]
lib.zlist_first.restype = c_void_p
lib.zlist_first.argtypes = [zlist_p]
lib.zlist_next.restype = c_void_p
lib.zlist_next.argtypes = [zlist_p]
lib.zlist_last.restype = c_void_p
lib.zlist_last.argtypes = [zlist_p]
lib.zlist_head.restype = c_void_p
lib.zlist_head.argtypes = [zlist_p]
lib.zlist_tail.restype = c_void_p
lib.zlist_tail.argtypes = [zlist_p]
lib.zlist_item.restype = c_void_p
lib.zlist_item.argtypes = [zlist_p]
lib.zlist_append.restype = c_int
lib.zlist_append.argtypes = [zlist_p, c_void_p]
lib.zlist_push.restype = c_int
lib.zlist_push.argtypes = [zlist_p, c_void_p]
lib.zlist_pop.restype = c_void_p
lib.zlist_pop.argtypes = [zlist_p]
lib.zlist_exists.restype = c_bool
lib.zlist_exists.argtypes = [zlist_p, c_void_p]
lib.zlist_remove.restype = None
lib.zlist_remove.argtypes = [zlist_p, c_void_p]
lib.zlist_dup.restype = zlist_p
lib.zlist_dup.argtypes = [zlist_p]
lib.zlist_purge.restype = None
lib.zlist_purge.argtypes = [zlist_p]
lib.zlist_size.restype = c_size_t
lib.zlist_size.argtypes = [zlist_p]
lib.zlist_sort.restype = None
lib.zlist_sort.argtypes = [zlist_p, zlist_compare_fn]
lib.zlist_autofree.restype = None
lib.zlist_autofree.argtypes = [zlist_p]
lib.zlist_comparefn.restype = None
lib.zlist_comparefn.argtypes = [zlist_p, zlist_compare_fn]
lib.zlist_freefn.restype = c_void_p
lib.zlist_freefn.argtypes = [zlist_p, c_void_p, zlist_free_fn, c_bool]
lib.zlist_test.restype = None
lib.zlist_test.argtypes = [c_bool]

class Zlist(object):
    """
    simple generic list container
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new list container
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zlist_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zlist_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zlist_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a list container
        """
        if self.allow_destruct:
            lib.zlist_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def first(self):
        """
        Return the item at the head of list. If the list is empty, returns NULL.
Leaves cursor pointing at the head item, or NULL if the list is empty.
        """
        return c_void_p(lib.zlist_first(self._as_parameter_))

    def next(self):
        """
        Return the next item. If the list is empty, returns NULL. To move to
the start of the list call zlist_first (). Advances the cursor.
        """
        return c_void_p(lib.zlist_next(self._as_parameter_))

    def last(self):
        """
        Return the item at the tail of list. If the list is empty, returns NULL.
Leaves cursor pointing at the tail item, or NULL if the list is empty.
        """
        return c_void_p(lib.zlist_last(self._as_parameter_))

    def head(self):
        """
        Return first item in the list, or null, leaves the cursor
        """
        return c_void_p(lib.zlist_head(self._as_parameter_))

    def tail(self):
        """
        Return last item in the list, or null, leaves the cursor
        """
        return c_void_p(lib.zlist_tail(self._as_parameter_))

    def item(self):
        """
        Return the current item of list. If the list is empty, returns NULL.
Leaves cursor pointing at the current item, or NULL if the list is empty.
        """
        return c_void_p(lib.zlist_item(self._as_parameter_))

    def append(self, item):
        """
        Append an item to the end of the list, return 0 if OK or -1 if this
failed for some reason (invalid input). Note that if a duplicator has
been set, this method will also duplicate the item.
        """
        return lib.zlist_append(self._as_parameter_, item)

    def push(self, item):
        """
        Push an item to the start of the list, return 0 if OK or -1 if this
failed for some reason (invalid input). Note that if a duplicator has
been set, this method will also duplicate the item.
        """
        return lib.zlist_push(self._as_parameter_, item)

    def pop(self):
        """
        Pop the item off the start of the list, if any
        """
        return c_void_p(lib.zlist_pop(self._as_parameter_))

    def exists(self, item):
        """
        Checks if an item already is present. Uses compare method to determine if
items are equal. If the compare method is NULL the check will only compare
pointers. Returns true if item is present else false.
        """
        return lib.zlist_exists(self._as_parameter_, item)

    def remove(self, item):
        """
        Remove the specified item from the list if present
        """
        return lib.zlist_remove(self._as_parameter_, item)

    def dup(self):
        """
        Make a copy of list. If the list has autofree set, the copied list will
duplicate all items, which must be strings. Otherwise, the list will hold
pointers back to the items in the original list. If list is null, returns
NULL.
        """
        return Zlist(lib.zlist_dup(self._as_parameter_), True)

    def purge(self):
        """
        Purge all items from list
        """
        return lib.zlist_purge(self._as_parameter_)

    def size(self):
        """
        Return number of items in the list
        """
        return lib.zlist_size(self._as_parameter_)

    def sort(self, compare):
        """
        Sort the list. If the compare function is null, sorts the list by
ascending key value using a straight ASCII comparison. If you specify
a compare function, this decides how items are sorted. The sort is not
stable, so may reorder items with the same keys. The algorithm used is
combsort, a compromise between performance and simplicity.
        """
        return lib.zlist_sort(self._as_parameter_, compare)

    def autofree(self):
        """
        Set list for automatic item destruction; item values MUST be strings.
By default a list item refers to a value held elsewhere. When you set
this, each time you append or push a list item, zlist will take a copy
of the string value. Then, when you destroy the list, it will free all
item values automatically. If you use any other technique to allocate
list values, you must free them explicitly before destroying the list.
The usual technique is to pop list items and destroy them, until the
list is empty.
        """
        return lib.zlist_autofree(self._as_parameter_)

    def comparefn(self, fn):
        """
        Sets a compare function for this list. The function compares two items.
It returns an integer less than, equal to, or greater than zero if the
first item is found, respectively, to be less than, to match, or be
greater than the second item.
This function is used for sorting, removal and exists checking.
        """
        return lib.zlist_comparefn(self._as_parameter_, fn)

    def freefn(self, item, fn, at_tail):
        """
        Set a free function for the specified list item. When the item is
destroyed, the free function, if any, is called on that item.
Use this when list items are dynamically allocated, to ensure that
you don't have memory leaks. You can pass 'free' or NULL as a free_fn.
Returns the item, or NULL if there is no such item.
        """
        return c_void_p(lib.zlist_freefn(self._as_parameter_, item, fn, at_tail))

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zlist_test(verbose)


# zlistx
zlistx_destructor_fn = CFUNCTYPE(None, POINTER(c_void_p))
zlistx_duplicator_fn = CFUNCTYPE(c_void_p, c_void_p)
zlistx_comparator_fn = CFUNCTYPE(c_int, c_void_p, c_void_p)
lib.zlistx_new.restype = zlistx_p
lib.zlistx_new.argtypes = []
lib.zlistx_destroy.restype = None
lib.zlistx_destroy.argtypes = [POINTER(zlistx_p)]
lib.zlistx_unpack.restype = zlistx_p
lib.zlistx_unpack.argtypes = [zframe_p]
lib.zlistx_add_start.restype = c_void_p
lib.zlistx_add_start.argtypes = [zlistx_p, c_void_p]
lib.zlistx_add_end.restype = c_void_p
lib.zlistx_add_end.argtypes = [zlistx_p, c_void_p]
lib.zlistx_size.restype = c_size_t
lib.zlistx_size.argtypes = [zlistx_p]
lib.zlistx_head.restype = c_void_p
lib.zlistx_head.argtypes = [zlistx_p]
lib.zlistx_tail.restype = c_void_p
lib.zlistx_tail.argtypes = [zlistx_p]
lib.zlistx_first.restype = c_void_p
lib.zlistx_first.argtypes = [zlistx_p]
lib.zlistx_next.restype = c_void_p
lib.zlistx_next.argtypes = [zlistx_p]
lib.zlistx_prev.restype = c_void_p
lib.zlistx_prev.argtypes = [zlistx_p]
lib.zlistx_last.restype = c_void_p
lib.zlistx_last.argtypes = [zlistx_p]
lib.zlistx_item.restype = c_void_p
lib.zlistx_item.argtypes = [zlistx_p]
lib.zlistx_cursor.restype = c_void_p
lib.zlistx_cursor.argtypes = [zlistx_p]
lib.zlistx_handle_item.restype = c_void_p
lib.zlistx_handle_item.argtypes = [c_void_p]
lib.zlistx_find.restype = c_void_p
lib.zlistx_find.argtypes = [zlistx_p, c_void_p]
lib.zlistx_detach.restype = c_void_p
lib.zlistx_detach.argtypes = [zlistx_p, c_void_p]
lib.zlistx_detach_cur.restype = c_void_p
lib.zlistx_detach_cur.argtypes = [zlistx_p]
lib.zlistx_delete.restype = c_int
lib.zlistx_delete.argtypes = [zlistx_p, c_void_p]
lib.zlistx_move_start.restype = None
lib.zlistx_move_start.argtypes = [zlistx_p, c_void_p]
lib.zlistx_move_end.restype = None
lib.zlistx_move_end.argtypes = [zlistx_p, c_void_p]
lib.zlistx_purge.restype = None
lib.zlistx_purge.argtypes = [zlistx_p]
lib.zlistx_sort.restype = None
lib.zlistx_sort.argtypes = [zlistx_p]
lib.zlistx_insert.restype = c_void_p
lib.zlistx_insert.argtypes = [zlistx_p, c_void_p, c_bool]
lib.zlistx_reorder.restype = None
lib.zlistx_reorder.argtypes = [zlistx_p, c_void_p, c_bool]
lib.zlistx_dup.restype = zlistx_p
lib.zlistx_dup.argtypes = [zlistx_p]
lib.zlistx_set_destructor.restype = None
lib.zlistx_set_destructor.argtypes = [zlistx_p, zlistx_destructor_fn]
lib.zlistx_set_duplicator.restype = None
lib.zlistx_set_duplicator.argtypes = [zlistx_p, zlistx_duplicator_fn]
lib.zlistx_set_comparator.restype = None
lib.zlistx_set_comparator.argtypes = [zlistx_p, zlistx_comparator_fn]
lib.zlistx_pack.restype = zframe_p
lib.zlistx_pack.argtypes = [zlistx_p]
lib.zlistx_test.restype = None
lib.zlistx_test.argtypes = [c_bool]

class Zlistx(object):
    """
    extended generic list container
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new, empty list.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zlistx_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zlistx_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zlistx_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a list. If an item destructor was specified, all items in the
list are automatically destroyed as well.
        """
        if self.allow_destruct:
            lib.zlistx_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def unpack(frame):
        """
        Unpack binary frame into a new list. Packed data must follow format
defined by zlistx_pack. List is set to autofree. An empty frame
unpacks to an empty list.
        """
        return Zlistx(lib.zlistx_unpack(frame), True)

    def add_start(self, item):
        """
        Add an item to the head of the list. Calls the item duplicator, if any,
on the item. Resets cursor to list head. Returns an item handle on
success.
        """
        return c_void_p(lib.zlistx_add_start(self._as_parameter_, item))

    def add_end(self, item):
        """
        Add an item to the tail of the list. Calls the item duplicator, if any,
on the item. Resets cursor to list head. Returns an item handle on
success.
        """
        return c_void_p(lib.zlistx_add_end(self._as_parameter_, item))

    def size(self):
        """
        Return the number of items in the list
        """
        return lib.zlistx_size(self._as_parameter_)

    def head(self):
        """
        Return first item in the list, or null, leaves the cursor
        """
        return c_void_p(lib.zlistx_head(self._as_parameter_))

    def tail(self):
        """
        Return last item in the list, or null, leaves the cursor
        """
        return c_void_p(lib.zlistx_tail(self._as_parameter_))

    def first(self):
        """
        Return the item at the head of list. If the list is empty, returns NULL.
Leaves cursor pointing at the head item, or NULL if the list is empty.
        """
        return c_void_p(lib.zlistx_first(self._as_parameter_))

    def next(self):
        """
        Return the next item. At the end of the list (or in an empty list),
returns NULL. Use repeated zlistx_next () calls to work through the list
from zlistx_first (). First time, acts as zlistx_first().
        """
        return c_void_p(lib.zlistx_next(self._as_parameter_))

    def prev(self):
        """
        Return the previous item. At the start of the list (or in an empty list),
returns NULL. Use repeated zlistx_prev () calls to work through the list
backwards from zlistx_last (). First time, acts as zlistx_last().
        """
        return c_void_p(lib.zlistx_prev(self._as_parameter_))

    def last(self):
        """
        Return the item at the tail of list. If the list is empty, returns NULL.
Leaves cursor pointing at the tail item, or NULL if the list is empty.
        """
        return c_void_p(lib.zlistx_last(self._as_parameter_))

    def item(self):
        """
        Returns the value of the item at the cursor, or NULL if the cursor is
not pointing to an item.
        """
        return c_void_p(lib.zlistx_item(self._as_parameter_))

    def cursor(self):
        """
        Returns the handle of the item at the cursor, or NULL if the cursor is
not pointing to an item.
        """
        return c_void_p(lib.zlistx_cursor(self._as_parameter_))

    @staticmethod
    def handle_item(handle):
        """
        Returns the item associated with the given list handle, or NULL if passed
in handle is NULL. Asserts that the passed in handle points to a list element.
        """
        return c_void_p(lib.zlistx_handle_item(handle))

    def find(self, item):
        """
        Find an item in the list, searching from the start. Uses the item
comparator, if any, else compares item values directly. Returns the
item handle found, or NULL. Sets the cursor to the found item, if any.
        """
        return c_void_p(lib.zlistx_find(self._as_parameter_, item))

    def detach(self, handle):
        """
        Detach an item from the list, using its handle. The item is not modified,
and the caller is responsible for destroying it if necessary. If handle is
null, detaches the first item on the list. Returns item that was detached,
or null if none was. If cursor was at item, moves cursor to previous item,
so you can detach items while iterating forwards through a list.
        """
        return c_void_p(lib.zlistx_detach(self._as_parameter_, handle))

    def detach_cur(self):
        """
        Detach item at the cursor, if any, from the list. The item is not modified,
and the caller is responsible for destroying it as necessary. Returns item
that was detached, or null if none was. Moves cursor to previous item, so
you can detach items while iterating forwards through a list.
        """
        return c_void_p(lib.zlistx_detach_cur(self._as_parameter_))

    def delete(self, handle):
        """
        Delete an item, using its handle. Calls the item destructor if any is
set. If handle is null, deletes the first item on the list. Returns 0
if an item was deleted, -1 if not. If cursor was at item, moves cursor
to previous item, so you can delete items while iterating forwards
through a list.
        """
        return lib.zlistx_delete(self._as_parameter_, handle)

    def move_start(self, handle):
        """
        Move an item to the start of the list, via its handle.
        """
        return lib.zlistx_move_start(self._as_parameter_, handle)

    def move_end(self, handle):
        """
        Move an item to the end of the list, via its handle.
        """
        return lib.zlistx_move_end(self._as_parameter_, handle)

    def purge(self):
        """
        Remove all items from the list, and destroy them if the item destructor
is set.
        """
        return lib.zlistx_purge(self._as_parameter_)

    def sort(self):
        """
        Sort the list. If an item comparator was set, calls that to compare
items, otherwise compares on item value. The sort is not stable, so may
reorder equal items.
        """
        return lib.zlistx_sort(self._as_parameter_)

    def insert(self, item, low_value):
        """
        Create a new node and insert it into a sorted list. Calls the item
duplicator, if any, on the item. If low_value is true, starts searching
from the start of the list, otherwise searches from the end. Use the item
comparator, if any, to find where to place the new node. Returns a handle
to the new node. Resets the cursor to the list head.
        """
        return c_void_p(lib.zlistx_insert(self._as_parameter_, item, low_value))

    def reorder(self, handle, low_value):
        """
        Move an item, specified by handle, into position in a sorted list. Uses
the item comparator, if any, to determine the new location. If low_value
is true, starts searching from the start of the list, otherwise searches
from the end.
        """
        return lib.zlistx_reorder(self._as_parameter_, handle, low_value)

    def dup(self):
        """
        Make a copy of the list; items are duplicated if you set a duplicator
for the list, otherwise not. Copying a null reference returns a null
reference.
        """
        return Zlistx(lib.zlistx_dup(self._as_parameter_), False)

    def set_destructor(self, destructor):
        """
        Set a user-defined deallocator for list items; by default items are not
freed when the list is destroyed.
        """
        return lib.zlistx_set_destructor(self._as_parameter_, destructor)

    def set_duplicator(self, duplicator):
        """
        Set a user-defined duplicator for list items; by default items are not
copied when the list is duplicated.
        """
        return lib.zlistx_set_duplicator(self._as_parameter_, duplicator)

    def set_comparator(self, comparator):
        """
        Set a user-defined comparator for zlistx_find and zlistx_sort; the method
must return -1, 0, or 1 depending on whether item1 is less than, equal to,
or greater than, item2.
        """
        return lib.zlistx_set_comparator(self._as_parameter_, comparator)

    def pack(self):
        """
        Serialize list to a binary frame that can be sent in a message.
The packed format is compatible with the 'strings' type implemented by zproto:

   ; A list of strings
   list            = list-count *longstr
   list-count      = number-4

   ; Strings are always length + text contents
   longstr         = number-4 *VCHAR

   ; Numbers are unsigned integers in network byte order
   number-4        = 4OCTET
        """
        return Zframe(lib.zlistx_pack(self._as_parameter_), True)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zlistx_test(verbose)


# zloop
zloop_reader_fn = CFUNCTYPE(c_int, zloop_p, zsock_p, c_void_p)
zloop_fn = CFUNCTYPE(c_int, zloop_p, zmq_pollitem_p, c_void_p)
zloop_timer_fn = CFUNCTYPE(c_int, zloop_p, c_int, c_void_p)
lib.zloop_new.restype = zloop_p
lib.zloop_new.argtypes = []
lib.zloop_destroy.restype = None
lib.zloop_destroy.argtypes = [POINTER(zloop_p)]
lib.zloop_reader.restype = c_int
lib.zloop_reader.argtypes = [zloop_p, zsock_p, zloop_reader_fn, c_void_p]
lib.zloop_reader_end.restype = None
lib.zloop_reader_end.argtypes = [zloop_p, zsock_p]
lib.zloop_reader_set_tolerant.restype = None
lib.zloop_reader_set_tolerant.argtypes = [zloop_p, zsock_p]
lib.zloop_poller.restype = c_int
lib.zloop_poller.argtypes = [zloop_p, zmq_pollitem_p, zloop_fn, c_void_p]
lib.zloop_poller_end.restype = None
lib.zloop_poller_end.argtypes = [zloop_p, zmq_pollitem_p]
lib.zloop_poller_set_tolerant.restype = None
lib.zloop_poller_set_tolerant.argtypes = [zloop_p, zmq_pollitem_p]
lib.zloop_timer.restype = c_int
lib.zloop_timer.argtypes = [zloop_p, c_size_t, c_size_t, zloop_timer_fn, c_void_p]
lib.zloop_timer_end.restype = c_int
lib.zloop_timer_end.argtypes = [zloop_p, c_int]
lib.zloop_ticket.restype = c_void_p
lib.zloop_ticket.argtypes = [zloop_p, zloop_timer_fn, c_void_p]
lib.zloop_ticket_reset.restype = None
lib.zloop_ticket_reset.argtypes = [zloop_p, c_void_p]
lib.zloop_ticket_delete.restype = None
lib.zloop_ticket_delete.argtypes = [zloop_p, c_void_p]
lib.zloop_set_ticket_delay.restype = None
lib.zloop_set_ticket_delay.argtypes = [zloop_p, c_size_t]
lib.zloop_set_max_timers.restype = None
lib.zloop_set_max_timers.argtypes = [zloop_p, c_size_t]
lib.zloop_set_verbose.restype = None
lib.zloop_set_verbose.argtypes = [zloop_p, c_bool]
lib.zloop_set_nonstop.restype = None
lib.zloop_set_nonstop.argtypes = [zloop_p, c_bool]
lib.zloop_start.restype = c_int
lib.zloop_start.argtypes = [zloop_p]
lib.zloop_test.restype = None
lib.zloop_test.argtypes = [c_bool]

class Zloop(object):
    """
    event-driven reactor
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new zloop reactor
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zloop_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zloop_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zloop_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a reactor
        """
        if self.allow_destruct:
            lib.zloop_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def reader(self, sock, handler, arg):
        """
        Register socket reader with the reactor. When the reader has messages,
the reactor will call the handler, passing the arg. Returns 0 if OK, -1
if there was an error. If you register the same socket more than once,
each instance will invoke its corresponding handler.
        """
        return lib.zloop_reader(self._as_parameter_, sock, handler, arg)

    def reader_end(self, sock):
        """
        Cancel a socket reader from the reactor. If multiple readers exist for
same socket, cancels ALL of them.
        """
        return lib.zloop_reader_end(self._as_parameter_, sock)

    def reader_set_tolerant(self, sock):
        """
        Configure a registered reader to ignore errors. If you do not set this,
then readers that have errors are removed from the reactor silently.
        """
        return lib.zloop_reader_set_tolerant(self._as_parameter_, sock)

    def poller(self, item, handler, arg):
        """
        Register low-level libzmq pollitem with the reactor. When the pollitem
is ready, will call the handler, passing the arg. Returns 0 if OK, -1
if there was an error. If you register the pollitem more than once, each
instance will invoke its corresponding handler. A pollitem with
socket=NULL and fd=0 means 'poll on FD zero'.
        """
        return lib.zloop_poller(self._as_parameter_, item, handler, arg)

    def poller_end(self, item):
        """
        Cancel a pollitem from the reactor, specified by socket or FD. If both
are specified, uses only socket. If multiple poll items exist for same
socket/FD, cancels ALL of them.
        """
        return lib.zloop_poller_end(self._as_parameter_, item)

    def poller_set_tolerant(self, item):
        """
        Configure a registered poller to ignore errors. If you do not set this,
then poller that have errors are removed from the reactor silently.
        """
        return lib.zloop_poller_set_tolerant(self._as_parameter_, item)

    def timer(self, delay, times, handler, arg):
        """
        Register a timer that expires after some delay and repeats some number of
times. At each expiry, will call the handler, passing the arg. To run a
timer forever, use 0 times. Returns a timer_id that is used to cancel the
timer in the future. Returns -1 if there was an error.
        """
        return lib.zloop_timer(self._as_parameter_, delay, times, handler, arg)

    def timer_end(self, timer_id):
        """
        Cancel a specific timer identified by a specific timer_id (as returned by
zloop_timer).
        """
        return lib.zloop_timer_end(self._as_parameter_, timer_id)

    def ticket(self, handler, arg):
        """
        Register a ticket timer. Ticket timers are very fast in the case where
you use a lot of timers (thousands), and frequently remove and add them.
The main use case is expiry timers for servers that handle many clients,
and which reset the expiry timer for each message received from a client.
Whereas normal timers perform poorly as the number of clients grows, the
cost of ticket timers is constant, no matter the number of clients. You
must set the ticket delay using zloop_set_ticket_delay before creating a
ticket. Returns a handle to the timer that you should use in
zloop_ticket_reset and zloop_ticket_delete.
        """
        return c_void_p(lib.zloop_ticket(self._as_parameter_, handler, arg))

    def ticket_reset(self, handle):
        """
        Reset a ticket timer, which moves it to the end of the ticket list and
resets its execution time. This is a very fast operation.
        """
        return lib.zloop_ticket_reset(self._as_parameter_, handle)

    def ticket_delete(self, handle):
        """
        Delete a ticket timer. We do not actually delete the ticket here, as
other code may still refer to the ticket. We mark as deleted, and remove
later and safely.
        """
        return lib.zloop_ticket_delete(self._as_parameter_, handle)

    def set_ticket_delay(self, ticket_delay):
        """
        Set the ticket delay, which applies to all tickets. If you lower the
delay and there are already tickets created, the results are undefined.
        """
        return lib.zloop_set_ticket_delay(self._as_parameter_, ticket_delay)

    def set_max_timers(self, max_timers):
        """
        Set hard limit on number of timers allowed. Setting more than a small
number of timers (10-100) can have a dramatic impact on the performance
of the reactor. For high-volume cases, use ticket timers. If the hard
limit is reached, the reactor stops creating new timers and logs an
error.
        """
        return lib.zloop_set_max_timers(self._as_parameter_, max_timers)

    def set_verbose(self, verbose):
        """
        Set verbose tracing of reactor on/off. The default verbose setting is
off (false).
        """
        return lib.zloop_set_verbose(self._as_parameter_, verbose)

    def set_nonstop(self, nonstop):
        """
        By default the reactor stops if the process receives a SIGINT or SIGTERM
signal. This makes it impossible to shut-down message based architectures
like zactors. This method lets you switch off break handling. The default
nonstop setting is off (false).
        """
        return lib.zloop_set_nonstop(self._as_parameter_, nonstop)

    def start(self):
        """
        Start the reactor. Takes control of the thread and returns when the 0MQ
context is terminated or the process is interrupted, or any event handler
returns -1. Event handlers may register new sockets and timers, and
cancel sockets. Returns 0 if interrupted, -1 if canceled by a handler.
        """
        return lib.zloop_start(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zloop_test(verbose)


# zmsg
lib.zmsg_new.restype = zmsg_p
lib.zmsg_new.argtypes = []
lib.zmsg_destroy.restype = None
lib.zmsg_destroy.argtypes = [POINTER(zmsg_p)]
lib.zmsg_recv.restype = zmsg_p
lib.zmsg_recv.argtypes = [c_void_p]
lib.zmsg_load.restype = zmsg_p
lib.zmsg_load.argtypes = [FILE_p]
lib.zmsg_decode.restype = zmsg_p
lib.zmsg_decode.argtypes = [zframe_p]
lib.zmsg_new_signal.restype = zmsg_p
lib.zmsg_new_signal.argtypes = [c_ubyte]
lib.zmsg_send.restype = c_int
lib.zmsg_send.argtypes = [POINTER(zmsg_p), c_void_p]
lib.zmsg_sendm.restype = c_int
lib.zmsg_sendm.argtypes = [POINTER(zmsg_p), c_void_p]
lib.zmsg_size.restype = c_size_t
lib.zmsg_size.argtypes = [zmsg_p]
lib.zmsg_content_size.restype = c_size_t
lib.zmsg_content_size.argtypes = [zmsg_p]
lib.zmsg_routing_id.restype = c_int
lib.zmsg_routing_id.argtypes = [zmsg_p]
lib.zmsg_set_routing_id.restype = None
lib.zmsg_set_routing_id.argtypes = [zmsg_p, c_int]
lib.zmsg_prepend.restype = c_int
lib.zmsg_prepend.argtypes = [zmsg_p, POINTER(zframe_p)]
lib.zmsg_append.restype = c_int
lib.zmsg_append.argtypes = [zmsg_p, POINTER(zframe_p)]
lib.zmsg_pop.restype = zframe_p
lib.zmsg_pop.argtypes = [zmsg_p]
lib.zmsg_pushmem.restype = c_int
lib.zmsg_pushmem.argtypes = [zmsg_p, c_void_p, c_size_t]
lib.zmsg_addmem.restype = c_int
lib.zmsg_addmem.argtypes = [zmsg_p, c_void_p, c_size_t]
lib.zmsg_pushstr.restype = c_int
lib.zmsg_pushstr.argtypes = [zmsg_p, c_char_p]
lib.zmsg_addstr.restype = c_int
lib.zmsg_addstr.argtypes = [zmsg_p, c_char_p]
lib.zmsg_pushstrf.restype = c_int
lib.zmsg_pushstrf.argtypes = [zmsg_p, c_char_p]
lib.zmsg_addstrf.restype = c_int
lib.zmsg_addstrf.argtypes = [zmsg_p, c_char_p]
lib.zmsg_popstr.restype = POINTER(c_char)
lib.zmsg_popstr.argtypes = [zmsg_p]
lib.zmsg_addmsg.restype = c_int
lib.zmsg_addmsg.argtypes = [zmsg_p, POINTER(zmsg_p)]
lib.zmsg_popmsg.restype = zmsg_p
lib.zmsg_popmsg.argtypes = [zmsg_p]
lib.zmsg_remove.restype = None
lib.zmsg_remove.argtypes = [zmsg_p, zframe_p]
lib.zmsg_first.restype = zframe_p
lib.zmsg_first.argtypes = [zmsg_p]
lib.zmsg_next.restype = zframe_p
lib.zmsg_next.argtypes = [zmsg_p]
lib.zmsg_last.restype = zframe_p
lib.zmsg_last.argtypes = [zmsg_p]
lib.zmsg_save.restype = c_int
lib.zmsg_save.argtypes = [zmsg_p, FILE_p]
lib.zmsg_encode.restype = zframe_p
lib.zmsg_encode.argtypes = [zmsg_p]
lib.zmsg_dup.restype = zmsg_p
lib.zmsg_dup.argtypes = [zmsg_p]
lib.zmsg_print.restype = None
lib.zmsg_print.argtypes = [zmsg_p]
lib.zmsg_print_n.restype = None
lib.zmsg_print_n.argtypes = [zmsg_p, c_size_t]
lib.zmsg_eq.restype = c_bool
lib.zmsg_eq.argtypes = [zmsg_p, zmsg_p]
lib.zmsg_signal.restype = c_int
lib.zmsg_signal.argtypes = [zmsg_p]
lib.zmsg_is.restype = c_bool
lib.zmsg_is.argtypes = [c_void_p]
lib.zmsg_test.restype = None
lib.zmsg_test.argtypes = [c_bool]

class Zmsg(object):
    """
    working with multipart messages
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new empty message object
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zmsg_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zmsg_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zmsg_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a message object and all frames it contains
        """
        if self.allow_destruct:
            lib.zmsg_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def recv(source):
        """
        Receive message from socket, returns zmsg_t object or NULL if the recv
was interrupted. Does a blocking recv. If you want to not block then use
the zloop class or zmsg_recv_nowait or zmq_poll to check for socket input
before receiving.
        """
        return Zmsg(lib.zmsg_recv(source), True)

    @staticmethod
    def load(file):
        """
        Load/append an open file into new message, return the message.
Returns NULL if the message could not be loaded.
        """
        return Zmsg(lib.zmsg_load(coerce_py_file(file)), True)

    @staticmethod
    def decode(frame):
        """
        Decodes a serialized message frame created by zmsg_encode () and returns
a new zmsg_t object. Returns NULL if the frame was badly formatted or
there was insufficient memory to work.
        """
        return Zmsg(lib.zmsg_decode(frame), True)

    @staticmethod
    def new_signal(status):
        """
        Generate a signal message encoding the given status. A signal is a short
message carrying a 1-byte success/failure code (by convention, 0 means
OK). Signals are encoded to be distinguishable from "normal" messages.
        """
        return Zmsg(lib.zmsg_new_signal(status), True)

    @staticmethod
    def send(self_p, dest):
        """
        Send message to destination socket, and destroy the message after sending
it successfully. If the message has no frames, sends nothing but destroys
the message anyhow. Nullifies the caller's reference to the message (as
it is a destructor).
        """
        return lib.zmsg_send(byref(zmsg_p.from_param(self_p)), dest)

    @staticmethod
    def sendm(self_p, dest):
        """
        Send message to destination socket as part of a multipart sequence, and
destroy the message after sending it successfully. Note that after a
zmsg_sendm, you must call zmsg_send or another method that sends a final
message part. If the message has no frames, sends nothing but destroys
the message anyhow. Nullifies the caller's reference to the message (as
it is a destructor).
        """
        return lib.zmsg_sendm(byref(zmsg_p.from_param(self_p)), dest)

    def size(self):
        """
        Return size of message, i.e. number of frames (0 or more).
        """
        return lib.zmsg_size(self._as_parameter_)

    def content_size(self):
        """
        Return total size of all frames in message.
        """
        return lib.zmsg_content_size(self._as_parameter_)

    def routing_id(self):
        """
        Return message routing ID, if the message came from a ZMQ_SERVER socket.
Else returns zero.
        """
        return lib.zmsg_routing_id(self._as_parameter_)

    def set_routing_id(self, routing_id):
        """
        Set routing ID on message. This is used if/when the message is sent to a
ZMQ_SERVER socket.
        """
        return lib.zmsg_set_routing_id(self._as_parameter_, routing_id)

    def prepend(self, frame_p):
        """
        Push frame to the front of the message, i.e. before all other frames.
Message takes ownership of frame, will destroy it when message is sent.
Returns 0 on success, -1 on error. Deprecates zmsg_push, which did not
nullify the caller's frame reference.
        """
        return lib.zmsg_prepend(self._as_parameter_, byref(zframe_p.from_param(frame_p)))

    def append(self, frame_p):
        """
        Add frame to the end of the message, i.e. after all other frames.
Message takes ownership of frame, will destroy it when message is sent.
Returns 0 on success. Deprecates zmsg_add, which did not nullify the
caller's frame reference.
        """
        return lib.zmsg_append(self._as_parameter_, byref(zframe_p.from_param(frame_p)))

    def pop(self):
        """
        Remove first frame from message, if any. Returns frame, or NULL.
        """
        return Zframe(lib.zmsg_pop(self._as_parameter_), True)

    def pushmem(self, data, size):
        """
        Push block of memory to front of message, as a new frame.
Returns 0 on success, -1 on error.
        """
        return lib.zmsg_pushmem(self._as_parameter_, data, size)

    def addmem(self, data, size):
        """
        Add block of memory to the end of the message, as a new frame.
Returns 0 on success, -1 on error.
        """
        return lib.zmsg_addmem(self._as_parameter_, data, size)

    def pushstr(self, string):
        """
        Push string as new frame to front of message.
Returns 0 on success, -1 on error.
        """
        return lib.zmsg_pushstr(self._as_parameter_, string)

    def addstr(self, string):
        """
        Push string as new frame to end of message.
Returns 0 on success, -1 on error.
        """
        return lib.zmsg_addstr(self._as_parameter_, string)

    def pushstrf(self, format, *args):
        """
        Push formatted string as new frame to front of message.
Returns 0 on success, -1 on error.
        """
        return lib.zmsg_pushstrf(self._as_parameter_, format, *args)

    def addstrf(self, format, *args):
        """
        Push formatted string as new frame to end of message.
Returns 0 on success, -1 on error.
        """
        return lib.zmsg_addstrf(self._as_parameter_, format, *args)

    def popstr(self):
        """
        Pop frame off front of message, return as fresh string. If there were
no more frames in the message, returns NULL.
        """
        return return_fresh_string(lib.zmsg_popstr(self._as_parameter_))

    def addmsg(self, msg_p):
        """
        Push encoded message as a new frame. Message takes ownership of
submessage, so the original is destroyed in this call. Returns 0 on
success, -1 on error.
        """
        return lib.zmsg_addmsg(self._as_parameter_, byref(zmsg_p.from_param(msg_p)))

    def popmsg(self):
        """
        Remove first submessage from message, if any. Returns zmsg_t, or NULL if
decoding was not successful.
        """
        return Zmsg(lib.zmsg_popmsg(self._as_parameter_), True)

    def remove(self, frame):
        """
        Remove specified frame from list, if present. Does not destroy frame.
        """
        return lib.zmsg_remove(self._as_parameter_, frame)

    def first(self):
        """
        Set cursor to first frame in message. Returns frame, or NULL, if the
message is empty. Use this to navigate the frames as a list.
        """
        return Zframe(lib.zmsg_first(self._as_parameter_), False)

    def next(self):
        """
        Return the next frame. If there are no more frames, returns NULL. To move
to the first frame call zmsg_first(). Advances the cursor.
        """
        return Zframe(lib.zmsg_next(self._as_parameter_), False)

    def last(self):
        """
        Return the last frame. If there are no frames, returns NULL.
        """
        return Zframe(lib.zmsg_last(self._as_parameter_), False)

    def save(self, file):
        """
        Save message to an open file, return 0 if OK, else -1. The message is
saved as a series of frames, each with length and data. Note that the
file is NOT guaranteed to be portable between operating systems, not
versions of CZMQ. The file format is at present undocumented and liable
to arbitrary change.
        """
        return lib.zmsg_save(self._as_parameter_, coerce_py_file(file))

    def encode(self):
        """
        Serialize multipart message to a single message frame. Use this method
to send structured messages across transports that do not support
multipart data. Allocates and returns a new frame containing the
serialized message. To decode a serialized message frame, use
zmsg_decode ().
        """
        return Zframe(lib.zmsg_encode(self._as_parameter_), True)

    def dup(self):
        """
        Create copy of message, as new message object. Returns a fresh zmsg_t
object. If message is null, or memory was exhausted, returns null.
        """
        return Zmsg(lib.zmsg_dup(self._as_parameter_), True)

    def print(self):
        """
        Send message to zsys log sink (may be stdout, or system facility as
configured by zsys_set_logstream).
Long messages are truncated.
        """
        return lib.zmsg_print(self._as_parameter_)

    def print_n(self, size):
        """
        Send message to zsys log sink (may be stdout, or system facility as
configured by zsys_set_logstream).
Message length is specified; no truncation unless length is zero.
Backwards compatible with zframe_print when length is zero.
        """
        return lib.zmsg_print_n(self._as_parameter_, size)

    def eq(self, other):
        """
        Return true if the two messages have the same number of frames and each
frame in the first message is identical to the corresponding frame in the
other message. As with zframe_eq, return false if either message is NULL.
        """
        return lib.zmsg_eq(self._as_parameter_, other)

    def signal(self):
        """
        Return signal value, 0 or greater, if message is a signal, -1 if not.
        """
        return lib.zmsg_signal(self._as_parameter_)

    @staticmethod
    def is_(self):
        """
        Probe the supplied object, and report if it looks like a zmsg_t.
        """
        return lib.zmsg_is(self)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zmsg_test(verbose)


# zpoller
lib.zpoller_new.restype = zpoller_p
lib.zpoller_new.argtypes = [c_void_p]
lib.zpoller_destroy.restype = None
lib.zpoller_destroy.argtypes = [POINTER(zpoller_p)]
lib.zpoller_add.restype = c_int
lib.zpoller_add.argtypes = [zpoller_p, c_void_p]
lib.zpoller_remove.restype = c_int
lib.zpoller_remove.argtypes = [zpoller_p, c_void_p]
lib.zpoller_set_nonstop.restype = None
lib.zpoller_set_nonstop.argtypes = [zpoller_p, c_bool]
lib.zpoller_wait.restype = c_void_p
lib.zpoller_wait.argtypes = [zpoller_p, c_int]
lib.zpoller_expired.restype = c_bool
lib.zpoller_expired.argtypes = [zpoller_p]
lib.zpoller_terminated.restype = c_bool
lib.zpoller_terminated.argtypes = [zpoller_p]
lib.zpoller_test.restype = None
lib.zpoller_test.argtypes = [c_bool]

class Zpoller(object):
    """
    event-driven reactor
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create new poller, specifying zero or more readers. The list of
readers ends in a NULL. Each reader can be a zsock_t instance, a
zactor_t instance, a libzmq socket (void *), or a file handle.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zpoller_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zpoller_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) >= 1)
            self._as_parameter_ = lib.zpoller_new(args[0], *args[1:]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a poller
        """
        if self.allow_destruct:
            lib.zpoller_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def add(self, reader):
        """
        Add a reader to be polled. Returns 0 if OK, -1 on failure. The reader may
be a libzmq void * socket, a zsock_t instance, a zactor_t instance or a
file handle.
        """
        return lib.zpoller_add(self._as_parameter_, reader)

    def remove(self, reader):
        """
        Remove a reader from the poller; returns 0 if OK, -1 on failure. The reader
must have been passed during construction, or in an zpoller_add () call.
        """
        return lib.zpoller_remove(self._as_parameter_, reader)

    def set_nonstop(self, nonstop):
        """
        By default the poller stops if the process receives a SIGINT or SIGTERM
signal. This makes it impossible to shut-down message based architectures
like zactors. This method lets you switch off break handling. The default
nonstop setting is off (false).
        """
        return lib.zpoller_set_nonstop(self._as_parameter_, nonstop)

    def wait(self, timeout):
        """
        Poll the registered readers for I/O, return first reader that has input.
The reader will be a libzmq void * socket, a zsock_t, a zactor_t
instance or a file handle as specified in zpoller_new/zpoller_add. The
timeout should be zero or greater, or -1 to wait indefinitely. Socket
priority is defined by their order in the poll list. If you need a
balanced poll, use the low level zmq_poll method directly. If the poll
call was interrupted (SIGINT), or the ZMQ context was destroyed, or the
timeout expired, returns NULL. You can test the actual exit condition by
calling zpoller_expired () and zpoller_terminated (). The timeout is in
msec.
        """
        return c_void_p(lib.zpoller_wait(self._as_parameter_, timeout))

    def expired(self):
        """
        Return true if the last zpoller_wait () call ended because the timeout
expired, without any error.
        """
        return lib.zpoller_expired(self._as_parameter_)

    def terminated(self):
        """
        Return true if the last zpoller_wait () call ended because the process
was interrupted, or the parent context was destroyed.
        """
        return lib.zpoller_terminated(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zpoller_test(verbose)


# zproc
lib.zproc_new.restype = zproc_p
lib.zproc_new.argtypes = []
lib.zproc_destroy.restype = None
lib.zproc_destroy.argtypes = [POINTER(zproc_p)]
lib.zproc_args.restype = zlist_p
lib.zproc_args.argtypes = [zproc_p]
lib.zproc_set_args.restype = None
lib.zproc_set_args.argtypes = [zproc_p, POINTER(zlist_p)]
lib.zproc_set_argsx.restype = None
lib.zproc_set_argsx.argtypes = [zproc_p, c_char_p]
lib.zproc_set_env.restype = None
lib.zproc_set_env.argtypes = [zproc_p, POINTER(zhash_p)]
lib.zproc_set_stdin.restype = None
lib.zproc_set_stdin.argtypes = [zproc_p, c_void_p]
lib.zproc_set_stdout.restype = None
lib.zproc_set_stdout.argtypes = [zproc_p, c_void_p]
lib.zproc_set_stderr.restype = None
lib.zproc_set_stderr.argtypes = [zproc_p, c_void_p]
lib.zproc_stdin.restype = c_void_p
lib.zproc_stdin.argtypes = [zproc_p]
lib.zproc_stdout.restype = c_void_p
lib.zproc_stdout.argtypes = [zproc_p]
lib.zproc_stderr.restype = c_void_p
lib.zproc_stderr.argtypes = [zproc_p]
lib.zproc_run.restype = c_int
lib.zproc_run.argtypes = [zproc_p]
lib.zproc_returncode.restype = c_int
lib.zproc_returncode.argtypes = [zproc_p]
lib.zproc_pid.restype = c_int
lib.zproc_pid.argtypes = [zproc_p]
lib.zproc_running.restype = c_bool
lib.zproc_running.argtypes = [zproc_p]
lib.zproc_wait.restype = c_int
lib.zproc_wait.argtypes = [zproc_p, c_int]
lib.zproc_shutdown.restype = None
lib.zproc_shutdown.argtypes = [zproc_p, c_int]
lib.zproc_actor.restype = c_void_p
lib.zproc_actor.argtypes = [zproc_p]
lib.zproc_kill.restype = None
lib.zproc_kill.argtypes = [zproc_p, c_int]
lib.zproc_set_verbose.restype = None
lib.zproc_set_verbose.argtypes = [zproc_p, c_bool]
lib.zproc_test.restype = None
lib.zproc_test.argtypes = [c_bool]

class Zproc(object):
    """
    process configuration and status
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new zproc.
NOTE: On Windows and with libzmq3 and libzmq2 this function
returns NULL. Code needs to be ported there.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zproc_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zproc_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zproc_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy zproc, wait until process ends.
        """
        if self.allow_destruct:
            lib.zproc_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def args(self):
        """
        Return command line arguments (the first item is the executable) or
NULL if not set.
        """
        return Zlist(lib.zproc_args(self._as_parameter_), True)

    def set_args(self, arguments):
        """
        Setup the command line arguments, the first item must be an (absolute) filename
to run.
        """
        return lib.zproc_set_args(self._as_parameter_, byref(zlist_p.from_param(arguments)))

    def set_argsx(self, arguments, *args):
        """
        Setup the command line arguments, the first item must be an (absolute) filename
to run. Variadic function, must be NULL terminated.
        """
        return lib.zproc_set_argsx(self._as_parameter_, arguments, *args)

    def set_env(self, arguments):
        """
        Setup the environment variables for the process.
        """
        return lib.zproc_set_env(self._as_parameter_, byref(zhash_p.from_param(arguments)))

    def set_stdin(self, socket):
        """
        Connects process stdin with a readable ('>', connect) zeromq socket. If
socket argument is NULL, zproc creates own managed pair of inproc
sockets.  The writable one is then accessible via zproc_stdin method.
        """
        return lib.zproc_set_stdin(self._as_parameter_, socket)

    def set_stdout(self, socket):
        """
        Connects process stdout with a writable ('@', bind) zeromq socket. If
socket argument is NULL, zproc creates own managed pair of inproc
sockets.  The readable one is then accessible via zproc_stdout method.
        """
        return lib.zproc_set_stdout(self._as_parameter_, socket)

    def set_stderr(self, socket):
        """
        Connects process stderr with a writable ('@', bind) zeromq socket. If
socket argument is NULL, zproc creates own managed pair of inproc
sockets.  The readable one is then accessible via zproc_stderr method.
        """
        return lib.zproc_set_stderr(self._as_parameter_, socket)

    def stdin(self):
        """
        Return subprocess stdin writable socket. NULL for
not initialized or external sockets.
        """
        return c_void_p(lib.zproc_stdin(self._as_parameter_))

    def stdout(self):
        """
        Return subprocess stdout readable socket. NULL for
not initialized or external sockets.
        """
        return c_void_p(lib.zproc_stdout(self._as_parameter_))

    def stderr(self):
        """
        Return subprocess stderr readable socket. NULL for
not initialized or external sockets.
        """
        return c_void_p(lib.zproc_stderr(self._as_parameter_))

    def run(self):
        """
        Starts the process, return just before execve/CreateProcess.
        """
        return lib.zproc_run(self._as_parameter_)

    def returncode(self):
        """
        process exit code
        """
        return lib.zproc_returncode(self._as_parameter_)

    def pid(self):
        """
        PID of the process
        """
        return lib.zproc_pid(self._as_parameter_)

    def running(self):
        """
        return true if process is running, false if not yet started or finished
        """
        return lib.zproc_running(self._as_parameter_)

    def wait(self, timeout):
        """
        The timeout should be zero or greater, or -1 to wait indefinitely.
wait or poll process status, return return code
        """
        return lib.zproc_wait(self._as_parameter_, timeout)

    def shutdown(self, timeout):
        """
        send SIGTERM signal to the subprocess, wait for grace period and
eventually send SIGKILL
        """
        return lib.zproc_shutdown(self._as_parameter_, timeout)

    def actor(self):
        """
        return internal actor, useful for the polling if process died
        """
        return c_void_p(lib.zproc_actor(self._as_parameter_))

    def kill(self, signal):
        """
        send a signal to the subprocess
        """
        return lib.zproc_kill(self._as_parameter_, signal)

    def set_verbose(self, verbose):
        """
        set verbose mode
        """
        return lib.zproc_set_verbose(self._as_parameter_, verbose)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zproc_test(verbose)


# zsock
lib.zsock_new.restype = zsock_p
lib.zsock_new.argtypes = [c_int]
lib.zsock_destroy.restype = None
lib.zsock_destroy.argtypes = [POINTER(zsock_p)]
lib.zsock_new_pub.restype = zsock_p
lib.zsock_new_pub.argtypes = [c_char_p]
lib.zsock_new_sub.restype = zsock_p
lib.zsock_new_sub.argtypes = [c_char_p, c_char_p]
lib.zsock_new_req.restype = zsock_p
lib.zsock_new_req.argtypes = [c_char_p]
lib.zsock_new_rep.restype = zsock_p
lib.zsock_new_rep.argtypes = [c_char_p]
lib.zsock_new_dealer.restype = zsock_p
lib.zsock_new_dealer.argtypes = [c_char_p]
lib.zsock_new_router.restype = zsock_p
lib.zsock_new_router.argtypes = [c_char_p]
lib.zsock_new_push.restype = zsock_p
lib.zsock_new_push.argtypes = [c_char_p]
lib.zsock_new_pull.restype = zsock_p
lib.zsock_new_pull.argtypes = [c_char_p]
lib.zsock_new_xpub.restype = zsock_p
lib.zsock_new_xpub.argtypes = [c_char_p]
lib.zsock_new_xsub.restype = zsock_p
lib.zsock_new_xsub.argtypes = [c_char_p]
lib.zsock_new_pair.restype = zsock_p
lib.zsock_new_pair.argtypes = [c_char_p]
lib.zsock_new_stream.restype = zsock_p
lib.zsock_new_stream.argtypes = [c_char_p]
lib.zsock_new_server.restype = zsock_p
lib.zsock_new_server.argtypes = [c_char_p]
lib.zsock_new_client.restype = zsock_p
lib.zsock_new_client.argtypes = [c_char_p]
lib.zsock_new_radio.restype = zsock_p
lib.zsock_new_radio.argtypes = [c_char_p]
lib.zsock_new_dish.restype = zsock_p
lib.zsock_new_dish.argtypes = [c_char_p]
lib.zsock_new_gather.restype = zsock_p
lib.zsock_new_gather.argtypes = [c_char_p]
lib.zsock_new_scatter.restype = zsock_p
lib.zsock_new_scatter.argtypes = [c_char_p]
lib.zsock_new_dgram.restype = zsock_p
lib.zsock_new_dgram.argtypes = [c_char_p]
lib.zsock_bind.restype = c_int
lib.zsock_bind.argtypes = [zsock_p, c_char_p]
lib.zsock_endpoint.restype = c_char_p
lib.zsock_endpoint.argtypes = [zsock_p]
lib.zsock_unbind.restype = c_int
lib.zsock_unbind.argtypes = [zsock_p, c_char_p]
lib.zsock_connect.restype = c_int
lib.zsock_connect.argtypes = [zsock_p, c_char_p]
lib.zsock_disconnect.restype = c_int
lib.zsock_disconnect.argtypes = [zsock_p, c_char_p]
lib.zsock_attach.restype = c_int
lib.zsock_attach.argtypes = [zsock_p, c_char_p, c_bool]
lib.zsock_type_str.restype = c_char_p
lib.zsock_type_str.argtypes = [zsock_p]
lib.zsock_send.restype = c_int
lib.zsock_send.argtypes = [zsock_p, c_char_p]
lib.zsock_vsend.restype = c_int
lib.zsock_vsend.argtypes = [zsock_p, c_char_p, va_list_p]
lib.zsock_recv.restype = c_int
lib.zsock_recv.argtypes = [zsock_p, c_char_p]
lib.zsock_vrecv.restype = c_int
lib.zsock_vrecv.argtypes = [zsock_p, c_char_p, va_list_p]
lib.zsock_bsend.restype = c_int
lib.zsock_bsend.argtypes = [zsock_p, c_char_p]
lib.zsock_brecv.restype = c_int
lib.zsock_brecv.argtypes = [zsock_p, c_char_p]
lib.zsock_routing_id.restype = c_int
lib.zsock_routing_id.argtypes = [zsock_p]
lib.zsock_set_routing_id.restype = None
lib.zsock_set_routing_id.argtypes = [zsock_p, c_int]
lib.zsock_set_unbounded.restype = None
lib.zsock_set_unbounded.argtypes = [zsock_p]
lib.zsock_signal.restype = c_int
lib.zsock_signal.argtypes = [zsock_p, c_ubyte]
lib.zsock_wait.restype = c_int
lib.zsock_wait.argtypes = [zsock_p]
lib.zsock_flush.restype = None
lib.zsock_flush.argtypes = [zsock_p]
lib.zsock_join.restype = c_int
lib.zsock_join.argtypes = [zsock_p, c_char_p]
lib.zsock_leave.restype = c_int
lib.zsock_leave.argtypes = [zsock_p, c_char_p]
lib.zsock_is.restype = c_bool
lib.zsock_is.argtypes = [c_void_p]
lib.zsock_resolve.restype = c_void_p
lib.zsock_resolve.argtypes = [c_void_p]
lib.zsock_has_in.restype = c_bool
lib.zsock_has_in.argtypes = [zsock_p]
lib.zsock_priority.restype = c_int
lib.zsock_priority.argtypes = [zsock_p]
lib.zsock_set_priority.restype = None
lib.zsock_set_priority.argtypes = [zsock_p, c_int]
lib.zsock_reconnect_stop.restype = c_int
lib.zsock_reconnect_stop.argtypes = [zsock_p]
lib.zsock_set_reconnect_stop.restype = None
lib.zsock_set_reconnect_stop.argtypes = [zsock_p, c_int]
lib.zsock_set_only_first_subscribe.restype = None
lib.zsock_set_only_first_subscribe.argtypes = [zsock_p, c_int]
lib.zsock_set_hello_msg.restype = None
lib.zsock_set_hello_msg.argtypes = [zsock_p, zframe_p]
lib.zsock_set_disconnect_msg.restype = None
lib.zsock_set_disconnect_msg.argtypes = [zsock_p, zframe_p]
lib.zsock_set_wss_trust_system.restype = None
lib.zsock_set_wss_trust_system.argtypes = [zsock_p, c_int]
lib.zsock_set_wss_hostname.restype = None
lib.zsock_set_wss_hostname.argtypes = [zsock_p, c_char_p]
lib.zsock_set_wss_trust_pem.restype = None
lib.zsock_set_wss_trust_pem.argtypes = [zsock_p, c_char_p]
lib.zsock_set_wss_cert_pem.restype = None
lib.zsock_set_wss_cert_pem.argtypes = [zsock_p, c_char_p]
lib.zsock_set_wss_key_pem.restype = None
lib.zsock_set_wss_key_pem.argtypes = [zsock_p, c_char_p]
lib.zsock_out_batch_size.restype = c_int
lib.zsock_out_batch_size.argtypes = [zsock_p]
lib.zsock_set_out_batch_size.restype = None
lib.zsock_set_out_batch_size.argtypes = [zsock_p, c_int]
lib.zsock_in_batch_size.restype = c_int
lib.zsock_in_batch_size.argtypes = [zsock_p]
lib.zsock_set_in_batch_size.restype = None
lib.zsock_set_in_batch_size.argtypes = [zsock_p, c_int]
lib.zsock_socks_password.restype = POINTER(c_char)
lib.zsock_socks_password.argtypes = [zsock_p]
lib.zsock_set_socks_password.restype = None
lib.zsock_set_socks_password.argtypes = [zsock_p, c_char_p]
lib.zsock_socks_username.restype = POINTER(c_char)
lib.zsock_socks_username.argtypes = [zsock_p]
lib.zsock_set_socks_username.restype = None
lib.zsock_set_socks_username.argtypes = [zsock_p, c_char_p]
lib.zsock_set_xpub_manual_last_value.restype = None
lib.zsock_set_xpub_manual_last_value.argtypes = [zsock_p, c_int]
lib.zsock_router_notify.restype = c_int
lib.zsock_router_notify.argtypes = [zsock_p]
lib.zsock_set_router_notify.restype = None
lib.zsock_set_router_notify.argtypes = [zsock_p, c_int]
lib.zsock_multicast_loop.restype = c_int
lib.zsock_multicast_loop.argtypes = [zsock_p]
lib.zsock_set_multicast_loop.restype = None
lib.zsock_set_multicast_loop.argtypes = [zsock_p, c_int]
lib.zsock_metadata.restype = POINTER(c_char)
lib.zsock_metadata.argtypes = [zsock_p]
lib.zsock_set_metadata.restype = None
lib.zsock_set_metadata.argtypes = [zsock_p, c_char_p]
lib.zsock_loopback_fastpath.restype = c_int
lib.zsock_loopback_fastpath.argtypes = [zsock_p]
lib.zsock_set_loopback_fastpath.restype = None
lib.zsock_set_loopback_fastpath.argtypes = [zsock_p, c_int]
lib.zsock_zap_enforce_domain.restype = c_int
lib.zsock_zap_enforce_domain.argtypes = [zsock_p]
lib.zsock_set_zap_enforce_domain.restype = None
lib.zsock_set_zap_enforce_domain.argtypes = [zsock_p, c_int]
lib.zsock_gssapi_principal_nametype.restype = c_int
lib.zsock_gssapi_principal_nametype.argtypes = [zsock_p]
lib.zsock_set_gssapi_principal_nametype.restype = None
lib.zsock_set_gssapi_principal_nametype.argtypes = [zsock_p, c_int]
lib.zsock_gssapi_service_principal_nametype.restype = c_int
lib.zsock_gssapi_service_principal_nametype.argtypes = [zsock_p]
lib.zsock_set_gssapi_service_principal_nametype.restype = None
lib.zsock_set_gssapi_service_principal_nametype.argtypes = [zsock_p, c_int]
lib.zsock_bindtodevice.restype = POINTER(c_char)
lib.zsock_bindtodevice.argtypes = [zsock_p]
lib.zsock_set_bindtodevice.restype = None
lib.zsock_set_bindtodevice.argtypes = [zsock_p, c_char_p]
lib.zsock_heartbeat_ivl.restype = c_int
lib.zsock_heartbeat_ivl.argtypes = [zsock_p]
lib.zsock_set_heartbeat_ivl.restype = None
lib.zsock_set_heartbeat_ivl.argtypes = [zsock_p, c_int]
lib.zsock_heartbeat_ttl.restype = c_int
lib.zsock_heartbeat_ttl.argtypes = [zsock_p]
lib.zsock_set_heartbeat_ttl.restype = None
lib.zsock_set_heartbeat_ttl.argtypes = [zsock_p, c_int]
lib.zsock_heartbeat_timeout.restype = c_int
lib.zsock_heartbeat_timeout.argtypes = [zsock_p]
lib.zsock_set_heartbeat_timeout.restype = None
lib.zsock_set_heartbeat_timeout.argtypes = [zsock_p, c_int]
lib.zsock_use_fd.restype = c_int
lib.zsock_use_fd.argtypes = [zsock_p]
lib.zsock_set_use_fd.restype = None
lib.zsock_set_use_fd.argtypes = [zsock_p, c_int]
lib.zsock_set_xpub_manual.restype = None
lib.zsock_set_xpub_manual.argtypes = [zsock_p, c_int]
lib.zsock_set_xpub_welcome_msg.restype = None
lib.zsock_set_xpub_welcome_msg.argtypes = [zsock_p, c_char_p]
lib.zsock_set_stream_notify.restype = None
lib.zsock_set_stream_notify.argtypes = [zsock_p, c_int]
lib.zsock_invert_matching.restype = c_int
lib.zsock_invert_matching.argtypes = [zsock_p]
lib.zsock_set_invert_matching.restype = None
lib.zsock_set_invert_matching.argtypes = [zsock_p, c_int]
lib.zsock_set_xpub_verboser.restype = None
lib.zsock_set_xpub_verboser.argtypes = [zsock_p, c_int]
lib.zsock_connect_timeout.restype = c_int
lib.zsock_connect_timeout.argtypes = [zsock_p]
lib.zsock_set_connect_timeout.restype = None
lib.zsock_set_connect_timeout.argtypes = [zsock_p, c_int]
lib.zsock_tcp_maxrt.restype = c_int
lib.zsock_tcp_maxrt.argtypes = [zsock_p]
lib.zsock_set_tcp_maxrt.restype = None
lib.zsock_set_tcp_maxrt.argtypes = [zsock_p, c_int]
lib.zsock_thread_safe.restype = c_int
lib.zsock_thread_safe.argtypes = [zsock_p]
lib.zsock_multicast_maxtpdu.restype = c_int
lib.zsock_multicast_maxtpdu.argtypes = [zsock_p]
lib.zsock_set_multicast_maxtpdu.restype = None
lib.zsock_set_multicast_maxtpdu.argtypes = [zsock_p, c_int]
lib.zsock_vmci_buffer_size.restype = c_int
lib.zsock_vmci_buffer_size.argtypes = [zsock_p]
lib.zsock_set_vmci_buffer_size.restype = None
lib.zsock_set_vmci_buffer_size.argtypes = [zsock_p, c_int]
lib.zsock_vmci_buffer_min_size.restype = c_int
lib.zsock_vmci_buffer_min_size.argtypes = [zsock_p]
lib.zsock_set_vmci_buffer_min_size.restype = None
lib.zsock_set_vmci_buffer_min_size.argtypes = [zsock_p, c_int]
lib.zsock_vmci_buffer_max_size.restype = c_int
lib.zsock_vmci_buffer_max_size.argtypes = [zsock_p]
lib.zsock_set_vmci_buffer_max_size.restype = None
lib.zsock_set_vmci_buffer_max_size.argtypes = [zsock_p, c_int]
lib.zsock_vmci_connect_timeout.restype = c_int
lib.zsock_vmci_connect_timeout.argtypes = [zsock_p]
lib.zsock_set_vmci_connect_timeout.restype = None
lib.zsock_set_vmci_connect_timeout.argtypes = [zsock_p, c_int]
lib.zsock_tos.restype = c_int
lib.zsock_tos.argtypes = [zsock_p]
lib.zsock_set_tos.restype = None
lib.zsock_set_tos.argtypes = [zsock_p, c_int]
lib.zsock_set_router_handover.restype = None
lib.zsock_set_router_handover.argtypes = [zsock_p, c_int]
lib.zsock_set_connect_rid.restype = None
lib.zsock_set_connect_rid.argtypes = [zsock_p, c_char_p]
lib.zsock_set_connect_rid_bin.restype = None
lib.zsock_set_connect_rid_bin.argtypes = [zsock_p, c_void_p]
lib.zsock_handshake_ivl.restype = c_int
lib.zsock_handshake_ivl.argtypes = [zsock_p]
lib.zsock_set_handshake_ivl.restype = None
lib.zsock_set_handshake_ivl.argtypes = [zsock_p, c_int]
lib.zsock_socks_proxy.restype = POINTER(c_char)
lib.zsock_socks_proxy.argtypes = [zsock_p]
lib.zsock_set_socks_proxy.restype = None
lib.zsock_set_socks_proxy.argtypes = [zsock_p, c_char_p]
lib.zsock_set_xpub_nodrop.restype = None
lib.zsock_set_xpub_nodrop.argtypes = [zsock_p, c_int]
lib.zsock_set_router_mandatory.restype = None
lib.zsock_set_router_mandatory.argtypes = [zsock_p, c_int]
lib.zsock_set_probe_router.restype = None
lib.zsock_set_probe_router.argtypes = [zsock_p, c_int]
lib.zsock_set_req_relaxed.restype = None
lib.zsock_set_req_relaxed.argtypes = [zsock_p, c_int]
lib.zsock_set_req_correlate.restype = None
lib.zsock_set_req_correlate.argtypes = [zsock_p, c_int]
lib.zsock_set_conflate.restype = None
lib.zsock_set_conflate.argtypes = [zsock_p, c_int]
lib.zsock_zap_domain.restype = POINTER(c_char)
lib.zsock_zap_domain.argtypes = [zsock_p]
lib.zsock_set_zap_domain.restype = None
lib.zsock_set_zap_domain.argtypes = [zsock_p, c_char_p]
lib.zsock_mechanism.restype = c_int
lib.zsock_mechanism.argtypes = [zsock_p]
lib.zsock_plain_server.restype = c_int
lib.zsock_plain_server.argtypes = [zsock_p]
lib.zsock_set_plain_server.restype = None
lib.zsock_set_plain_server.argtypes = [zsock_p, c_int]
lib.zsock_plain_username.restype = POINTER(c_char)
lib.zsock_plain_username.argtypes = [zsock_p]
lib.zsock_set_plain_username.restype = None
lib.zsock_set_plain_username.argtypes = [zsock_p, c_char_p]
lib.zsock_plain_password.restype = POINTER(c_char)
lib.zsock_plain_password.argtypes = [zsock_p]
lib.zsock_set_plain_password.restype = None
lib.zsock_set_plain_password.argtypes = [zsock_p, c_char_p]
lib.zsock_curve_server.restype = c_int
lib.zsock_curve_server.argtypes = [zsock_p]
lib.zsock_set_curve_server.restype = None
lib.zsock_set_curve_server.argtypes = [zsock_p, c_int]
lib.zsock_curve_publickey.restype = POINTER(c_char)
lib.zsock_curve_publickey.argtypes = [zsock_p]
lib.zsock_set_curve_publickey.restype = None
lib.zsock_set_curve_publickey.argtypes = [zsock_p, c_char_p]
lib.zsock_set_curve_publickey_bin.restype = None
lib.zsock_set_curve_publickey_bin.argtypes = [zsock_p, c_void_p]
lib.zsock_curve_secretkey.restype = POINTER(c_char)
lib.zsock_curve_secretkey.argtypes = [zsock_p]
lib.zsock_set_curve_secretkey.restype = None
lib.zsock_set_curve_secretkey.argtypes = [zsock_p, c_char_p]
lib.zsock_set_curve_secretkey_bin.restype = None
lib.zsock_set_curve_secretkey_bin.argtypes = [zsock_p, c_void_p]
lib.zsock_curve_serverkey.restype = POINTER(c_char)
lib.zsock_curve_serverkey.argtypes = [zsock_p]
lib.zsock_set_curve_serverkey.restype = None
lib.zsock_set_curve_serverkey.argtypes = [zsock_p, c_char_p]
lib.zsock_set_curve_serverkey_bin.restype = None
lib.zsock_set_curve_serverkey_bin.argtypes = [zsock_p, c_void_p]
lib.zsock_gssapi_server.restype = c_int
lib.zsock_gssapi_server.argtypes = [zsock_p]
lib.zsock_set_gssapi_server.restype = None
lib.zsock_set_gssapi_server.argtypes = [zsock_p, c_int]
lib.zsock_gssapi_plaintext.restype = c_int
lib.zsock_gssapi_plaintext.argtypes = [zsock_p]
lib.zsock_set_gssapi_plaintext.restype = None
lib.zsock_set_gssapi_plaintext.argtypes = [zsock_p, c_int]
lib.zsock_gssapi_principal.restype = POINTER(c_char)
lib.zsock_gssapi_principal.argtypes = [zsock_p]
lib.zsock_set_gssapi_principal.restype = None
lib.zsock_set_gssapi_principal.argtypes = [zsock_p, c_char_p]
lib.zsock_gssapi_service_principal.restype = POINTER(c_char)
lib.zsock_gssapi_service_principal.argtypes = [zsock_p]
lib.zsock_set_gssapi_service_principal.restype = None
lib.zsock_set_gssapi_service_principal.argtypes = [zsock_p, c_char_p]
lib.zsock_ipv6.restype = c_int
lib.zsock_ipv6.argtypes = [zsock_p]
lib.zsock_set_ipv6.restype = None
lib.zsock_set_ipv6.argtypes = [zsock_p, c_int]
lib.zsock_immediate.restype = c_int
lib.zsock_immediate.argtypes = [zsock_p]
lib.zsock_set_immediate.restype = None
lib.zsock_set_immediate.argtypes = [zsock_p, c_int]
lib.zsock_sndhwm.restype = c_int
lib.zsock_sndhwm.argtypes = [zsock_p]
lib.zsock_set_sndhwm.restype = None
lib.zsock_set_sndhwm.argtypes = [zsock_p, c_int]
lib.zsock_rcvhwm.restype = c_int
lib.zsock_rcvhwm.argtypes = [zsock_p]
lib.zsock_set_rcvhwm.restype = None
lib.zsock_set_rcvhwm.argtypes = [zsock_p, c_int]
lib.zsock_maxmsgsize.restype = c_int
lib.zsock_maxmsgsize.argtypes = [zsock_p]
lib.zsock_set_maxmsgsize.restype = None
lib.zsock_set_maxmsgsize.argtypes = [zsock_p, c_int]
lib.zsock_multicast_hops.restype = c_int
lib.zsock_multicast_hops.argtypes = [zsock_p]
lib.zsock_set_multicast_hops.restype = None
lib.zsock_set_multicast_hops.argtypes = [zsock_p, c_int]
lib.zsock_set_xpub_verbose.restype = None
lib.zsock_set_xpub_verbose.argtypes = [zsock_p, c_int]
lib.zsock_tcp_keepalive.restype = c_int
lib.zsock_tcp_keepalive.argtypes = [zsock_p]
lib.zsock_set_tcp_keepalive.restype = None
lib.zsock_set_tcp_keepalive.argtypes = [zsock_p, c_int]
lib.zsock_tcp_keepalive_idle.restype = c_int
lib.zsock_tcp_keepalive_idle.argtypes = [zsock_p]
lib.zsock_set_tcp_keepalive_idle.restype = None
lib.zsock_set_tcp_keepalive_idle.argtypes = [zsock_p, c_int]
lib.zsock_tcp_keepalive_cnt.restype = c_int
lib.zsock_tcp_keepalive_cnt.argtypes = [zsock_p]
lib.zsock_set_tcp_keepalive_cnt.restype = None
lib.zsock_set_tcp_keepalive_cnt.argtypes = [zsock_p, c_int]
lib.zsock_tcp_keepalive_intvl.restype = c_int
lib.zsock_tcp_keepalive_intvl.argtypes = [zsock_p]
lib.zsock_set_tcp_keepalive_intvl.restype = None
lib.zsock_set_tcp_keepalive_intvl.argtypes = [zsock_p, c_int]
lib.zsock_tcp_accept_filter.restype = POINTER(c_char)
lib.zsock_tcp_accept_filter.argtypes = [zsock_p]
lib.zsock_set_tcp_accept_filter.restype = None
lib.zsock_set_tcp_accept_filter.argtypes = [zsock_p, c_char_p]
lib.zsock_last_endpoint.restype = POINTER(c_char)
lib.zsock_last_endpoint.argtypes = [zsock_p]
lib.zsock_set_router_raw.restype = None
lib.zsock_set_router_raw.argtypes = [zsock_p, c_int]
lib.zsock_ipv4only.restype = c_int
lib.zsock_ipv4only.argtypes = [zsock_p]
lib.zsock_set_ipv4only.restype = None
lib.zsock_set_ipv4only.argtypes = [zsock_p, c_int]
lib.zsock_set_delay_attach_on_connect.restype = None
lib.zsock_set_delay_attach_on_connect.argtypes = [zsock_p, c_int]
lib.zsock_hwm.restype = c_int
lib.zsock_hwm.argtypes = [zsock_p]
lib.zsock_set_hwm.restype = None
lib.zsock_set_hwm.argtypes = [zsock_p, c_int]
lib.zsock_swap.restype = c_int
lib.zsock_swap.argtypes = [zsock_p]
lib.zsock_set_swap.restype = None
lib.zsock_set_swap.argtypes = [zsock_p, c_int]
lib.zsock_affinity.restype = c_int
lib.zsock_affinity.argtypes = [zsock_p]
lib.zsock_set_affinity.restype = None
lib.zsock_set_affinity.argtypes = [zsock_p, c_int]
lib.zsock_identity.restype = POINTER(c_char)
lib.zsock_identity.argtypes = [zsock_p]
lib.zsock_set_identity.restype = None
lib.zsock_set_identity.argtypes = [zsock_p, c_char_p]
lib.zsock_rate.restype = c_int
lib.zsock_rate.argtypes = [zsock_p]
lib.zsock_set_rate.restype = None
lib.zsock_set_rate.argtypes = [zsock_p, c_int]
lib.zsock_recovery_ivl.restype = c_int
lib.zsock_recovery_ivl.argtypes = [zsock_p]
lib.zsock_set_recovery_ivl.restype = None
lib.zsock_set_recovery_ivl.argtypes = [zsock_p, c_int]
lib.zsock_recovery_ivl_msec.restype = c_int
lib.zsock_recovery_ivl_msec.argtypes = [zsock_p]
lib.zsock_set_recovery_ivl_msec.restype = None
lib.zsock_set_recovery_ivl_msec.argtypes = [zsock_p, c_int]
lib.zsock_mcast_loop.restype = c_int
lib.zsock_mcast_loop.argtypes = [zsock_p]
lib.zsock_set_mcast_loop.restype = None
lib.zsock_set_mcast_loop.argtypes = [zsock_p, c_int]
lib.zsock_rcvtimeo.restype = c_int
lib.zsock_rcvtimeo.argtypes = [zsock_p]
lib.zsock_set_rcvtimeo.restype = None
lib.zsock_set_rcvtimeo.argtypes = [zsock_p, c_int]
lib.zsock_sndtimeo.restype = c_int
lib.zsock_sndtimeo.argtypes = [zsock_p]
lib.zsock_set_sndtimeo.restype = None
lib.zsock_set_sndtimeo.argtypes = [zsock_p, c_int]
lib.zsock_sndbuf.restype = c_int
lib.zsock_sndbuf.argtypes = [zsock_p]
lib.zsock_set_sndbuf.restype = None
lib.zsock_set_sndbuf.argtypes = [zsock_p, c_int]
lib.zsock_rcvbuf.restype = c_int
lib.zsock_rcvbuf.argtypes = [zsock_p]
lib.zsock_set_rcvbuf.restype = None
lib.zsock_set_rcvbuf.argtypes = [zsock_p, c_int]
lib.zsock_linger.restype = c_int
lib.zsock_linger.argtypes = [zsock_p]
lib.zsock_set_linger.restype = None
lib.zsock_set_linger.argtypes = [zsock_p, c_int]
lib.zsock_reconnect_ivl.restype = c_int
lib.zsock_reconnect_ivl.argtypes = [zsock_p]
lib.zsock_set_reconnect_ivl.restype = None
lib.zsock_set_reconnect_ivl.argtypes = [zsock_p, c_int]
lib.zsock_reconnect_ivl_max.restype = c_int
lib.zsock_reconnect_ivl_max.argtypes = [zsock_p]
lib.zsock_set_reconnect_ivl_max.restype = None
lib.zsock_set_reconnect_ivl_max.argtypes = [zsock_p, c_int]
lib.zsock_backlog.restype = c_int
lib.zsock_backlog.argtypes = [zsock_p]
lib.zsock_set_backlog.restype = None
lib.zsock_set_backlog.argtypes = [zsock_p, c_int]
lib.zsock_set_subscribe.restype = None
lib.zsock_set_subscribe.argtypes = [zsock_p, c_char_p]
lib.zsock_set_unsubscribe.restype = None
lib.zsock_set_unsubscribe.argtypes = [zsock_p, c_char_p]
lib.zsock_type.restype = c_int
lib.zsock_type.argtypes = [zsock_p]
lib.zsock_rcvmore.restype = c_int
lib.zsock_rcvmore.argtypes = [zsock_p]
lib.zsock_fd.restype = socket_p
lib.zsock_fd.argtypes = [zsock_p]
lib.zsock_events.restype = c_int
lib.zsock_events.argtypes = [zsock_p]
lib.zsock_test.restype = None
lib.zsock_test.argtypes = [c_bool]

class Zsock(object):
    """
    high-level socket API that hides libzmq contexts and sockets
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new socket. Returns the new socket, or NULL if the new socket
could not be created. Note that the symbol zsock_new (and other
constructors/destructors for zsock) are redirected to the *_checked
variant, enabling intelligent socket leak detection. This can have
performance implications if you use a LOT of sockets. To turn off this
redirection behaviour, define ZSOCK_NOCHECK.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zsock_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zsock_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 1)
            self._as_parameter_ = lib.zsock_new(args[0]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy the socket. You must use this for any socket created via the
zsock_new method.
        """
        if self.allow_destruct:
            lib.zsock_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def new_pub(endpoint):
        """
        Create a PUB socket. Default action is bind.
        """
        return Zsock(lib.zsock_new_pub(endpoint), True)

    @staticmethod
    def new_sub(endpoint, subscribe):
        """
        Create a SUB socket, and optionally subscribe to some prefix string. Default
action is connect.
        """
        return Zsock(lib.zsock_new_sub(endpoint, subscribe), True)

    @staticmethod
    def new_req(endpoint):
        """
        Create a REQ socket. Default action is connect.
        """
        return Zsock(lib.zsock_new_req(endpoint), True)

    @staticmethod
    def new_rep(endpoint):
        """
        Create a REP socket. Default action is bind.
        """
        return Zsock(lib.zsock_new_rep(endpoint), True)

    @staticmethod
    def new_dealer(endpoint):
        """
        Create a DEALER socket. Default action is connect.
        """
        return Zsock(lib.zsock_new_dealer(endpoint), True)

    @staticmethod
    def new_router(endpoint):
        """
        Create a ROUTER socket. Default action is bind.
        """
        return Zsock(lib.zsock_new_router(endpoint), True)

    @staticmethod
    def new_push(endpoint):
        """
        Create a PUSH socket. Default action is connect.
        """
        return Zsock(lib.zsock_new_push(endpoint), True)

    @staticmethod
    def new_pull(endpoint):
        """
        Create a PULL socket. Default action is bind.
        """
        return Zsock(lib.zsock_new_pull(endpoint), True)

    @staticmethod
    def new_xpub(endpoint):
        """
        Create an XPUB socket. Default action is bind.
        """
        return Zsock(lib.zsock_new_xpub(endpoint), True)

    @staticmethod
    def new_xsub(endpoint):
        """
        Create an XSUB socket. Default action is connect.
        """
        return Zsock(lib.zsock_new_xsub(endpoint), True)

    @staticmethod
    def new_pair(endpoint):
        """
        Create a PAIR socket. Default action is connect.
        """
        return Zsock(lib.zsock_new_pair(endpoint), True)

    @staticmethod
    def new_stream(endpoint):
        """
        Create a STREAM socket. Default action is connect.
        """
        return Zsock(lib.zsock_new_stream(endpoint), True)

    @staticmethod
    def new_server(endpoint):
        """
        Create a SERVER socket. Default action is bind.
        """
        return Zsock(lib.zsock_new_server(endpoint), True)

    @staticmethod
    def new_client(endpoint):
        """
        Create a CLIENT socket. Default action is connect.
        """
        return Zsock(lib.zsock_new_client(endpoint), True)

    @staticmethod
    def new_radio(endpoint):
        """
        Create a RADIO socket. Default action is bind.
        """
        return Zsock(lib.zsock_new_radio(endpoint), True)

    @staticmethod
    def new_dish(endpoint):
        """
        Create a DISH socket. Default action is connect.
        """
        return Zsock(lib.zsock_new_dish(endpoint), True)

    @staticmethod
    def new_gather(endpoint):
        """
        Create a GATHER socket. Default action is bind.
        """
        return Zsock(lib.zsock_new_gather(endpoint), True)

    @staticmethod
    def new_scatter(endpoint):
        """
        Create a SCATTER socket. Default action is connect.
        """
        return Zsock(lib.zsock_new_scatter(endpoint), True)

    @staticmethod
    def new_dgram(endpoint):
        """
        Create a DGRAM (UDP) socket. Default action is bind.
The endpoint is a string consisting of a
'transport'`://` followed by an 'address'. As this is
a UDP socket the 'transport' has to be 'udp'. The
'address' specifies the ip address and port to
bind to. For example:  udp://127.0.0.1:1234
Note: To send to an endpoint over UDP you have to
send a message with the destination endpoint address
as a first message!
        """
        return Zsock(lib.zsock_new_dgram(endpoint), True)

    def bind(self, format, *args):
        """
        Bind a socket to a formatted endpoint. For tcp:// endpoints, supports
ephemeral ports, if you specify the port number as "*". By default
zsock uses the IANA designated range from C000 (49152) to FFFF (65535).
To override this range, follow the "*" with "[first-last]". Either or
both first and last may be empty. To bind to a random port within the
range, use "!" in place of "*".

Examples:
    tcp://127.0.0.1:*           bind to first free port from C000 up
    tcp://127.0.0.1:!           bind to random port from C000 to FFFF
    tcp://127.0.0.1:*[60000-]   bind to first free port from 60000 up
    tcp://127.0.0.1:![-60000]   bind to random port from C000 to 60000
    tcp://127.0.0.1:![55000-55999]
                                bind to random port from 55000 to 55999

On success, returns the actual port number used, for tcp:// endpoints,
and 0 for other transports. On failure, returns -1. Note that when using
ephemeral ports, a port may be reused by different services without
clients being aware. Protocols that run on ephemeral ports should take
this into account.
        """
        return lib.zsock_bind(self._as_parameter_, format, *args)

    def endpoint(self):
        """
        Returns last bound endpoint, if any.
        """
        return lib.zsock_endpoint(self._as_parameter_)

    def unbind(self, format, *args):
        """
        Unbind a socket from a formatted endpoint.
Returns 0 if OK, -1 if the endpoint was invalid or the function
isn't supported.
        """
        return lib.zsock_unbind(self._as_parameter_, format, *args)

    def connect(self, format, *args):
        """
        Connect a socket to a formatted endpoint
Returns 0 if OK, -1 if the endpoint was invalid.
        """
        return lib.zsock_connect(self._as_parameter_, format, *args)

    def disconnect(self, format, *args):
        """
        Disconnect a socket from a formatted endpoint
Returns 0 if OK, -1 if the endpoint was invalid or the function
isn't supported.
        """
        return lib.zsock_disconnect(self._as_parameter_, format, *args)

    def attach(self, endpoints, serverish):
        """
        Attach a socket to zero or more endpoints. If endpoints is not null,
parses as list of ZeroMQ endpoints, separated by commas, and prefixed by
'@' (to bind the socket) or '>' (to connect the socket). Returns 0 if all
endpoints were valid, or -1 if there was a syntax error. If the endpoint
does not start with '@' or '>', the serverish argument defines whether
it is used to bind (serverish = true) or connect (serverish = false).
        """
        return lib.zsock_attach(self._as_parameter_, endpoints, serverish)

    def type_str(self):
        """
        Returns socket type as printable constant string.
        """
        return lib.zsock_type_str(self._as_parameter_)

    def send(self, picture, *args):
        """
        Send a 'picture' message to the socket (or actor). The picture is a
string that defines the type of each frame. This makes it easy to send
a complex multiframe message in one call. The picture can contain any
of these characters, each corresponding to one or two arguments:

    i = int (signed)
    1 = uint8_t
    2 = uint16_t
    4 = uint32_t
    8 = uint64_t
    s = char *
    b = byte *, size_t (2 arguments)
    c = zchunk_t *
    f = zframe_t *
    h = zhashx_t *
    l = zlistx_t * (DRAFT)
    U = zuuid_t *
    p = void * (sends the pointer value, only meaningful over inproc)
    m = zmsg_t * (sends all frames in the zmsg)
    z = sends zero-sized frame (0 arguments)
    u = uint (deprecated)

Note that s, b, c, and f are encoded the same way and the choice is
offered as a convenience to the sender, which may or may not already
have data in a zchunk or zframe. Does not change or take ownership of
any arguments. Returns 0 if successful, -1 if sending failed for any
reason.
        """
        return lib.zsock_send(self._as_parameter_, picture, *args)

    def vsend(self, picture, argptr):
        """
        Send a 'picture' message to the socket (or actor). This is a va_list
version of zsock_send (), so please consult its documentation for the
details.
        """
        return lib.zsock_vsend(self._as_parameter_, picture, argptr)

    def recv(self, picture, *args):
        """
        Receive a 'picture' message to the socket (or actor). See zsock_send for
the format and meaning of the picture. Returns the picture elements into
a series of pointers as provided by the caller:

    i = int * (stores signed integer)
    4 = uint32_t * (stores 32-bit unsigned integer)
    8 = uint64_t * (stores 64-bit unsigned integer)
    s = char ** (allocates new string)
    b = byte **, size_t * (2 arguments) (allocates memory)
    c = zchunk_t ** (creates zchunk)
    f = zframe_t ** (creates zframe)
    U = zuuid_t * (creates a zuuid with the data)
    h = zhashx_t ** (creates zhashx)
    l = zlistx_t ** (creates zlistx) (DRAFT)
    p = void ** (stores pointer)
    m = zmsg_t ** (creates a zmsg with the remaining frames)
    z = null, asserts empty frame (0 arguments)
    u = uint * (stores unsigned integer, deprecated)

Note that zsock_recv creates the returned objects, and the caller must
destroy them when finished with them. The supplied pointers do not need
to be initialized. Returns 0 if successful, or -1 if it failed to recv
a message, in which case the pointers are not modified. When message
frames are truncated (a short message), sets return values to zero/null.
If an argument pointer is NULL, does not store any value (skips it).
An 'n' picture matches an empty frame; if the message does not match,
the method will return -1.
        """
        return lib.zsock_recv(self._as_parameter_, picture, *args)

    def vrecv(self, picture, argptr):
        """
        Receive a 'picture' message from the socket (or actor). This is a
va_list version of zsock_recv (), so please consult its documentation
for the details.
        """
        return lib.zsock_vrecv(self._as_parameter_, picture, argptr)

    def bsend(self, picture, *args):
        """
        Send a binary encoded 'picture' message to the socket (or actor). This
method is similar to zsock_send, except the arguments are encoded in a
binary format that is compatible with zproto, and is designed to reduce
memory allocations. The pattern argument is a string that defines the
type of each argument. Supports these argument types:

 pattern    C type                  zproto type:
    1       uint8_t                 type = "number" size = "1"
    2       uint16_t                type = "number" size = "2"
    4       uint32_t                type = "number" size = "3"
    8       uint64_t                type = "number" size = "4"
    s       char *, 0-255 chars     type = "string"
    S       char *, 0-2^32-1 chars  type = "longstr"
    c       zchunk_t *              type = "chunk"
    f       zframe_t *              type = "frame"
    u       zuuid_t *               type = "uuid"
    m       zmsg_t *                type = "msg"
    p       void *, sends pointer value, only over inproc

Does not change or take ownership of any arguments. Returns 0 if
successful, -1 if sending failed for any reason.
        """
        return lib.zsock_bsend(self._as_parameter_, picture, *args)

    def brecv(self, picture, *args):
        """
        Receive a binary encoded 'picture' message from the socket (or actor).
This method is similar to zsock_recv, except the arguments are encoded
in a binary format that is compatible with zproto, and is designed to
reduce memory allocations. The pattern argument is a string that defines
the type of each argument. See zsock_bsend for the supported argument
types. All arguments must be pointers; this call sets them to point to
values held on a per-socket basis.
For types 1, 2, 4 and 8 the caller must allocate the memory itself before
calling zsock_brecv.
For types S, the caller must free the value once finished with it, as
zsock_brecv will allocate the buffer.
For type s, the caller must not free the value as it is stored in a
local cache for performance purposes.
For types c, f, u and m the caller must call the appropriate destructor
depending on the object as zsock_brecv will create new objects.
For type p the caller must coordinate with the sender, as it is just a
pointer value being passed.
        """
        return lib.zsock_brecv(self._as_parameter_, picture, *args)

    def routing_id(self):
        """
        Return socket routing ID if any. This returns 0 if the socket is not
of type ZMQ_SERVER or if no request was already received on it.
        """
        return lib.zsock_routing_id(self._as_parameter_)

    def set_routing_id(self, routing_id):
        """
        Set routing ID on socket. The socket MUST be of type ZMQ_SERVER.
This will be used when sending messages on the socket via the zsock API.
        """
        return lib.zsock_set_routing_id(self._as_parameter_, routing_id)

    def set_unbounded(self):
        """
        Set socket to use unbounded pipes (HWM=0); use this in cases when you are
totally certain the message volume can fit in memory. This method works
across all versions of ZeroMQ. Takes a polymorphic socket reference.
        """
        return lib.zsock_set_unbounded(self._as_parameter_)

    def signal(self, status):
        """
        Send a signal over a socket. A signal is a short message carrying a
success/failure code (by convention, 0 means OK). Signals are encoded
to be distinguishable from "normal" messages. Accepts a zsock_t or a
zactor_t argument, and returns 0 if successful, -1 if the signal could
not be sent. Takes a polymorphic socket reference.
        """
        return lib.zsock_signal(self._as_parameter_, status)

    def wait(self):
        """
        Wait on a signal. Use this to coordinate between threads, over pipe
pairs. Blocks until the signal is received. Returns -1 on error, 0 or
greater on success. Accepts a zsock_t or a zactor_t as argument.
Takes a polymorphic socket reference.
        """
        return lib.zsock_wait(self._as_parameter_)

    def flush(self):
        """
        If there is a partial message still waiting on the socket, remove and
discard it. This is useful when reading partial messages, to get specific
message types.
        """
        return lib.zsock_flush(self._as_parameter_)

    def join(self, group):
        """
        Join a group for the RADIO-DISH pattern. Call only on ZMQ_DISH.
Returns 0 if OK, -1 if failed.
        """
        return lib.zsock_join(self._as_parameter_, group)

    def leave(self, group):
        """
        Leave a group for the RADIO-DISH pattern. Call only on ZMQ_DISH.
Returns 0 if OK, -1 if failed.
        """
        return lib.zsock_leave(self._as_parameter_, group)

    @staticmethod
    def is_(self):
        """
        Probe the supplied object, and report if it looks like a zsock_t.
Takes a polymorphic socket reference.
        """
        return lib.zsock_is(self)

    @staticmethod
    def resolve(self):
        """
        Probe the supplied reference. If it looks like a zsock_t instance, return
the underlying libzmq socket handle; else if it looks like a file
descriptor, return NULL; else if it looks like a libzmq socket handle,
return the supplied value. Takes a polymorphic socket reference.
        """
        return c_void_p(lib.zsock_resolve(self))

    def has_in(self):
        """
        Check whether the socket has available message to read.
        """
        return lib.zsock_has_in(self._as_parameter_)

    def priority(self):
        """
        Get socket option `priority`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_priority(self._as_parameter_)

    def set_priority(self, priority):
        """
        Set socket option `priority`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_priority(self._as_parameter_, priority)

    def reconnect_stop(self):
        """
        Get socket option `reconnect_stop`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_reconnect_stop(self._as_parameter_)

    def set_reconnect_stop(self, reconnect_stop):
        """
        Set socket option `reconnect_stop`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_reconnect_stop(self._as_parameter_, reconnect_stop)

    def set_only_first_subscribe(self, only_first_subscribe):
        """
        Set socket option `only_first_subscribe`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_only_first_subscribe(self._as_parameter_, only_first_subscribe)

    def set_hello_msg(self, hello_msg):
        """
        Set socket option `hello_msg`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_hello_msg(self._as_parameter_, hello_msg)

    def set_disconnect_msg(self, disconnect_msg):
        """
        Set socket option `disconnect_msg`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_disconnect_msg(self._as_parameter_, disconnect_msg)

    def set_wss_trust_system(self, wss_trust_system):
        """
        Set socket option `wss_trust_system`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_wss_trust_system(self._as_parameter_, wss_trust_system)

    def set_wss_hostname(self, wss_hostname):
        """
        Set socket option `wss_hostname`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_wss_hostname(self._as_parameter_, wss_hostname)

    def set_wss_trust_pem(self, wss_trust_pem):
        """
        Set socket option `wss_trust_pem`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_wss_trust_pem(self._as_parameter_, wss_trust_pem)

    def set_wss_cert_pem(self, wss_cert_pem):
        """
        Set socket option `wss_cert_pem`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_wss_cert_pem(self._as_parameter_, wss_cert_pem)

    def set_wss_key_pem(self, wss_key_pem):
        """
        Set socket option `wss_key_pem`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_wss_key_pem(self._as_parameter_, wss_key_pem)

    def out_batch_size(self):
        """
        Get socket option `out_batch_size`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_out_batch_size(self._as_parameter_)

    def set_out_batch_size(self, out_batch_size):
        """
        Set socket option `out_batch_size`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_out_batch_size(self._as_parameter_, out_batch_size)

    def in_batch_size(self):
        """
        Get socket option `in_batch_size`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_in_batch_size(self._as_parameter_)

    def set_in_batch_size(self, in_batch_size):
        """
        Set socket option `in_batch_size`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_in_batch_size(self._as_parameter_, in_batch_size)

    def socks_password(self):
        """
        Get socket option `socks_password`.
Available from libzmq 4.3.0.
        """
        return return_fresh_string(lib.zsock_socks_password(self._as_parameter_))

    def set_socks_password(self, socks_password):
        """
        Set socket option `socks_password`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_socks_password(self._as_parameter_, socks_password)

    def socks_username(self):
        """
        Get socket option `socks_username`.
Available from libzmq 4.3.0.
        """
        return return_fresh_string(lib.zsock_socks_username(self._as_parameter_))

    def set_socks_username(self, socks_username):
        """
        Set socket option `socks_username`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_socks_username(self._as_parameter_, socks_username)

    def set_xpub_manual_last_value(self, xpub_manual_last_value):
        """
        Set socket option `xpub_manual_last_value`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_xpub_manual_last_value(self._as_parameter_, xpub_manual_last_value)

    def router_notify(self):
        """
        Get socket option `router_notify`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_router_notify(self._as_parameter_)

    def set_router_notify(self, router_notify):
        """
        Set socket option `router_notify`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_router_notify(self._as_parameter_, router_notify)

    def multicast_loop(self):
        """
        Get socket option `multicast_loop`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_multicast_loop(self._as_parameter_)

    def set_multicast_loop(self, multicast_loop):
        """
        Set socket option `multicast_loop`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_multicast_loop(self._as_parameter_, multicast_loop)

    def metadata(self):
        """
        Get socket option `metadata`.
Available from libzmq 4.3.0.
        """
        return return_fresh_string(lib.zsock_metadata(self._as_parameter_))

    def set_metadata(self, metadata):
        """
        Set socket option `metadata`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_metadata(self._as_parameter_, metadata)

    def loopback_fastpath(self):
        """
        Get socket option `loopback_fastpath`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_loopback_fastpath(self._as_parameter_)

    def set_loopback_fastpath(self, loopback_fastpath):
        """
        Set socket option `loopback_fastpath`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_loopback_fastpath(self._as_parameter_, loopback_fastpath)

    def zap_enforce_domain(self):
        """
        Get socket option `zap_enforce_domain`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_zap_enforce_domain(self._as_parameter_)

    def set_zap_enforce_domain(self, zap_enforce_domain):
        """
        Set socket option `zap_enforce_domain`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_zap_enforce_domain(self._as_parameter_, zap_enforce_domain)

    def gssapi_principal_nametype(self):
        """
        Get socket option `gssapi_principal_nametype`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_gssapi_principal_nametype(self._as_parameter_)

    def set_gssapi_principal_nametype(self, gssapi_principal_nametype):
        """
        Set socket option `gssapi_principal_nametype`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_gssapi_principal_nametype(self._as_parameter_, gssapi_principal_nametype)

    def gssapi_service_principal_nametype(self):
        """
        Get socket option `gssapi_service_principal_nametype`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_gssapi_service_principal_nametype(self._as_parameter_)

    def set_gssapi_service_principal_nametype(self, gssapi_service_principal_nametype):
        """
        Set socket option `gssapi_service_principal_nametype`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_gssapi_service_principal_nametype(self._as_parameter_, gssapi_service_principal_nametype)

    def bindtodevice(self):
        """
        Get socket option `bindtodevice`.
Available from libzmq 4.3.0.
        """
        return return_fresh_string(lib.zsock_bindtodevice(self._as_parameter_))

    def set_bindtodevice(self, bindtodevice):
        """
        Set socket option `bindtodevice`.
Available from libzmq 4.3.0.
        """
        return lib.zsock_set_bindtodevice(self._as_parameter_, bindtodevice)

    def heartbeat_ivl(self):
        """
        Get socket option `heartbeat_ivl`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_heartbeat_ivl(self._as_parameter_)

    def set_heartbeat_ivl(self, heartbeat_ivl):
        """
        Set socket option `heartbeat_ivl`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_heartbeat_ivl(self._as_parameter_, heartbeat_ivl)

    def heartbeat_ttl(self):
        """
        Get socket option `heartbeat_ttl`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_heartbeat_ttl(self._as_parameter_)

    def set_heartbeat_ttl(self, heartbeat_ttl):
        """
        Set socket option `heartbeat_ttl`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_heartbeat_ttl(self._as_parameter_, heartbeat_ttl)

    def heartbeat_timeout(self):
        """
        Get socket option `heartbeat_timeout`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_heartbeat_timeout(self._as_parameter_)

    def set_heartbeat_timeout(self, heartbeat_timeout):
        """
        Set socket option `heartbeat_timeout`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_heartbeat_timeout(self._as_parameter_, heartbeat_timeout)

    def use_fd(self):
        """
        Get socket option `use_fd`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_use_fd(self._as_parameter_)

    def set_use_fd(self, use_fd):
        """
        Set socket option `use_fd`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_use_fd(self._as_parameter_, use_fd)

    def set_xpub_manual(self, xpub_manual):
        """
        Set socket option `xpub_manual`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_xpub_manual(self._as_parameter_, xpub_manual)

    def set_xpub_welcome_msg(self, xpub_welcome_msg):
        """
        Set socket option `xpub_welcome_msg`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_xpub_welcome_msg(self._as_parameter_, xpub_welcome_msg)

    def set_stream_notify(self, stream_notify):
        """
        Set socket option `stream_notify`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_stream_notify(self._as_parameter_, stream_notify)

    def invert_matching(self):
        """
        Get socket option `invert_matching`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_invert_matching(self._as_parameter_)

    def set_invert_matching(self, invert_matching):
        """
        Set socket option `invert_matching`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_invert_matching(self._as_parameter_, invert_matching)

    def set_xpub_verboser(self, xpub_verboser):
        """
        Set socket option `xpub_verboser`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_xpub_verboser(self._as_parameter_, xpub_verboser)

    def connect_timeout(self):
        """
        Get socket option `connect_timeout`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_connect_timeout(self._as_parameter_)

    def set_connect_timeout(self, connect_timeout):
        """
        Set socket option `connect_timeout`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_connect_timeout(self._as_parameter_, connect_timeout)

    def tcp_maxrt(self):
        """
        Get socket option `tcp_maxrt`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_tcp_maxrt(self._as_parameter_)

    def set_tcp_maxrt(self, tcp_maxrt):
        """
        Set socket option `tcp_maxrt`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_tcp_maxrt(self._as_parameter_, tcp_maxrt)

    def thread_safe(self):
        """
        Get socket option `thread_safe`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_thread_safe(self._as_parameter_)

    def multicast_maxtpdu(self):
        """
        Get socket option `multicast_maxtpdu`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_multicast_maxtpdu(self._as_parameter_)

    def set_multicast_maxtpdu(self, multicast_maxtpdu):
        """
        Set socket option `multicast_maxtpdu`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_multicast_maxtpdu(self._as_parameter_, multicast_maxtpdu)

    def vmci_buffer_size(self):
        """
        Get socket option `vmci_buffer_size`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_vmci_buffer_size(self._as_parameter_)

    def set_vmci_buffer_size(self, vmci_buffer_size):
        """
        Set socket option `vmci_buffer_size`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_vmci_buffer_size(self._as_parameter_, vmci_buffer_size)

    def vmci_buffer_min_size(self):
        """
        Get socket option `vmci_buffer_min_size`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_vmci_buffer_min_size(self._as_parameter_)

    def set_vmci_buffer_min_size(self, vmci_buffer_min_size):
        """
        Set socket option `vmci_buffer_min_size`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_vmci_buffer_min_size(self._as_parameter_, vmci_buffer_min_size)

    def vmci_buffer_max_size(self):
        """
        Get socket option `vmci_buffer_max_size`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_vmci_buffer_max_size(self._as_parameter_)

    def set_vmci_buffer_max_size(self, vmci_buffer_max_size):
        """
        Set socket option `vmci_buffer_max_size`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_vmci_buffer_max_size(self._as_parameter_, vmci_buffer_max_size)

    def vmci_connect_timeout(self):
        """
        Get socket option `vmci_connect_timeout`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_vmci_connect_timeout(self._as_parameter_)

    def set_vmci_connect_timeout(self, vmci_connect_timeout):
        """
        Set socket option `vmci_connect_timeout`.
Available from libzmq 4.2.0.
        """
        return lib.zsock_set_vmci_connect_timeout(self._as_parameter_, vmci_connect_timeout)

    def tos(self):
        """
        Get socket option `tos`.
Available from libzmq 4.1.0.
        """
        return lib.zsock_tos(self._as_parameter_)

    def set_tos(self, tos):
        """
        Set socket option `tos`.
Available from libzmq 4.1.0.
        """
        return lib.zsock_set_tos(self._as_parameter_, tos)

    def set_router_handover(self, router_handover):
        """
        Set socket option `router_handover`.
Available from libzmq 4.1.0.
        """
        return lib.zsock_set_router_handover(self._as_parameter_, router_handover)

    def set_connect_rid(self, connect_rid):
        """
        Set socket option `connect_rid`.
Available from libzmq 4.1.0.
        """
        return lib.zsock_set_connect_rid(self._as_parameter_, connect_rid)

    def set_connect_rid_bin(self, connect_rid):
        """
        Set socket option `connect_rid` from 32-octet binary
Available from libzmq 4.1.0.
        """
        return lib.zsock_set_connect_rid_bin(self._as_parameter_, connect_rid)

    def handshake_ivl(self):
        """
        Get socket option `handshake_ivl`.
Available from libzmq 4.1.0.
        """
        return lib.zsock_handshake_ivl(self._as_parameter_)

    def set_handshake_ivl(self, handshake_ivl):
        """
        Set socket option `handshake_ivl`.
Available from libzmq 4.1.0.
        """
        return lib.zsock_set_handshake_ivl(self._as_parameter_, handshake_ivl)

    def socks_proxy(self):
        """
        Get socket option `socks_proxy`.
Available from libzmq 4.1.0.
        """
        return return_fresh_string(lib.zsock_socks_proxy(self._as_parameter_))

    def set_socks_proxy(self, socks_proxy):
        """
        Set socket option `socks_proxy`.
Available from libzmq 4.1.0.
        """
        return lib.zsock_set_socks_proxy(self._as_parameter_, socks_proxy)

    def set_xpub_nodrop(self, xpub_nodrop):
        """
        Set socket option `xpub_nodrop`.
Available from libzmq 4.1.0.
        """
        return lib.zsock_set_xpub_nodrop(self._as_parameter_, xpub_nodrop)

    def set_router_mandatory(self, router_mandatory):
        """
        Set socket option `router_mandatory`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_router_mandatory(self._as_parameter_, router_mandatory)

    def set_probe_router(self, probe_router):
        """
        Set socket option `probe_router`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_probe_router(self._as_parameter_, probe_router)

    def set_req_relaxed(self, req_relaxed):
        """
        Set socket option `req_relaxed`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_req_relaxed(self._as_parameter_, req_relaxed)

    def set_req_correlate(self, req_correlate):
        """
        Set socket option `req_correlate`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_req_correlate(self._as_parameter_, req_correlate)

    def set_conflate(self, conflate):
        """
        Set socket option `conflate`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_conflate(self._as_parameter_, conflate)

    def zap_domain(self):
        """
        Get socket option `zap_domain`.
Available from libzmq 4.0.0.
        """
        return return_fresh_string(lib.zsock_zap_domain(self._as_parameter_))

    def set_zap_domain(self, zap_domain):
        """
        Set socket option `zap_domain`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_zap_domain(self._as_parameter_, zap_domain)

    def mechanism(self):
        """
        Get socket option `mechanism`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_mechanism(self._as_parameter_)

    def plain_server(self):
        """
        Get socket option `plain_server`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_plain_server(self._as_parameter_)

    def set_plain_server(self, plain_server):
        """
        Set socket option `plain_server`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_plain_server(self._as_parameter_, plain_server)

    def plain_username(self):
        """
        Get socket option `plain_username`.
Available from libzmq 4.0.0.
        """
        return return_fresh_string(lib.zsock_plain_username(self._as_parameter_))

    def set_plain_username(self, plain_username):
        """
        Set socket option `plain_username`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_plain_username(self._as_parameter_, plain_username)

    def plain_password(self):
        """
        Get socket option `plain_password`.
Available from libzmq 4.0.0.
        """
        return return_fresh_string(lib.zsock_plain_password(self._as_parameter_))

    def set_plain_password(self, plain_password):
        """
        Set socket option `plain_password`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_plain_password(self._as_parameter_, plain_password)

    def curve_server(self):
        """
        Get socket option `curve_server`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_curve_server(self._as_parameter_)

    def set_curve_server(self, curve_server):
        """
        Set socket option `curve_server`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_curve_server(self._as_parameter_, curve_server)

    def curve_publickey(self):
        """
        Get socket option `curve_publickey`.
Available from libzmq 4.0.0.
        """
        return return_fresh_string(lib.zsock_curve_publickey(self._as_parameter_))

    def set_curve_publickey(self, curve_publickey):
        """
        Set socket option `curve_publickey`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_curve_publickey(self._as_parameter_, curve_publickey)

    def set_curve_publickey_bin(self, curve_publickey):
        """
        Set socket option `curve_publickey` from 32-octet binary
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_curve_publickey_bin(self._as_parameter_, curve_publickey)

    def curve_secretkey(self):
        """
        Get socket option `curve_secretkey`.
Available from libzmq 4.0.0.
        """
        return return_fresh_string(lib.zsock_curve_secretkey(self._as_parameter_))

    def set_curve_secretkey(self, curve_secretkey):
        """
        Set socket option `curve_secretkey`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_curve_secretkey(self._as_parameter_, curve_secretkey)

    def set_curve_secretkey_bin(self, curve_secretkey):
        """
        Set socket option `curve_secretkey` from 32-octet binary
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_curve_secretkey_bin(self._as_parameter_, curve_secretkey)

    def curve_serverkey(self):
        """
        Get socket option `curve_serverkey`.
Available from libzmq 4.0.0.
        """
        return return_fresh_string(lib.zsock_curve_serverkey(self._as_parameter_))

    def set_curve_serverkey(self, curve_serverkey):
        """
        Set socket option `curve_serverkey`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_curve_serverkey(self._as_parameter_, curve_serverkey)

    def set_curve_serverkey_bin(self, curve_serverkey):
        """
        Set socket option `curve_serverkey` from 32-octet binary
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_curve_serverkey_bin(self._as_parameter_, curve_serverkey)

    def gssapi_server(self):
        """
        Get socket option `gssapi_server`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_gssapi_server(self._as_parameter_)

    def set_gssapi_server(self, gssapi_server):
        """
        Set socket option `gssapi_server`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_gssapi_server(self._as_parameter_, gssapi_server)

    def gssapi_plaintext(self):
        """
        Get socket option `gssapi_plaintext`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_gssapi_plaintext(self._as_parameter_)

    def set_gssapi_plaintext(self, gssapi_plaintext):
        """
        Set socket option `gssapi_plaintext`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_gssapi_plaintext(self._as_parameter_, gssapi_plaintext)

    def gssapi_principal(self):
        """
        Get socket option `gssapi_principal`.
Available from libzmq 4.0.0.
        """
        return return_fresh_string(lib.zsock_gssapi_principal(self._as_parameter_))

    def set_gssapi_principal(self, gssapi_principal):
        """
        Set socket option `gssapi_principal`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_gssapi_principal(self._as_parameter_, gssapi_principal)

    def gssapi_service_principal(self):
        """
        Get socket option `gssapi_service_principal`.
Available from libzmq 4.0.0.
        """
        return return_fresh_string(lib.zsock_gssapi_service_principal(self._as_parameter_))

    def set_gssapi_service_principal(self, gssapi_service_principal):
        """
        Set socket option `gssapi_service_principal`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_gssapi_service_principal(self._as_parameter_, gssapi_service_principal)

    def ipv6(self):
        """
        Get socket option `ipv6`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_ipv6(self._as_parameter_)

    def set_ipv6(self, ipv6):
        """
        Set socket option `ipv6`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_ipv6(self._as_parameter_, ipv6)

    def immediate(self):
        """
        Get socket option `immediate`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_immediate(self._as_parameter_)

    def set_immediate(self, immediate):
        """
        Set socket option `immediate`.
Available from libzmq 4.0.0.
        """
        return lib.zsock_set_immediate(self._as_parameter_, immediate)

    def sndhwm(self):
        """
        Get socket option `sndhwm`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_sndhwm(self._as_parameter_)

    def set_sndhwm(self, sndhwm):
        """
        Set socket option `sndhwm`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_sndhwm(self._as_parameter_, sndhwm)

    def rcvhwm(self):
        """
        Get socket option `rcvhwm`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_rcvhwm(self._as_parameter_)

    def set_rcvhwm(self, rcvhwm):
        """
        Set socket option `rcvhwm`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_rcvhwm(self._as_parameter_, rcvhwm)

    def maxmsgsize(self):
        """
        Get socket option `maxmsgsize`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_maxmsgsize(self._as_parameter_)

    def set_maxmsgsize(self, maxmsgsize):
        """
        Set socket option `maxmsgsize`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_maxmsgsize(self._as_parameter_, maxmsgsize)

    def multicast_hops(self):
        """
        Get socket option `multicast_hops`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_multicast_hops(self._as_parameter_)

    def set_multicast_hops(self, multicast_hops):
        """
        Set socket option `multicast_hops`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_multicast_hops(self._as_parameter_, multicast_hops)

    def set_xpub_verbose(self, xpub_verbose):
        """
        Set socket option `xpub_verbose`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_xpub_verbose(self._as_parameter_, xpub_verbose)

    def tcp_keepalive(self):
        """
        Get socket option `tcp_keepalive`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_tcp_keepalive(self._as_parameter_)

    def set_tcp_keepalive(self, tcp_keepalive):
        """
        Set socket option `tcp_keepalive`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_tcp_keepalive(self._as_parameter_, tcp_keepalive)

    def tcp_keepalive_idle(self):
        """
        Get socket option `tcp_keepalive_idle`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_tcp_keepalive_idle(self._as_parameter_)

    def set_tcp_keepalive_idle(self, tcp_keepalive_idle):
        """
        Set socket option `tcp_keepalive_idle`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_tcp_keepalive_idle(self._as_parameter_, tcp_keepalive_idle)

    def tcp_keepalive_cnt(self):
        """
        Get socket option `tcp_keepalive_cnt`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_tcp_keepalive_cnt(self._as_parameter_)

    def set_tcp_keepalive_cnt(self, tcp_keepalive_cnt):
        """
        Set socket option `tcp_keepalive_cnt`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_tcp_keepalive_cnt(self._as_parameter_, tcp_keepalive_cnt)

    def tcp_keepalive_intvl(self):
        """
        Get socket option `tcp_keepalive_intvl`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_tcp_keepalive_intvl(self._as_parameter_)

    def set_tcp_keepalive_intvl(self, tcp_keepalive_intvl):
        """
        Set socket option `tcp_keepalive_intvl`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_tcp_keepalive_intvl(self._as_parameter_, tcp_keepalive_intvl)

    def tcp_accept_filter(self):
        """
        Get socket option `tcp_accept_filter`.
Available from libzmq 3.0.0.
        """
        return return_fresh_string(lib.zsock_tcp_accept_filter(self._as_parameter_))

    def set_tcp_accept_filter(self, tcp_accept_filter):
        """
        Set socket option `tcp_accept_filter`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_tcp_accept_filter(self._as_parameter_, tcp_accept_filter)

    def last_endpoint(self):
        """
        Get socket option `last_endpoint`.
Available from libzmq 3.0.0.
        """
        return return_fresh_string(lib.zsock_last_endpoint(self._as_parameter_))

    def set_router_raw(self, router_raw):
        """
        Set socket option `router_raw`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_router_raw(self._as_parameter_, router_raw)

    def ipv4only(self):
        """
        Get socket option `ipv4only`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_ipv4only(self._as_parameter_)

    def set_ipv4only(self, ipv4only):
        """
        Set socket option `ipv4only`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_ipv4only(self._as_parameter_, ipv4only)

    def set_delay_attach_on_connect(self, delay_attach_on_connect):
        """
        Set socket option `delay_attach_on_connect`.
Available from libzmq 3.0.0.
        """
        return lib.zsock_set_delay_attach_on_connect(self._as_parameter_, delay_attach_on_connect)

    def hwm(self):
        """
        Get socket option `hwm`.
Available from libzmq 2.0.0 to 3.0.0.
        """
        return lib.zsock_hwm(self._as_parameter_)

    def set_hwm(self, hwm):
        """
        Set socket option `hwm`.
Available from libzmq 2.0.0 to 3.0.0.
        """
        return lib.zsock_set_hwm(self._as_parameter_, hwm)

    def swap(self):
        """
        Get socket option `swap`.
Available from libzmq 2.0.0 to 3.0.0.
        """
        return lib.zsock_swap(self._as_parameter_)

    def set_swap(self, swap):
        """
        Set socket option `swap`.
Available from libzmq 2.0.0 to 3.0.0.
        """
        return lib.zsock_set_swap(self._as_parameter_, swap)

    def affinity(self):
        """
        Get socket option `affinity`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_affinity(self._as_parameter_)

    def set_affinity(self, affinity):
        """
        Set socket option `affinity`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_set_affinity(self._as_parameter_, affinity)

    def identity(self):
        """
        Get socket option `identity`.
Available from libzmq 2.0.0.
        """
        return return_fresh_string(lib.zsock_identity(self._as_parameter_))

    def set_identity(self, identity):
        """
        Set socket option `identity`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_set_identity(self._as_parameter_, identity)

    def rate(self):
        """
        Get socket option `rate`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_rate(self._as_parameter_)

    def set_rate(self, rate):
        """
        Set socket option `rate`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_set_rate(self._as_parameter_, rate)

    def recovery_ivl(self):
        """
        Get socket option `recovery_ivl`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_recovery_ivl(self._as_parameter_)

    def set_recovery_ivl(self, recovery_ivl):
        """
        Set socket option `recovery_ivl`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_set_recovery_ivl(self._as_parameter_, recovery_ivl)

    def recovery_ivl_msec(self):
        """
        Get socket option `recovery_ivl_msec`.
Available from libzmq 2.0.0 to 3.0.0.
        """
        return lib.zsock_recovery_ivl_msec(self._as_parameter_)

    def set_recovery_ivl_msec(self, recovery_ivl_msec):
        """
        Set socket option `recovery_ivl_msec`.
Available from libzmq 2.0.0 to 3.0.0.
        """
        return lib.zsock_set_recovery_ivl_msec(self._as_parameter_, recovery_ivl_msec)

    def mcast_loop(self):
        """
        Get socket option `mcast_loop`.
Available from libzmq 2.0.0 to 3.0.0.
        """
        return lib.zsock_mcast_loop(self._as_parameter_)

    def set_mcast_loop(self, mcast_loop):
        """
        Set socket option `mcast_loop`.
Available from libzmq 2.0.0 to 3.0.0.
        """
        return lib.zsock_set_mcast_loop(self._as_parameter_, mcast_loop)

    def rcvtimeo(self):
        """
        Get socket option `rcvtimeo`.
Available from libzmq 2.2.0.
        """
        return lib.zsock_rcvtimeo(self._as_parameter_)

    def set_rcvtimeo(self, rcvtimeo):
        """
        Set socket option `rcvtimeo`.
Available from libzmq 2.2.0.
        """
        return lib.zsock_set_rcvtimeo(self._as_parameter_, rcvtimeo)

    def sndtimeo(self):
        """
        Get socket option `sndtimeo`.
Available from libzmq 2.2.0.
        """
        return lib.zsock_sndtimeo(self._as_parameter_)

    def set_sndtimeo(self, sndtimeo):
        """
        Set socket option `sndtimeo`.
Available from libzmq 2.2.0.
        """
        return lib.zsock_set_sndtimeo(self._as_parameter_, sndtimeo)

    def sndbuf(self):
        """
        Get socket option `sndbuf`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_sndbuf(self._as_parameter_)

    def set_sndbuf(self, sndbuf):
        """
        Set socket option `sndbuf`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_set_sndbuf(self._as_parameter_, sndbuf)

    def rcvbuf(self):
        """
        Get socket option `rcvbuf`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_rcvbuf(self._as_parameter_)

    def set_rcvbuf(self, rcvbuf):
        """
        Set socket option `rcvbuf`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_set_rcvbuf(self._as_parameter_, rcvbuf)

    def linger(self):
        """
        Get socket option `linger`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_linger(self._as_parameter_)

    def set_linger(self, linger):
        """
        Set socket option `linger`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_set_linger(self._as_parameter_, linger)

    def reconnect_ivl(self):
        """
        Get socket option `reconnect_ivl`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_reconnect_ivl(self._as_parameter_)

    def set_reconnect_ivl(self, reconnect_ivl):
        """
        Set socket option `reconnect_ivl`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_set_reconnect_ivl(self._as_parameter_, reconnect_ivl)

    def reconnect_ivl_max(self):
        """
        Get socket option `reconnect_ivl_max`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_reconnect_ivl_max(self._as_parameter_)

    def set_reconnect_ivl_max(self, reconnect_ivl_max):
        """
        Set socket option `reconnect_ivl_max`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_set_reconnect_ivl_max(self._as_parameter_, reconnect_ivl_max)

    def backlog(self):
        """
        Get socket option `backlog`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_backlog(self._as_parameter_)

    def set_backlog(self, backlog):
        """
        Set socket option `backlog`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_set_backlog(self._as_parameter_, backlog)

    def set_subscribe(self, subscribe):
        """
        Set socket option `subscribe`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_set_subscribe(self._as_parameter_, subscribe)

    def set_unsubscribe(self, unsubscribe):
        """
        Set socket option `unsubscribe`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_set_unsubscribe(self._as_parameter_, unsubscribe)

    def type(self):
        """
        Get socket option `type`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_type(self._as_parameter_)

    def rcvmore(self):
        """
        Get socket option `rcvmore`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_rcvmore(self._as_parameter_)

    def fd(self):
        """
        Get socket option `fd`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_fd(self._as_parameter_)

    def events(self):
        """
        Get socket option `events`.
Available from libzmq 2.0.0.
        """
        return lib.zsock_events(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zsock_test(verbose)


# zstr
lib.zstr_recv.restype = POINTER(c_char)
lib.zstr_recv.argtypes = [c_void_p]
lib.zstr_recvx.restype = c_int
lib.zstr_recvx.argtypes = [c_void_p, POINTER(c_char_p)]
lib.zstr_recv_compress.restype = POINTER(c_char)
lib.zstr_recv_compress.argtypes = [c_void_p]
lib.zstr_send.restype = c_int
lib.zstr_send.argtypes = [c_void_p, c_char_p]
lib.zstr_sendm.restype = c_int
lib.zstr_sendm.argtypes = [c_void_p, c_char_p]
lib.zstr_sendf.restype = c_int
lib.zstr_sendf.argtypes = [c_void_p, c_char_p]
lib.zstr_sendfm.restype = c_int
lib.zstr_sendfm.argtypes = [c_void_p, c_char_p]
lib.zstr_sendx.restype = c_int
lib.zstr_sendx.argtypes = [c_void_p, c_char_p]
lib.zstr_send_compress.restype = c_int
lib.zstr_send_compress.argtypes = [c_void_p, c_char_p]
lib.zstr_sendm_compress.restype = c_int
lib.zstr_sendm_compress.argtypes = [c_void_p, c_char_p]
lib.zstr_str.restype = POINTER(c_char)
lib.zstr_str.argtypes = [c_void_p]
lib.zstr_free.restype = None
lib.zstr_free.argtypes = [POINTER(c_char_p)]
lib.zstr_test.restype = None
lib.zstr_test.argtypes = [c_bool]

class Zstr(object):
    """
    sending and receiving strings
    """

    allow_destruct = False
    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def recv(source):
        """
        Receive C string from socket. Caller must free returned string using
zstr_free(). Returns NULL if the context is being terminated or the
process was interrupted.
        """
        return return_fresh_string(lib.zstr_recv(source))

    @staticmethod
    def recvx(source, string_p, *args):
        """
        Receive a series of strings (until NULL) from multipart data.
Each string is allocated and filled with string data; if there
are not enough frames, unallocated strings are set to NULL.
Returns -1 if the message could not be read, else returns the
number of strings filled, zero or more. Free each returned string
using zstr_free(). If not enough strings are provided, remaining
multipart frames in the message are dropped.
        """
        return lib.zstr_recvx(source, byref(c_char_p.from_param(string_p)), *args)

    @staticmethod
    def recv_compress(source):
        """
        De-compress and receive C string from socket, received as a message
with two frames: size of the uncompressed string, and the string itself.
Caller must free returned string using zstr_free(). Returns NULL if the
context is being terminated or the process was interrupted.
        """
        return return_fresh_string(lib.zstr_recv_compress(source))

    @staticmethod
    def send(dest, string):
        """
        Send a C string to a socket, as a frame. The string is sent without
trailing null byte; to read this you can use zstr_recv, or a similar
method that adds a null terminator on the received string. String
may be NULL, which is sent as "".
        """
        return lib.zstr_send(dest, string)

    @staticmethod
    def sendm(dest, string):
        """
        Send a C string to a socket, as zstr_send(), with a MORE flag, so that
you can send further strings in the same multi-part message.
        """
        return lib.zstr_sendm(dest, string)

    @staticmethod
    def sendf(dest, format, *args):
        """
        Send a formatted string to a socket. Note that you should NOT use
user-supplied strings in the format (they may contain '%' which
will create security holes).
        """
        return lib.zstr_sendf(dest, format, *args)

    @staticmethod
    def sendfm(dest, format, *args):
        """
        Send a formatted string to a socket, as for zstr_sendf(), with a
MORE flag, so that you can send further strings in the same multi-part
message.
        """
        return lib.zstr_sendfm(dest, format, *args)

    @staticmethod
    def sendx(dest, string, *args):
        """
        Send a series of strings (until NULL) as multipart data
Returns 0 if the strings could be sent OK, or -1 on error.
        """
        return lib.zstr_sendx(dest, string, *args)

    @staticmethod
    def send_compress(dest, string):
        """
        Compress and send a C string to a socket, as a message with two frames:
size of the uncompressed string, and the string itself. The string is
sent without trailing null byte; to read this you can use
zstr_recv_compress, or a similar method that de-compresses and adds a
null terminator on the received string.
        """
        return lib.zstr_send_compress(dest, string)

    @staticmethod
    def sendm_compress(dest, string):
        """
        Compress and send a C string to a socket, as zstr_send_compress(),
with a MORE flag, so that you can send further strings in the same
multi-part message.
        """
        return lib.zstr_sendm_compress(dest, string)

    @staticmethod
    def str(source):
        """
        Accepts a void pointer and returns a fresh character string. If source
is null, returns an empty string.
        """
        return return_fresh_string(lib.zstr_str(source))

    @staticmethod
    def free(string_p):
        """
        Free a provided string, and nullify the parent pointer. Safe to call on
a null pointer.
        """
        return lib.zstr_free(byref(c_char_p.from_param(string_p)))

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zstr_test(verbose)


# zsys
zsys_handler_fn = CFUNCTYPE(None, c_int)
lib.zsys_init.restype = c_void_p
lib.zsys_init.argtypes = []
lib.zsys_shutdown.restype = None
lib.zsys_shutdown.argtypes = []
lib.zsys_socket.restype = c_void_p
lib.zsys_socket.argtypes = [c_int, c_char_p, c_size_t]
lib.zsys_close.restype = c_int
lib.zsys_close.argtypes = [c_void_p, c_char_p, c_size_t]
lib.zsys_sockname.restype = c_char_p
lib.zsys_sockname.argtypes = [c_int]
lib.zsys_create_pipe.restype = zsock_p
lib.zsys_create_pipe.argtypes = [POINTER(zsock_p)]
lib.zsys_handler_set.restype = None
lib.zsys_handler_set.argtypes = [POINTER(zsys_handler_fn)]
lib.zsys_handler_reset.restype = None
lib.zsys_handler_reset.argtypes = []
lib.zsys_catch_interrupts.restype = None
lib.zsys_catch_interrupts.argtypes = []
lib.zsys_is_interrupted.restype = c_bool
lib.zsys_is_interrupted.argtypes = []
lib.zsys_set_interrupted.restype = None
lib.zsys_set_interrupted.argtypes = []
lib.zsys_file_exists.restype = c_bool
lib.zsys_file_exists.argtypes = [c_char_p]
lib.zsys_file_modified.restype = c_int
lib.zsys_file_modified.argtypes = [c_char_p]
lib.zsys_file_mode.restype = c_int
lib.zsys_file_mode.argtypes = [c_char_p]
lib.zsys_file_delete.restype = c_int
lib.zsys_file_delete.argtypes = [c_char_p]
lib.zsys_file_stable.restype = c_bool
lib.zsys_file_stable.argtypes = [c_char_p]
lib.zsys_dir_create.restype = c_int
lib.zsys_dir_create.argtypes = [c_char_p]
lib.zsys_dir_delete.restype = c_int
lib.zsys_dir_delete.argtypes = [c_char_p]
lib.zsys_dir_change.restype = c_int
lib.zsys_dir_change.argtypes = [c_char_p]
lib.zsys_file_mode_private.restype = None
lib.zsys_file_mode_private.argtypes = []
lib.zsys_file_mode_default.restype = None
lib.zsys_file_mode_default.argtypes = []
lib.zsys_version.restype = None
lib.zsys_version.argtypes = [POINTER(c_int), POINTER(c_int), POINTER(c_int)]
lib.zsys_sprintf_hint.restype = c_char_p
lib.zsys_sprintf_hint.argtypes = [c_int, c_char_p]
lib.zsys_sprintf.restype = c_char_p
lib.zsys_sprintf.argtypes = [c_char_p]
lib.zsys_vprintf.restype = c_char_p
lib.zsys_vprintf.argtypes = [c_char_p, va_list_p]
lib.zsys_udp_new.restype = socket_p
lib.zsys_udp_new.argtypes = [c_bool]
lib.zsys_udp_close.restype = c_int
lib.zsys_udp_close.argtypes = [socket_p]
lib.zsys_udp_send.restype = c_int
lib.zsys_udp_send.argtypes = [socket_p, zframe_p, c_void_p, c_int]
lib.zsys_udp_recv.restype = zframe_p
lib.zsys_udp_recv.argtypes = [socket_p, c_char_p, c_int]
lib.zsys_socket_error.restype = None
lib.zsys_socket_error.argtypes = [c_char_p]
lib.zsys_hostname.restype = c_char_p
lib.zsys_hostname.argtypes = []
lib.zsys_daemonize.restype = c_int
lib.zsys_daemonize.argtypes = [c_char_p]
lib.zsys_run_as.restype = c_int
lib.zsys_run_as.argtypes = [c_char_p, c_char_p, c_char_p]
lib.zsys_has_curve.restype = c_bool
lib.zsys_has_curve.argtypes = []
lib.zsys_set_io_threads.restype = None
lib.zsys_set_io_threads.argtypes = [c_size_t]
lib.zsys_set_thread_sched_policy.restype = None
lib.zsys_set_thread_sched_policy.argtypes = [c_int]
lib.zsys_set_thread_priority.restype = None
lib.zsys_set_thread_priority.argtypes = [c_int]
lib.zsys_set_thread_name_prefix.restype = None
lib.zsys_set_thread_name_prefix.argtypes = [c_int]
lib.zsys_thread_name_prefix.restype = c_int
lib.zsys_thread_name_prefix.argtypes = []
lib.zsys_set_thread_name_prefix_str.restype = None
lib.zsys_set_thread_name_prefix_str.argtypes = [c_char_p]
lib.zsys_thread_name_prefix_str.restype = c_char_p
lib.zsys_thread_name_prefix_str.argtypes = []
lib.zsys_thread_affinity_cpu_add.restype = None
lib.zsys_thread_affinity_cpu_add.argtypes = [c_int]
lib.zsys_thread_affinity_cpu_remove.restype = None
lib.zsys_thread_affinity_cpu_remove.argtypes = [c_int]
lib.zsys_set_max_sockets.restype = None
lib.zsys_set_max_sockets.argtypes = [c_size_t]
lib.zsys_socket_limit.restype = c_size_t
lib.zsys_socket_limit.argtypes = []
lib.zsys_set_max_msgsz.restype = None
lib.zsys_set_max_msgsz.argtypes = [c_int]
lib.zsys_max_msgsz.restype = c_int
lib.zsys_max_msgsz.argtypes = []
lib.zsys_set_zero_copy_recv.restype = None
lib.zsys_set_zero_copy_recv.argtypes = [c_int]
lib.zsys_zero_copy_recv.restype = c_int
lib.zsys_zero_copy_recv.argtypes = []
lib.zsys_set_file_stable_age_msec.restype = None
lib.zsys_set_file_stable_age_msec.argtypes = [msecs_p]
lib.zsys_file_stable_age_msec.restype = msecs_p
lib.zsys_file_stable_age_msec.argtypes = []
lib.zsys_set_linger.restype = None
lib.zsys_set_linger.argtypes = [c_size_t]
lib.zsys_set_sndhwm.restype = None
lib.zsys_set_sndhwm.argtypes = [c_size_t]
lib.zsys_set_rcvhwm.restype = None
lib.zsys_set_rcvhwm.argtypes = [c_size_t]
lib.zsys_set_pipehwm.restype = None
lib.zsys_set_pipehwm.argtypes = [c_size_t]
lib.zsys_pipehwm.restype = c_size_t
lib.zsys_pipehwm.argtypes = []
lib.zsys_set_ipv6.restype = None
lib.zsys_set_ipv6.argtypes = [c_int]
lib.zsys_ipv6.restype = c_int
lib.zsys_ipv6.argtypes = []
lib.zsys_ipv6_available.restype = c_bool
lib.zsys_ipv6_available.argtypes = []
lib.zsys_set_interface.restype = None
lib.zsys_set_interface.argtypes = [c_char_p]
lib.zsys_interface.restype = c_char_p
lib.zsys_interface.argtypes = []
lib.zsys_set_ipv6_address.restype = None
lib.zsys_set_ipv6_address.argtypes = [c_char_p]
lib.zsys_ipv6_address.restype = c_char_p
lib.zsys_ipv6_address.argtypes = []
lib.zsys_set_ipv6_mcast_address.restype = None
lib.zsys_set_ipv6_mcast_address.argtypes = [c_char_p]
lib.zsys_ipv6_mcast_address.restype = c_char_p
lib.zsys_ipv6_mcast_address.argtypes = []
lib.zsys_set_ipv4_mcast_address.restype = None
lib.zsys_set_ipv4_mcast_address.argtypes = [c_char_p]
lib.zsys_ipv4_mcast_address.restype = c_char_p
lib.zsys_ipv4_mcast_address.argtypes = []
lib.zsys_set_mcast_ttl.restype = None
lib.zsys_set_mcast_ttl.argtypes = [c_ubyte]
lib.zsys_mcast_ttl.restype = c_ubyte
lib.zsys_mcast_ttl.argtypes = []
lib.zsys_set_auto_use_fd.restype = None
lib.zsys_set_auto_use_fd.argtypes = [c_int]
lib.zsys_auto_use_fd.restype = c_int
lib.zsys_auto_use_fd.argtypes = []
lib.zsys_zprintf.restype = POINTER(c_char)
lib.zsys_zprintf.argtypes = [c_char_p, zhash_p]
lib.zsys_zprintf_error.restype = POINTER(c_char)
lib.zsys_zprintf_error.argtypes = [c_char_p, zhash_p]
lib.zsys_zplprintf.restype = POINTER(c_char)
lib.zsys_zplprintf.argtypes = [c_char_p, zconfig_p]
lib.zsys_zplprintf_error.restype = POINTER(c_char)
lib.zsys_zplprintf_error.argtypes = [c_char_p, zconfig_p]
lib.zsys_set_logident.restype = None
lib.zsys_set_logident.argtypes = [c_char_p]
lib.zsys_set_logstream.restype = None
lib.zsys_set_logstream.argtypes = [FILE_p]
lib.zsys_set_logsender.restype = None
lib.zsys_set_logsender.argtypes = [c_char_p]
lib.zsys_set_logsystem.restype = None
lib.zsys_set_logsystem.argtypes = [c_bool]
lib.zsys_error.restype = None
lib.zsys_error.argtypes = [c_char_p]
lib.zsys_warning.restype = None
lib.zsys_warning.argtypes = [c_char_p]
lib.zsys_notice.restype = None
lib.zsys_notice.argtypes = [c_char_p]
lib.zsys_info.restype = None
lib.zsys_info.argtypes = [c_char_p]
lib.zsys_debug.restype = None
lib.zsys_debug.argtypes = [c_char_p]
lib.zsys_test.restype = None
lib.zsys_test.argtypes = [c_bool]

class Zsys(object):
    """

    """

    allow_destruct = False
    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def init():
        """
        Initialize CZMQ zsys layer; this happens automatically when you create
a socket or an actor; however this call lets you force initialization
earlier, so e.g. logging is properly set-up before you start working.
Not threadsafe, so call only from main thread. Safe to call multiple
times. Returns global CZMQ context.
        """
        return c_void_p(lib.zsys_init())

    @staticmethod
    def shutdown():
        """
        Optionally shut down the CZMQ zsys layer; this normally happens automatically
when the process exits; however this call lets you force a shutdown
earlier, avoiding any potential problems with atexit() ordering, especially
with Windows dlls.
        """
        return lib.zsys_shutdown()

    @staticmethod
    def socket(type, filename, line_nbr):
        """
        Get a new ZMQ socket, automagically creating a ZMQ context if this is
the first time. Caller is responsible for destroying the ZMQ socket
before process exits, to avoid a ZMQ deadlock. Note: you should not use
this method in CZMQ apps, use zsock_new() instead.
*** This is for CZMQ internal use only and may change arbitrarily ***
        """
        return c_void_p(lib.zsys_socket(type, filename, line_nbr))

    @staticmethod
    def close(handle, filename, line_nbr):
        """
        Destroy/close a ZMQ socket. You should call this for every socket you
create using zsys_socket().
*** This is for CZMQ internal use only and may change arbitrarily ***
        """
        return lib.zsys_close(handle, filename, line_nbr)

    @staticmethod
    def sockname(socktype):
        """
        Return ZMQ socket name for socket type
*** This is for CZMQ internal use only and may change arbitrarily ***
        """
        return lib.zsys_sockname(socktype)

    @staticmethod
    def create_pipe(backend_p):
        """
        Create a pipe, which consists of two PAIR sockets connected over inproc.
The pipe is configured to use the zsys_pipehwm setting. Returns the
frontend socket successful, NULL if failed.
        """
        return Zsock(lib.zsys_create_pipe(byref(zsock_p.from_param(backend_p))), False)

    @staticmethod
    def handler_set(handler_fn):
        """
        Set interrupt handler; this saves the default handlers so that a
zsys_handler_reset () can restore them. If you call this multiple times
then the last handler will take affect. If handler_fn is NULL, disables
default SIGINT/SIGTERM handling in CZMQ.
        """
        return lib.zsys_handler_set(byref(zsys_handler_fn.from_param(handler_fn)))

    @staticmethod
    def handler_reset():
        """
        Reset interrupt handler, call this at exit if needed
        """
        return lib.zsys_handler_reset()

    @staticmethod
    def catch_interrupts():
        """
        Set default interrupt handler, so Ctrl-C or SIGTERM will set
zsys_interrupted. Idempotent; safe to call multiple times.
Can be suppressed by ZSYS_SIGHANDLER=false
*** This is for CZMQ internal use only and may change arbitrarily ***
        """
        return lib.zsys_catch_interrupts()

    @staticmethod
    def is_interrupted():
        """
        Check if default interrupt handler of Ctrl-C or SIGTERM was called.
Does not work if ZSYS_SIGHANDLER is false and code does not call
set interrupted on signal.
        """
        return lib.zsys_is_interrupted()

    @staticmethod
    def set_interrupted():
        """
        Set interrupted flag. This is done by default signal handler, however
this can be handy for language bindings or cases without default
signal handler.
        """
        return lib.zsys_set_interrupted()

    @staticmethod
    def file_exists(filename):
        """
        Return 1 if file exists, else zero
        """
        return lib.zsys_file_exists(filename)

    @staticmethod
    def file_modified(filename):
        """
        Return file modification time. Returns 0 if the file does not exist.
        """
        return lib.zsys_file_modified(filename)

    @staticmethod
    def file_mode(filename):
        """
        Return file mode; provides at least support for the POSIX S_ISREG(m)
and S_ISDIR(m) macros and the S_IRUSR and S_IWUSR bits, on all boxes.
Returns a mode_t cast to int, or -1 in case of error.
        """
        return lib.zsys_file_mode(filename)

    @staticmethod
    def file_delete(filename):
        """
        Delete file. Does not complain if the file is absent
        """
        return lib.zsys_file_delete(filename)

    @staticmethod
    def file_stable(filename):
        """
        Check if file is 'stable'
        """
        return lib.zsys_file_stable(filename)

    @staticmethod
    def dir_create(pathname, *args):
        """
        Create a file path if it doesn't exist. The file path is treated as
printf format.
        """
        return lib.zsys_dir_create(pathname, *args)

    @staticmethod
    def dir_delete(pathname, *args):
        """
        Remove a file path if empty; the pathname is treated as printf format.
        """
        return lib.zsys_dir_delete(pathname, *args)

    @staticmethod
    def dir_change(pathname):
        """
        Move to a specified working directory. Returns 0 if OK, -1 if this failed.
        """
        return lib.zsys_dir_change(pathname)

    @staticmethod
    def file_mode_private():
        """
        Set private file creation mode; all files created from here will be
readable/writable by the owner only.
        """
        return lib.zsys_file_mode_private()

    @staticmethod
    def file_mode_default():
        """
        Reset default file creation mode; all files created from here will use
process file mode defaults.
        """
        return lib.zsys_file_mode_default()

    @staticmethod
    def version(major, minor, patch):
        """
        Return the CZMQ version for run-time API detection; returns version
number into provided fields, providing reference isn't null in each case.
        """
        return lib.zsys_version(byref(c_int.from_param(major)), byref(c_int.from_param(minor)), byref(c_int.from_param(patch)))

    @staticmethod
    def sprintf_hint(hint, format, *args):
        """
        Format a string using printf formatting, returning a freshly allocated
buffer. If there was insufficient memory, returns NULL. Free the returned
string using zstr_free(). The hinted version allows one to optimize by using
a larger starting buffer size (known to/assumed by the developer) and so
avoid reallocations.
        """
        return lib.zsys_sprintf_hint(hint, format, *args)

    @staticmethod
    def sprintf(format, *args):
        """
        Format a string using printf formatting, returning a freshly allocated
buffer. If there was insufficient memory, returns NULL. Free the returned
string using zstr_free().
        """
        return lib.zsys_sprintf(format, *args)

    @staticmethod
    def vprintf(format, argptr):
        """
        Format a string with a va_list argument, returning a freshly allocated
buffer. If there was insufficient memory, returns NULL. Free the returned
string using zstr_free().
        """
        return lib.zsys_vprintf(format, argptr)

    @staticmethod
    def udp_new(routable):
        """
        Create UDP beacon socket; if the routable option is true, uses
multicast (not yet implemented), else uses broadcast. This method
and related ones might _eventually_ be moved to a zudp class.
*** This is for CZMQ internal use only and may change arbitrarily ***
        """
        return lib.zsys_udp_new(routable)

    @staticmethod
    def udp_close(handle):
        """
        Close a UDP socket
*** This is for CZMQ internal use only and may change arbitrarily ***
        """
        return lib.zsys_udp_close(handle)

    @staticmethod
    def udp_send(udpsock, frame, address, addrlen):
        """
        Send zframe to UDP socket, return -1 if sending failed due to
interface having disappeared (happens easily with WiFi)
*** This is for CZMQ internal use only and may change arbitrarily ***
        """
        return lib.zsys_udp_send(udpsock, frame, address, addrlen)

    @staticmethod
    def udp_recv(udpsock, peername, peerlen):
        """
        Receive zframe from UDP socket, and set address of peer that sent it
The peername must be a char [INET_ADDRSTRLEN] array if IPv6 is disabled or
NI_MAXHOST if it's enabled. Returns NULL when failing to get peer address.
*** This is for CZMQ internal use only and may change arbitrarily ***
        """
        return Zframe(lib.zsys_udp_recv(udpsock, peername, peerlen), False)

    @staticmethod
    def socket_error(reason):
        """
        Handle an I/O error on some socket operation; will report and die on
fatal errors, and continue silently on "try again" errors.
*** This is for CZMQ internal use only and may change arbitrarily ***
        """
        return lib.zsys_socket_error(reason)

    @staticmethod
    def hostname():
        """
        Return current host name, for use in public tcp:// endpoints. Caller gets
a freshly allocated string, should free it using zstr_free(). If the host
name is not resolvable, returns NULL.
        """
        return lib.zsys_hostname()

    @staticmethod
    def daemonize(workdir):
        """
        Move the current process into the background. The precise effect depends
on the operating system. On POSIX boxes, moves to a specified working
directory (if specified), closes all file handles, reopens stdin, stdout,
and stderr to the null device, and sets the process to ignore SIGHUP. On
Windows, does nothing. Returns 0 if OK, -1 if there was an error.
        """
        return lib.zsys_daemonize(workdir)

    @staticmethod
    def run_as(lockfile, group, user):
        """
        Drop the process ID into the lockfile, with exclusive lock, and switch
the process to the specified group and/or user. Any of the arguments
may be null, indicating a no-op. Returns 0 on success, -1 on failure.
Note if you combine this with zsys_daemonize, run after, not before
that method, or the lockfile will hold the wrong process ID.
        """
        return lib.zsys_run_as(lockfile, group, user)

    @staticmethod
    def has_curve():
        """
        Returns true if the underlying libzmq supports CURVE security.
Uses a heuristic probe according to the version of libzmq being used.
        """
        return lib.zsys_has_curve()

    @staticmethod
    def set_io_threads(io_threads):
        """
        Configure the number of I/O threads that ZeroMQ will use. A good
rule of thumb is one thread per gigabit of traffic in or out. The
default is 1, sufficient for most applications. If the environment
variable ZSYS_IO_THREADS is defined, that provides the default.
Note that this method is valid only before any socket is created.
        """
        return lib.zsys_set_io_threads(io_threads)

    @staticmethod
    def set_thread_sched_policy(policy):
        """
        Configure the scheduling policy of the ZMQ context thread pool.
Not available on Windows. See the sched_setscheduler man page or sched.h
for more information. If the environment variable ZSYS_THREAD_SCHED_POLICY
is defined, that provides the default.
Note that this method is valid only before any socket is created.
        """
        return lib.zsys_set_thread_sched_policy(policy)

    @staticmethod
    def set_thread_priority(priority):
        """
        Configure the scheduling priority of the ZMQ context thread pool.
Not available on Windows. See the sched_setscheduler man page or sched.h
for more information. If the environment variable ZSYS_THREAD_PRIORITY is
defined, that provides the default.
Note that this method is valid only before any socket is created.
        """
        return lib.zsys_set_thread_priority(priority)

    @staticmethod
    def set_thread_name_prefix(prefix):
        """
        Configure the numeric prefix to each thread created for the internal
context's thread pool. This option is only supported on Linux.
If the environment variable ZSYS_THREAD_NAME_PREFIX is defined, that
provides the default.
Note that this method is valid only before any socket is created.
        """
        return lib.zsys_set_thread_name_prefix(prefix)

    @staticmethod
    def thread_name_prefix():
        """
        Return thread name prefix.
        """
        return lib.zsys_thread_name_prefix()

    @staticmethod
    def set_thread_name_prefix_str(prefix):
        """
        Configure the numeric prefix to each thread created for the internal
context's thread pool. This option is only supported on Linux.
If the environment variable ZSYS_THREAD_NAME_PREFIX_STR is defined, that
provides the default.
Note that this method is valid only before any socket is created.
        """
        return lib.zsys_set_thread_name_prefix_str(prefix)

    @staticmethod
    def thread_name_prefix_str():
        """
        Return thread name prefix.
        """
        return lib.zsys_thread_name_prefix_str()

    @staticmethod
    def thread_affinity_cpu_add(cpu):
        """
        Adds a specific CPU to the affinity list of the ZMQ context thread pool.
This option is only supported on Linux.
Note that this method is valid only before any socket is created.
        """
        return lib.zsys_thread_affinity_cpu_add(cpu)

    @staticmethod
    def thread_affinity_cpu_remove(cpu):
        """
        Removes a specific CPU to the affinity list of the ZMQ context thread pool.
This option is only supported on Linux.
Note that this method is valid only before any socket is created.
        """
        return lib.zsys_thread_affinity_cpu_remove(cpu)

    @staticmethod
    def set_max_sockets(max_sockets):
        """
        Configure the number of sockets that ZeroMQ will allow. The default
is 1024. The actual limit depends on the system, and you can query it
by using zsys_socket_limit (). A value of zero means "maximum".
Note that this method is valid only before any socket is created.
        """
        return lib.zsys_set_max_sockets(max_sockets)

    @staticmethod
    def socket_limit():
        """
        Return maximum number of ZeroMQ sockets that the system will support.
        """
        return lib.zsys_socket_limit()

    @staticmethod
    def set_max_msgsz(max_msgsz):
        """
        Configure the maximum allowed size of a message sent.
The default is INT_MAX.
        """
        return lib.zsys_set_max_msgsz(max_msgsz)

    @staticmethod
    def max_msgsz():
        """
        Return maximum message size.
        """
        return lib.zsys_max_msgsz()

    @staticmethod
    def set_zero_copy_recv(zero_copy):
        """
        Configure whether to use zero copy strategy in libzmq. If the environment
variable ZSYS_ZERO_COPY_RECV is defined, that provides the default.
Otherwise the default is 1.
        """
        return lib.zsys_set_zero_copy_recv(zero_copy)

    @staticmethod
    def zero_copy_recv():
        """
        Return ZMQ_ZERO_COPY_RECV option.
        """
        return lib.zsys_zero_copy_recv()

    @staticmethod
    def set_file_stable_age_msec(file_stable_age_msec):
        """
        Configure the threshold value of filesystem object age per st_mtime
that should elapse until we consider that object "stable" at the
current zclock_time() moment.
The default is S_DEFAULT_ZSYS_FILE_STABLE_AGE_MSEC defined in zsys.c
which generally depends on host OS, with fallback value of 5000.
        """
        return lib.zsys_set_file_stable_age_msec(file_stable_age_msec)

    @staticmethod
    def file_stable_age_msec():
        """
        Return current threshold value of file stable age in msec.
This can be used in code that chooses to wait for this timeout
before testing if a filesystem object is "stable" or not.
        """
        return lib.zsys_file_stable_age_msec()

    @staticmethod
    def set_linger(linger):
        """
        Configure the default linger timeout in msecs for new zsock instances.
You can also set this separately on each zsock_t instance. The default
linger time is zero, i.e. any pending messages will be dropped. If the
environment variable ZSYS_LINGER is defined, that provides the default.
Note that process exit will typically be delayed by the linger time.
        """
        return lib.zsys_set_linger(linger)

    @staticmethod
    def set_sndhwm(sndhwm):
        """
        Configure the default outgoing pipe limit (HWM) for new zsock instances.
You can also set this separately on each zsock_t instance. The default
HWM is 1,000, on all versions of ZeroMQ. If the environment variable
ZSYS_SNDHWM is defined, that provides the default. Note that a value of
zero means no limit, i.e. infinite memory consumption.
        """
        return lib.zsys_set_sndhwm(sndhwm)

    @staticmethod
    def set_rcvhwm(rcvhwm):
        """
        Configure the default incoming pipe limit (HWM) for new zsock instances.
You can also set this separately on each zsock_t instance. The default
HWM is 1,000, on all versions of ZeroMQ. If the environment variable
ZSYS_RCVHWM is defined, that provides the default. Note that a value of
zero means no limit, i.e. infinite memory consumption.
        """
        return lib.zsys_set_rcvhwm(rcvhwm)

    @staticmethod
    def set_pipehwm(pipehwm):
        """
        Configure the default HWM for zactor internal pipes; this is set on both
ends of the pipe, for outgoing messages only (sndhwm). The default HWM is
1,000, on all versions of ZeroMQ. If the environment var ZSYS_ACTORHWM is
defined, that provides the default. Note that a value of zero means no
limit, i.e. infinite memory consumption.
        """
        return lib.zsys_set_pipehwm(pipehwm)

    @staticmethod
    def pipehwm():
        """
        Return the HWM for zactor internal pipes.
        """
        return lib.zsys_pipehwm()

    @staticmethod
    def set_ipv6(ipv6):
        """
        Configure use of IPv6 for new zsock instances. By default sockets accept
and make only IPv4 connections. When you enable IPv6, sockets will accept
and connect to both IPv4 and IPv6 peers. You can override the setting on
each zsock_t instance. The default is IPv4 only (ipv6 set to 0). If the
environment variable ZSYS_IPV6 is defined (as 1 or 0), this provides the
default. Note: has no effect on ZMQ v2.
        """
        return lib.zsys_set_ipv6(ipv6)

    @staticmethod
    def ipv6():
        """
        Return use of IPv6 for zsock instances.
        """
        return lib.zsys_ipv6()

    @staticmethod
    def ipv6_available():
        """
        Test if ipv6 is available on the system. Return true if available.
The only way to reliably check is to actually open a socket and
try to bind it. (ported from libzmq)
        """
        return lib.zsys_ipv6_available()

    @staticmethod
    def set_interface(value):
        """
        Set network interface name to use for broadcasts, particularly zbeacon.
This lets the interface be configured for test environments where required.
For example, on Mac OS X, zbeacon cannot bind to *************** which is
the default when there is no specified interface. If the environment
variable ZSYS_INTERFACE is set, use that as the default interface name.
Setting the interface to "*" means "use all available interfaces".
        """
        return lib.zsys_set_interface(value)

    @staticmethod
    def interface():
        """
        Return network interface to use for broadcasts, or "" if none was set.
        """
        return lib.zsys_interface()

    @staticmethod
    def set_ipv6_address(value):
        """
        Set IPv6 address to use zbeacon socket, particularly for receiving zbeacon.
This needs to be set IPv6 is enabled as IPv6 can have multiple addresses
on a given interface. If the environment variable ZSYS_IPV6_ADDRESS is set,
use that as the default IPv6 address.
        """
        return lib.zsys_set_ipv6_address(value)

    @staticmethod
    def ipv6_address():
        """
        Return IPv6 address to use for zbeacon reception, or "" if none was set.
        """
        return lib.zsys_ipv6_address()

    @staticmethod
    def set_ipv6_mcast_address(value):
        """
        Set IPv6 milticast address to use for sending zbeacon messages. This needs
to be set if IPv6 is enabled. If the environment variable
ZSYS_IPV6_MCAST_ADDRESS is set, use that as the default IPv6 multicast
address.
        """
        return lib.zsys_set_ipv6_mcast_address(value)

    @staticmethod
    def ipv6_mcast_address():
        """
        Return IPv6 multicast address to use for sending zbeacon, or "" if none was
set.
        """
        return lib.zsys_ipv6_mcast_address()

    @staticmethod
    def set_ipv4_mcast_address(value):
        """
        Set IPv4 multicast address to use for sending zbeacon messages. By default
IPv4 multicast is NOT used. If the environment variable
ZSYS_IPV4_MCAST_ADDRESS is set, use that as the default IPv4 multicast
address. Calling this function or setting ZSYS_IPV4_MCAST_ADDRESS
will enable IPv4 zbeacon messages.
        """
        return lib.zsys_set_ipv4_mcast_address(value)

    @staticmethod
    def ipv4_mcast_address():
        """
        Return IPv4 multicast address to use for sending zbeacon, or NULL if none was
set.
        """
        return lib.zsys_ipv4_mcast_address()

    @staticmethod
    def set_mcast_ttl(value):
        """
        Set multicast TTL default is 1
        """
        return lib.zsys_set_mcast_ttl(value)

    @staticmethod
    def mcast_ttl():
        """
        Get multicast TTL
        """
        return lib.zsys_mcast_ttl()

    @staticmethod
    def set_auto_use_fd(auto_use_fd):
        """
        Configure the automatic use of pre-allocated FDs when creating new sockets.
If 0 (default), nothing will happen. Else, when a new socket is bound, the
system API will be used to check if an existing pre-allocated FD with a
matching port (if TCP) or path (if IPC) exists, and if it does it will be
set via the ZMQ_USE_FD socket option so that the library will use it
instead of creating a new socket.
        """
        return lib.zsys_set_auto_use_fd(auto_use_fd)

    @staticmethod
    def auto_use_fd():
        """
        Return use of automatic pre-allocated FDs for zsock instances.
        """
        return lib.zsys_auto_use_fd()

    @staticmethod
    def zprintf(format, args):
        """
        Print formatted string. Format is specified by variable names
in Python-like format style

"%(KEY)s=%(VALUE)s", KEY=key, VALUE=value
become
"key=value"

Returns freshly allocated string or NULL in a case of error.
Not enough memory, invalid format specifier, name not in args
        """
        return return_fresh_string(lib.zsys_zprintf(format, args))

    @staticmethod
    def zprintf_error(format, args):
        """
        Return error string for given format/args combination.
        """
        return return_fresh_string(lib.zsys_zprintf_error(format, args))

    @staticmethod
    def zplprintf(format, args):
        """
        Print formatted string. Format is specified by variable names
in Python-like format style

"%(KEY)s=%(VALUE)s", KEY=key, VALUE=value
become
"key=value"

Returns freshly allocated string or NULL in a case of error.
Not enough memory, invalid format specifier, name not in args
        """
        return return_fresh_string(lib.zsys_zplprintf(format, args))

    @staticmethod
    def zplprintf_error(format, args):
        """
        Return error string for given format/args combination.
        """
        return return_fresh_string(lib.zsys_zplprintf_error(format, args))

    @staticmethod
    def set_logident(value):
        """
        Set log identity, which is a string that prefixes all log messages sent
by this process. The log identity defaults to the environment variable
ZSYS_LOGIDENT, if that is set.
        """
        return lib.zsys_set_logident(value)

    @staticmethod
    def set_logstream(stream):
        """
        Set stream to receive log traffic. By default, log traffic is sent to
stdout. If you set the stream to NULL, no stream will receive the log
traffic (it may still be sent to the system facility).
        """
        return lib.zsys_set_logstream(coerce_py_file(stream))

    @staticmethod
    def set_logsender(endpoint):
        """
        Sends log output to a PUB socket bound to the specified endpoint. To
collect such log output, create a SUB socket, subscribe to the traffic
you care about, and connect to the endpoint. Log traffic is sent as a
single string frame, in the same format as when sent to stdout. The
log system supports a single sender; multiple calls to this method will
bind the same sender to multiple endpoints. To disable the sender, call
this method with a null argument.
        """
        return lib.zsys_set_logsender(endpoint)

    @staticmethod
    def set_logsystem(logsystem):
        """
        Enable or disable logging to the system facility (syslog on POSIX boxes,
event log on Windows). By default this is disabled.
        """
        return lib.zsys_set_logsystem(logsystem)

    @staticmethod
    def error(format, *args):
        """
        Log error condition - highest priority
        """
        return lib.zsys_error(format, *args)

    @staticmethod
    def warning(format, *args):
        """
        Log warning condition - high priority
        """
        return lib.zsys_warning(format, *args)

    @staticmethod
    def notice(format, *args):
        """
        Log normal, but significant, condition - normal priority
        """
        return lib.zsys_notice(format, *args)

    @staticmethod
    def info(format, *args):
        """
        Log informational message - low priority
        """
        return lib.zsys_info(format, *args)

    @staticmethod
    def debug(format, *args):
        """
        Log debug-level message - lowest priority
        """
        return lib.zsys_debug(format, *args)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zsys_test(verbose)


# ztimerset
ztimerset_fn = CFUNCTYPE(None, c_int, c_void_p)
lib.ztimerset_new.restype = ztimerset_p
lib.ztimerset_new.argtypes = []
lib.ztimerset_destroy.restype = None
lib.ztimerset_destroy.argtypes = [POINTER(ztimerset_p)]
lib.ztimerset_add.restype = c_int
lib.ztimerset_add.argtypes = [ztimerset_p, c_size_t, ztimerset_fn, c_void_p]
lib.ztimerset_cancel.restype = c_int
lib.ztimerset_cancel.argtypes = [ztimerset_p, c_int]
lib.ztimerset_set_interval.restype = c_int
lib.ztimerset_set_interval.argtypes = [ztimerset_p, c_int, c_size_t]
lib.ztimerset_reset.restype = c_int
lib.ztimerset_reset.argtypes = [ztimerset_p, c_int]
lib.ztimerset_timeout.restype = c_int
lib.ztimerset_timeout.argtypes = [ztimerset_p]
lib.ztimerset_execute.restype = c_int
lib.ztimerset_execute.argtypes = [ztimerset_p]
lib.ztimerset_test.restype = None
lib.ztimerset_test.argtypes = [c_bool]

class Ztimerset(object):
    """
    timer set
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create new timer set.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], ztimerset_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is ztimerset_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.ztimerset_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a timer set
        """
        if self.allow_destruct:
            lib.ztimerset_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def add(self, interval, handler, arg):
        """
        Add a timer to the set. Returns timer id if OK, -1 on failure.
        """
        return lib.ztimerset_add(self._as_parameter_, interval, handler, arg)

    def cancel(self, timer_id):
        """
        Cancel a timer. Returns 0 if OK, -1 on failure.
        """
        return lib.ztimerset_cancel(self._as_parameter_, timer_id)

    def set_interval(self, timer_id, interval):
        """
        Set timer interval. Returns 0 if OK, -1 on failure.
This method is slow, canceling the timer and adding a new one yield better performance.
        """
        return lib.ztimerset_set_interval(self._as_parameter_, timer_id, interval)

    def reset(self, timer_id):
        """
        Reset timer to start interval counting from current time. Returns 0 if OK, -1 on failure.
This method is slow, canceling the timer and adding a new one yield better performance.
        """
        return lib.ztimerset_reset(self._as_parameter_, timer_id)

    def timeout(self):
        """
        Return the time until the next interval.
Should be used as timeout parameter for the zpoller wait method.
The timeout is in msec.
        """
        return lib.ztimerset_timeout(self._as_parameter_)

    def execute(self):
        """
        Invoke callback function of all timers which their interval has elapsed.
Should be call after zpoller wait method.
Returns 0 if OK, -1 on failure.
        """
        return lib.ztimerset_execute(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.ztimerset_test(verbose)


# ztrie
ztrie_destroy_data_fn = CFUNCTYPE(None, POINTER(c_void_p))
lib.ztrie_new.restype = ztrie_p
lib.ztrie_new.argtypes = [char_p]
lib.ztrie_destroy.restype = None
lib.ztrie_destroy.argtypes = [POINTER(ztrie_p)]
lib.ztrie_insert_route.restype = c_int
lib.ztrie_insert_route.argtypes = [ztrie_p, c_char_p, c_void_p, ztrie_destroy_data_fn]
lib.ztrie_remove_route.restype = c_int
lib.ztrie_remove_route.argtypes = [ztrie_p, c_char_p]
lib.ztrie_matches.restype = c_bool
lib.ztrie_matches.argtypes = [ztrie_p, c_char_p]
lib.ztrie_hit_data.restype = c_void_p
lib.ztrie_hit_data.argtypes = [ztrie_p]
lib.ztrie_hit_parameter_count.restype = c_size_t
lib.ztrie_hit_parameter_count.argtypes = [ztrie_p]
lib.ztrie_hit_parameters.restype = zhashx_p
lib.ztrie_hit_parameters.argtypes = [ztrie_p]
lib.ztrie_hit_asterisk_match.restype = c_char_p
lib.ztrie_hit_asterisk_match.argtypes = [ztrie_p]
lib.ztrie_print.restype = None
lib.ztrie_print.argtypes = [ztrie_p]
lib.ztrie_test.restype = None
lib.ztrie_test.argtypes = [c_bool]

class Ztrie(object):
    """
    simple trie for tokenizable strings
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Creates a new ztrie.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], ztrie_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is ztrie_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 1)
            self._as_parameter_ = lib.ztrie_new(args[0]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy the ztrie.
        """
        if self.allow_destruct:
            lib.ztrie_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def insert_route(self, path, data, destroy_data_fn):
        """
        Inserts a new route into the tree and attaches the data. Returns -1
if the route already exists, otherwise 0. This method takes ownership of
the provided data if a destroy_data_fn is provided.
        """
        return lib.ztrie_insert_route(self._as_parameter_, path, data, destroy_data_fn)

    def remove_route(self, path):
        """
        Removes a route from the trie and destroys its data. Returns -1 if the
route does not exists, otherwise 0.
the start of the list call zlist_first (). Advances the cursor.
        """
        return lib.ztrie_remove_route(self._as_parameter_, path)

    def matches(self, path):
        """
        Returns true if the path matches a route in the tree, otherwise false.
        """
        return lib.ztrie_matches(self._as_parameter_, path)

    def hit_data(self):
        """
        Returns the data of a matched route from last ztrie_matches. If the path
did not match, returns NULL. Do not delete the data as it's owned by
ztrie.
        """
        return c_void_p(lib.ztrie_hit_data(self._as_parameter_))

    def hit_parameter_count(self):
        """
        Returns the count of parameters that a matched route has.
        """
        return lib.ztrie_hit_parameter_count(self._as_parameter_)

    def hit_parameters(self):
        """
        Returns the parameters of a matched route with named regexes from last
ztrie_matches. If the path did not match or the route did not contain any
named regexes, returns NULL.
        """
        return Zhashx(lib.ztrie_hit_parameters(self._as_parameter_), False)

    def hit_asterisk_match(self):
        """
        Returns the asterisk matched part of a route, if there has been no match
or no asterisk match, returns NULL.
        """
        return lib.ztrie_hit_asterisk_match(self._as_parameter_)

    def print(self):
        """
        Print the trie
        """
        return lib.ztrie_print(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.ztrie_test(verbose)


# zuuid
lib.zuuid_new.restype = zuuid_p
lib.zuuid_new.argtypes = []
lib.zuuid_destroy.restype = None
lib.zuuid_destroy.argtypes = [POINTER(zuuid_p)]
lib.zuuid_new_from.restype = zuuid_p
lib.zuuid_new_from.argtypes = [c_void_p]
lib.zuuid_set.restype = None
lib.zuuid_set.argtypes = [zuuid_p, c_void_p]
lib.zuuid_set_str.restype = c_int
lib.zuuid_set_str.argtypes = [zuuid_p, c_char_p]
lib.zuuid_data.restype = c_void_p
lib.zuuid_data.argtypes = [zuuid_p]
lib.zuuid_size.restype = c_size_t
lib.zuuid_size.argtypes = [zuuid_p]
lib.zuuid_str.restype = c_char_p
lib.zuuid_str.argtypes = [zuuid_p]
lib.zuuid_str_canonical.restype = c_char_p
lib.zuuid_str_canonical.argtypes = [zuuid_p]
lib.zuuid_export.restype = None
lib.zuuid_export.argtypes = [zuuid_p, c_void_p]
lib.zuuid_eq.restype = c_bool
lib.zuuid_eq.argtypes = [zuuid_p, c_void_p]
lib.zuuid_neq.restype = c_bool
lib.zuuid_neq.argtypes = [zuuid_p, c_void_p]
lib.zuuid_dup.restype = zuuid_p
lib.zuuid_dup.argtypes = [zuuid_p]
lib.zuuid_test.restype = None
lib.zuuid_test.argtypes = [c_bool]

class Zuuid(object):
    """
    UUID support class
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new UUID object.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zuuid_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zuuid_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zuuid_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy a specified UUID object.
        """
        if self.allow_destruct:
            lib.zuuid_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def new_from(source):
        """
        Create UUID object from supplied ZUUID_LEN-octet value.
        """
        return Zuuid(lib.zuuid_new_from(source), True)

    def set(self, source):
        """
        Set UUID to new supplied ZUUID_LEN-octet value.
        """
        return lib.zuuid_set(self._as_parameter_, source)

    def set_str(self, source):
        """
        Set UUID to new supplied string value skipping '-' and '{' '}'
optional delimiters. Return 0 if OK, else returns -1.
        """
        return lib.zuuid_set_str(self._as_parameter_, source)

    def data(self):
        """
        Return UUID binary data.
        """
        return lib.zuuid_data(self._as_parameter_)

    def size(self):
        """
        Return UUID binary size
        """
        return lib.zuuid_size(self._as_parameter_)

    def str(self):
        """
        Returns UUID as string
        """
        return lib.zuuid_str(self._as_parameter_)

    def str_canonical(self):
        """
        Return UUID in the canonical string format: 8-4-4-4-12, in lower
case. Caller does not modify or free returned value. See
http://en.wikipedia.org/wiki/Universally_unique_identifier
        """
        return lib.zuuid_str_canonical(self._as_parameter_)

    def export(self, target):
        """
        Store UUID blob in target array
        """
        return lib.zuuid_export(self._as_parameter_, target)

    def eq(self, compare):
        """
        Check if UUID is same as supplied value
        """
        return lib.zuuid_eq(self._as_parameter_, compare)

    def neq(self, compare):
        """
        Check if UUID is different from supplied value
        """
        return lib.zuuid_neq(self._as_parameter_, compare)

    def dup(self):
        """
        Make copy of UUID object; if uuid is null, or memory was exhausted,
returns null.
        """
        return Zuuid(lib.zuuid_dup(self._as_parameter_), False)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zuuid_test(verbose)


# zhttp_client
lib.zhttp_client_new.restype = zhttp_client_p
lib.zhttp_client_new.argtypes = [c_bool]
lib.zhttp_client_destroy.restype = None
lib.zhttp_client_destroy.argtypes = [POINTER(zhttp_client_p)]
lib.zhttp_client_test.restype = None
lib.zhttp_client_test.argtypes = [c_bool]

class ZhttpClient(object):
    """
    Http client, allowing multiple requests simultaneously and integrate easily with zpoller.
Use zhttp_request class to create and send the request.
Use zhttp_response class to receive the response.
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new http client
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zhttp_client_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zhttp_client_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 1)
            self._as_parameter_ = lib.zhttp_client_new(args[0]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy an http client
        """
        if self.allow_destruct:
            lib.zhttp_client_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zhttp_client_test(verbose)


# zhttp_server
lib.zhttp_server_new.restype = zhttp_server_p
lib.zhttp_server_new.argtypes = [zhttp_server_options_p]
lib.zhttp_server_destroy.restype = None
lib.zhttp_server_destroy.argtypes = [POINTER(zhttp_server_p)]
lib.zhttp_server_port.restype = c_int
lib.zhttp_server_port.argtypes = [zhttp_server_p]
lib.zhttp_server_test.restype = None
lib.zhttp_server_test.argtypes = [c_bool]

class ZhttpServer(object):
    """
    Simple http server.
To start handling requests:
1. Create a dealer socket
2. Connect the socket to the server backend address provided in the options.
3. Create a zhttp_request.
4. Call zhttp_request_recv to accept a new request.
5. Call zhttp_response_send to send a response.

You can connect as many dealers as you want.
The Server is using simple dealer socket to route the requests.

NOTE: when using libmicrohttpd << 0.9.34 the application might experience
high CPU usage due to the lack of MHD_suspend_connection and
MHD_resume_connection APIs. It is recommended to use this class only with
libmicrohttpd at least 0.9.34 in production.
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new http server
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zhttp_server_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zhttp_server_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 1)
            self._as_parameter_ = lib.zhttp_server_new(args[0]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy an http server
        """
        if self.allow_destruct:
            lib.zhttp_server_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def port(self):
        """
        Return the port the server is listening on.
        """
        return lib.zhttp_server_port(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zhttp_server_test(verbose)


# zhttp_server_options
lib.zhttp_server_options_new.restype = zhttp_server_options_p
lib.zhttp_server_options_new.argtypes = []
lib.zhttp_server_options_destroy.restype = None
lib.zhttp_server_options_destroy.argtypes = [POINTER(zhttp_server_options_p)]
lib.zhttp_server_options_from_config.restype = zhttp_server_options_p
lib.zhttp_server_options_from_config.argtypes = [zconfig_p]
lib.zhttp_server_options_port.restype = c_int
lib.zhttp_server_options_port.argtypes = [zhttp_server_options_p]
lib.zhttp_server_options_set_port.restype = None
lib.zhttp_server_options_set_port.argtypes = [zhttp_server_options_p, c_int]
lib.zhttp_server_options_backend_address.restype = c_char_p
lib.zhttp_server_options_backend_address.argtypes = [zhttp_server_options_p]
lib.zhttp_server_options_set_backend_address.restype = None
lib.zhttp_server_options_set_backend_address.argtypes = [zhttp_server_options_p, c_char_p]
lib.zhttp_server_options_test.restype = None
lib.zhttp_server_options_test.argtypes = [c_bool]

class ZhttpServerOptions(object):
    """
    zhttp server.
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new zhttp_server_options.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zhttp_server_options_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zhttp_server_options_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zhttp_server_options_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy the zhttp_server_options.
        """
        if self.allow_destruct:
            lib.zhttp_server_options_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def from_config(config):
        """
        Create options from config tree.
        """
        return ZhttpServerOptions(lib.zhttp_server_options_from_config(config), True)

    def port(self):
        """
        Get the server listening port.
        """
        return lib.zhttp_server_options_port(self._as_parameter_)

    def set_port(self, port):
        """
        Set the server listening port
        """
        return lib.zhttp_server_options_set_port(self._as_parameter_, port)

    def backend_address(self):
        """
        Get the address sockets should connect to in order to receive requests.
        """
        return lib.zhttp_server_options_backend_address(self._as_parameter_)

    def set_backend_address(self, address):
        """
        Set the address sockets should connect to in order to receive requests.
        """
        return lib.zhttp_server_options_set_backend_address(self._as_parameter_, address)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zhttp_server_options_test(verbose)


# zhttp_request
lib.zhttp_request_new.restype = zhttp_request_p
lib.zhttp_request_new.argtypes = []
lib.zhttp_request_destroy.restype = None
lib.zhttp_request_destroy.argtypes = [POINTER(zhttp_request_p)]
lib.zhttp_request_recv.restype = c_void_p
lib.zhttp_request_recv.argtypes = [zhttp_request_p, zsock_p]
lib.zhttp_request_send.restype = c_int
lib.zhttp_request_send.argtypes = [zhttp_request_p, zhttp_client_p, c_int, c_void_p, c_void_p]
lib.zhttp_request_method.restype = c_char_p
lib.zhttp_request_method.argtypes = [zhttp_request_p]
lib.zhttp_request_set_method.restype = None
lib.zhttp_request_set_method.argtypes = [zhttp_request_p, c_char_p]
lib.zhttp_request_url.restype = c_char_p
lib.zhttp_request_url.argtypes = [zhttp_request_p]
lib.zhttp_request_set_url.restype = None
lib.zhttp_request_set_url.argtypes = [zhttp_request_p, c_char_p]
lib.zhttp_request_content_type.restype = c_char_p
lib.zhttp_request_content_type.argtypes = [zhttp_request_p]
lib.zhttp_request_set_content_type.restype = None
lib.zhttp_request_set_content_type.argtypes = [zhttp_request_p, c_char_p]
lib.zhttp_request_content_length.restype = c_size_t
lib.zhttp_request_content_length.argtypes = [zhttp_request_p]
lib.zhttp_request_headers.restype = zhash_p
lib.zhttp_request_headers.argtypes = [zhttp_request_p]
lib.zhttp_request_content.restype = c_char_p
lib.zhttp_request_content.argtypes = [zhttp_request_p]
lib.zhttp_request_get_content.restype = POINTER(c_char)
lib.zhttp_request_get_content.argtypes = [zhttp_request_p]
lib.zhttp_request_set_content.restype = None
lib.zhttp_request_set_content.argtypes = [zhttp_request_p, POINTER(c_char_p)]
lib.zhttp_request_set_content_const.restype = None
lib.zhttp_request_set_content_const.argtypes = [zhttp_request_p, c_char_p]
lib.zhttp_request_reset_content.restype = None
lib.zhttp_request_reset_content.argtypes = [zhttp_request_p]
lib.zhttp_request_set_username.restype = None
lib.zhttp_request_set_username.argtypes = [zhttp_request_p, c_char_p]
lib.zhttp_request_set_password.restype = None
lib.zhttp_request_set_password.argtypes = [zhttp_request_p, c_char_p]
lib.zhttp_request_match.restype = c_bool
lib.zhttp_request_match.argtypes = [zhttp_request_p, c_char_p, c_char_p]
lib.zhttp_request_test.restype = None
lib.zhttp_request_test.argtypes = [c_bool]

class ZhttpRequest(object):
    """
    Http request that can be received from zhttp_server or sent to zhttp_client.
Class can be reused between send & recv calls.
Headers and Content is being destroyed after every send call.
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new http request.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zhttp_request_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zhttp_request_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zhttp_request_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy an http request.
        """
        if self.allow_destruct:
            lib.zhttp_request_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def recv(self, sock):
        """
        Receive a new request from zhttp_server.
Return the underlying connection if successful, to be used when calling zhttp_response_send.
        """
        return c_void_p(lib.zhttp_request_recv(self._as_parameter_, sock))

    def send(self, client, timeout, arg, arg2):
        """
        Send a request to zhttp_client.
Url and the request path will be concatenated.
This behavior is useful for url rewrite and reverse proxy.

Send also allow two user provided arguments which will be returned with the response.
The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an arg.
        """
        return lib.zhttp_request_send(self._as_parameter_, client, timeout, arg, arg2)

    def method(self):
        """
        Get the request method
        """
        return lib.zhttp_request_method(self._as_parameter_)

    def set_method(self, method):
        """
        Set the request method
        """
        return lib.zhttp_request_set_method(self._as_parameter_, method)

    def url(self):
        """
        Get the request url.
When receiving a request from http server this is only the path part of the url.
        """
        return lib.zhttp_request_url(self._as_parameter_)

    def set_url(self, url):
        """
        Set the request url
When sending a request to http client this should be full url.
        """
        return lib.zhttp_request_set_url(self._as_parameter_, url)

    def content_type(self):
        """
        Get the request content type
        """
        return lib.zhttp_request_content_type(self._as_parameter_)

    def set_content_type(self, content_type):
        """
        Set the request content type
        """
        return lib.zhttp_request_set_content_type(self._as_parameter_, content_type)

    def content_length(self):
        """
        Get the content length of the request
        """
        return lib.zhttp_request_content_length(self._as_parameter_)

    def headers(self):
        """
        Get the headers of the request
        """
        return Zhash(lib.zhttp_request_headers(self._as_parameter_), False)

    def content(self):
        """
        Get the content of the request.
        """
        return lib.zhttp_request_content(self._as_parameter_)

    def get_content(self):
        """
        Get the content of the request.
        """
        return return_fresh_string(lib.zhttp_request_get_content(self._as_parameter_))

    def set_content(self, content):
        """
        Set the content of the request.
Content must by dynamically allocated string.
Takes ownership of the content.
        """
        return lib.zhttp_request_set_content(self._as_parameter_, byref(c_char_p.from_param(content)))

    def set_content_const(self, content):
        """
        Set the content of the request..
The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
        """
        return lib.zhttp_request_set_content_const(self._as_parameter_, content)

    def reset_content(self):
        """
        Set the content to NULL
        """
        return lib.zhttp_request_reset_content(self._as_parameter_)

    def set_username(self, username):
        """
        Set the request username
        """
        return lib.zhttp_request_set_username(self._as_parameter_, username)

    def set_password(self, password):
        """
        Set the request password
        """
        return lib.zhttp_request_set_password(self._as_parameter_, password)

    def match(self, method, path, *args):
        """
        Match the path of the request.
Support wildcards with '%s' symbol inside the match string.
Matching wildcards until the next '/', '?' or '\0'.
On successful match the variadic arguments will be filled with the matching strings.
On successful match the method is modifying the url field and break it into substrings.
If you need to use the url, do it before matching or take a copy.

User must not free the variadic arguments as they are part of the url.

To use the percent symbol, just double it, e.g "%%something".

Example:
if (zhttp_request_match (request, "POST", "/send/%s/%s", &name, &id))
        """
        return lib.zhttp_request_match(self._as_parameter_, method, path, *args)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zhttp_request_test(verbose)


# zhttp_response
lib.zhttp_response_new.restype = zhttp_response_p
lib.zhttp_response_new.argtypes = []
lib.zhttp_response_destroy.restype = None
lib.zhttp_response_destroy.argtypes = [POINTER(zhttp_response_p)]
lib.zhttp_response_send.restype = c_int
lib.zhttp_response_send.argtypes = [zhttp_response_p, zsock_p, POINTER(c_void_p)]
lib.zhttp_response_recv.restype = c_int
lib.zhttp_response_recv.argtypes = [zhttp_response_p, zhttp_client_p, POINTER(c_void_p), POINTER(c_void_p)]
lib.zhttp_response_content_type.restype = c_char_p
lib.zhttp_response_content_type.argtypes = [zhttp_response_p]
lib.zhttp_response_set_content_type.restype = None
lib.zhttp_response_set_content_type.argtypes = [zhttp_response_p, c_char_p]
lib.zhttp_response_status_code.restype = c_int
lib.zhttp_response_status_code.argtypes = [zhttp_response_p]
lib.zhttp_response_set_status_code.restype = None
lib.zhttp_response_set_status_code.argtypes = [zhttp_response_p, c_int]
lib.zhttp_response_headers.restype = zhash_p
lib.zhttp_response_headers.argtypes = [zhttp_response_p]
lib.zhttp_response_content_length.restype = c_size_t
lib.zhttp_response_content_length.argtypes = [zhttp_response_p]
lib.zhttp_response_content.restype = c_char_p
lib.zhttp_response_content.argtypes = [zhttp_response_p]
lib.zhttp_response_get_content.restype = POINTER(c_char)
lib.zhttp_response_get_content.argtypes = [zhttp_response_p]
lib.zhttp_response_set_content.restype = None
lib.zhttp_response_set_content.argtypes = [zhttp_response_p, POINTER(c_char_p)]
lib.zhttp_response_set_content_const.restype = None
lib.zhttp_response_set_content_const.argtypes = [zhttp_response_p, c_char_p]
lib.zhttp_response_reset_content.restype = None
lib.zhttp_response_reset_content.argtypes = [zhttp_response_p]
lib.zhttp_response_test.restype = None
lib.zhttp_response_test.argtypes = [c_bool]

class ZhttpResponse(object):
    """
    Http response that can be received from zhttp_client or sent to zhttp_server.
Class can be reused between send & recv calls.
Headers and Content is being destroyed after every send call.
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new zhttp_response.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zhttp_response_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zhttp_response_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 0)
            self._as_parameter_ = lib.zhttp_response_new() # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy the zhttp_response.
        """
        if self.allow_destruct:
            lib.zhttp_response_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    def send(self, sock, connection):
        """
        Send a response to a request.
Returns 0 if successful and -1 otherwise.
        """
        return lib.zhttp_response_send(self._as_parameter_, sock, byref(c_void_p.from_param(connection)))

    def recv(self, client, arg, arg2):
        """
        Receive a response from zhttp_client.
On success return 0, -1 otherwise.

Recv returns the two user arguments which was provided with the request.
The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an argument.
        """
        return lib.zhttp_response_recv(self._as_parameter_, client, byref(c_void_p.from_param(arg)), byref(c_void_p.from_param(arg2)))

    def content_type(self):
        """
        Get the response content type
        """
        return lib.zhttp_response_content_type(self._as_parameter_)

    def set_content_type(self, value):
        """
        Set the content type of the response.
        """
        return lib.zhttp_response_set_content_type(self._as_parameter_, value)

    def status_code(self):
        """
        Get the status code of the response.
        """
        return lib.zhttp_response_status_code(self._as_parameter_)

    def set_status_code(self, status_code):
        """
        Set the status code of the response.
        """
        return lib.zhttp_response_set_status_code(self._as_parameter_, status_code)

    def headers(self):
        """
        Get the headers of the response.
        """
        return Zhash(lib.zhttp_response_headers(self._as_parameter_), False)

    def content_length(self):
        """
        Get the content length of the response
        """
        return lib.zhttp_response_content_length(self._as_parameter_)

    def content(self):
        """
        Get the content of the response.
        """
        return lib.zhttp_response_content(self._as_parameter_)

    def get_content(self):
        """
        Get the content of the response.
        """
        return return_fresh_string(lib.zhttp_response_get_content(self._as_parameter_))

    def set_content(self, content):
        """
        Set the content of the response.
Content must by dynamically allocated string.
Takes ownership of the content.
        """
        return lib.zhttp_response_set_content(self._as_parameter_, byref(c_char_p.from_param(content)))

    def set_content_const(self, content):
        """
        Set the content of the response.
The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
        """
        return lib.zhttp_response_set_content_const(self._as_parameter_, content)

    def reset_content(self):
        """
        Set the content to NULL
        """
        return lib.zhttp_response_reset_content(self._as_parameter_)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zhttp_response_test(verbose)


# zosc
lib.zosc_new.restype = zosc_p
lib.zosc_new.argtypes = [c_char_p]
lib.zosc_destroy.restype = None
lib.zosc_destroy.argtypes = [POINTER(zosc_p)]
lib.zosc_fromframe.restype = zosc_p
lib.zosc_fromframe.argtypes = [zframe_p]
lib.zosc_frommem.restype = zosc_p
lib.zosc_frommem.argtypes = [c_void_p, c_size_t]
lib.zosc_fromstring.restype = zosc_p
lib.zosc_fromstring.argtypes = [c_char_p]
lib.zosc_create.restype = zosc_p
lib.zosc_create.argtypes = [c_char_p, c_char_p]
lib.zosc_size.restype = c_size_t
lib.zosc_size.argtypes = [zosc_p]
lib.zosc_data.restype = c_void_p
lib.zosc_data.argtypes = [zosc_p]
lib.zosc_address.restype = c_char_p
lib.zosc_address.argtypes = [zosc_p]
lib.zosc_format.restype = c_char_p
lib.zosc_format.argtypes = [zosc_p]
lib.zosc_append.restype = c_int
lib.zosc_append.argtypes = [zosc_p, c_char_p]
lib.zosc_retr.restype = c_int
lib.zosc_retr.argtypes = [zosc_p, c_char_p]
lib.zosc_dup.restype = zosc_p
lib.zosc_dup.argtypes = [zosc_p]
lib.zosc_pack.restype = zframe_p
lib.zosc_pack.argtypes = [zosc_p]
lib.zosc_packx.restype = zframe_p
lib.zosc_packx.argtypes = [POINTER(zosc_p)]
lib.zosc_unpack.restype = zosc_p
lib.zosc_unpack.argtypes = [zframe_p]
lib.zosc_dump.restype = POINTER(c_char)
lib.zosc_dump.argtypes = [zosc_p]
lib.zosc_print.restype = None
lib.zosc_print.argtypes = [zosc_p]
lib.zosc_is.restype = c_bool
lib.zosc_is.argtypes = [c_void_p]
lib.zosc_first.restype = c_void_p
lib.zosc_first.argtypes = [zosc_p, POINTER(char_p)]
lib.zosc_next.restype = c_void_p
lib.zosc_next.argtypes = [zosc_p, POINTER(char_p)]
lib.zosc_last.restype = c_void_p
lib.zosc_last.argtypes = [zosc_p, POINTER(char_p)]
lib.zosc_pop_int32.restype = c_int
lib.zosc_pop_int32.argtypes = [zosc_p, POINTER(c_int)]
lib.zosc_pop_int64.restype = c_int
lib.zosc_pop_int64.argtypes = [zosc_p, POINTER(msecs_p)]
lib.zosc_pop_float.restype = c_int
lib.zosc_pop_float.argtypes = [zosc_p, POINTER(c_float)]
lib.zosc_pop_double.restype = c_int
lib.zosc_pop_double.argtypes = [zosc_p, POINTER(c_double)]
lib.zosc_pop_string.restype = c_int
lib.zosc_pop_string.argtypes = [zosc_p, POINTER(c_char_p)]
lib.zosc_pop_char.restype = c_int
lib.zosc_pop_char.argtypes = [zosc_p, POINTER(char_p)]
lib.zosc_pop_bool.restype = c_int
lib.zosc_pop_bool.argtypes = [zosc_p, POINTER(c_bool)]
lib.zosc_pop_midi.restype = c_int
lib.zosc_pop_midi.argtypes = [zosc_p, POINTER(c_int)]
lib.zosc_test.restype = None
lib.zosc_test.argtypes = [c_bool]

class Zosc(object):
    """
    Create and decode Open Sound Control messages. (OSC)

OSC is a serialisation format (and usually transported over UDP) which is
supported by many applications and appliances. It is a de facto protocol
for networking sound synthesizers, computers, and other multimedia devices
for purposes such as musical performance or show control. It is also often
used for rapid prototyping purposes due to the support by many applications
and frameworks in this field. With ZeroMQ's DGRAM sockets it is possible
to use ZeroMQ to send and receive OSC messages which can be understood by
any device supporting OSC.

Example creating an OSC message:

    zosc_t* conm = zosc_create("/someaddress", "iihfdscF",
                        1, 2, 3, 3.14, 6.283185307179586, "greetings", 'q');

Decoding a message:

    int rc = zosc_retr(oscmsg, "iihfdscF", &intx, &inty, &intz, &floatz,
                        &doublez, &strings, &charq, &someBool);

See the class's test method for more examples how to use the class.
    """

    allow_destruct = False
    def __init__(self, *args):
        """
        Create a new empty OSC message with the specified address string.
        """
        if len(args) == 2 and type(args[0]) is c_void_p and isinstance(args[1], bool):
            self._as_parameter_ = cast(args[0], zosc_p) # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        elif len(args) == 2 and type(args[0]) is zosc_p and isinstance(args[1], bool):
            self._as_parameter_ = args[0] # Conversion from raw type to binding
            self.allow_destruct = args[1] # This is a 'fresh' value, owned by us
        else:
            assert(len(args) == 1)
            self._as_parameter_ = lib.zosc_new(args[0]) # Creation of new raw type
            self.allow_destruct = True

    def __del__(self):
        """
        Destroy an OSC message
        """
        if self.allow_destruct:
            lib.zosc_destroy(byref(self._as_parameter_))

    def __eq__(self, other):
        if type(other) == type(self):
            return other.c_address() == self.c_address()
        elif type(other) == c_void_p:
            return other.value == self.c_address()

    def c_address(self):
        """
        Return the address of the object pointer in c.  Useful for comparison.
        """
        return addressof(self._as_parameter_.contents)

    def __bool__(self):
        "Determine whether the object is valid by converting to boolean" # Python 3
        return self._as_parameter_.__bool__()

    def __nonzero__(self):
        "Determine whether the object is valid by converting to boolean" # Python 2
        return self._as_parameter_.__nonzero__()

    @staticmethod
    def fromframe(frame):
        """
        Create a new OSC message from the specified zframe. Takes ownership of
the zframe.
        """
        return Zosc(lib.zosc_fromframe(frame), True)

    @staticmethod
    def frommem(data, size):
        """
        Create a new zosc message from memory. Take ownership of the memory
and calling free on the data after construction.
        """
        return Zosc(lib.zosc_frommem(data, size), True)

    @staticmethod
    def fromstring(oscstring):
        """
        Create a new zosc message from a string. This the same syntax as
zosc_create but written as a single line string.
        """
        return Zosc(lib.zosc_fromstring(oscstring), True)

    @staticmethod
    def create(address, format, *args):
        """
        Create a new zosc message from the given format and arguments.
The format type tags are as follows:
  i - 32bit integer
  h - 64bit integer
  f - 32bit floating point number (IEEE)
  d - 64bit (double) floating point number
  s - string (NULL terminated)
  t = timetag: an OSC timetag in NTP format (uint64_t)
  S - symbol
  c - char
  m - 4 byte midi packet (8 digits hexadecimal)
  T - TRUE (no value required)
  F - FALSE (no value required)
  N - NIL (no value required)
  I - Impulse (for triggers) or INFINITUM (no value required)
  b - binary blob
        """
        return Zosc(lib.zosc_create(address, format, *args), True)

    def size(self):
        """
        Return chunk data size
        """
        return lib.zosc_size(self._as_parameter_)

    def data(self):
        """
        Return OSC chunk data. Caller does not own the data!
        """
        return lib.zosc_data(self._as_parameter_)

    def address(self):
        """
        Return the OSC address string
        """
        return lib.zosc_address(self._as_parameter_)

    def format(self):
        """
        Return the OSC format of the message.
  i - 32bit integer
  h - 64bit integer
  f - 32bit floating point number (IEEE)
  d - 64bit (double) floating point number
  s - string (NULL terminated)
  t = timetag: an OSC timetag in NTP format (uint64_t)
  S - symbol
  c - char
  m - 4 byte midi packet (8 digits hexadecimal)
  T - TRUE (no value required)
  F - FALSE (no value required)
  N - NIL (no value required)
  I - Impulse (for triggers) or INFINITUM (no value required)
  b - binary blob
        """
        return lib.zosc_format(self._as_parameter_)

    def append(self, format, *args):
        """
        Append data to the osc message. The format describes the data that
needs to be appended in the message. This essentially relocates all
data!
The format type tags are as follows:
  i - 32bit integer
  h - 64bit integer
  f - 32bit floating point number (IEEE)
  d - 64bit (double) floating point number
  s - string (NULL terminated)
  t = timetag: an OSC timetag in NTP format (uint64_t)
  S - symbol
  c - char
  m - 4 byte midi packet (8 digits hexadecimal)
  T - TRUE (no value required)
  F - FALSE (no value required)
  N - NIL (no value required)
  I - Impulse (for triggers) or INFINITUM (no value required)
  b - binary blob
        """
        return lib.zosc_append(self._as_parameter_, format, *args)

    def retr(self, format, *args):
        """
        Retrieve the values provided by the given format. Note that zosc_retr
creates the objects and the caller must destroy them when finished.
The supplied pointers do not need to be initialized. Returns 0 if
successful, or -1 if it failed to retrieve a value in which case the
pointers are not modified. If an argument pointer is NULL is skips the
value. See the format method for a detailed list op type tags for the
format string.
        """
        return lib.zosc_retr(self._as_parameter_, format, *args)

    def dup(self):
        """
        Create copy of the message, as new chunk object. Returns a fresh zosc_t
object, or null if there was not enough heap memory. If chunk is null,
returns null.
        """
        return Zosc(lib.zosc_dup(self._as_parameter_), True)

    def pack(self):
        """
        Transform zosc into a zframe that can be sent in a message.
        """
        return Zframe(lib.zosc_pack(self._as_parameter_), True)

    @staticmethod
    def packx(self_p):
        """
        Transform zosc into a zframe that can be sent in a message.
Take ownership of the chunk.
        """
        return Zframe(lib.zosc_packx(byref(zosc_p.from_param(self_p))), True)

    @staticmethod
    def unpack(frame):
        """
        Transform a zframe into a zosc.
        """
        return Zosc(lib.zosc_unpack(frame), True)

    def dump(self):
        """
        Return a string describing the the OSC message. The returned string must be freed by the caller.
        """
        return return_fresh_string(lib.zosc_dump(self._as_parameter_))

    def print(self):
        """
        Dump OSC message to stdout, for debugging and tracing.
        """
        return lib.zosc_print(self._as_parameter_)

    @staticmethod
    def is_(self):
        """
        Probe the supplied object, and report if it looks like a zosc_t.
        """
        return lib.zosc_is(self)

    def first(self, type):
        """
        Return a pointer to the item at the head of the OSC data.
Sets the given char argument to the type tag of the data.
If the message is empty, returns NULL and the sets the
given char to NULL.
        """
        return c_void_p(lib.zosc_first(self._as_parameter_, byref(char_p.from_param(type))))

    def next(self, type):
        """
        Return the next item of the OSC message. If the list is empty, returns
NULL. To move to the start of the OSC message call zosc_first ().
        """
        return c_void_p(lib.zosc_next(self._as_parameter_, byref(char_p.from_param(type))))

    def last(self, type):
        """
        Return a pointer to the item at the tail of the OSC message.
Sets the given char argument to the type tag of the data. If
the message is empty, returns NULL.
        """
        return c_void_p(lib.zosc_last(self._as_parameter_, byref(char_p.from_param(type))))

    def pop_int32(self, val):
        """
        Set the provided 32 bit integer from value at the current cursor position in the message.
If the type tag at the current position does not correspond it will fail and
return -1. Returns 0 on success.
        """
        return lib.zosc_pop_int32(self._as_parameter_, byref(c_int.from_param(val)))

    def pop_int64(self, val):
        """
        Set the provided 64 bit integer from the value at the current cursor position in the message.
If the type tag at the current position does not correspond it will fail and
return -1. Returns 0 on success.
        """
        return lib.zosc_pop_int64(self._as_parameter_, byref(msecs_p.from_param(val)))

    def pop_float(self, val):
        """
        Set the provided float from the value at the current cursor position in the message.
If the type tag at the current position does not correspond it will fail and
return -1. Returns 0 on success.
        """
        return lib.zosc_pop_float(self._as_parameter_, byref(c_float.from_param(val)))

    def pop_double(self, val):
        """
        Set the provided double from the value at the current cursor position in the message.
If the type tag at the current position does not correspond it will fail and
return -1. Returns 0 on success.
        """
        return lib.zosc_pop_double(self._as_parameter_, byref(c_double.from_param(val)))

    def pop_string(self, val):
        """
        Set the provided string from the value at the current cursor position in the message.
If the type tag at the current position does not correspond it will fail and
return -1. Returns 0 on success. Caller owns the string!
        """
        return lib.zosc_pop_string(self._as_parameter_, byref(c_char_p.from_param(val)))

    def pop_char(self, val):
        """
        Set the provided char from the value at the current cursor position in the message.
If the type tag at the current position does not correspond it will fail and
return -1. Returns 0 on success.
        """
        return lib.zosc_pop_char(self._as_parameter_, byref(char_p.from_param(val)))

    def pop_bool(self, val):
        """
        Set the provided boolean from the type tag in the message. Booleans are not represented
in the data in the message, only in the type tag. If the type tag at the current
position does not correspond it will fail and return -1. Returns 0 on success.
        """
        return lib.zosc_pop_bool(self._as_parameter_, byref(c_bool.from_param(val)))

    def pop_midi(self, val):
        """
        Set the provided 4 bytes (unsigned 32bit int) from the value at the current
cursor position in the message. If the type tag at the current position does
not correspond it will fail and return -1. Returns 0 on success.
        """
        return lib.zosc_pop_midi(self._as_parameter_, byref(c_int.from_param(val)))

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        return lib.zosc_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
