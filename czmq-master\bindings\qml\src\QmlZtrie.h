/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#ifndef QML_ZTRIE_H
#define QML_ZTRIE_H

#include <QtQml>

#include <czmq.h>
#include "qml_czmq_plugin.h"


class QmlZtrie : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isNULL READ isNULL)

public:
    ztrie_t *self;

    QmlZtrie() { self = NULL; }
    bool isNULL() { return self == NULL; }

    static QObject* qmlAttachedProperties(QObject* object); // defined in QmlZtrie.cpp

public slots:
    //  Inserts a new route into the tree and attaches the data. Returns -1
    //  if the route already exists, otherwise 0. This method takes ownership of
    //  the provided data if a destroy_data_fn is provided.
    int insertRoute (const QString &path, void *data, ztrie_destroy_data_fn destroyDataFn);

    //  Removes a route from the trie and destroys its data. Returns -1 if the
    //  route does not exists, otherwise 0.
    //  the start of the list call zlist_first (). Advances the cursor.
    int removeRoute (const QString &path);

    //  Returns true if the path matches a route in the tree, otherwise false.
    bool matches (const QString &path);

    //  Returns the data of a matched route from last ztrie_matches. If the path
    //  did not match, returns NULL. Do not delete the data as it's owned by
    //  ztrie.
    void *hitData ();

    //  Returns the count of parameters that a matched route has.
    size_t hitParameterCount ();

    //  Returns the parameters of a matched route with named regexes from last
    //  ztrie_matches. If the path did not match or the route did not contain any
    //  named regexes, returns NULL.
    QmlZhashx *hitParameters ();

    //  Returns the asterisk matched part of a route, if there has been no match
    //  or no asterisk match, returns NULL.
    const QString hitAsteriskMatch ();

    //  Print the trie
    void print ();
};

class QmlZtrieAttached : public QObject
{
    Q_OBJECT
    QObject* m_attached;

public:
    QmlZtrieAttached (QObject* attached) {
        Q_UNUSED (attached);
    };

public slots:
    //  Self test of this class.
    void test (bool verbose);

    //  Creates a new ztrie.
    QmlZtrie *construct (char delimiter);

    //  Destroy the ztrie.
    void destruct (QmlZtrie *qmlSelf);
};


QML_DECLARE_TYPEINFO(QmlZtrie, QML_HAS_ATTACHED_PROPERTIES)

#endif
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
