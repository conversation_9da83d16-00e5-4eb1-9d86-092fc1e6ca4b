/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

ext.jni_dependencies_version = project.hasProperty('isRelease') ? 'latest.release' : 'latest.integration'
ext.hasNotEmptyProperty = { propertyName ->
    return project.hasProperty(propertyName) ? project[propertyName]?.trim() : false
}

dependencies {
    implementation 'org.scijava:native-lib-loader:2.5.0'
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.hamcrest:hamcrest:2.2'
}

//  ------------------------------------------------------------------
//  Build section

tasks.register('generateJniHeaders', Exec) {
    dependsOn 'classes'
    def classpath = sourceSets.main.output.classesDirs
    def appclasspath = configurations.runtimeClasspath.files*.getAbsolutePath().join(File.pathSeparator)
    def nativeIncludes = 'src/native/include'
    def jniClasses = [
            'src/main/java/org/zeromq/czmq/Zarmour.java',
            'src/main/java/org/zeromq/czmq/Zcert.java',
            'src/main/java/org/zeromq/czmq/Zcertstore.java',
            'src/main/java/org/zeromq/czmq/Zchunk.java',
            'src/main/java/org/zeromq/czmq/Zclock.java',
            'src/main/java/org/zeromq/czmq/Zconfig.java',
            'src/main/java/org/zeromq/czmq/Zdigest.java',
            'src/main/java/org/zeromq/czmq/Zdir.java',
            'src/main/java/org/zeromq/czmq/ZdirPatch.java',
            'src/main/java/org/zeromq/czmq/Zfile.java',
            'src/main/java/org/zeromq/czmq/Zframe.java',
            'src/main/java/org/zeromq/czmq/Zhash.java',
            'src/main/java/org/zeromq/czmq/Zhashx.java',
            'src/main/java/org/zeromq/czmq/Ziflist.java',
            'src/main/java/org/zeromq/czmq/Zlist.java',
            'src/main/java/org/zeromq/czmq/Zlistx.java',
            'src/main/java/org/zeromq/czmq/Zloop.java',
            'src/main/java/org/zeromq/czmq/Zmsg.java',
            'src/main/java/org/zeromq/czmq/Zpoller.java',
            'src/main/java/org/zeromq/czmq/Zproc.java',
            'src/main/java/org/zeromq/czmq/Zsock.java',
            'src/main/java/org/zeromq/czmq/Zstr.java',
            'src/main/java/org/zeromq/czmq/Zsys.java',
            'src/main/java/org/zeromq/czmq/Ztimerset.java',
            'src/main/java/org/zeromq/czmq/Ztrie.java',
            'src/main/java/org/zeromq/czmq/Zuuid.java',
            'src/main/java/org/zeromq/czmq/ZhttpClient.java',
            'src/main/java/org/zeromq/czmq/ZhttpServer.java',
            'src/main/java/org/zeromq/czmq/ZhttpServerOptions.java',
            'src/main/java/org/zeromq/czmq/ZhttpRequest.java',
            'src/main/java/org/zeromq/czmq/ZhttpResponse.java',
            'src/main/java/org/zeromq/czmq/Zosc.java'
    ]
    def utilityClasses = [
            'src/main/java/org/zeromq/tools/ZmqNativeLoader.java'
    ]
    commandLine("javac", "-h", "$nativeIncludes", "-classpath", "$classpath${File.pathSeparator}$appclasspath", *jniClasses, *utilityClasses)
}

tasks.withType(Test).configureEach {
    def defaultJavaLibraryPath = System.getProperty("java.library.path")
    if (osdetector.os == 'windows') {
        def extraJavaLibraryPath = hasNotEmptyProperty('buildPrefix') ? "$project.buildPrefix\\bin;$project.buildPrefix\\lib" : ''
        extraJavaLibraryPath = extraJavaLibraryPath.replace("/", "\\")
        systemProperty "java.library.path", "${projectDir}\\build\\Release${File.pathSeparator}" +
                                            "${extraJavaLibraryPath}${File.pathSeparator}" +
                                            "${defaultJavaLibraryPath}"
    } else {
        def extraJavaLibraryPath = hasNotEmptyProperty('buildPrefix') ? "$project.buildPrefix/lib" : ''
        systemProperty "java.library.path", "${projectDir}/build${File.pathSeparator}" +
                                            "/usr/local/lib${File.pathSeparator}" +
                                            "/tmp/lib${File.pathSeparator}" +
                                            "${extraJavaLibraryPath}${File.pathSeparator}" +
                                            "${defaultJavaLibraryPath}"
    }
}

tasks.register('initCMake', Exec) {
    dependsOn 'generateJniHeaders'
    workingDir 'build'
    def prefixPath = hasNotEmptyProperty('buildPrefix') ? "-DCMAKE_PREFIX_PATH=$project.buildPrefix" : ''
    commandLine 'cmake', "$prefixPath", '..'
}

tasks.register('buildNative', Exec) {
    dependsOn 'initCMake'
    if (osdetector.os == 'windows') {
        commandLine 'cmake',
                    '--build', 'build',
                    '--config', 'Release',
                    '--target', 'czmqjni',
                    '--', '-verbosity:Minimal', '-maxcpucount'
    } else {
        commandLine 'cmake',
                    '--build', 'build'
    }
}

jar.dependsOn buildNative
test.dependsOn buildNative

//  ------------------------------------------------------------------
//  Install and Publish section

tasks.register('sourcesJar', Jar) {
    dependsOn 'classes'
    archiveClassifier = 'sources'
    from sourceSets.main.allSource
}

tasks.register('javadocJar', Jar) {
    dependsOn 'javadoc'
    archiveClassifier = 'javadoc'
    from javadoc.destinationDir
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
            artifact sourcesJar
            artifact javadocJar
            artifactId = 'czmq-jni'
            pom {
                name = 'czmq-jni'
                description = 'The high-level C binding for 0MQ'
                packaging = 'jar'
                url = 'https://github.com/zeromq/czmq'
                licenses {
                    license {
                        name = 'Mozilla Public License Version 2.0'
                        url = 'https://www.mozilla.org/en-US/MPL/2.0/'
                    }
                }
                scm {
                    connection = 'https://github.com/zeromq/czmq.git'
                    developerConnection = 'https://github.com/zeromq/czmq.git'
                    url = 'https://github.com/zeromq/czmq'
                }
            }
        }
    }
}

artifactoryPublish {
    publications ('mavenJava')
}

bintray {
    user = System.getenv('BINTRAY_USER')
    key = System.getenv('BINTRAY_KEY')
    publications = ['mavenJava']
    publish = true
    override = true
    pkg {
        repo = 'maven'
        name = 'czmq-jni'
        desc = 'The high-level C binding for 0MQ'
        userOrg = System.getenv('BINTRAY_USER_ORG')
        licenses = ['MPL-2.0']
        websiteUrl = 'https://github.com/zeromq/czmq'
        issueTrackerUrl = 'https://github.com/zeromq/czmq/issues'
        vcsUrl = 'https://github.com/zeromq/czmq.git'
        githubRepo = System.getenv('BINTRAY_USER_ORG') + '/czmq'
        version {
            name = project.version
            vcsTag= project.version
        }
    }
}

//  ------------------------------------------------------------------
//  Cleanup section

clean.doFirst {
    delete 'CMakeFiles', 'msvc'
    delete fileTree(projectDir) {
        include '*.so'
        include '*.dylib'
        include 'cmake_install.cmake'
        include 'Makefile'
        include 'CMakeCache.txt'
    }
}
