################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

module CZMQ
  module FFI

    # timer set
    # @note This class is 100% generated using zproject.
    class Ztimerset
      # Raised when one tries to use an instance of {Ztimerset} after
      # the internal pointer to the native object has been nullified.
      class DestroyedError < RuntimeError; end

      # Boilerplate for self pointer, initializer, and finalizer
      class << self
        alias :__new :new
      end
      # Attaches the pointer _ptr_ to this instance and defines a finalizer for
      # it if necessary.
      # @param ptr [::FFI::Pointer]
      # @param finalize [Boolean]
      def initialize(ptr, finalize = true)
        @ptr = ptr
        if @ptr.null?
          @ptr = nil # Remove null pointers so we don't have to test for them.
        elsif finalize
          @finalizer = self.class.create_finalizer_for @ptr
          ObjectSpace.define_finalizer self, @finalizer
        end
      end
      # @param ptr [::FFI::Pointer]
      # @return [Proc]
      def self.create_finalizer_for(ptr)
        ptr_ptr = ::FFI::MemoryPointer.new :pointer

        Proc.new do
          ptr_ptr.write_pointer ptr
          ::CZMQ::FFI.ztimerset_destroy ptr_ptr
        end
      end
      # @return [Boolean]
      def null?
        !@ptr or @ptr.null?
      end
      # Return internal pointer
      # @return [::FFI::Pointer]
      def __ptr
        raise DestroyedError unless @ptr
        @ptr
      end
      # So external Libraries can just pass the Object to a FFI function which expects a :pointer
      alias_method :to_ptr, :__ptr
      # Nullify internal pointer and return pointer pointer.
      # @note This detaches the current instance from the native object
      #   and thus makes it unusable.
      # @return [::FFI::MemoryPointer] the pointer pointing to a pointer
      #   pointing to the native object
      def __ptr_give_ref
        raise DestroyedError unless @ptr
        ptr_ptr = ::FFI::MemoryPointer.new :pointer
        ptr_ptr.write_pointer @ptr
        __undef_finalizer if @finalizer
        @ptr = nil
        ptr_ptr
      end
      # Undefines the finalizer for this object.
      # @note Only use this if you need to and can guarantee that the native
      #   object will be freed by other means.
      # @return [void]
      def __undef_finalizer
        ObjectSpace.undefine_finalizer self
        @finalizer = nil
      end

      # Create a new callback of the following type:
      # Callback function for timer event.
      #     typedef void (ztimerset_fn) (
      #         int timer_id, void *arg);
      #
      # @note WARNING: If your Ruby code doesn't retain a reference to the
      #   FFI::Function object after passing it to a C function call,
      #   it may be garbage collected while C still holds the pointer,
      #   potentially resulting in a segmentation fault.
      def self.fn
        ::FFI::Function.new :void, [:int, :pointer], blocking: true do |timer_id, arg|
          result = yield timer_id, arg
          result
        end
      end

      # Create new timer set.
      # @return [CZMQ::Ztimerset]
      def self.new()
        ptr = ::CZMQ::FFI.ztimerset_new()
        __new ptr
      end

      # Destroy a timer set
      #
      # @return [void]
      def destroy()
        return unless @ptr
        self_p = __ptr_give_ref
        result = ::CZMQ::FFI.ztimerset_destroy(self_p)
        result
      end

      # Add a timer to the set. Returns timer id if OK, -1 on failure.
      #
      # @param interval [Integer, #to_int, #to_i]
      # @param handler [::FFI::Pointer, #to_ptr]
      # @param arg [::FFI::Pointer, #to_ptr]
      # @return [Integer]
      def add(interval, handler, arg)
        raise DestroyedError unless @ptr
        self_p = @ptr
        interval = Integer(interval)
        result = ::CZMQ::FFI.ztimerset_add(self_p, interval, handler, arg)
        result
      end

      # Cancel a timer. Returns 0 if OK, -1 on failure.
      #
      # @param timer_id [Integer, #to_int, #to_i]
      # @return [Integer]
      def cancel(timer_id)
        raise DestroyedError unless @ptr
        self_p = @ptr
        timer_id = Integer(timer_id)
        result = ::CZMQ::FFI.ztimerset_cancel(self_p, timer_id)
        result
      end

      # Set timer interval. Returns 0 if OK, -1 on failure.
      # This method is slow, canceling the timer and adding a new one yield better performance.
      #
      # @param timer_id [Integer, #to_int, #to_i]
      # @param interval [Integer, #to_int, #to_i]
      # @return [Integer]
      def set_interval(timer_id, interval)
        raise DestroyedError unless @ptr
        self_p = @ptr
        timer_id = Integer(timer_id)
        interval = Integer(interval)
        result = ::CZMQ::FFI.ztimerset_set_interval(self_p, timer_id, interval)
        result
      end

      # Reset timer to start interval counting from current time. Returns 0 if OK, -1 on failure.
      # This method is slow, canceling the timer and adding a new one yield better performance.
      #
      # @param timer_id [Integer, #to_int, #to_i]
      # @return [Integer]
      def reset(timer_id)
        raise DestroyedError unless @ptr
        self_p = @ptr
        timer_id = Integer(timer_id)
        result = ::CZMQ::FFI.ztimerset_reset(self_p, timer_id)
        result
      end

      # Return the time until the next interval.
      # Should be used as timeout parameter for the zpoller wait method.
      # The timeout is in msec.
      #
      # @return [Integer]
      def timeout()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ztimerset_timeout(self_p)
        result
      end

      # Invoke callback function of all timers which their interval has elapsed.
      # Should be call after zpoller wait method.
      # Returns 0 if OK, -1 on failure.
      #
      # @return [Integer]
      def execute()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ztimerset_execute(self_p)
        result
      end

      # Self test of this class.
      #
      # @param verbose [Boolean]
      # @return [void]
      def self.test(verbose)
        verbose = !(0==verbose||!verbose) # boolean
        result = ::CZMQ::FFI.ztimerset_test(verbose)
        result
      end
    end
  end
end

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
