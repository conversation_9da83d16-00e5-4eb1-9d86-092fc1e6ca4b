/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#ifndef Q_ZIFLIST_H
#define Q_ZIFLIST_H

#include "qczmq.h"

class QT_CZMQ_EXPORT QZiflist : public QObject
{
    Q_OBJECT
public:

    //  Copy-construct to return the proper wrapped c types
    QZiflist (ziflist_t *self, QObject *qObjParent = 0);

    //  Get a list of network interfaces currently defined on the system
    explicit QZiflist (QObject *qObjParent = 0);

    //  Destroy a ziflist instance
    ~QZiflist ();

    //  Reload network interfaces from system
    void reload ();

    //  Return the number of network interfaces on system
    size_t size ();

    //  Get first network interface, return NULL if there are none
    const QString first ();

    //  Get next network interface, return NULL if we hit the last one
    const QString next ();

    //  Return the current interface IP address as a printable string
    const QString address ();

    //  Return the current interface broadcast address as a printable string
    const QString broadcast ();

    //  Return the current interface network mask as a printable string
    const QString netmask ();

    //  Return the current interface MAC address as a printable string
    const QString mac ();

    //  Return the list of interfaces.
    void print ();

    //  Get a list of network interfaces currently defined on the system
    //  Includes IPv6 interfaces
    static QZiflist * newIpv6 ();

    //  Reload network interfaces from system, including IPv6
    void reloadIpv6 ();

    //  Return true if the current interface uses IPv6
    bool isIpv6 ();

    //  Self test of this class.
    static void test (bool verbose);

    ziflist_t *self;
};
#endif //  Q_ZIFLIST_H
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
