<?xml version="1.0" encoding="utf-8"?>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zarmour.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zcert.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zcertstore.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zchunk.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zclock.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zconfig.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zdigest.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zdir.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_ZdirPatch.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zfile.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zframe.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zhash.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zhashx.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Ziflist.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zlist.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zlistx.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zloop.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zmsg.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zpoller.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zproc.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zsock.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zstr.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zsys.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Ztimerset.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Ztrie.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zuuid.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_ZhttpClient.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_ZhttpServer.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_ZhttpServerOptions.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_ZhttpRequest.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_ZhttpResponse.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zosc.c">
      <Filter>src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\..\include\czmq.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zarmour.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zcert.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zcertstore.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zchunk.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zclock.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zconfig.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zdigest.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zdir.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_ZdirPatch.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zfile.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zframe.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zhash.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zhashx.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Ziflist.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zlist.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zlistx.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zloop.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zmsg.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zpoller.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zproc.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zsock.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zstr.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zsys.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Ztimerset.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Ztrie.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zuuid.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_ZhttpClient.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_ZhttpServer.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_ZhttpServerOptions.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_ZhttpRequest.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_ZhttpResponse.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zosc.h">
      <Filter>include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\platform.h">
      <Filter>src\include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\resource.h">
      <Filter>resource</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\..\resource.rc">
      <Filter>resource</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="src">
      <UniqueIdentifier>{48f852d3-9723-4499-bf1a-35c0234b8ba9}</UniqueIdentifier>
    </Filter>
    <Filter Include="include">
      <UniqueIdentifier>{95e5d24a-57a2-429a-a1f1-304165f2e3da}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\include">
      <UniqueIdentifier>{d0c837b5-cb58-4b82-b9bf-38727c7b25bd}</UniqueIdentifier>
    </Filter>
    <Filter Include="resource">
      <UniqueIdentifier>{48e93f8c-156c-4379-a901-4b5ad39a4eac}</UniqueIdentifier>
    </Filter>
    <Filter Include="packaging">
      <UniqueIdentifier>{04a473ca-1d88-4e12-9190-8d9cc20efd74}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
</Project>
