/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

buildscript {
    configurations.configureEach {
        resolutionStrategy {
            force 'org.codehaus.groovy.modules.http-builder:http-builder:0.7.1'
        }
        exclude group: 'xerces', module: 'xercesImpl'
    }
}

plugins {
    id 'java'
    id 'maven-publish'
    id 'com.jfrog.artifactory' version '5.2.3'
    id 'com.jfrog.bintray' version '1.8.5'
    id 'com.google.osdetector' version '1.7.3'
}

wrapper.gradleVersion = '8.9'

subprojects {
    apply plugin: 'java'
    apply plugin: 'maven-publish'
    apply plugin: 'com.jfrog.bintray'
    apply plugin: 'com.jfrog.artifactory'
    apply plugin: 'com.google.osdetector'

    sourceCompatibility = 1.8
    targetCompatibility = 1.8

    repositories {
        mavenLocal()
        mavenCentral()
        jcenter()
    }

    group = 'org.zeromq.czmq'

    if (project.hasProperty('isRelease')) {
        version = '4.2.2'
    } else {
        version = '4.2.2-SNAPSHOT'
    }
}

artifactory {
    contextUrl = "https://oss.jfrog.org/artifactory"   //The base Artifactory URL if not overridden by the publisher/resolver
    publish {
        repository {
            repoKey = 'oss-snapshot-local'
            username = System.getenv('ARTIFACTORY_USERNAME')
            password = System.getenv('ARTIFACTORY_PASSWORD')
        }
    }
}

bintrayPublish.enabled = false
