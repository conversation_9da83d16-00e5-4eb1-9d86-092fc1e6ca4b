/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#ifndef Q_ZCLOCK_H
#define Q_ZCLOCK_H

#include "qczmq.h"

class QT_CZMQ_EXPORT QZclock : public QObject
{
    Q_OBJECT
public:

    //  Copy-construct to return the proper wrapped c types
    QZclock (zclock_t *self, QObject *qObjParent = 0);

    //  Sleep for a number of milliseconds
    static void sleep (int msecs);

    //  Return current system clock as milliseconds. Note that this clock can
    //  jump backwards (if the system clock is changed) so is unsafe to use for
    //  timers and time offsets. Use zclock_mono for that instead.
    static int64_t time ();

    //  Return current monotonic clock in milliseconds. Use this when you compute
    //  time offsets. The monotonic clock is not affected by system changes and
    //  so will never be reset backwards, unlike a system clock.
    static int64_t mono ();

    //  Return current monotonic clock in microseconds. Use this when you compute
    //  time offsets. The monotonic clock is not affected by system changes and
    //  so will never be reset backwards, unlike a system clock.
    static int64_t usecs ();

    //  Return formatted date/time as fresh string. Free using zstr_free().
    static QString timestr ();

    //  Self test of this class.
    static void test (bool verbose);

    zclock_t *self;
};
#endif //  Q_ZCLOCK_H
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
