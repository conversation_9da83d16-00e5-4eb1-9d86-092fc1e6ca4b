<class name = "ztrie" state = "draft">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    simple trie for tokenizable strings

    <callback_type name = "destroy_data_fn">
        Callback function for ztrie_node to destroy node data.
        <argument name = "data" type = "anything" by_reference = "1" />
    </callback_type>

    <constructor>
        Creates a new ztrie.
        <argument name = "delimiter" type = "char" />
    </constructor>

    <destructor>
        Destroy the ztrie.
    </destructor>

    <method name = "insert route">
        Inserts a new route into the tree and attaches the data. Returns -1
        if the route already exists, otherwise 0. This method takes ownership of
        the provided data if a destroy_data_fn is provided.
        <argument name = "path" type = "string" />
        <argument name = "data" type = "anything" />
        <argument name = "destroy_data_fn" type = "ztrie_destroy_data_fn" callback = "1" />
        <return type = "integer" />
    </method>

    <method name = "remove route">
        Removes a route from the trie and destroys its data. Returns -1 if the
        route does not exists, otherwise 0.
        the start of the list call zlist_first (). Advances the cursor.
        <argument name = "path" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "matches">
        Returns true if the path matches a route in the tree, otherwise false.
        <argument name = "path" type = "string" />
        <return type = "boolean" />
    </method>

    <method name = "hit data">
        Returns the data of a matched route from last ztrie_matches. If the path
        did not match, returns NULL. Do not delete the data as it's owned by
        ztrie.
        <return type = "anything" />
    </method>

    <method name = "hit parameter count">
        Returns the count of parameters that a matched route has.
        <return type = "size" />
    </method>

    <method name = "hit parameters">
        Returns the parameters of a matched route with named regexes from last
        ztrie_matches. If the path did not match or the route did not contain any
        named regexes, returns NULL.
        <return type = "zhashx" />
    </method>

    <method name = "hit asterisk match">
        Returns the asterisk matched part of a route, if there has been no match
        or no asterisk match, returns NULL.
        <return type = "string" />
    </method>

    <method name = "print">
        Print the trie
    </method>
</class>

