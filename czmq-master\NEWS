CZMQ version 4.2.x stable, released on 20xx/xx/xx
=================================================

CZMQ version 4.2.1 stable, released on 2021/01/17
=================================================

* Note for packagers: NSS can now be used and linked against to avoid using
  an internal embedded reimplementation of SHA. It is enabled by default if
  present.

* New DRAFT zsock API to match libzmq's 4.3.4 socket options:
  - zsock_set_xpub_manual_last_value
  - zsock_socks_username, zsock_set_socks_username
  - zsock_socks_password, zsock_set_socks_password
  - zsock_in_batch_size, zsock_set_in_batch_size
  - zsock_out_batch_size, zsock_set_out_batch_size
  - zsock_set_wss_key_pem, zsock_set_wss_cert_pem, zsock_set_wss_trust_pem,
    zsock_set_wss_hostname, zsock_set_wss_trust_system
  - zsock_set_only_first_subscribe
  - zsock_hello_msg, zsock_set_hello_msg
  - zsock_disconnect_msg, zsock_set_disconnect_msg
  - zsock_priority, zsock_set_priority
  - zsock_reconnect_stop, zsock_set_reconnect_stop
  See zsock manpage for more details.

* New DRAFT zframe and zmsg API to print arbitrary length messages:
  - zframe_print_n
  - zmsg_print_n
  See zframe and zmsg manpages for more details.

* New DRAFT zosc* APIs to support the OSC (Open Sound Control) protocol.
  See zosc_* manpages for more details.

* New DRAFT zcertstore_get_state API to get the state stored in the certstore.
  See the zcertstore manpage for more details.

* Support for new libzmq DRAFT socket type ZMQ_DGRAM was added to zsock.

* Fixed #1972 - Findlibzmq.cmake out of date, Windows build fails

* Fixed #1998 - zproxy uses fixed ipc endpoint, causing race condition

* Fixed #2002 - fix socket leak in zbeacon

* Fixed #2020 - use libnss in zdigest if available for SHA functions.

* Fixed #2028 - zpoller docs isn't mentioning file handles

* Fixed #2043 - zhttp_response_recv leaks memory when result is non-zero

* Fixed #2045 - Problem: zframe_print output is truncated

* Fixed #2048 - no logging if zsys is not initialized

* Fixed #2057 - zchunk_slurp() causes assert if file can't be opened

* Fixed #2062 - allow zbeacon to resolve ip address to interface or interface
                name to interface

* Fixed #2064 - Enable ipv4 multicast packet types when using zbeacon

* Fixed #2072 - fix build on Android NDK >= 24

* Fixed #2086 - zsys is re-initialized when logging an error in zsys_shutdown

* Fixed #2090 - "quit" variable in zproc_wait() can overflow

* Fixed #2087 - logsender destroyed before last log message

* Fixed #2103 - if gossip server disconnects and reconnects, persisting gossip
                clients do not exchange tuples anymore when server reconnects

* Fixed #2113 - zcertstore has no API to get the disk state from a custom loader

* Fixed #2115 - various issues around zcertstore API string ownership

* Fixed #2038 - zsock_t sockets do not work in child process after fork

* Fixed #2144 - zhashx_dup() does not copy callbacks


CZMQ version 4.2.0 stable, released on 2019/02/10
=================================================

* Note for packagers: as pkg-config's Requires.private is now used to properly
  propagate dependencies for static builds, the libczmq*-dev or czmq-devel or
  equivalent package should now depend on the libfoo-dev or foo-devel packages
  of all the libraries that czmq is linked against, or pkg-config --libs libczmq
  will fail due to missing dependencies on end users machines.

* New STABLE APIs have been added to wrap new libzmq context
  options:
  - zsys_thread_name_prefix, zsys_set_thread_name_prefix
  - zsys_thread_affinity_cpu_add, zsys_thread_affinity_cpu_remove
  See zsys manpage for more details.

* New STABLE zsock API to match libzmq's 4.3.0 stable socket options:
  - zsock_bindtodevice, zsock_set_bindtodevice
  - zsock_gssapi_service_principal_nametype, zsock_set_gssapi_service_principal_nametype
  - zsock_gssapi_principal_nametype, zsock_set_gssapi_principal_nametype
  See zsock manpage for more details.

* New DRAFT zsock API to match libzmq's 4.3.0 stable socket options:
  - zsock_router_notify, zsock_set_router_notify
  - zsock_multicast_loop, zsock_set_multicast_loop
  - zsock_metadata, zsock_set_metadata
  - zsock_loopback_fastpath, zsock_set_loopback_fastpath
  - zsock_zap_enforce_domain, zsock_set_zap_enforce_domain
  See zsock manpage for more details.

* New DRAFT zconfig API to duplicate a zconfig instance:
  - zconfig_dup
  See zconfig manpage for more details.

* New DRAFT zproxy functionality to let SUB/XSUB subscribe to topics by appending
  additional strings to the FRONTEND/BACKEND control messages.
  See zproxy manpage for more details.

* New DRAFT zsys APIs to print python-like formatted strings:
  - zsys_zprintf
  - zsys_zprintf_error
  - zsys_zplprintf
  - zsys_zplprintf_error
  See zsys manpage for more details.

* New DRAFT zgossip API to unpublish to stop sending a key/value pair to the
  cluster. Use the UNPUBLISH keyword string as a command.
  See zconfig manpage for more details.

* New DRAFT zlistx API to pack and unpack zframes:
  - zlistx_pack, zlistx_unpack
  zlistx are now supported by the zsock_send/receive APIs with type "l".
  See zlistx manpage for more details.

* New DRAFT zlistx API to pack and unpack zframes:
  - zlistx_pack, zlistx_unpack
  zlistx are now supported by the zsock_send/receive APIs with type "l".
  See zlistx manpage for more details.

* New DRAFT zhttp_client, zhttp_server, zhttp_server_options, zhttp_request and
  zhttp_response to build both server and client http applications.
  Requires linking with libcurl and libmicrohttpd.
  See zhttp_* manpages for more details.

* New DRAFT zchunk API to create zero-copy chunks:
  - zchunk_frommem
  - zchunk_packx
  See zchunk manpage for more details.

* Many fixes and improvements to the zproc and zargs DRAFT classes.

* Fixed #1825 - zbeacon_test and zpoller_test hang indefinitely with libzmq 2.x.

* Fixed #1899 - zsys_udp_new leaks file descriptor when process is exec'd.

* Fixed #1907 - build fails on debian/kfreebsd.

* Fixed #1948 - zlistx_head always returns NULL.

* Fixed #1959 - zhash_pack crash on armv7 due to unaligned pointer access.

* Fixed #1961 - unnecessary uuid includes in public headers break builds without
                libuuid.

* Fixed #1995 - timers are called twice if they change the pollset.

CZMQ version 4.1.1 stable, released on 2018/03/22
=================================================

* New DRAFT APIs have been added to the zsys class to get and set
  the zsys_interrupted global variable:
  - zsys_is_interrupted
  - zsys_set_interrupted
  The following DRAFT APIs wrap a new libzmq context API (minimum
  libzmq version with DRAFTs enabled required: 4.2.4):
  - zsys_set_zero_copy_recv
  - zsys_zero_copy_recv
  See zsys manpage for more info.

* New DRAFT APIs have been added to the zproc class to set and get
  the command line arguments string:
  - zproc_args
  - zproc_set_argsx
  The zproc_set_args and zproc_set_env DRAFT APIs have had their
  signature changed.
  See the zproc manpage for more details.

* Existing DRAFT APIs have been deleted from to the zproc class as they
  are redundant and already offered by zsys:
  - zproc_log_debug
  - zproc_log_info
  - zproc_log_notice
  - zproc_log_warning
  - zproc_log_error
  - zproc_set_log_system
  - zproc_set_log_sender
  - zproc_set_log_ident
  - zproc_biface
  - zproc_set_biface
  - zproc_set_max_sockets
  - zproc_set_io_threads
  - zproc_run_as
  - zproc_daemonize
  - zproc_hostname
  - zproc_has_curve
  - zproc_interrupted
  - zproc_czmq_version

* Exising DRAFT APIs to create DRAFT sockets will now return ENOTSUP
  instead of EINVAL when libzmq does not support the socket type:
  - zsock_new_server
  - zsock_new_client
  - zsock_new_radio
  - zsock_new_dish
  - zsock_new_scatter
  - zsock_new_gather
  - zsock_join
  - zsock_leave

* Fixed #1828 - fix build on GNU/Hurd

* Fixed #1829 - fix build on FreeBSD 10.4 and 11.1

* Fixed #1840 - implement basic zproc support for Windows

* Fixed #1847 - fix test segfault when no USER env variable exists

* Fixed #1848 - fix zsock_resolve when using many thousands of sockets

* Fixed #1853 - use SIGTERM on Windows as SIGKILL does not exist

* Fixed #1858 - fix build on MINGGW

* Fixed #1875 - fix build with GCC 8

* Fixed #1876 - fix crash in zsys_shutdown with libzmq 4.2.4 built with
                Tweetnacl

CZMQ version 4.1.0 stable, released on 2017/12/31
=================================================

* New STABLE APIs have been added to wrap new libzmq context
  options:
  - zsys_set_thread_sched_policy
  - zsys_set_thread_priority
  See zsys manpage for more details.

* New DRAFT class to parse command line arguments in a platform
  independent way: zargs. See zargs manpage for more info.

* New DRAFT APIs have been added to the zproc class to manage
  subprocesses in a platform-independent unified API. See the
  zproc manpage for more details.

* New DRAFT APIs have been added to the zactor class to override
  the default destructor:
  - zactor_destructor_fn
  - zactor_set_destructor
  See zactor manpage for more info.

* New DRAFT APIs have been added to the zcertstore class to return
  a zlistx of all the certificates in the store:
  - zcertstore_certs
  See zcertstore manpage for more info.

* New DRAFT APIs have been added to the zcert class to create a new
  zcert from public/secret keypair in text form:
  - zcert_new_from_txt
  See zcert manpage for more info.

* New DRAFT APIs have been added to the zsys class to get/set the
  default "stable" age value of files in zfile:
  - zsys_file_stable_age_msec
  - zsys_set_file_stable_age_msec
  See zsys and zfile manpages for more info.

* New DRAFT APIs have been added to the zstr class to send and receive
  compressed strings:
  - zstr_recv_compress
  - zstr_send_compress
  - zstr_sendm_compress
  To enable them, build with the new (optional) lz4 dependency.
  See zstr manpage for more info.

* New DRAFT APIs have been added to the zgossip class to support CURVE
  security:
  - "ZAP DOMAIN" actor message to set the ZAP domain for authentication
  - "SET PUBLICKEY" actor message to set the local public key
  - "SET SECRETKEY" actor message to set the local secret key
  - new optional parameter appended to "CONNECT" actor message, public
    key of the server
  See zgossip manpage for more info.

* New DRAFT APIs have been added to the zfile class to support managing
  temporary files in a platform independent way:
  - zfile_tmp
  See zfile manpage for more info.

* New DRAFT APIs have been added to the zconfig class to support removing
  a node and its subtrees or only the subtrees:
  - zconfig_remove_subtree
  - zconfig_remove
  See zconfig manpage for more info.

* Added new FFI Python and Ruby bindings.

* Add support for new ZMQ socket monitoring events to the zmonitor class:
  - HANDSHAKE_SUCCEEDED
  - HANDSHAKE_FAILED_NO_DETAIL
  - HANDSHAKE_FAILED_PROTOCOL
  - HANDSHAKE_FAILED_AUTH
  Requires libzmq version 4.2.3 or higher with DRAFT APIs enabled.

* Fixed #1811 - add CZMQ_BUILD_SHARED/STATIC options to CMake to control
                whether to build shared/static libraries. Default to ON.
                The minimum required CMake version has been bumped from
                2.8 to 2.8.8.

* Fixed #1802 - fix using ziflist in a VirtualBox VM running on OSX.

* Fixed #1798 - fix build on Cygwin.

* Fixed #1773 - fix zfile_restat on Windows XP with newer MSVC++.

* Fixed #1765 - fix build on OpenBSD.

* Fixed #1755 - improve reliability of zproxy selftest in slower
                environments.

* Fixed #1753 - fix zloop_set_nonstop to actually use the parameter
                passed by the user rather than always setting
                nonstop to true.

* Fixed #1747 - check for OOM condition in zstr_send* functions.

* Fixed #1730 - improve the internal usage of system random generator
                APIs to be more reliable.

* Fixed #1728 - chomp all leading "/" in zfile_filename.

* Fixed #1726 - do not bind to hard-coded TCP ports in self tests.

* Fixed #1722 - define the PRI* types if not available on Windows.

* Fixed #1715 - document ZSYS_SIGHANDLER behaviour w.r.t. the
                zsys_catch_interrupts API.

* Fixed #1712 - clarify zsock_brecv documentation w.r.t buffers.

* Fixed #1711 - set User-Id field in ZAP responses.

* Fixed #1710 - clarify documentation of zframe_meta - the caller
                must not modify the returned value.

* Fixed #1702 - clarify documentation of zhashx_set_key_comparator
                and zhashx_set_key_hasher.

* Fixed #1675 - fix zhashx_purge memory leak when shrinking.

* Fixed #1672 - fix compile error 'sockaddr_in': undeclared
                identifier on MSVC.

* Fixed #1671 - add support for new (DRAFT) socket types in
                zframe_recv_nowait and zmsg_recv_nowait.

* Fixed #1671 - correctly complete multipart receives in
                zmsg_recv_nowait.

* Fixed #1667 - if ZSYS_IPV6_ADDRESS is link-local but does not
                have the %interface suffix, append it from
                ZSYS_INTERFACE.

* Fixed #1659 - reset all CZMQ internal state in zsys_shutdown
                to facilitate use with fork without exec.

* Fixed #1649 - set static pointers to NULL to fix restarting
                CZMQ via zsys_shutdown -> zsys_init.

* Fixed #1644 - zbeacon on Windows only sends on one interfaces
                with INADDR_BROADCAST set (interface option "*").

* Fixed #1634 - zloop is using reader callback after
                zloop_reader_end.

* Fixed #1632 - NULL pointer exception in ziflist on Windows
                if network adapters are added/removed whilst
                being enumerated.

* Fixed #1623 - remove mentions of retired APIs from manpages.

* Fixed #1618 - fix build with GCC 7.

* Fixed #1616 - zsys_udp_recv will now return NULL when failing
                to retrieve the peer's address instead of
                silently failing.

* Fixed #1616 - zsys_udp_recv's parameter peername must now be
                at least NI_MAXHOST long when IPv6 is enabled.

* Fixed #1616 - add IPv6 support to zbeacon using, by default,
                link-local all-node address fe02::1 in lieu of
                broadcast (which does not exist on v6).
                The multicast address can be specified via zsys
                so, theoretically, it should work across
                multiple networks as well (untested!).

* Fixed #1615 - add link-local all-node fe02::1 as the default
                zsys IPv6 multicast address.

* Fixed #1615 - add IPv6 support to ziflist on *nix through new
                (DRAFT) APIs to avoid changing the output of old
                ones: ziflist_new_ipv6, ziflist_reload_ipv6 and
                ziflist_is_ipv6.

* Fixed #1603 - generated socket options, and public API/ABI, were
                changing depending on the version of libzmq used
                at build time. Keep all the symbols stable instead
                and add additional runtime checks.
                Socket options APIs will now be built as empty
                stubs if unavailable in the libzmq version used at
                build time, and will return an error if not
                available in the libzmq version used at runtime.

* Fixed #1609 - fix zdir selftest on Windows.

* Fixed #1608 - document that on Windows it is necessary to call
                zsys_shutdown manually as DLLs do not support
                atexit() callbacks, and manually call it from all
                unit tests.

* Fixed #1606 - use inproc instead of ipc in zproxy selftest to
                fix failure on Windows.

* Fixed #1602 - ZSYS_INTERFACE: if the value is a single digit,
                interpret it as an index. Facilitates using it
                on Windows with complicated interface names.

* Fixed #1599 - correctly handle CTRL_CLOSE_EVENT in Windows.

* Fixed #1597 - added czmq_private_selftest to test private
                classes without exporting their symbols in the
                shared library.

* Fixed #1597 - declare some internal-only helper functions as
                static to avoid exporting their symbols.

* Fixed #1594 - correctly return -1 and set errno to EINVAL from
                zpoller_remove if the reader does not exist

* Fixed #1590 - zsys_shutdown does not call zmq_term when it
                closes sockets which results in dangling sockets
                warnings.

* Fixed #1588 - fix debian/kfreebsd build and always use external
                UUID library, if available, regardless of the OS.

* Fixed #1459 - clarify zgossip documentation.

* Fixed #1026 - zmsg_remove decreased count of frames even when it
                failed.

* Fixed #1032 - clarify documentation of zhash_autofree and zhashx
                callbacks.


CZMQ version 4.0.2 stable, released on 2016/12/31
=================================================

* Fixed #1559 - DRAFT zsock_new_*_checked symbols leak

* Fixed #1560 - zsock options could be available at build time but
                not at runtime. Added runtime check, an error will
                be printed if the option is not available.

* Fixed #1565 - improved compatibility with Solaris/Illumos

* Fixed #1573 - memory leak when message send fails in internal usage


CZMQ version 4.0.1 stable, released on 2016/11/10
=================================================

* Version 4.0.0 introduced the DRAFT mechanism, but it had a flaw:
  the internally defined DRAFT symbols were leaking as global in the
  shared library. This meant that although the API was stable, the
  ABI could in some cases not be.
  This has now been fixed using compiler's visibility attribute to
  avoid this problem.


CZMQ version 4.0.0 stable, released on 2016/11/04
=================================================

* v4.0.0 breaks API and ABI compatibility with v3.0.2.
  The ABI version has been bumped to 4.

* The v2 API, which have been declared DEPRECATED in v3.0.0
  released on 2014/10/15, have now been declared RETIRED
  and removed from this release. The following classes are gone:

  - zauth_v2, use zauth
  - zbeacon_v2, use zbeacon
  - zctx, use zsock
  - zmonitor_v2, use zmonitor
  - zmutex
  - zproxy_v2, use zproxy
  - zsocket, use zsock
  - zsockopt, use zsock
  - zthread, use zactor

  The following DEPRECATED methods have also been declared
  RETIRED and removed from their STABLE classes:

  - zcert_fprint, use zcert_print
  - zcertstore_fprint, use zcertstore_print
  - zhash_foreach (and callback), use zhash_first/next
  - zhashx_foreach (and callback), use zhashx_first/next
  - zhashx_autofree, use zhashx_set_destructor
  - zloop_ignore_interrupts, use zloop_set_nonstop
  - zpoller_ignore_interrupts, use zpoller_set_nonstop

* Some STABLE APIs unfortunately had to be slightly changed to
  fix critical issues. From v4.0.0 we commit to avoid incompatible changes
  to STABLE APIs.
  The following signatures have changed:

  - v3.0.2: byte * zarmour_decode (zarmour_t *self, const char *data, size_t *decode_size)
    v4.0.0: zchunk_t * zarmour_decode (zarmour_t *self, const char *data)
  - v3.0.2: zarmour_mode_t zarmour_mode (zarmour_t *self)
    v4.0.0: int zarmour_mode (zarmour_t *self)
  - v3.0.2: int zarmour_test (bool verbose)
    v4.0.0: void zarmour_test (bool verbose)
  - v3.0.2: zcert_t * zcert_new_from (byte *public_key, byte *secret_key)
    v4.0.0: zcert_t * zcert_new_from (const byte *public_key, const byte *secret_key)
  - v3.0.2: byte * zcert_public_key (zcert_t *self)
    v4.0.0: const byte * zcert_public_key (zcert_t *self)
  - v3.0.2: byte * zcert_secret_key (zcert_t *self)
    v4.0.0: const byte * zcert_secret_key (zcert_t *self)
  - v3.0.2: char * zcert_public_txt (zcert_t *self)
    v4.0.0: const * zcert_public_txt (zcert_t *self)
  - v3.0.2: char * zcert_secret_txt (zcert_t *self)
    v4.0.0: const * zcert_secret_txt (zcert_t *self)
  - v3.0.2: char * zcert_meta (zcert_t *self, const char *name)
    v4.0.0: const * zcert_meta (zcert_t *self, const char *name)
  - v3.0.2: void zdigest_update (zdigest_t *self, byte *buffer, size_t length)
    v4.0.0: void zdigest_update (zdigest_t *self, const byte *buffer, size_t length)
  - v3.0.2: byte * zdigest_data (zdigest_t *self)
    v4.0.0: const byte * zdigest_data (zdigest_t *self)
  - v3.0.2: enum zdir_patch_op_t {ZDIR_PATCH_CREATE, ZDIR_PATCH_DELETE}
    v4.0.0: define ZDIR_PATCH_CREATE ZDIR_PATCH_DELETE
  - v3.0.2: zdir_patch_t * zdir_patch_new (const char *path, zfile_t *file,
                zdir_patch_op_t op, const char *alias)
    v4.0.0: zdir_patch_t * zdir_patch_new (const char *path, zfile_t *file, int op,
                const char *alias)
  - v3.0.2: zdir_patch_op_t zdir_patch_op (zdir_patch_t *self)
    v4.0.0: int zdir_patch_op (zdir_patch_t *self)
  - v3.0.2: void zhash_test (int verbose)
    v4.0.0: void zhash_test (bool verbose)
  - v3.0.2: void zlist_test (int verbose)
    v4.0.0: void zlist_test (bool verbose)
  - v3.0.2: void zlistx_set_destructor (zlistx_t *self, czmq_destructor destructor)
    v4.0.0: void zlistx_set_destructor (zlistx_t *self, zlistx_destructor_fn destructor)
  - v3.0.2: void zlistx_set_duplicator (zlistx_t *self, czmq_duplicator duplicator)
    v4.0.0: void zlistx_set_duplicator (zlistx_t *self, zlistx_duplicator_fn duplicator)
  - v3.0.2: void zlistx_set_comparator (zlistx_t *self, czmq_comparator comparator)
    v4.0.0: void zlistx_set_comparator (zlistx_t *self, zlistx_comparator_fn comparator)
  - v3.0.2: void zlistx_test (int verbose)
    v4.0.0: void zlistx_test (bool verbose)
  - v3.0.2: zmsg_t * zmsg_load (zmsg_t *self, FILE *file)
    v4.0.0: zmsg_t * zmsg_load (FILE *file)
  - v3.0.2: zmsg_t * zmsg_decode (const byte *buffer, size_t buffer_size)
    v4.0.0: zmsg_t * zmsg_decode (zframe_t *frame)
  - v3.0.2: size_t zmsg_encode (zmsg_t *self, byte **buffer)
    v4.0.0: zframe_t * zmsg_encode (zmsg_t *self)
  - v3.0.2: int zsys_udp_send (SOCKET udpsock, zframe_t *frame, inaddr_t *address)
    v4.0.0: int zsys_udp_send (SOCKET udpsock, zframe_t *frame, inaddr_t *address, int addrlen)
  - v3.0.2: zframe_t * zsys_udp_recv (SOCKET udpsock, char *peername)
    v4.0.0: zframe_t * zsys_udp_recv (SOCKET udpsock, char *peername, int peerlen)

* The following new STABLE methods were added to STABLE classes:

  - zarmour_mode_str, zarmour_print (see doc/zarmour.txt for details)
  - zconfig_test (see doc/zconfig.txt for details)
  - zframe_meta  (see doc/zframe.txt for details)
  - zlistx_head zlistx_tail, zlistx_destructor_fn, zlistx_destructor_fn,
    zlistx_comparator_fn (see doc/zlistx.txt for details)
  - zloop_set_nonstop (see doc/zloop.txt for details)
  - zpoller_set_nonstop (see doc/zpoller.txt for details)
  - zsys_set_max_msgsz, zsys_max_msgsz, zsys_set_ipv6_address, zsys_ipv6_address,
    zsys_set_ipv6_mcast_address, zsys_ipv6_mcast_address, zsys_set_auto_use_fd,
    zsys_auto_use_fd (see doc/zsys.txt for details)

* New DRAFT APIs early-release mechanism. New APIs will be introduced early
  in public releases, and until they are stabilized and guaranteed not to
  change anymore they will be unavailable unless the new build flag
  --enable-drafts is used. This will allow developers and early adopters to
  test new APIs before they are finalized.
  NOTE: as the name implies, NO GUARANTEE is made on the stability of these APIs.
  They might change or disappear entirely. Distributions are recommended NOT to
  build with them.

  The following DRAFT classes were added:

  - zproc
  - ztimerset
  - ztrie

  The following DRAFT methods were added to their respective STABLE class:

  - zcert_unset_meta
  - zcertstore_set_loader, zcertstore_empty
  - zframe_routing_id, zframe_set_routing_id, zframe_group, zframe_set_group
  - zhashx_unpack_own, zhashx_pack_own, zhashx_serializer_fn, zhashx_deserializer_fn
  - zmsg_routing_id, zmsg_set_routing_id
  - zstr_str

* Support for new libzmq DRAFT socket types was added to zsock. See libzmq 4.2.0
  release notes and doc/zsock.txt for more details. NOTE: until libzmq declares
  these as STABLE, the CZMQ support will be in DRAFT state as well.

* Support for all new libzmq socket and context options was added to zsock. See
  libzmq 4.2.0 release notes and doc/zsock.txt for more details.

* Many, many bug fixes. Check git log for the complete set of changes.


CZMQ version 4.0.0 (rc1), released on 2016/11/01
=================================================

Release candidate 1 for 4.0.0. Changelog TBD.


CZMQ version 3.0.2 stable, released on 2015/06/08
=================================================

Added LICENSE, README, and CONTRIBUTING.


CZMQ version 3.0.1 stable, released on 2015/06/02
=================================================


CZMQ version 3.0.0 (rc1), released on 2014/10/15
================================================


CZMQ version 2.2.0 stable, released on 2014/04/23
=================================================


CZMQ version 2.1.0 stable, released on 2014/03/20
=================================================


CZMQ version 2.0.3 stable, released on 2013/11/16
=================================================

Fixed to support ZeroMQ 3.2; was causing a build error due to use of
ZMQ_STREAM in zsockopt.c.

CZMQ version 2.0.2 stable, released on 2013/10/08
=================================================

* Fixed builds on MSVC and FreeBSD.
* Fixed minor build issues in RC2.

CZMQ version 2.0.1 (rc2), released on 2013/09/21
================================================

Updated to support ZeroMQ v4.x. If you are using an older version of CZMQ
it will not build correctly with ZeroMQ v4.x. Sorry about that.

Added support for the new socket options:
 
 * zsocket_set_req_relaxed ()
 * zsocket_set_req_correlate ()
 * zsocket_set_conflate ()


CZMQ version 2.0.0 (rc1), released on 2013/09/19
================================================

The changes are mainly tools to support encryption via the libzmq CURVE 
mechanism, but also some new API constructs to make it simpler to do 
common messaging work.

Bug fixes and cleanups of codebase:

* zconfig_load () failed if the ZPL file contained a blank line.

* zconfig_locate () allows and ignores initial '/' on pathname.

* Added zsys_vprintf () to centralize all variable string formatting;
  this code was duplicated many times across the project.

* Updated zmsg interface documentation to be more detailed.

* Removed deprecated zstr_sendfm () method.

* Removed deprecated zstr_sendf () method.

* Removed definition of ZMQ_IGNERR and added zloop_set_tolerant ()
  to configure a specific poller as error-tolerant.
  
* Removed deprecated zframe and zmsg zero-copy methods; this API was
  too complex and split over several classes, and IMO if we want this 
  optimization it should be in a new zero-copy class with a clean API.

* Removed deprecated zsocket zero-copy methods, for above reason.

* Removed deprecated zlist_copy method.

* Renamed zframe_print_to_stream to zframe_fprint and kept old name
  as deprecated macro.

* Removed (not deprecated) zctx_set_hwm () as its semantics were not
  consistent, and confusing to people.
  
* Removed (not deprecated) zctx_hwm (). We now have separate HWMs for
  normal socket SND and RCV, and for inter-thread pipes. There is no
  need to provide accessor methods for these.
  
New API methods to simplify common messaging tasks:

* Added zpoller class to provide new minimalist interface to zmq_poll.
  This class makes it very simple to read off a set of sockets, which
  is an 80% usecase.

* Added zstr_sendx () to send multiple strings in one call. Inspired
  by PyZMQ's multipart send method.

* Added zstr_recvx () to receive multiple strings in one call. We do
  a lot of multipart string reads; this makes it simpler for simple
  cases.

* Added zclock_timestr () to provide date/time as printable string.

* Added zmsg_append () that nullifies caller's frame reference; this
  is more consistent with the API style than zmsg_add (), which is
  deprecated.
  
New classes used for security infrastructure:

* Added zchunk class (from FileMQ), for managing memory blocks. 
  This is a simpler model than zframes, which are optimized for 
  message data. 
  
* Added zhash_comment () to add comments to a serialized zhash file.

* Added zhash_refresh () to reload a backing file if modified on disk.

* Added zconfig_comment () to add comments to saved config file, and
  added code to save such comments from file when loading it.

* Moved zfile (char *filename) methods to zsys, to make place for 
  a new zfile class based on the CLASS object model. The old methods 
  are still provided but are deprecated.
  
* Added zfile class (from FileMQ), for managing files. This provides
  a rich file class with metadata and operations. This is a component
  for directory management (zdir).
  
* Added zdir class (from FileMQ), for managing directories. This lets
  us work with filesystem directories. I wanted this to be able to 
  load certificates for security authentication. 
  
* The zdir and zfile classes are slightly stripped down from their 
  FileMQ originals; no symbolic links, no directory difference tools.

* Unfortunately to keep the old zfile_size and zfile_time methods, I
  had to use zfile_cursize and zfile_modified in the new class. This
  is a little clumsy.

* Added zsys_file_modified () to return timestamp for a filename.
  
* Added zcert class to work with Curve certificates. A certificate is 
  a public + secret key pair, plus metadata like name, email, etc. and
  is saved as two files, a public text file and a secret text file.
  Certificates are stored in ZPL (ZMQ RFC 4) configuration file format.
  
* Added zauth class to automate ZAP authenticators. This class does a
  simple plug and play authentication of clients against certificates
  stored in the $HOME/.curve directory. This can be extended over time
  with other designs.
  
* Added zcertstore class to work with Curve certificate stores. This is
  a directory or in-memory store holding a set of certificates.

* Extended zsockopt generation to allow use of binary keys in CURVE
  key options (e.g. zsocket_set_curve_secretkey_bin ()).
  
* Added addons/makecert.c tool to generate CURVE certificates.

* Added examples/security directory with several examples.

For full details, see git log.


CZMQ version 1.4.1 (stable), released on 2013/05/01
===================================================

Changes
-------

* Fixed assertion in zbeacon on sendto error.
* Fixed packaging of platform scripts in /builds.
* See git log for rest.


CZMQ version 1.4.0 (stable), released on 2013/04/30
===================================================

Changes
-------

* Added zbeacon class.
* See git log for rest.


CZMQ version 1.3.2 (stable), released on 2012/12/22
===================================================

Changes
-------

* See git log.


CZMQ version 1.3.1 (stable), released on 2012/10/27
===================================================

Changes
-------

* See git log.


CZMQ version 1.3.0 (rc), not released
=====================================


CZMQ version 1.2.0 (stable), released on 2012/08/06
===================================================

Changes
-------

* zsockopt_ functions renamed to zsocket_, for clarity. Old names are
  still provided for compatibility.

* SUB sockets are no longer subscribed to everything.

* Fixed issue CZMQ-7, wrong name used for CZMQ man page.

* Changed zsocket_connect() to return 0 or -1, since zmq_connect() now
  checks endpoints properly (issue LIBZMQ-207).

* All classes handle memory exhaustion correctly by returning an error
  instead of asserting.

* zsocket_identity implemented correctly.

* Added zctx_underlying() to provide access to low-level 0MQ context
  object, to allow manipulation for extreme cases and tests.

* For the rest, check the git history :-)


CZMQ version 1.1.0 (stable), released on 2011/08/30
===================================================

Changes
-------

* Fixed issue CZMQ-2. (https://zeromq.jira.com/browse/CZMQ-2)

* Removed unused ctx argument from zthread_new() call.

* zloop works with zmq_pollitem_t * instead of void * sockets; allows
  use of native FDs in reactor as well as 0MQ sockets.

* zloop_reader renamed to zloop_poller.

* zloop_cancel renamed to zloop_poller_end, and API changed to work with
  zmq_pollitem_t * instead of a single 0MQ socket void *.

* Added zloop_timer_end, taking same argument as for zloop_timer.

* zloop_poller_end cancels ALL handlers for the socket or FD.

* Added zmsg_content_size method to report full content size of message.

* zmsg_save returns 0/-1 depending on success/failure (previously returned
  void).

* zmsg_dump limited to first 10 frames, to allow mix of large and small
  messages in applications.

* zframe_print truncates output at 70 chars to prevent over-long output.

* Added ability for zsocket_bind to bind to ephemeral port in range 0xc000
  to 0xffff.

* zmq_bind always returns port number bound to, if successful.

* Added zfile class with minimal helper functions.

* Added zfile_size method to return file size, as ssize_t.

* Added socket multipart flush to zctx_destroy to work around libzmq issue
  134, which asserts if a fair-queuing socket still has input at close.

* Added zframe_eq method to compare two frames' size and content.

* Changed zmsg_load to append to an optional existing message.

* Fixed zloop to allow timers to be added after reactor was started.
  Without this fix, the reactor would spin at max speed instead of
  properly calculating the timer. (Luc Heinrich)

* Modified zmsg_pushstr and zmsg_addstr to accept variable argument list
  and do vsnprintf formatting of provided string.

* Added zhash_rename function to allow soft renaming of item keys.

* Added tblsize and tbllast macros, so very useful when you need them.

* Added zmsg_encode and zmsg_decode functions to serialize messages to
  and from a byte buffer.

* Added zframe_recv_nowait method to read frame without blocking.


CZMQ version 1.0.0 (stable), released on 2011/04/26
===================================================

Changes
-------

* At build time, reports error if libzmq version isn't at least 2.1.

* Renamed project to CZMQ for coherence with other language bindings.


libzapi version 1.3.2 (beta), released on 2011/04/15
====================================================

Changes
-------

* Added zsocket_type_str method to return printable socket type.

* Added zloop_set_verbose method to enable/disable reactor activity.

* Fixed zloop to call right handlers even if handlers register/cancel
  other handlers.

* Fixed zloop to not call handlers when interrupted.

* Added zsocket_set_hwm method to emulate 2.x option (sets both send
  and recv high-water marks).

* Several parts of the 3.0 porting didn't work properly - fixed.

* Added macro set to aid portability from 2.1 to 3.0: ZMQ_POLL_MSEC,
  ZMQ_DONTWAIT, zmq_sendmsg, zmq_recvmsg.


libzapi version 1.3.1 (beta), released on 2011/04/09
====================================================

Changes
-------

* Added compatibility for new libzmq/3.0 API.


libzapi version 1.3.0 (beta), released on 2011/04/08
====================================================

Changes
-------

* Ported to win32, all selftests pass successfully.

* New zsockopt class provides API to set/get socket options.

* New zsocket class provides API to connect and bind sockets using
  printf semantics to format endpoints.

* Threading API now supports detached and attached threads via zthread
  class, and zthread_new and zthread_fork methods.

* Pipes to attached threads have HWM of 1 (each side) to block runaway
  writers.

* In zframe class, added print, reset, strdup, streq, strhex, dup methods.

* In zmsg class, added last, wrap, unwrap, popstr, pushstr, addstr, dup
  methods.

* In zclock class, added log method.

* In zstr class, added sendm and recv_nowait methods.

* Added Valgrind suppression file for libzmq.

Bug Fixes
---------

* Fixed error in thread pipe handling which sometimes provoked libzmq
  assertion failure at shutdown.


libzapi version 1.2.1 (beta), released on 2011/04/01
====================================================

Changes
-------

* Rewrote zctx class, which was wrongly working with sockets in two
  threads. Much simpler, and properly stable now.

* Project renamed to 'libzapi' for consistency with 'libzmq'.

* Expanded main documentation page significantly.


zapi version 1.2.0 (beta), released on 2011/03/24
=================================================

Changes
-------

* Added zclock class for portable millisecond timers and delays.

* Expanded documentation significantly.


zapi version 1.1.0 (beta), released on 2011/03/23
=================================================

Changes
-------

* Added zctx_thread_new method to create child threads with a PAIR pipe
  to talk to them.

* Made zapi threadsafe, specifically multiple threads can share the same
  context and thus talk to each other.


zapi version 1.0.0 (beta), released on 2011/03/22
=================================================

Changes
-------

* Initial version of library finished, tested, documented, and packaged.


