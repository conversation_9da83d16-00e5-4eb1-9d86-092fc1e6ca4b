/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#include <stdio.h>
#include <stdlib.h>
#include <jni.h>
#include "czmq.h"
#include "org_zeromq_czmq_ZhttpResponse.h"

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1new (JNIEnv *env, jclass c)
{
    //  Disable CZMQ signal handling; allow Java to deal with it
    zsys_handler_set (NULL);
    jlong new_ = (jlong) (intptr_t) zhttp_response_new ();
    return new_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1destroy (JNIEnv *env, jclass c, jlong self)
{
    zhttp_response_destroy ((zhttp_response_t **) &self);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1send (JNIEnv *env, jclass c, jlong self, jlong sock, jlong connection)
{
    jint send_ = (jint) zhttp_response_send ((zhttp_response_t *) (intptr_t) self, (zsock_t *) (intptr_t) sock, (void **) (intptr_t) &connection);
    return send_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1recv (JNIEnv *env, jclass c, jlong self, jlong client, jlong arg, jlong arg2)
{
    jint recv_ = (jint) zhttp_response_recv ((zhttp_response_t *) (intptr_t) self, (zhttp_client_t *) (intptr_t) client, (void **) (intptr_t) &arg, (void **) (intptr_t) &arg2);
    return recv_;
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1contentType (JNIEnv *env, jclass c, jlong self)
{
    char *content_type_ = (char *) zhttp_response_content_type ((zhttp_response_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, content_type_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1setContentType (JNIEnv *env, jclass c, jlong self, jstring value)
{
    char *value_ = (char *) (*env)->GetStringUTFChars (env, value, NULL);
    zhttp_response_set_content_type ((zhttp_response_t *) (intptr_t) self, value_);
    (*env)->ReleaseStringUTFChars (env, value, value_);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1statusCode (JNIEnv *env, jclass c, jlong self)
{
    jint status_code_ = (jint) zhttp_response_status_code ((zhttp_response_t *) (intptr_t) self);
    return status_code_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1setStatusCode (JNIEnv *env, jclass c, jlong self, jint status_code)
{
    zhttp_response_set_status_code ((zhttp_response_t *) (intptr_t) self, (uint32_t) status_code);
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1headers (JNIEnv *env, jclass c, jlong self)
{
    jlong headers_ = (jlong) (intptr_t) zhttp_response_headers ((zhttp_response_t *) (intptr_t) self);
    return headers_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1contentLength (JNIEnv *env, jclass c, jlong self)
{
    jlong content_length_ = (jlong) zhttp_response_content_length ((zhttp_response_t *) (intptr_t) self);
    return content_length_;
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1content (JNIEnv *env, jclass c, jlong self)
{
    char *content_ = (char *) zhttp_response_content ((zhttp_response_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, content_);
    return return_string_;
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1getContent (JNIEnv *env, jclass c, jlong self)
{
    char *get_content_ = (char *) zhttp_response_get_content ((zhttp_response_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, get_content_);
    zstr_free (&get_content_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1setContentConst (JNIEnv *env, jclass c, jlong self, jstring content)
{
    char *content_ = (char *) (*env)->GetStringUTFChars (env, content, NULL);
    zhttp_response_set_content_const ((zhttp_response_t *) (intptr_t) self, content_);
    (*env)->ReleaseStringUTFChars (env, content, content_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1resetContent (JNIEnv *env, jclass c, jlong self)
{
    zhttp_response_reset_content ((zhttp_response_t *) (intptr_t) self);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpResponse__1_1test (JNIEnv *env, jclass c, jboolean verbose)
{
    zhttp_response_test ((bool) verbose);
}

