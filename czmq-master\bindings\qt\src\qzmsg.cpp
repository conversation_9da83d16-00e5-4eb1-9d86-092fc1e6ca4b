/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZmsg::QZmsg (zmsg_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Create a new empty message object
QZmsg::QZmsg (QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zmsg_new ();
}

///
//  Receive message from socket, returns zmsg_t object or NULL if the recv
//  was interrupted. Does a blocking recv. If you want to not block then use
//  the zloop class or zmsg_recv_nowait or zmq_poll to check for socket input
//  before receiving.
QZmsg* QZmsg::recv (void *source, QObject *qObjParent)
{
    return new QZmsg (zmsg_recv (source), qObjParent);
}

///
//  Load/append an open file into new message, return the message.
//  Returns NULL if the message could not be loaded.
QZmsg* QZmsg::load (FILE *file, QObject *qObjParent)
{
    return new QZmsg (zmsg_load (file), qObjParent);
}

///
//  Decodes a serialized message frame created by zmsg_encode () and returns
//  a new zmsg_t object. Returns NULL if the frame was badly formatted or
//  there was insufficient memory to work.
QZmsg* QZmsg::decode (QZframe *frame, QObject *qObjParent)
{
    return new QZmsg (zmsg_decode (frame->self), qObjParent);
}

///
//  Generate a signal message encoding the given status. A signal is a short
//  message carrying a 1-byte success/failure code (by convention, 0 means
//  OK). Signals are encoded to be distinguishable from "normal" messages.
QZmsg* QZmsg::newSignal (byte status, QObject *qObjParent)
{
    return new QZmsg (zmsg_new_signal (status), qObjParent);
}

///
//  Destroy a message object and all frames it contains
QZmsg::~QZmsg ()
{
    zmsg_destroy (&self);
}

///
//  Send message to destination socket, and destroy the message after sending
//  it successfully. If the message has no frames, sends nothing but destroys
//  the message anyhow. Nullifies the caller's reference to the message (as
//  it is a destructor).
int QZmsg::send (void *dest)
{
    int rv = zmsg_send (&self, dest);
    return rv;
}

///
//  Send message to destination socket as part of a multipart sequence, and
//  destroy the message after sending it successfully. Note that after a
//  zmsg_sendm, you must call zmsg_send or another method that sends a final
//  message part. If the message has no frames, sends nothing but destroys
//  the message anyhow. Nullifies the caller's reference to the message (as
//  it is a destructor).
int QZmsg::sendm (void *dest)
{
    int rv = zmsg_sendm (&self, dest);
    return rv;
}

///
//  Return size of message, i.e. number of frames (0 or more).
size_t QZmsg::size ()
{
    size_t rv = zmsg_size (self);
    return rv;
}

///
//  Return total size of all frames in message.
size_t QZmsg::contentSize ()
{
    size_t rv = zmsg_content_size (self);
    return rv;
}

///
//  Return message routing ID, if the message came from a ZMQ_SERVER socket.
//  Else returns zero.
quint32 QZmsg::routingId ()
{
    uint32_t rv = zmsg_routing_id (self);
    return rv;
}

///
//  Set routing ID on message. This is used if/when the message is sent to a
//  ZMQ_SERVER socket.
void QZmsg::setRoutingId (quint32 routingId)
{
    zmsg_set_routing_id (self, (uint32_t) routingId);

}

///
//  Push frame to the front of the message, i.e. before all other frames.
//  Message takes ownership of frame, will destroy it when message is sent.
//  Returns 0 on success, -1 on error. Deprecates zmsg_push, which did not
//  nullify the caller's frame reference.
int QZmsg::prepend (QZframe *frameP)
{
    int rv = zmsg_prepend (self, &frameP->self);
    return rv;
}

///
//  Add frame to the end of the message, i.e. after all other frames.
//  Message takes ownership of frame, will destroy it when message is sent.
//  Returns 0 on success. Deprecates zmsg_add, which did not nullify the
//  caller's frame reference.
int QZmsg::append (QZframe *frameP)
{
    int rv = zmsg_append (self, &frameP->self);
    return rv;
}

///
//  Remove first frame from message, if any. Returns frame, or NULL.
QZframe * QZmsg::pop ()
{
    QZframe *rv = new QZframe (zmsg_pop (self));
    return rv;
}

///
//  Push block of memory to front of message, as a new frame.
//  Returns 0 on success, -1 on error.
int QZmsg::pushmem (const void *data, size_t size)
{
    int rv = zmsg_pushmem (self, data, size);
    return rv;
}

///
//  Add block of memory to the end of the message, as a new frame.
//  Returns 0 on success, -1 on error.
int QZmsg::addmem (const void *data, size_t size)
{
    int rv = zmsg_addmem (self, data, size);
    return rv;
}

///
//  Push string as new frame to front of message.
//  Returns 0 on success, -1 on error.
int QZmsg::pushstr (const QString &string)
{
    int rv = zmsg_pushstr (self, string.toUtf8().data());
    return rv;
}

///
//  Push string as new frame to end of message.
//  Returns 0 on success, -1 on error.
int QZmsg::addstr (const QString &string)
{
    int rv = zmsg_addstr (self, string.toUtf8().data());
    return rv;
}

///
//  Push formatted string as new frame to front of message.
//  Returns 0 on success, -1 on error.
int QZmsg::pushstrf (const QString &param)
{
    int rv = zmsg_pushstrf (self, "%s", param.toUtf8().data());
    return rv;
}

///
//  Push formatted string as new frame to end of message.
//  Returns 0 on success, -1 on error.
int QZmsg::addstrf (const QString &param)
{
    int rv = zmsg_addstrf (self, "%s", param.toUtf8().data());
    return rv;
}

///
//  Pop frame off front of message, return as fresh string. If there were
//  no more frames in the message, returns NULL.
QString QZmsg::popstr ()
{
    char *retStr_ = zmsg_popstr (self);
    QString rv = QString (retStr_);
    zstr_free (&retStr_);
    return rv;
}

///
//  Push encoded message as a new frame. Message takes ownership of
//  submessage, so the original is destroyed in this call. Returns 0 on
//  success, -1 on error.
int QZmsg::addmsg (QZmsg *msgP)
{
    int rv = zmsg_addmsg (self, &msgP->self);
    return rv;
}

///
//  Remove first submessage from message, if any. Returns zmsg_t, or NULL if
//  decoding was not successful.
QZmsg * QZmsg::popmsg ()
{
    QZmsg *rv = new QZmsg (zmsg_popmsg (self));
    return rv;
}

///
//  Remove specified frame from list, if present. Does not destroy frame.
void QZmsg::remove (QZframe *frame)
{
    zmsg_remove (self, frame->self);

}

///
//  Set cursor to first frame in message. Returns frame, or NULL, if the
//  message is empty. Use this to navigate the frames as a list.
QZframe * QZmsg::first ()
{
    QZframe *rv = new QZframe (zmsg_first (self));
    return rv;
}

///
//  Return the next frame. If there are no more frames, returns NULL. To move
//  to the first frame call zmsg_first(). Advances the cursor.
QZframe * QZmsg::next ()
{
    QZframe *rv = new QZframe (zmsg_next (self));
    return rv;
}

///
//  Return the last frame. If there are no frames, returns NULL.
QZframe * QZmsg::last ()
{
    QZframe *rv = new QZframe (zmsg_last (self));
    return rv;
}

///
//  Save message to an open file, return 0 if OK, else -1. The message is
//  saved as a series of frames, each with length and data. Note that the
//  file is NOT guaranteed to be portable between operating systems, not
//  versions of CZMQ. The file format is at present undocumented and liable
//  to arbitrary change.
int QZmsg::save (FILE *file)
{
    int rv = zmsg_save (self, file);
    return rv;
}

///
//  Serialize multipart message to a single message frame. Use this method
//  to send structured messages across transports that do not support
//  multipart data. Allocates and returns a new frame containing the
//  serialized message. To decode a serialized message frame, use
//  zmsg_decode ().
QZframe * QZmsg::encode ()
{
    QZframe *rv = new QZframe (zmsg_encode (self));
    return rv;
}

///
//  Create copy of message, as new message object. Returns a fresh zmsg_t
//  object. If message is null, or memory was exhausted, returns null.
QZmsg * QZmsg::dup ()
{
    QZmsg *rv = new QZmsg (zmsg_dup (self));
    return rv;
}

///
//  Send message to zsys log sink (may be stdout, or system facility as
//  configured by zsys_set_logstream).
//  Long messages are truncated.
void QZmsg::print ()
{
    zmsg_print (self);

}

///
//  Send message to zsys log sink (may be stdout, or system facility as
//  configured by zsys_set_logstream).
//  Message length is specified; no truncation unless length is zero.
//  Backwards compatible with zframe_print when length is zero.
void QZmsg::printN (size_t size)
{
    zmsg_print_n (self, size);

}

///
//  Return true if the two messages have the same number of frames and each
//  frame in the first message is identical to the corresponding frame in the
//  other message. As with zframe_eq, return false if either message is NULL.
bool QZmsg::eq (QZmsg *other)
{
    bool rv = zmsg_eq (self, other->self);
    return rv;
}

///
//  Return signal value, 0 or greater, if message is a signal, -1 if not.
int QZmsg::signal ()
{
    int rv = zmsg_signal (self);
    return rv;
}

///
//  Probe the supplied object, and report if it looks like a zmsg_t.
bool QZmsg::is (void *self)
{
    bool rv = zmsg_is (self);
    return rv;
}

///
//  Self test of this class.
void QZmsg::test (bool verbose)
{
    zmsg_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
