/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#ifndef QML_ZTIMERSET_H
#define QML_ZTIMERSET_H

#include <QtQml>

#include <czmq.h>
#include "qml_czmq_plugin.h"


class QmlZtimerset : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isNULL READ isNULL)

public:
    ztimerset_t *self;

    QmlZtimerset() { self = NULL; }
    bool isNULL() { return self == NULL; }

    static QObject* qmlAttachedProperties(QObject* object); // defined in QmlZtimerset.cpp

public slots:
    //  Add a timer to the set. Returns timer id if OK, -1 on failure.
    int add (size_t interval, ztimerset_fn handler, void *arg);

    //  Cancel a timer. Returns 0 if OK, -1 on failure.
    int cancel (int timerId);

    //  Set timer interval. Returns 0 if OK, -1 on failure.
    //  This method is slow, canceling the timer and adding a new one yield better performance.
    int setInterval (int timerId, size_t interval);

    //  Reset timer to start interval counting from current time. Returns 0 if OK, -1 on failure.
    //  This method is slow, canceling the timer and adding a new one yield better performance.
    int reset (int timerId);

    //  Return the time until the next interval.
    //  Should be used as timeout parameter for the zpoller wait method.
    //  The timeout is in msec.
    int timeout ();

    //  Invoke callback function of all timers which their interval has elapsed.
    //  Should be call after zpoller wait method.
    //  Returns 0 if OK, -1 on failure.
    int execute ();
};

class QmlZtimersetAttached : public QObject
{
    Q_OBJECT
    QObject* m_attached;

public:
    QmlZtimersetAttached (QObject* attached) {
        Q_UNUSED (attached);
    };

public slots:
    //  Self test of this class.
    void test (bool verbose);

    //  Create new timer set.
    QmlZtimerset *construct ();

    //  Destroy a timer set
    void destruct (QmlZtimerset *qmlSelf);
};


QML_DECLARE_TYPEINFO(QmlZtimerset, QML_HAS_ATTACHED_PROPERTIES)

#endif
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
