/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZargs.h"


///
//  Return program name (argv[0])
const QString QmlZargs::progname () {
    return QString (zargs_progname (self));
};

///
//  Return number of positional arguments
size_t QmlZargs::arguments () {
    return zargs_arguments (self);
};

///
//  Return first positional argument or NULL
const QString QmlZargs::first () {
    return QString (zargs_first (self));
};

///
//  Return next positional argument or NULL
const QString QmlZargs::next () {
    return QString (zargs_next (self));
};

///
//  Return first named parameter value, or NULL if there are no named
//  parameters, or value for which zargs_param_empty (arg) returns true.
const QString QmlZargs::paramFirst () {
    return QString (zargs_param_first (self));
};

///
//  Return next named parameter value, or NULL if there are no named
//  parameters, or value for which zargs_param_empty (arg) returns true.
const QString QmlZargs::paramNext () {
    return QString (zargs_param_next (self));
};

///
//  Return current parameter name, or NULL if there are no named parameters.
const QString QmlZargs::paramName () {
    return QString (zargs_param_name (self));
};

///
//  Return value of named parameter or NULL is it has no value (or was not specified)
const QString QmlZargs::get (const QString &name) {
    return QString (zargs_get (self, name.toUtf8().data()));
};

///
//  Return value of one of parameter(s) or NULL is it has no value (or was not specified)
const QString QmlZargs::getx (const QString &name) {
    return QString (zargs_getx (self, name.toUtf8().data()));
};

///
//  Returns true if named parameter was specified on command line
bool QmlZargs::has (const QString &name) {
    return zargs_has (self, name.toUtf8().data());
};

///
//  Returns true if named parameter(s) was specified on command line
bool QmlZargs::hasx (const QString &name) {
    return zargs_hasx (self, name.toUtf8().data());
};

///
//  Print an instance of zargs.
void QmlZargs::print () {
    zargs_print (self);
};


QObject* QmlZargs::qmlAttachedProperties(QObject* object) {
    return new QmlZargsAttached(object);
}


///
//  Self test of this class.
void QmlZargsAttached::test (bool verbose) {
    zargs_test (verbose);
};

///
//  Create a new zargs from command line arguments.
QmlZargs *QmlZargsAttached::construct (int argc, QString argv) {
    QmlZargs *qmlSelf = new QmlZargs ();
    qmlSelf->self = zargs_new (argc, argv.toUtf8().data());
    return qmlSelf;
};

///
//  Destroy zargs instance.
void QmlZargsAttached::destruct (QmlZargs *qmlSelf) {
    zargs_destroy (&qmlSelf->self);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
