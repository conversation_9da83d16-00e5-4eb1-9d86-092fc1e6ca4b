################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

module CZMQ
  module FFI

    # Http response that can be received from zhttp_client or sent to zhttp_server.
    # Class can be reused between send & recv calls.
    # Headers and Content is being destroyed after every send call.
    # @note This class is 100% generated using zproject.
    class ZhttpResponse
      # Raised when one tries to use an instance of {ZhttpResponse} after
      # the internal pointer to the native object has been nullified.
      class DestroyedError < RuntimeError; end

      # Boilerplate for self pointer, initializer, and finalizer
      class << self
        alias :__new :new
      end
      # Attaches the pointer _ptr_ to this instance and defines a finalizer for
      # it if necessary.
      # @param ptr [::FFI::Pointer]
      # @param finalize [Boolean]
      def initialize(ptr, finalize = true)
        @ptr = ptr
        if @ptr.null?
          @ptr = nil # Remove null pointers so we don't have to test for them.
        elsif finalize
          @finalizer = self.class.create_finalizer_for @ptr
          ObjectSpace.define_finalizer self, @finalizer
        end
      end
      # @param ptr [::FFI::Pointer]
      # @return [Proc]
      def self.create_finalizer_for(ptr)
        ptr_ptr = ::FFI::MemoryPointer.new :pointer

        Proc.new do
          ptr_ptr.write_pointer ptr
          ::CZMQ::FFI.zhttp_response_destroy ptr_ptr
        end
      end
      # @return [Boolean]
      def null?
        !@ptr or @ptr.null?
      end
      # Return internal pointer
      # @return [::FFI::Pointer]
      def __ptr
        raise DestroyedError unless @ptr
        @ptr
      end
      # So external Libraries can just pass the Object to a FFI function which expects a :pointer
      alias_method :to_ptr, :__ptr
      # Nullify internal pointer and return pointer pointer.
      # @note This detaches the current instance from the native object
      #   and thus makes it unusable.
      # @return [::FFI::MemoryPointer] the pointer pointing to a pointer
      #   pointing to the native object
      def __ptr_give_ref
        raise DestroyedError unless @ptr
        ptr_ptr = ::FFI::MemoryPointer.new :pointer
        ptr_ptr.write_pointer @ptr
        __undef_finalizer if @finalizer
        @ptr = nil
        ptr_ptr
      end
      # Undefines the finalizer for this object.
      # @note Only use this if you need to and can guarantee that the native
      #   object will be freed by other means.
      # @return [void]
      def __undef_finalizer
        ObjectSpace.undefine_finalizer self
        @finalizer = nil
      end

      # Create a new zhttp_response.
      # @return [CZMQ::ZhttpResponse]
      def self.new()
        ptr = ::CZMQ::FFI.zhttp_response_new()
        __new ptr
      end

      # Destroy the zhttp_response.
      #
      # @return [void]
      def destroy()
        return unless @ptr
        self_p = __ptr_give_ref
        result = ::CZMQ::FFI.zhttp_response_destroy(self_p)
        result
      end

      # Send a response to a request.
      # Returns 0 if successful and -1 otherwise.
      #
      # @param sock [Zsock, #__ptr]
      # @param connection [::FFI::Pointer, #to_ptr]
      # @return [Integer]
      def send(sock, connection)
        raise DestroyedError unless @ptr
        self_p = @ptr
        sock = sock.__ptr if sock
        result = ::CZMQ::FFI.zhttp_response_send(self_p, sock, connection)
        result
      end

      # Receive a response from zhttp_client.
      # On success return 0, -1 otherwise.
      #
      # Recv returns the two user arguments which was provided with the request.
      # The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an argument.
      #
      # @param client [ZhttpClient, #__ptr]
      # @param arg [::FFI::Pointer, #to_ptr]
      # @param arg2 [::FFI::Pointer, #to_ptr]
      # @return [Integer]
      def recv(client, arg, arg2)
        raise DestroyedError unless @ptr
        self_p = @ptr
        client = client.__ptr if client
        result = ::CZMQ::FFI.zhttp_response_recv(self_p, client, arg, arg2)
        result
      end

      # Get the response content type
      #
      # @return [String]
      def content_type()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zhttp_response_content_type(self_p)
        result
      end

      # Set the content type of the response.
      #
      # @param value [String, #to_s, nil]
      # @return [void]
      def set_content_type(value)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zhttp_response_set_content_type(self_p, value)
        result
      end

      # Get the status code of the response.
      #
      # @return [Integer]
      def status_code()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zhttp_response_status_code(self_p)
        result
      end

      # Set the status code of the response.
      #
      # @param status_code [Integer, #to_int, #to_i]
      # @return [void]
      def set_status_code(status_code)
        raise DestroyedError unless @ptr
        self_p = @ptr
        status_code = Integer(status_code)
        result = ::CZMQ::FFI.zhttp_response_set_status_code(self_p, status_code)
        result
      end

      # Get the headers of the response.
      #
      # @return [Zhash]
      def headers()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zhttp_response_headers(self_p)
        result = Zhash.__new result, false
        result
      end

      # Get the content length of the response
      #
      # @return [Integer]
      def content_length()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zhttp_response_content_length(self_p)
        result
      end

      # Get the content of the response.
      #
      # @return [String]
      def content()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zhttp_response_content(self_p)
        result
      end

      # Get the content of the response.
      #
      # @return [::FFI::AutoPointer]
      def get_content()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zhttp_response_get_content(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set the content of the response.
      # Content must by dynamically allocated string.
      # Takes ownership of the content.
      #
      # @param content [::FFI::Pointer, #to_ptr]
      # @return [void]
      def set_content(content)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zhttp_response_set_content(self_p, content)
        result
      end

      # Set the content of the response.
      # The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
      #
      # @param content [String, #to_s, nil]
      # @return [void]
      def set_content_const(content)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zhttp_response_set_content_const(self_p, content)
        result
      end

      # Set the content to NULL
      #
      # @return [void]
      def reset_content()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zhttp_response_reset_content(self_p)
        result
      end

      # Self test of this class.
      #
      # @param verbose [Boolean]
      # @return [void]
      def self.test(verbose)
        verbose = !(0==verbose||!verbose) # boolean
        result = ::CZMQ::FFI.zhttp_response_test(verbose)
        result
      end
    end
  end
end

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
