/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
package org.zeromq.czmq;

import org.zeromq.tools.ZmqNativeLoader;

import java.util.LinkedHashMap;
import java.util.Map;

public class ZhttpResponse implements AutoCloseable {
    static {
        Map<String, Boolean> libraries = new LinkedHashMap<>();
        libraries.put("zmq", false);
        libraries.put("uuid", true);
        libraries.put("systemd", true);
        libraries.put("lz4", true);
        libraries.put("curl", true);
        libraries.put("nss", true);
        libraries.put("microhttpd", true);
        libraries.put("czmq", false);
        libraries.put("czmqjni", false);
        ZmqNativeLoader.loadLibraries(libraries);
    }
    public long self;
    /*
    Create a new zhttp_response.
    */
    native static long __new ();
    public ZhttpResponse () {
        /*  TODO: if __new fails, self is null...            */
        self = __new ();
    }
    public ZhttpResponse (long pointer) {
        self = pointer;
    }
    /*
    Destroy the zhttp_response.
    */
    native static void __destroy (long self);
    @Override
    public void close () {
        __destroy (self);
        self = 0;
    }
    /*
    Send a response to a request.
    Returns 0 if successful and -1 otherwise.
    */
    native static int __send (long self, long sock, long connection);
    public int send (Zsock sock, long connection) {
        return __send (self, sock.self, connection);
    }
    /*
    Receive a response from zhttp_client.
    On success return 0, -1 otherwise.

    Recv returns the two user arguments which was provided with the request.
    The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an argument.
    */
    native static int __recv (long self, long client, long arg, long arg2);
    public int recv (ZhttpClient client, long arg, long arg2) {
        return __recv (self, client.self, arg, arg2);
    }
    /*
    Get the response content type
    */
    native static String __contentType (long self);
    public String contentType () {
        return __contentType (self);
    }
    /*
    Set the content type of the response.
    */
    native static void __setContentType (long self, String value);
    public void setContentType (String value) {
        __setContentType (self, value);
    }
    /*
    Get the status code of the response.
    */
    native static int __statusCode (long self);
    public int statusCode () {
        return __statusCode (self);
    }
    /*
    Set the status code of the response.
    */
    native static void __setStatusCode (long self, int statusCode);
    public void setStatusCode (int statusCode) {
        __setStatusCode (self, statusCode);
    }
    /*
    Get the headers of the response.
    */
    native static long __headers (long self);
    public Zhash headers () {
        return new Zhash (__headers (self));
    }
    /*
    Get the content length of the response
    */
    native static long __contentLength (long self);
    public long contentLength () {
        return __contentLength (self);
    }
    /*
    Get the content of the response.
    */
    native static String __content (long self);
    public String content () {
        return __content (self);
    }
    /*
    Get the content of the response.
    */
    native static String __getContent (long self);
    public String getContent () {
        return __getContent (self);
    }
    /*
    Set the content of the response.
    The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
    */
    native static void __setContentConst (long self, String content);
    public void setContentConst (String content) {
        __setContentConst (self, content);
    }
    /*
    Set the content to NULL
    */
    native static void __resetContent (long self);
    public void resetContent () {
        __resetContent (self);
    }
    /*
    Self test of this class.
    */
    native static void __test (boolean verbose);
    public static void test (boolean verbose) {
        __test (verbose);
    }
}
