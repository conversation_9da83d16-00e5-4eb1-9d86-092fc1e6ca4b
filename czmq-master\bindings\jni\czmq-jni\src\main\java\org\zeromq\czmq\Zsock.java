/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
package org.zeromq.czmq;

import org.zeromq.tools.ZmqNativeLoader;

import java.util.LinkedHashMap;
import java.util.Map;

public class Zsock implements AutoCloseable {
    static {
        Map<String, Boolean> libraries = new LinkedHashMap<>();
        libraries.put("zmq", false);
        libraries.put("uuid", true);
        libraries.put("systemd", true);
        libraries.put("lz4", true);
        libraries.put("curl", true);
        libraries.put("nss", true);
        libraries.put("microhttpd", true);
        libraries.put("czmq", false);
        libraries.put("czmqjni", false);
        ZmqNativeLoader.loadLibraries(libraries);
    }
    public long self;
    /*
    Create a new socket. Returns the new socket, or NULL if the new socket
    could not be created. Note that the symbol zsock_new (and other
    constructors/destructors for zsock) are redirected to the *_checked
    variant, enabling intelligent socket leak detection. This can have
    performance implications if you use a LOT of sockets. To turn off this
    redirection behaviour, define ZSOCK_NOCHECK.
    */
    native static long __new (int type);
    public Zsock (int type) {
        /*  TODO: if __new fails, self is null...            */
        self = __new (type);
    }
    public Zsock (long pointer) {
        self = pointer;
    }
    /*
    Create a PUB socket. Default action is bind.
    */
    native static long __newPub (String endpoint);
    public static Zsock newPub (String endpoint) {
        return new Zsock (__newPub (endpoint));
    }
    /*
    Create a SUB socket, and optionally subscribe to some prefix string. Default
    action is connect.
    */
    native static long __newSub (String endpoint, String subscribe);
    public static Zsock newSub (String endpoint, String subscribe) {
        return new Zsock (__newSub (endpoint, subscribe));
    }
    /*
    Create a REQ socket. Default action is connect.
    */
    native static long __newReq (String endpoint);
    public static Zsock newReq (String endpoint) {
        return new Zsock (__newReq (endpoint));
    }
    /*
    Create a REP socket. Default action is bind.
    */
    native static long __newRep (String endpoint);
    public static Zsock newRep (String endpoint) {
        return new Zsock (__newRep (endpoint));
    }
    /*
    Create a DEALER socket. Default action is connect.
    */
    native static long __newDealer (String endpoint);
    public static Zsock newDealer (String endpoint) {
        return new Zsock (__newDealer (endpoint));
    }
    /*
    Create a ROUTER socket. Default action is bind.
    */
    native static long __newRouter (String endpoint);
    public static Zsock newRouter (String endpoint) {
        return new Zsock (__newRouter (endpoint));
    }
    /*
    Create a PUSH socket. Default action is connect.
    */
    native static long __newPush (String endpoint);
    public static Zsock newPush (String endpoint) {
        return new Zsock (__newPush (endpoint));
    }
    /*
    Create a PULL socket. Default action is bind.
    */
    native static long __newPull (String endpoint);
    public static Zsock newPull (String endpoint) {
        return new Zsock (__newPull (endpoint));
    }
    /*
    Create an XPUB socket. Default action is bind.
    */
    native static long __newXpub (String endpoint);
    public static Zsock newXpub (String endpoint) {
        return new Zsock (__newXpub (endpoint));
    }
    /*
    Create an XSUB socket. Default action is connect.
    */
    native static long __newXsub (String endpoint);
    public static Zsock newXsub (String endpoint) {
        return new Zsock (__newXsub (endpoint));
    }
    /*
    Create a PAIR socket. Default action is connect.
    */
    native static long __newPair (String endpoint);
    public static Zsock newPair (String endpoint) {
        return new Zsock (__newPair (endpoint));
    }
    /*
    Create a STREAM socket. Default action is connect.
    */
    native static long __newStream (String endpoint);
    public static Zsock newStream (String endpoint) {
        return new Zsock (__newStream (endpoint));
    }
    /*
    Create a SERVER socket. Default action is bind.
    */
    native static long __newServer (String endpoint);
    public static Zsock newServer (String endpoint) {
        return new Zsock (__newServer (endpoint));
    }
    /*
    Create a CLIENT socket. Default action is connect.
    */
    native static long __newClient (String endpoint);
    public static Zsock newClient (String endpoint) {
        return new Zsock (__newClient (endpoint));
    }
    /*
    Create a RADIO socket. Default action is bind.
    */
    native static long __newRadio (String endpoint);
    public static Zsock newRadio (String endpoint) {
        return new Zsock (__newRadio (endpoint));
    }
    /*
    Create a DISH socket. Default action is connect.
    */
    native static long __newDish (String endpoint);
    public static Zsock newDish (String endpoint) {
        return new Zsock (__newDish (endpoint));
    }
    /*
    Create a GATHER socket. Default action is bind.
    */
    native static long __newGather (String endpoint);
    public static Zsock newGather (String endpoint) {
        return new Zsock (__newGather (endpoint));
    }
    /*
    Create a SCATTER socket. Default action is connect.
    */
    native static long __newScatter (String endpoint);
    public static Zsock newScatter (String endpoint) {
        return new Zsock (__newScatter (endpoint));
    }
    /*
    Create a DGRAM (UDP) socket. Default action is bind.
    The endpoint is a string consisting of a
    'transport'`://` followed by an 'address'. As this is
    a UDP socket the 'transport' has to be 'udp'. The
    'address' specifies the ip address and port to
    bind to. For example:  udp://127.0.0.1:1234
    Note: To send to an endpoint over UDP you have to
    send a message with the destination endpoint address
    as a first message!
    */
    native static long __newDgram (String endpoint);
    public static Zsock newDgram (String endpoint) {
        return new Zsock (__newDgram (endpoint));
    }
    /*
    Destroy the socket. You must use this for any socket created via the
    zsock_new method.
    */
    native static void __destroy (long self);
    @Override
    public void close () {
        __destroy (self);
        self = 0;
    }
    /*
    Bind a socket to a formatted endpoint. For tcp:// endpoints, supports
    ephemeral ports, if you specify the port number as "*". By default
    zsock uses the IANA designated range from C000 (49152) to FFFF (65535).
    To override this range, follow the "*" with "[first-last]". Either or
    both first and last may be empty. To bind to a random port within the
    range, use "!" in place of "*".

    Examples:
        tcp://127.0.0.1:*           bind to first free port from C000 up
        tcp://127.0.0.1:!           bind to random port from C000 to FFFF
        tcp://127.0.0.1:*[60000-]   bind to first free port from 60000 up
        tcp://127.0.0.1:![-60000]   bind to random port from C000 to 60000
        tcp://127.0.0.1:![55000-55999]
                                    bind to random port from 55000 to 55999

    On success, returns the actual port number used, for tcp:// endpoints,
    and 0 for other transports. On failure, returns -1. Note that when using
    ephemeral ports, a port may be reused by different services without
    clients being aware. Protocols that run on ephemeral ports should take
    this into account.
    */
    native static int __bind (long self, String format);
    public int bind (String format) {
        return __bind (self, format);
    }
    /*
    Returns last bound endpoint, if any.
    */
    native static String __endpoint (long self);
    public String endpoint () {
        return __endpoint (self);
    }
    /*
    Unbind a socket from a formatted endpoint.
    Returns 0 if OK, -1 if the endpoint was invalid or the function
    isn't supported.
    */
    native static int __unbind (long self, String format);
    public int unbind (String format) {
        return __unbind (self, format);
    }
    /*
    Connect a socket to a formatted endpoint
    Returns 0 if OK, -1 if the endpoint was invalid.
    */
    native static int __connect (long self, String format);
    public int connect (String format) {
        return __connect (self, format);
    }
    /*
    Disconnect a socket from a formatted endpoint
    Returns 0 if OK, -1 if the endpoint was invalid or the function
    isn't supported.
    */
    native static int __disconnect (long self, String format);
    public int disconnect (String format) {
        return __disconnect (self, format);
    }
    /*
    Attach a socket to zero or more endpoints. If endpoints is not null,
    parses as list of ZeroMQ endpoints, separated by commas, and prefixed by
    '@' (to bind the socket) or '>' (to connect the socket). Returns 0 if all
    endpoints were valid, or -1 if there was a syntax error. If the endpoint
    does not start with '@' or '>', the serverish argument defines whether
    it is used to bind (serverish = true) or connect (serverish = false).
    */
    native static int __attach (long self, String endpoints, boolean serverish);
    public int attach (String endpoints, boolean serverish) {
        return __attach (self, endpoints, serverish);
    }
    /*
    Returns socket type as printable constant string.
    */
    native static String __typeStr (long self);
    public String typeStr () {
        return __typeStr (self);
    }
    /*
    Send a 'picture' message to the socket (or actor). The picture is a
    string that defines the type of each frame. This makes it easy to send
    a complex multiframe message in one call. The picture can contain any
    of these characters, each corresponding to one or two arguments:

        i = int (signed)
        1 = uint8_t
        2 = uint16_t
        4 = uint32_t
        8 = uint64_t
        s = char *
        b = byte *, size_t (2 arguments)
        c = zchunk_t *
        f = zframe_t *
        h = zhashx_t *
        l = zlistx_t * (DRAFT)
        U = zuuid_t *
        p = void * (sends the pointer value, only meaningful over inproc)
        m = zmsg_t * (sends all frames in the zmsg)
        z = sends zero-sized frame (0 arguments)
        u = uint (deprecated)

    Note that s, b, c, and f are encoded the same way and the choice is
    offered as a convenience to the sender, which may or may not already
    have data in a zchunk or zframe. Does not change or take ownership of
    any arguments. Returns 0 if successful, -1 if sending failed for any
    reason.
    */
    native static int __send (long self, String picture);
    public int send (String picture []) {
        return __send (self, picture [0]);
    }
    /*
    Receive a 'picture' message to the socket (or actor). See zsock_send for
    the format and meaning of the picture. Returns the picture elements into
    a series of pointers as provided by the caller:

        i = int * (stores signed integer)
        4 = uint32_t * (stores 32-bit unsigned integer)
        8 = uint64_t * (stores 64-bit unsigned integer)
        s = char ** (allocates new string)
        b = byte **, size_t * (2 arguments) (allocates memory)
        c = zchunk_t ** (creates zchunk)
        f = zframe_t ** (creates zframe)
        U = zuuid_t * (creates a zuuid with the data)
        h = zhashx_t ** (creates zhashx)
        l = zlistx_t ** (creates zlistx) (DRAFT)
        p = void ** (stores pointer)
        m = zmsg_t ** (creates a zmsg with the remaining frames)
        z = null, asserts empty frame (0 arguments)
        u = uint * (stores unsigned integer, deprecated)

    Note that zsock_recv creates the returned objects, and the caller must
    destroy them when finished with them. The supplied pointers do not need
    to be initialized. Returns 0 if successful, or -1 if it failed to recv
    a message, in which case the pointers are not modified. When message
    frames are truncated (a short message), sets return values to zero/null.
    If an argument pointer is NULL, does not store any value (skips it).
    An 'n' picture matches an empty frame; if the message does not match,
    the method will return -1.
    */
    native static int __recv (long self, String picture);
    public int recv (String picture []) {
        return __recv (self, picture [0]);
    }
    /*
    Send a binary encoded 'picture' message to the socket (or actor). This
    method is similar to zsock_send, except the arguments are encoded in a
    binary format that is compatible with zproto, and is designed to reduce
    memory allocations. The pattern argument is a string that defines the
    type of each argument. Supports these argument types:

     pattern    C type                  zproto type:
        1       uint8_t                 type = "number" size = "1"
        2       uint16_t                type = "number" size = "2"
        4       uint32_t                type = "number" size = "3"
        8       uint64_t                type = "number" size = "4"
        s       char *, 0-255 chars     type = "string"
        S       char *, 0-2^32-1 chars  type = "longstr"
        c       zchunk_t *              type = "chunk"
        f       zframe_t *              type = "frame"
        u       zuuid_t *               type = "uuid"
        m       zmsg_t *                type = "msg"
        p       void *, sends pointer value, only over inproc

    Does not change or take ownership of any arguments. Returns 0 if
    successful, -1 if sending failed for any reason.
    */
    native static int __bsend (long self, String picture);
    public int bsend (String picture []) {
        return __bsend (self, picture [0]);
    }
    /*
    Receive a binary encoded 'picture' message from the socket (or actor).
    This method is similar to zsock_recv, except the arguments are encoded
    in a binary format that is compatible with zproto, and is designed to
    reduce memory allocations. The pattern argument is a string that defines
    the type of each argument. See zsock_bsend for the supported argument
    types. All arguments must be pointers; this call sets them to point to
    values held on a per-socket basis.
    For types 1, 2, 4 and 8 the caller must allocate the memory itself before
    calling zsock_brecv.
    For types S, the caller must free the value once finished with it, as
    zsock_brecv will allocate the buffer.
    For type s, the caller must not free the value as it is stored in a
    local cache for performance purposes.
    For types c, f, u and m the caller must call the appropriate destructor
    depending on the object as zsock_brecv will create new objects.
    For type p the caller must coordinate with the sender, as it is just a
    pointer value being passed.
    */
    native static int __brecv (long self, String picture);
    public int brecv (String picture []) {
        return __brecv (self, picture [0]);
    }
    /*
    Return socket routing ID if any. This returns 0 if the socket is not
    of type ZMQ_SERVER or if no request was already received on it.
    */
    native static int __routingId (long self);
    public int routingId () {
        return __routingId (self);
    }
    /*
    Set routing ID on socket. The socket MUST be of type ZMQ_SERVER.
    This will be used when sending messages on the socket via the zsock API.
    */
    native static void __setRoutingId (long self, int routingId);
    public void setRoutingId (int routingId) {
        __setRoutingId (self, routingId);
    }
    /*
    Set socket to use unbounded pipes (HWM=0); use this in cases when you are
    totally certain the message volume can fit in memory. This method works
    across all versions of ZeroMQ. Takes a polymorphic socket reference.
    */
    native static void __setUnbounded (long self);
    public void setUnbounded () {
        __setUnbounded (self);
    }
    /*
    Send a signal over a socket. A signal is a short message carrying a
    success/failure code (by convention, 0 means OK). Signals are encoded
    to be distinguishable from "normal" messages. Accepts a zsock_t or a
    zactor_t argument, and returns 0 if successful, -1 if the signal could
    not be sent. Takes a polymorphic socket reference.
    */
    native static int __signal (long self, byte status);
    public int signal (byte status) {
        return __signal (self, status);
    }
    /*
    Wait on a signal. Use this to coordinate between threads, over pipe
    pairs. Blocks until the signal is received. Returns -1 on error, 0 or
    greater on success. Accepts a zsock_t or a zactor_t as argument.
    Takes a polymorphic socket reference.
    */
    native static int __wait (long self);
    public int Wait () {
        return __wait (self);
    }
    /*
    If there is a partial message still waiting on the socket, remove and
    discard it. This is useful when reading partial messages, to get specific
    message types.
    */
    native static void __flush (long self);
    public void flush () {
        __flush (self);
    }
    /*
    Join a group for the RADIO-DISH pattern. Call only on ZMQ_DISH.
    Returns 0 if OK, -1 if failed.
    */
    native static int __join (long self, String group);
    public int join (String group) {
        return __join (self, group);
    }
    /*
    Leave a group for the RADIO-DISH pattern. Call only on ZMQ_DISH.
    Returns 0 if OK, -1 if failed.
    */
    native static int __leave (long self, String group);
    public int leave (String group) {
        return __leave (self, group);
    }
    /*
    Probe the supplied object, and report if it looks like a zsock_t.
    Takes a polymorphic socket reference.
    */
    native static boolean __is (long self);
    public static boolean is (long self) {
        return __is (self);
    }
    /*
    Probe the supplied reference. If it looks like a zsock_t instance, return
    the underlying libzmq socket handle; else if it looks like a file
    descriptor, return NULL; else if it looks like a libzmq socket handle,
    return the supplied value. Takes a polymorphic socket reference.
    */
    native static long __resolve (long self);
    public static long resolve (long self) {
        return __resolve (self);
    }
    /*
    Check whether the socket has available message to read.
    */
    native static boolean __hasIn (long self);
    public boolean hasIn () {
        return __hasIn (self);
    }
    /*
    Get socket option `priority`.
    Available from libzmq 4.3.0.
    */
    native static int __priority (long self);
    public int priority () {
        return __priority (self);
    }
    /*
    Set socket option `priority`.
    Available from libzmq 4.3.0.
    */
    native static void __setPriority (long self, int priority);
    public void setPriority (int priority) {
        __setPriority (self, priority);
    }
    /*
    Get socket option `reconnect_stop`.
    Available from libzmq 4.3.0.
    */
    native static int __reconnectStop (long self);
    public int reconnectStop () {
        return __reconnectStop (self);
    }
    /*
    Set socket option `reconnect_stop`.
    Available from libzmq 4.3.0.
    */
    native static void __setReconnectStop (long self, int reconnectStop);
    public void setReconnectStop (int reconnectStop) {
        __setReconnectStop (self, reconnectStop);
    }
    /*
    Set socket option `only_first_subscribe`.
    Available from libzmq 4.3.0.
    */
    native static void __setOnlyFirstSubscribe (long self, int onlyFirstSubscribe);
    public void setOnlyFirstSubscribe (int onlyFirstSubscribe) {
        __setOnlyFirstSubscribe (self, onlyFirstSubscribe);
    }
    /*
    Set socket option `hello_msg`.
    Available from libzmq 4.3.0.
    */
    native static void __setHelloMsg (long self, long helloMsg);
    public void setHelloMsg (Zframe helloMsg) {
        __setHelloMsg (self, helloMsg.self);
    }
    /*
    Set socket option `disconnect_msg`.
    Available from libzmq 4.3.0.
    */
    native static void __setDisconnectMsg (long self, long disconnectMsg);
    public void setDisconnectMsg (Zframe disconnectMsg) {
        __setDisconnectMsg (self, disconnectMsg.self);
    }
    /*
    Set socket option `wss_trust_system`.
    Available from libzmq 4.3.0.
    */
    native static void __setWssTrustSystem (long self, int wssTrustSystem);
    public void setWssTrustSystem (int wssTrustSystem) {
        __setWssTrustSystem (self, wssTrustSystem);
    }
    /*
    Set socket option `wss_hostname`.
    Available from libzmq 4.3.0.
    */
    native static void __setWssHostname (long self, String wssHostname);
    public void setWssHostname (String wssHostname) {
        __setWssHostname (self, wssHostname);
    }
    /*
    Set socket option `wss_trust_pem`.
    Available from libzmq 4.3.0.
    */
    native static void __setWssTrustPem (long self, String wssTrustPem);
    public void setWssTrustPem (String wssTrustPem) {
        __setWssTrustPem (self, wssTrustPem);
    }
    /*
    Set socket option `wss_cert_pem`.
    Available from libzmq 4.3.0.
    */
    native static void __setWssCertPem (long self, String wssCertPem);
    public void setWssCertPem (String wssCertPem) {
        __setWssCertPem (self, wssCertPem);
    }
    /*
    Set socket option `wss_key_pem`.
    Available from libzmq 4.3.0.
    */
    native static void __setWssKeyPem (long self, String wssKeyPem);
    public void setWssKeyPem (String wssKeyPem) {
        __setWssKeyPem (self, wssKeyPem);
    }
    /*
    Get socket option `out_batch_size`.
    Available from libzmq 4.3.0.
    */
    native static int __outBatchSize (long self);
    public int outBatchSize () {
        return __outBatchSize (self);
    }
    /*
    Set socket option `out_batch_size`.
    Available from libzmq 4.3.0.
    */
    native static void __setOutBatchSize (long self, int outBatchSize);
    public void setOutBatchSize (int outBatchSize) {
        __setOutBatchSize (self, outBatchSize);
    }
    /*
    Get socket option `in_batch_size`.
    Available from libzmq 4.3.0.
    */
    native static int __inBatchSize (long self);
    public int inBatchSize () {
        return __inBatchSize (self);
    }
    /*
    Set socket option `in_batch_size`.
    Available from libzmq 4.3.0.
    */
    native static void __setInBatchSize (long self, int inBatchSize);
    public void setInBatchSize (int inBatchSize) {
        __setInBatchSize (self, inBatchSize);
    }
    /*
    Get socket option `socks_password`.
    Available from libzmq 4.3.0.
    */
    native static String __socksPassword (long self);
    public String socksPassword () {
        return __socksPassword (self);
    }
    /*
    Set socket option `socks_password`.
    Available from libzmq 4.3.0.
    */
    native static void __setSocksPassword (long self, String socksPassword);
    public void setSocksPassword (String socksPassword) {
        __setSocksPassword (self, socksPassword);
    }
    /*
    Get socket option `socks_username`.
    Available from libzmq 4.3.0.
    */
    native static String __socksUsername (long self);
    public String socksUsername () {
        return __socksUsername (self);
    }
    /*
    Set socket option `socks_username`.
    Available from libzmq 4.3.0.
    */
    native static void __setSocksUsername (long self, String socksUsername);
    public void setSocksUsername (String socksUsername) {
        __setSocksUsername (self, socksUsername);
    }
    /*
    Set socket option `xpub_manual_last_value`.
    Available from libzmq 4.3.0.
    */
    native static void __setXpubManualLastValue (long self, int xpubManualLastValue);
    public void setXpubManualLastValue (int xpubManualLastValue) {
        __setXpubManualLastValue (self, xpubManualLastValue);
    }
    /*
    Get socket option `router_notify`.
    Available from libzmq 4.3.0.
    */
    native static int __routerNotify (long self);
    public int routerNotify () {
        return __routerNotify (self);
    }
    /*
    Set socket option `router_notify`.
    Available from libzmq 4.3.0.
    */
    native static void __setRouterNotify (long self, int routerNotify);
    public void setRouterNotify (int routerNotify) {
        __setRouterNotify (self, routerNotify);
    }
    /*
    Get socket option `multicast_loop`.
    Available from libzmq 4.3.0.
    */
    native static int __multicastLoop (long self);
    public int multicastLoop () {
        return __multicastLoop (self);
    }
    /*
    Set socket option `multicast_loop`.
    Available from libzmq 4.3.0.
    */
    native static void __setMulticastLoop (long self, int multicastLoop);
    public void setMulticastLoop (int multicastLoop) {
        __setMulticastLoop (self, multicastLoop);
    }
    /*
    Get socket option `metadata`.
    Available from libzmq 4.3.0.
    */
    native static String __metadata (long self);
    public String metadata () {
        return __metadata (self);
    }
    /*
    Set socket option `metadata`.
    Available from libzmq 4.3.0.
    */
    native static void __setMetadata (long self, String metadata);
    public void setMetadata (String metadata) {
        __setMetadata (self, metadata);
    }
    /*
    Get socket option `loopback_fastpath`.
    Available from libzmq 4.3.0.
    */
    native static int __loopbackFastpath (long self);
    public int loopbackFastpath () {
        return __loopbackFastpath (self);
    }
    /*
    Set socket option `loopback_fastpath`.
    Available from libzmq 4.3.0.
    */
    native static void __setLoopbackFastpath (long self, int loopbackFastpath);
    public void setLoopbackFastpath (int loopbackFastpath) {
        __setLoopbackFastpath (self, loopbackFastpath);
    }
    /*
    Get socket option `zap_enforce_domain`.
    Available from libzmq 4.3.0.
    */
    native static int __zapEnforceDomain (long self);
    public int zapEnforceDomain () {
        return __zapEnforceDomain (self);
    }
    /*
    Set socket option `zap_enforce_domain`.
    Available from libzmq 4.3.0.
    */
    native static void __setZapEnforceDomain (long self, int zapEnforceDomain);
    public void setZapEnforceDomain (int zapEnforceDomain) {
        __setZapEnforceDomain (self, zapEnforceDomain);
    }
    /*
    Get socket option `gssapi_principal_nametype`.
    Available from libzmq 4.3.0.
    */
    native static int __gssapiPrincipalNametype (long self);
    public int gssapiPrincipalNametype () {
        return __gssapiPrincipalNametype (self);
    }
    /*
    Set socket option `gssapi_principal_nametype`.
    Available from libzmq 4.3.0.
    */
    native static void __setGssapiPrincipalNametype (long self, int gssapiPrincipalNametype);
    public void setGssapiPrincipalNametype (int gssapiPrincipalNametype) {
        __setGssapiPrincipalNametype (self, gssapiPrincipalNametype);
    }
    /*
    Get socket option `gssapi_service_principal_nametype`.
    Available from libzmq 4.3.0.
    */
    native static int __gssapiServicePrincipalNametype (long self);
    public int gssapiServicePrincipalNametype () {
        return __gssapiServicePrincipalNametype (self);
    }
    /*
    Set socket option `gssapi_service_principal_nametype`.
    Available from libzmq 4.3.0.
    */
    native static void __setGssapiServicePrincipalNametype (long self, int gssapiServicePrincipalNametype);
    public void setGssapiServicePrincipalNametype (int gssapiServicePrincipalNametype) {
        __setGssapiServicePrincipalNametype (self, gssapiServicePrincipalNametype);
    }
    /*
    Get socket option `bindtodevice`.
    Available from libzmq 4.3.0.
    */
    native static String __bindtodevice (long self);
    public String bindtodevice () {
        return __bindtodevice (self);
    }
    /*
    Set socket option `bindtodevice`.
    Available from libzmq 4.3.0.
    */
    native static void __setBindtodevice (long self, String bindtodevice);
    public void setBindtodevice (String bindtodevice) {
        __setBindtodevice (self, bindtodevice);
    }
    /*
    Get socket option `heartbeat_ivl`.
    Available from libzmq 4.2.0.
    */
    native static int __heartbeatIvl (long self);
    public int heartbeatIvl () {
        return __heartbeatIvl (self);
    }
    /*
    Set socket option `heartbeat_ivl`.
    Available from libzmq 4.2.0.
    */
    native static void __setHeartbeatIvl (long self, int heartbeatIvl);
    public void setHeartbeatIvl (int heartbeatIvl) {
        __setHeartbeatIvl (self, heartbeatIvl);
    }
    /*
    Get socket option `heartbeat_ttl`.
    Available from libzmq 4.2.0.
    */
    native static int __heartbeatTtl (long self);
    public int heartbeatTtl () {
        return __heartbeatTtl (self);
    }
    /*
    Set socket option `heartbeat_ttl`.
    Available from libzmq 4.2.0.
    */
    native static void __setHeartbeatTtl (long self, int heartbeatTtl);
    public void setHeartbeatTtl (int heartbeatTtl) {
        __setHeartbeatTtl (self, heartbeatTtl);
    }
    /*
    Get socket option `heartbeat_timeout`.
    Available from libzmq 4.2.0.
    */
    native static int __heartbeatTimeout (long self);
    public int heartbeatTimeout () {
        return __heartbeatTimeout (self);
    }
    /*
    Set socket option `heartbeat_timeout`.
    Available from libzmq 4.2.0.
    */
    native static void __setHeartbeatTimeout (long self, int heartbeatTimeout);
    public void setHeartbeatTimeout (int heartbeatTimeout) {
        __setHeartbeatTimeout (self, heartbeatTimeout);
    }
    /*
    Get socket option `use_fd`.
    Available from libzmq 4.2.0.
    */
    native static int __useFd (long self);
    public int useFd () {
        return __useFd (self);
    }
    /*
    Set socket option `use_fd`.
    Available from libzmq 4.2.0.
    */
    native static void __setUseFd (long self, int useFd);
    public void setUseFd (int useFd) {
        __setUseFd (self, useFd);
    }
    /*
    Set socket option `xpub_manual`.
    Available from libzmq 4.2.0.
    */
    native static void __setXpubManual (long self, int xpubManual);
    public void setXpubManual (int xpubManual) {
        __setXpubManual (self, xpubManual);
    }
    /*
    Set socket option `xpub_welcome_msg`.
    Available from libzmq 4.2.0.
    */
    native static void __setXpubWelcomeMsg (long self, String xpubWelcomeMsg);
    public void setXpubWelcomeMsg (String xpubWelcomeMsg) {
        __setXpubWelcomeMsg (self, xpubWelcomeMsg);
    }
    /*
    Set socket option `stream_notify`.
    Available from libzmq 4.2.0.
    */
    native static void __setStreamNotify (long self, int streamNotify);
    public void setStreamNotify (int streamNotify) {
        __setStreamNotify (self, streamNotify);
    }
    /*
    Get socket option `invert_matching`.
    Available from libzmq 4.2.0.
    */
    native static int __invertMatching (long self);
    public int invertMatching () {
        return __invertMatching (self);
    }
    /*
    Set socket option `invert_matching`.
    Available from libzmq 4.2.0.
    */
    native static void __setInvertMatching (long self, int invertMatching);
    public void setInvertMatching (int invertMatching) {
        __setInvertMatching (self, invertMatching);
    }
    /*
    Set socket option `xpub_verboser`.
    Available from libzmq 4.2.0.
    */
    native static void __setXpubVerboser (long self, int xpubVerboser);
    public void setXpubVerboser (int xpubVerboser) {
        __setXpubVerboser (self, xpubVerboser);
    }
    /*
    Get socket option `connect_timeout`.
    Available from libzmq 4.2.0.
    */
    native static int __connectTimeout (long self);
    public int connectTimeout () {
        return __connectTimeout (self);
    }
    /*
    Set socket option `connect_timeout`.
    Available from libzmq 4.2.0.
    */
    native static void __setConnectTimeout (long self, int connectTimeout);
    public void setConnectTimeout (int connectTimeout) {
        __setConnectTimeout (self, connectTimeout);
    }
    /*
    Get socket option `tcp_maxrt`.
    Available from libzmq 4.2.0.
    */
    native static int __tcpMaxrt (long self);
    public int tcpMaxrt () {
        return __tcpMaxrt (self);
    }
    /*
    Set socket option `tcp_maxrt`.
    Available from libzmq 4.2.0.
    */
    native static void __setTcpMaxrt (long self, int tcpMaxrt);
    public void setTcpMaxrt (int tcpMaxrt) {
        __setTcpMaxrt (self, tcpMaxrt);
    }
    /*
    Get socket option `thread_safe`.
    Available from libzmq 4.2.0.
    */
    native static int __threadSafe (long self);
    public int threadSafe () {
        return __threadSafe (self);
    }
    /*
    Get socket option `multicast_maxtpdu`.
    Available from libzmq 4.2.0.
    */
    native static int __multicastMaxtpdu (long self);
    public int multicastMaxtpdu () {
        return __multicastMaxtpdu (self);
    }
    /*
    Set socket option `multicast_maxtpdu`.
    Available from libzmq 4.2.0.
    */
    native static void __setMulticastMaxtpdu (long self, int multicastMaxtpdu);
    public void setMulticastMaxtpdu (int multicastMaxtpdu) {
        __setMulticastMaxtpdu (self, multicastMaxtpdu);
    }
    /*
    Get socket option `vmci_buffer_size`.
    Available from libzmq 4.2.0.
    */
    native static int __vmciBufferSize (long self);
    public int vmciBufferSize () {
        return __vmciBufferSize (self);
    }
    /*
    Set socket option `vmci_buffer_size`.
    Available from libzmq 4.2.0.
    */
    native static void __setVmciBufferSize (long self, int vmciBufferSize);
    public void setVmciBufferSize (int vmciBufferSize) {
        __setVmciBufferSize (self, vmciBufferSize);
    }
    /*
    Get socket option `vmci_buffer_min_size`.
    Available from libzmq 4.2.0.
    */
    native static int __vmciBufferMinSize (long self);
    public int vmciBufferMinSize () {
        return __vmciBufferMinSize (self);
    }
    /*
    Set socket option `vmci_buffer_min_size`.
    Available from libzmq 4.2.0.
    */
    native static void __setVmciBufferMinSize (long self, int vmciBufferMinSize);
    public void setVmciBufferMinSize (int vmciBufferMinSize) {
        __setVmciBufferMinSize (self, vmciBufferMinSize);
    }
    /*
    Get socket option `vmci_buffer_max_size`.
    Available from libzmq 4.2.0.
    */
    native static int __vmciBufferMaxSize (long self);
    public int vmciBufferMaxSize () {
        return __vmciBufferMaxSize (self);
    }
    /*
    Set socket option `vmci_buffer_max_size`.
    Available from libzmq 4.2.0.
    */
    native static void __setVmciBufferMaxSize (long self, int vmciBufferMaxSize);
    public void setVmciBufferMaxSize (int vmciBufferMaxSize) {
        __setVmciBufferMaxSize (self, vmciBufferMaxSize);
    }
    /*
    Get socket option `vmci_connect_timeout`.
    Available from libzmq 4.2.0.
    */
    native static int __vmciConnectTimeout (long self);
    public int vmciConnectTimeout () {
        return __vmciConnectTimeout (self);
    }
    /*
    Set socket option `vmci_connect_timeout`.
    Available from libzmq 4.2.0.
    */
    native static void __setVmciConnectTimeout (long self, int vmciConnectTimeout);
    public void setVmciConnectTimeout (int vmciConnectTimeout) {
        __setVmciConnectTimeout (self, vmciConnectTimeout);
    }
    /*
    Get socket option `tos`.
    Available from libzmq 4.1.0.
    */
    native static int __tos (long self);
    public int tos () {
        return __tos (self);
    }
    /*
    Set socket option `tos`.
    Available from libzmq 4.1.0.
    */
    native static void __setTos (long self, int tos);
    public void setTos (int tos) {
        __setTos (self, tos);
    }
    /*
    Set socket option `router_handover`.
    Available from libzmq 4.1.0.
    */
    native static void __setRouterHandover (long self, int routerHandover);
    public void setRouterHandover (int routerHandover) {
        __setRouterHandover (self, routerHandover);
    }
    /*
    Set socket option `connect_rid`.
    Available from libzmq 4.1.0.
    */
    native static void __setConnectRid (long self, String connectRid);
    public void setConnectRid (String connectRid) {
        __setConnectRid (self, connectRid);
    }
    /*
    Set socket option `connect_rid` from 32-octet binary
    Available from libzmq 4.1.0.
    */
    native static void __setConnectRidBin (long self, byte [] connectRid);
    public void setConnectRidBin (byte [] connectRid) {
        __setConnectRidBin (self, connectRid);
    }
    /*
    Get socket option `handshake_ivl`.
    Available from libzmq 4.1.0.
    */
    native static int __handshakeIvl (long self);
    public int handshakeIvl () {
        return __handshakeIvl (self);
    }
    /*
    Set socket option `handshake_ivl`.
    Available from libzmq 4.1.0.
    */
    native static void __setHandshakeIvl (long self, int handshakeIvl);
    public void setHandshakeIvl (int handshakeIvl) {
        __setHandshakeIvl (self, handshakeIvl);
    }
    /*
    Get socket option `socks_proxy`.
    Available from libzmq 4.1.0.
    */
    native static String __socksProxy (long self);
    public String socksProxy () {
        return __socksProxy (self);
    }
    /*
    Set socket option `socks_proxy`.
    Available from libzmq 4.1.0.
    */
    native static void __setSocksProxy (long self, String socksProxy);
    public void setSocksProxy (String socksProxy) {
        __setSocksProxy (self, socksProxy);
    }
    /*
    Set socket option `xpub_nodrop`.
    Available from libzmq 4.1.0.
    */
    native static void __setXpubNodrop (long self, int xpubNodrop);
    public void setXpubNodrop (int xpubNodrop) {
        __setXpubNodrop (self, xpubNodrop);
    }
    /*
    Set socket option `router_mandatory`.
    Available from libzmq 4.0.0.
    */
    native static void __setRouterMandatory (long self, int routerMandatory);
    public void setRouterMandatory (int routerMandatory) {
        __setRouterMandatory (self, routerMandatory);
    }
    /*
    Set socket option `probe_router`.
    Available from libzmq 4.0.0.
    */
    native static void __setProbeRouter (long self, int probeRouter);
    public void setProbeRouter (int probeRouter) {
        __setProbeRouter (self, probeRouter);
    }
    /*
    Set socket option `req_relaxed`.
    Available from libzmq 4.0.0.
    */
    native static void __setReqRelaxed (long self, int reqRelaxed);
    public void setReqRelaxed (int reqRelaxed) {
        __setReqRelaxed (self, reqRelaxed);
    }
    /*
    Set socket option `req_correlate`.
    Available from libzmq 4.0.0.
    */
    native static void __setReqCorrelate (long self, int reqCorrelate);
    public void setReqCorrelate (int reqCorrelate) {
        __setReqCorrelate (self, reqCorrelate);
    }
    /*
    Set socket option `conflate`.
    Available from libzmq 4.0.0.
    */
    native static void __setConflate (long self, int conflate);
    public void setConflate (int conflate) {
        __setConflate (self, conflate);
    }
    /*
    Get socket option `zap_domain`.
    Available from libzmq 4.0.0.
    */
    native static String __zapDomain (long self);
    public String zapDomain () {
        return __zapDomain (self);
    }
    /*
    Set socket option `zap_domain`.
    Available from libzmq 4.0.0.
    */
    native static void __setZapDomain (long self, String zapDomain);
    public void setZapDomain (String zapDomain) {
        __setZapDomain (self, zapDomain);
    }
    /*
    Get socket option `mechanism`.
    Available from libzmq 4.0.0.
    */
    native static int __mechanism (long self);
    public int mechanism () {
        return __mechanism (self);
    }
    /*
    Get socket option `plain_server`.
    Available from libzmq 4.0.0.
    */
    native static int __plainServer (long self);
    public int plainServer () {
        return __plainServer (self);
    }
    /*
    Set socket option `plain_server`.
    Available from libzmq 4.0.0.
    */
    native static void __setPlainServer (long self, int plainServer);
    public void setPlainServer (int plainServer) {
        __setPlainServer (self, plainServer);
    }
    /*
    Get socket option `plain_username`.
    Available from libzmq 4.0.0.
    */
    native static String __plainUsername (long self);
    public String plainUsername () {
        return __plainUsername (self);
    }
    /*
    Set socket option `plain_username`.
    Available from libzmq 4.0.0.
    */
    native static void __setPlainUsername (long self, String plainUsername);
    public void setPlainUsername (String plainUsername) {
        __setPlainUsername (self, plainUsername);
    }
    /*
    Get socket option `plain_password`.
    Available from libzmq 4.0.0.
    */
    native static String __plainPassword (long self);
    public String plainPassword () {
        return __plainPassword (self);
    }
    /*
    Set socket option `plain_password`.
    Available from libzmq 4.0.0.
    */
    native static void __setPlainPassword (long self, String plainPassword);
    public void setPlainPassword (String plainPassword) {
        __setPlainPassword (self, plainPassword);
    }
    /*
    Get socket option `curve_server`.
    Available from libzmq 4.0.0.
    */
    native static int __curveServer (long self);
    public int curveServer () {
        return __curveServer (self);
    }
    /*
    Set socket option `curve_server`.
    Available from libzmq 4.0.0.
    */
    native static void __setCurveServer (long self, int curveServer);
    public void setCurveServer (int curveServer) {
        __setCurveServer (self, curveServer);
    }
    /*
    Get socket option `curve_publickey`.
    Available from libzmq 4.0.0.
    */
    native static String __curvePublickey (long self);
    public String curvePublickey () {
        return __curvePublickey (self);
    }
    /*
    Set socket option `curve_publickey`.
    Available from libzmq 4.0.0.
    */
    native static void __setCurvePublickey (long self, String curvePublickey);
    public void setCurvePublickey (String curvePublickey) {
        __setCurvePublickey (self, curvePublickey);
    }
    /*
    Set socket option `curve_publickey` from 32-octet binary
    Available from libzmq 4.0.0.
    */
    native static void __setCurvePublickeyBin (long self, byte [] curvePublickey);
    public void setCurvePublickeyBin (byte [] curvePublickey) {
        __setCurvePublickeyBin (self, curvePublickey);
    }
    /*
    Get socket option `curve_secretkey`.
    Available from libzmq 4.0.0.
    */
    native static String __curveSecretkey (long self);
    public String curveSecretkey () {
        return __curveSecretkey (self);
    }
    /*
    Set socket option `curve_secretkey`.
    Available from libzmq 4.0.0.
    */
    native static void __setCurveSecretkey (long self, String curveSecretkey);
    public void setCurveSecretkey (String curveSecretkey) {
        __setCurveSecretkey (self, curveSecretkey);
    }
    /*
    Set socket option `curve_secretkey` from 32-octet binary
    Available from libzmq 4.0.0.
    */
    native static void __setCurveSecretkeyBin (long self, byte [] curveSecretkey);
    public void setCurveSecretkeyBin (byte [] curveSecretkey) {
        __setCurveSecretkeyBin (self, curveSecretkey);
    }
    /*
    Get socket option `curve_serverkey`.
    Available from libzmq 4.0.0.
    */
    native static String __curveServerkey (long self);
    public String curveServerkey () {
        return __curveServerkey (self);
    }
    /*
    Set socket option `curve_serverkey`.
    Available from libzmq 4.0.0.
    */
    native static void __setCurveServerkey (long self, String curveServerkey);
    public void setCurveServerkey (String curveServerkey) {
        __setCurveServerkey (self, curveServerkey);
    }
    /*
    Set socket option `curve_serverkey` from 32-octet binary
    Available from libzmq 4.0.0.
    */
    native static void __setCurveServerkeyBin (long self, byte [] curveServerkey);
    public void setCurveServerkeyBin (byte [] curveServerkey) {
        __setCurveServerkeyBin (self, curveServerkey);
    }
    /*
    Get socket option `gssapi_server`.
    Available from libzmq 4.0.0.
    */
    native static int __gssapiServer (long self);
    public int gssapiServer () {
        return __gssapiServer (self);
    }
    /*
    Set socket option `gssapi_server`.
    Available from libzmq 4.0.0.
    */
    native static void __setGssapiServer (long self, int gssapiServer);
    public void setGssapiServer (int gssapiServer) {
        __setGssapiServer (self, gssapiServer);
    }
    /*
    Get socket option `gssapi_plaintext`.
    Available from libzmq 4.0.0.
    */
    native static int __gssapiPlaintext (long self);
    public int gssapiPlaintext () {
        return __gssapiPlaintext (self);
    }
    /*
    Set socket option `gssapi_plaintext`.
    Available from libzmq 4.0.0.
    */
    native static void __setGssapiPlaintext (long self, int gssapiPlaintext);
    public void setGssapiPlaintext (int gssapiPlaintext) {
        __setGssapiPlaintext (self, gssapiPlaintext);
    }
    /*
    Get socket option `gssapi_principal`.
    Available from libzmq 4.0.0.
    */
    native static String __gssapiPrincipal (long self);
    public String gssapiPrincipal () {
        return __gssapiPrincipal (self);
    }
    /*
    Set socket option `gssapi_principal`.
    Available from libzmq 4.0.0.
    */
    native static void __setGssapiPrincipal (long self, String gssapiPrincipal);
    public void setGssapiPrincipal (String gssapiPrincipal) {
        __setGssapiPrincipal (self, gssapiPrincipal);
    }
    /*
    Get socket option `gssapi_service_principal`.
    Available from libzmq 4.0.0.
    */
    native static String __gssapiServicePrincipal (long self);
    public String gssapiServicePrincipal () {
        return __gssapiServicePrincipal (self);
    }
    /*
    Set socket option `gssapi_service_principal`.
    Available from libzmq 4.0.0.
    */
    native static void __setGssapiServicePrincipal (long self, String gssapiServicePrincipal);
    public void setGssapiServicePrincipal (String gssapiServicePrincipal) {
        __setGssapiServicePrincipal (self, gssapiServicePrincipal);
    }
    /*
    Get socket option `ipv6`.
    Available from libzmq 4.0.0.
    */
    native static int __ipv6 (long self);
    public int ipv6 () {
        return __ipv6 (self);
    }
    /*
    Set socket option `ipv6`.
    Available from libzmq 4.0.0.
    */
    native static void __setIpv6 (long self, int ipv6);
    public void setIpv6 (int ipv6) {
        __setIpv6 (self, ipv6);
    }
    /*
    Get socket option `immediate`.
    Available from libzmq 4.0.0.
    */
    native static int __immediate (long self);
    public int immediate () {
        return __immediate (self);
    }
    /*
    Set socket option `immediate`.
    Available from libzmq 4.0.0.
    */
    native static void __setImmediate (long self, int immediate);
    public void setImmediate (int immediate) {
        __setImmediate (self, immediate);
    }
    /*
    Get socket option `sndhwm`.
    Available from libzmq 3.0.0.
    */
    native static int __sndhwm (long self);
    public int sndhwm () {
        return __sndhwm (self);
    }
    /*
    Set socket option `sndhwm`.
    Available from libzmq 3.0.0.
    */
    native static void __setSndhwm (long self, int sndhwm);
    public void setSndhwm (int sndhwm) {
        __setSndhwm (self, sndhwm);
    }
    /*
    Get socket option `rcvhwm`.
    Available from libzmq 3.0.0.
    */
    native static int __rcvhwm (long self);
    public int rcvhwm () {
        return __rcvhwm (self);
    }
    /*
    Set socket option `rcvhwm`.
    Available from libzmq 3.0.0.
    */
    native static void __setRcvhwm (long self, int rcvhwm);
    public void setRcvhwm (int rcvhwm) {
        __setRcvhwm (self, rcvhwm);
    }
    /*
    Get socket option `maxmsgsize`.
    Available from libzmq 3.0.0.
    */
    native static int __maxmsgsize (long self);
    public int maxmsgsize () {
        return __maxmsgsize (self);
    }
    /*
    Set socket option `maxmsgsize`.
    Available from libzmq 3.0.0.
    */
    native static void __setMaxmsgsize (long self, int maxmsgsize);
    public void setMaxmsgsize (int maxmsgsize) {
        __setMaxmsgsize (self, maxmsgsize);
    }
    /*
    Get socket option `multicast_hops`.
    Available from libzmq 3.0.0.
    */
    native static int __multicastHops (long self);
    public int multicastHops () {
        return __multicastHops (self);
    }
    /*
    Set socket option `multicast_hops`.
    Available from libzmq 3.0.0.
    */
    native static void __setMulticastHops (long self, int multicastHops);
    public void setMulticastHops (int multicastHops) {
        __setMulticastHops (self, multicastHops);
    }
    /*
    Set socket option `xpub_verbose`.
    Available from libzmq 3.0.0.
    */
    native static void __setXpubVerbose (long self, int xpubVerbose);
    public void setXpubVerbose (int xpubVerbose) {
        __setXpubVerbose (self, xpubVerbose);
    }
    /*
    Get socket option `tcp_keepalive`.
    Available from libzmq 3.0.0.
    */
    native static int __tcpKeepalive (long self);
    public int tcpKeepalive () {
        return __tcpKeepalive (self);
    }
    /*
    Set socket option `tcp_keepalive`.
    Available from libzmq 3.0.0.
    */
    native static void __setTcpKeepalive (long self, int tcpKeepalive);
    public void setTcpKeepalive (int tcpKeepalive) {
        __setTcpKeepalive (self, tcpKeepalive);
    }
    /*
    Get socket option `tcp_keepalive_idle`.
    Available from libzmq 3.0.0.
    */
    native static int __tcpKeepaliveIdle (long self);
    public int tcpKeepaliveIdle () {
        return __tcpKeepaliveIdle (self);
    }
    /*
    Set socket option `tcp_keepalive_idle`.
    Available from libzmq 3.0.0.
    */
    native static void __setTcpKeepaliveIdle (long self, int tcpKeepaliveIdle);
    public void setTcpKeepaliveIdle (int tcpKeepaliveIdle) {
        __setTcpKeepaliveIdle (self, tcpKeepaliveIdle);
    }
    /*
    Get socket option `tcp_keepalive_cnt`.
    Available from libzmq 3.0.0.
    */
    native static int __tcpKeepaliveCnt (long self);
    public int tcpKeepaliveCnt () {
        return __tcpKeepaliveCnt (self);
    }
    /*
    Set socket option `tcp_keepalive_cnt`.
    Available from libzmq 3.0.0.
    */
    native static void __setTcpKeepaliveCnt (long self, int tcpKeepaliveCnt);
    public void setTcpKeepaliveCnt (int tcpKeepaliveCnt) {
        __setTcpKeepaliveCnt (self, tcpKeepaliveCnt);
    }
    /*
    Get socket option `tcp_keepalive_intvl`.
    Available from libzmq 3.0.0.
    */
    native static int __tcpKeepaliveIntvl (long self);
    public int tcpKeepaliveIntvl () {
        return __tcpKeepaliveIntvl (self);
    }
    /*
    Set socket option `tcp_keepalive_intvl`.
    Available from libzmq 3.0.0.
    */
    native static void __setTcpKeepaliveIntvl (long self, int tcpKeepaliveIntvl);
    public void setTcpKeepaliveIntvl (int tcpKeepaliveIntvl) {
        __setTcpKeepaliveIntvl (self, tcpKeepaliveIntvl);
    }
    /*
    Get socket option `tcp_accept_filter`.
    Available from libzmq 3.0.0.
    */
    native static String __tcpAcceptFilter (long self);
    public String tcpAcceptFilter () {
        return __tcpAcceptFilter (self);
    }
    /*
    Set socket option `tcp_accept_filter`.
    Available from libzmq 3.0.0.
    */
    native static void __setTcpAcceptFilter (long self, String tcpAcceptFilter);
    public void setTcpAcceptFilter (String tcpAcceptFilter) {
        __setTcpAcceptFilter (self, tcpAcceptFilter);
    }
    /*
    Get socket option `last_endpoint`.
    Available from libzmq 3.0.0.
    */
    native static String __lastEndpoint (long self);
    public String lastEndpoint () {
        return __lastEndpoint (self);
    }
    /*
    Set socket option `router_raw`.
    Available from libzmq 3.0.0.
    */
    native static void __setRouterRaw (long self, int routerRaw);
    public void setRouterRaw (int routerRaw) {
        __setRouterRaw (self, routerRaw);
    }
    /*
    Get socket option `ipv4only`.
    Available from libzmq 3.0.0.
    */
    native static int __ipv4only (long self);
    public int ipv4only () {
        return __ipv4only (self);
    }
    /*
    Set socket option `ipv4only`.
    Available from libzmq 3.0.0.
    */
    native static void __setIpv4only (long self, int ipv4only);
    public void setIpv4only (int ipv4only) {
        __setIpv4only (self, ipv4only);
    }
    /*
    Set socket option `delay_attach_on_connect`.
    Available from libzmq 3.0.0.
    */
    native static void __setDelayAttachOnConnect (long self, int delayAttachOnConnect);
    public void setDelayAttachOnConnect (int delayAttachOnConnect) {
        __setDelayAttachOnConnect (self, delayAttachOnConnect);
    }
    /*
    Get socket option `hwm`.
    Available from libzmq 2.0.0 to 3.0.0.
    */
    native static int __hwm (long self);
    public int hwm () {
        return __hwm (self);
    }
    /*
    Set socket option `hwm`.
    Available from libzmq 2.0.0 to 3.0.0.
    */
    native static void __setHwm (long self, int hwm);
    public void setHwm (int hwm) {
        __setHwm (self, hwm);
    }
    /*
    Get socket option `swap`.
    Available from libzmq 2.0.0 to 3.0.0.
    */
    native static int __swap (long self);
    public int swap () {
        return __swap (self);
    }
    /*
    Set socket option `swap`.
    Available from libzmq 2.0.0 to 3.0.0.
    */
    native static void __setSwap (long self, int swap);
    public void setSwap (int swap) {
        __setSwap (self, swap);
    }
    /*
    Get socket option `affinity`.
    Available from libzmq 2.0.0.
    */
    native static int __affinity (long self);
    public int affinity () {
        return __affinity (self);
    }
    /*
    Set socket option `affinity`.
    Available from libzmq 2.0.0.
    */
    native static void __setAffinity (long self, int affinity);
    public void setAffinity (int affinity) {
        __setAffinity (self, affinity);
    }
    /*
    Get socket option `identity`.
    Available from libzmq 2.0.0.
    */
    native static String __identity (long self);
    public String identity () {
        return __identity (self);
    }
    /*
    Set socket option `identity`.
    Available from libzmq 2.0.0.
    */
    native static void __setIdentity (long self, String identity);
    public void setIdentity (String identity) {
        __setIdentity (self, identity);
    }
    /*
    Get socket option `rate`.
    Available from libzmq 2.0.0.
    */
    native static int __rate (long self);
    public int rate () {
        return __rate (self);
    }
    /*
    Set socket option `rate`.
    Available from libzmq 2.0.0.
    */
    native static void __setRate (long self, int rate);
    public void setRate (int rate) {
        __setRate (self, rate);
    }
    /*
    Get socket option `recovery_ivl`.
    Available from libzmq 2.0.0.
    */
    native static int __recoveryIvl (long self);
    public int recoveryIvl () {
        return __recoveryIvl (self);
    }
    /*
    Set socket option `recovery_ivl`.
    Available from libzmq 2.0.0.
    */
    native static void __setRecoveryIvl (long self, int recoveryIvl);
    public void setRecoveryIvl (int recoveryIvl) {
        __setRecoveryIvl (self, recoveryIvl);
    }
    /*
    Get socket option `recovery_ivl_msec`.
    Available from libzmq 2.0.0 to 3.0.0.
    */
    native static int __recoveryIvlMsec (long self);
    public int recoveryIvlMsec () {
        return __recoveryIvlMsec (self);
    }
    /*
    Set socket option `recovery_ivl_msec`.
    Available from libzmq 2.0.0 to 3.0.0.
    */
    native static void __setRecoveryIvlMsec (long self, int recoveryIvlMsec);
    public void setRecoveryIvlMsec (int recoveryIvlMsec) {
        __setRecoveryIvlMsec (self, recoveryIvlMsec);
    }
    /*
    Get socket option `mcast_loop`.
    Available from libzmq 2.0.0 to 3.0.0.
    */
    native static int __mcastLoop (long self);
    public int mcastLoop () {
        return __mcastLoop (self);
    }
    /*
    Set socket option `mcast_loop`.
    Available from libzmq 2.0.0 to 3.0.0.
    */
    native static void __setMcastLoop (long self, int mcastLoop);
    public void setMcastLoop (int mcastLoop) {
        __setMcastLoop (self, mcastLoop);
    }
    /*
    Get socket option `rcvtimeo`.
    Available from libzmq 2.2.0.
    */
    native static int __rcvtimeo (long self);
    public int rcvtimeo () {
        return __rcvtimeo (self);
    }
    /*
    Set socket option `rcvtimeo`.
    Available from libzmq 2.2.0.
    */
    native static void __setRcvtimeo (long self, int rcvtimeo);
    public void setRcvtimeo (int rcvtimeo) {
        __setRcvtimeo (self, rcvtimeo);
    }
    /*
    Get socket option `sndtimeo`.
    Available from libzmq 2.2.0.
    */
    native static int __sndtimeo (long self);
    public int sndtimeo () {
        return __sndtimeo (self);
    }
    /*
    Set socket option `sndtimeo`.
    Available from libzmq 2.2.0.
    */
    native static void __setSndtimeo (long self, int sndtimeo);
    public void setSndtimeo (int sndtimeo) {
        __setSndtimeo (self, sndtimeo);
    }
    /*
    Get socket option `sndbuf`.
    Available from libzmq 2.0.0.
    */
    native static int __sndbuf (long self);
    public int sndbuf () {
        return __sndbuf (self);
    }
    /*
    Set socket option `sndbuf`.
    Available from libzmq 2.0.0.
    */
    native static void __setSndbuf (long self, int sndbuf);
    public void setSndbuf (int sndbuf) {
        __setSndbuf (self, sndbuf);
    }
    /*
    Get socket option `rcvbuf`.
    Available from libzmq 2.0.0.
    */
    native static int __rcvbuf (long self);
    public int rcvbuf () {
        return __rcvbuf (self);
    }
    /*
    Set socket option `rcvbuf`.
    Available from libzmq 2.0.0.
    */
    native static void __setRcvbuf (long self, int rcvbuf);
    public void setRcvbuf (int rcvbuf) {
        __setRcvbuf (self, rcvbuf);
    }
    /*
    Get socket option `linger`.
    Available from libzmq 2.0.0.
    */
    native static int __linger (long self);
    public int linger () {
        return __linger (self);
    }
    /*
    Set socket option `linger`.
    Available from libzmq 2.0.0.
    */
    native static void __setLinger (long self, int linger);
    public void setLinger (int linger) {
        __setLinger (self, linger);
    }
    /*
    Get socket option `reconnect_ivl`.
    Available from libzmq 2.0.0.
    */
    native static int __reconnectIvl (long self);
    public int reconnectIvl () {
        return __reconnectIvl (self);
    }
    /*
    Set socket option `reconnect_ivl`.
    Available from libzmq 2.0.0.
    */
    native static void __setReconnectIvl (long self, int reconnectIvl);
    public void setReconnectIvl (int reconnectIvl) {
        __setReconnectIvl (self, reconnectIvl);
    }
    /*
    Get socket option `reconnect_ivl_max`.
    Available from libzmq 2.0.0.
    */
    native static int __reconnectIvlMax (long self);
    public int reconnectIvlMax () {
        return __reconnectIvlMax (self);
    }
    /*
    Set socket option `reconnect_ivl_max`.
    Available from libzmq 2.0.0.
    */
    native static void __setReconnectIvlMax (long self, int reconnectIvlMax);
    public void setReconnectIvlMax (int reconnectIvlMax) {
        __setReconnectIvlMax (self, reconnectIvlMax);
    }
    /*
    Get socket option `backlog`.
    Available from libzmq 2.0.0.
    */
    native static int __backlog (long self);
    public int backlog () {
        return __backlog (self);
    }
    /*
    Set socket option `backlog`.
    Available from libzmq 2.0.0.
    */
    native static void __setBacklog (long self, int backlog);
    public void setBacklog (int backlog) {
        __setBacklog (self, backlog);
    }
    /*
    Set socket option `subscribe`.
    Available from libzmq 2.0.0.
    */
    native static void __setSubscribe (long self, String subscribe);
    public void setSubscribe (String subscribe) {
        __setSubscribe (self, subscribe);
    }
    /*
    Set socket option `unsubscribe`.
    Available from libzmq 2.0.0.
    */
    native static void __setUnsubscribe (long self, String unsubscribe);
    public void setUnsubscribe (String unsubscribe) {
        __setUnsubscribe (self, unsubscribe);
    }
    /*
    Get socket option `type`.
    Available from libzmq 2.0.0.
    */
    native static int __type (long self);
    public int type () {
        return __type (self);
    }
    /*
    Get socket option `rcvmore`.
    Available from libzmq 2.0.0.
    */
    native static int __rcvmore (long self);
    public int rcvmore () {
        return __rcvmore (self);
    }
    /*
    Get socket option `events`.
    Available from libzmq 2.0.0.
    */
    native static int __events (long self);
    public int events () {
        return __events (self);
    }
    /*
    Self test of this class.
    */
    native static void __test (boolean verbose);
    public static void test (boolean verbose) {
        __test (verbose);
    }
}
