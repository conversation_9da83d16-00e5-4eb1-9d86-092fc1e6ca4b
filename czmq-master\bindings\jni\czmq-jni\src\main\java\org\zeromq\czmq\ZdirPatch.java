/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
package org.zeromq.czmq;

import org.zeromq.tools.ZmqNativeLoader;

import java.util.LinkedHashMap;
import java.util.Map;

public class ZdirPatch implements AutoCloseable {
    static {
        Map<String, Boolean> libraries = new LinkedHashMap<>();
        libraries.put("zmq", false);
        libraries.put("uuid", true);
        libraries.put("systemd", true);
        libraries.put("lz4", true);
        libraries.put("curl", true);
        libraries.put("nss", true);
        libraries.put("microhttpd", true);
        libraries.put("czmq", false);
        libraries.put("czmqjni", false);
        ZmqNativeLoader.loadLibraries(libraries);
    }
    public long self;
    /*
    Create new patch
    */
    native static long __new (String path, long file, int op, String alias);
    public ZdirPatch (String path, Zfile file, int op, String alias) {
        /*  TODO: if __new fails, self is null...            */
        self = __new (path, file.self, op, alias);
    }
    public ZdirPatch (long pointer) {
        self = pointer;
    }
    /*
    Destroy a patch
    */
    native static void __destroy (long self);
    @Override
    public void close () {
        __destroy (self);
        self = 0;
    }
    /*
    Create copy of a patch. If the patch is null, or memory was exhausted,
    returns null.
    */
    native static long __dup (long self);
    public ZdirPatch dup () {
        return new ZdirPatch (__dup (self));
    }
    /*
    Return patch file directory path
    */
    native static String __path (long self);
    public String path () {
        return __path (self);
    }
    /*
    Return patch file item
    */
    native static long __file (long self);
    public Zfile file () {
        return new Zfile (__file (self));
    }
    /*
    Return operation
    */
    native static int __op (long self);
    public int op () {
        return __op (self);
    }
    /*
    Return patch virtual file path
    */
    native static String __vpath (long self);
    public String vpath () {
        return __vpath (self);
    }
    /*
    Calculate hash digest for file (create only)
    */
    native static void __digestSet (long self);
    public void digestSet () {
        __digestSet (self);
    }
    /*
    Return hash digest for patch file
    */
    native static String __digest (long self);
    public String digest () {
        return __digest (self);
    }
    /*
    Self test of this class.
    */
    native static void __test (boolean verbose);
    public static void test (boolean verbose) {
        __test (verbose);
    }
}
