/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#ifndef Q_ZHTTP_SERVER_OPTIONS_H
#define Q_ZHTTP_SERVER_OPTIONS_H

#include "qczmq.h"

class QT_CZMQ_EXPORT QZhttpServerOptions : public QObject
{
    Q_OBJECT
public:

    //  Copy-construct to return the proper wrapped c types
    QZhttpServerOptions (zhttp_server_options_t *self, QObject *qObjParent = 0);

    //  Create a new zhttp_server_options.
    explicit QZhttpServerOptions (QObject *qObjParent = 0);

    //  Create options from config tree.
    static QZhttpServerOptions* fromConfig (QZconfig *config, QObject *qObjParent = 0);

    //  Destroy the zhttp_server_options.
    ~QZhttpServerOptions ();

    //  Get the server listening port.
    int port ();

    //  Set the server listening port
    void setPort (int port);

    //  Get the address sockets should connect to in order to receive requests.
    const QString backendAddress ();

    //  Set the address sockets should connect to in order to receive requests.
    void setBackendAddress (const QString &address);

    //  Self test of this class.
    static void test (bool verbose);

    zhttp_server_options_t *self;
};
#endif //  Q_ZHTTP_SERVER_OPTIONS_H
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
