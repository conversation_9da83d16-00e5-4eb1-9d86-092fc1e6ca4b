/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#ifndef QML_ZIFLIST_H
#define QML_ZIFLIST_H

#include <QtQml>

#include <czmq.h>
#include "qml_czmq_plugin.h"


class QmlZiflist : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isNULL READ isNULL)

public:
    ziflist_t *self;

    QmlZiflist() { self = NULL; }
    bool isNULL() { return self == NULL; }

    static QObject* qmlAttachedProperties(QObject* object); // defined in QmlZiflist.cpp

public slots:
    //  Reload network interfaces from system
    void reload ();

    //  Return the number of network interfaces on system
    size_t size ();

    //  Get first network interface, return NULL if there are none
    const QString first ();

    //  Get next network interface, return NULL if we hit the last one
    const QString next ();

    //  Return the current interface IP address as a printable string
    const QString address ();

    //  Return the current interface broadcast address as a printable string
    const QString broadcast ();

    //  Return the current interface network mask as a printable string
    const QString netmask ();

    //  Return the current interface MAC address as a printable string
    const QString mac ();

    //  Return the list of interfaces.
    void print ();

    //  Reload network interfaces from system, including IPv6
    void reloadIpv6 ();

    //  Return true if the current interface uses IPv6
    bool isIpv6 ();
};

class QmlZiflistAttached : public QObject
{
    Q_OBJECT
    QObject* m_attached;

public:
    QmlZiflistAttached (QObject* attached) {
        Q_UNUSED (attached);
    };

public slots:
    //  Get a list of network interfaces currently defined on the system
    //  Includes IPv6 interfaces
    QmlZiflist *newIpv6 ();

    //  Self test of this class.
    void test (bool verbose);

    //  Get a list of network interfaces currently defined on the system
    QmlZiflist *construct ();

    //  Destroy a ziflist instance
    void destruct (QmlZiflist *qmlSelf);
};


QML_DECLARE_TYPEINFO(QmlZiflist, QML_HAS_ATTACHED_PROPERTIES)

#endif
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
