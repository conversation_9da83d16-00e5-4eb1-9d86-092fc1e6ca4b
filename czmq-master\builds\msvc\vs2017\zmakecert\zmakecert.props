<?xml version="1.0" encoding="utf-8"?>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup Label="Globals">
    <_PropertySheetDisplayName>CZMQ Self Test Common Settings</_PropertySheetDisplayName>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <RunCodeAnalysis>false</RunCodeAnalysis>
  </PropertyGroup>

  <!-- Configuration -->
  <ItemDefinitionGroup>
    <ClCompile>
      <DisableSpecificWarnings>%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <EnablePREfast>false</EnablePREfast>
      <PreprocessorDefinitions>%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>Iphlpapi.lib;Rpcrt4.lib;Ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>

  <!-- Dependencies -->
  <ImportGroup Label="PropertySheets">
    <Import Project="$(SolutionDir)czmq.import.props" />
    <Import Project="$(SolutionDir)libzmq.import.props" />
    <Import Project="$(SolutionDir)uuid.import.props" Condition="'$(HAVE_UUID)'=='1'" />
    <Import Project="$(SolutionDir)systemd.import.props" Condition="'$(HAVE_SYSTEMD)'=='1'" />
    <Import Project="$(SolutionDir)lz4.import.props" Condition="'$(HAVE_LZ4)'=='1'" />
    <Import Project="$(SolutionDir)libcurl.import.props" Condition="'$(HAVE_LIBCURL)'=='1'" />
    <Import Project="$(SolutionDir)nss.import.props" Condition="'$(HAVE_NSS)'=='1'" />
    <Import Project="$(SolutionDir)libmicrohttpd.import.props" Condition="'$(HAVE_LIBMICROHTTPD)'=='1'" />
  </ImportGroup>

  <PropertyGroup Condition="$(Configuration.IndexOf('DEXE')) != -1">
    <Linkage-czmq>dynamic</Linkage-czmq>
    <Linkage-libzmq>dynamic</Linkage-libzmq>
    <Linkage-uuid Condition="'$(HAVE_UUID)'=='1'">dynamic</Linkage-uuid>
    <Linkage-systemd Condition="'$(HAVE_SYSTEMD)'=='1'">dynamic</Linkage-systemd>
    <Linkage-lz4 Condition="'$(HAVE_LZ4)'=='1'">dynamic</Linkage-lz4>
    <Linkage-libcurl Condition="'$(HAVE_LIBCURL)'=='1'">dynamic</Linkage-libcurl>
    <Linkage-nss Condition="'$(HAVE_NSS)'=='1'">dynamic</Linkage-nss>
    <Linkage-libmicrohttpd Condition="'$(HAVE_LIBMICROHTTPD)'=='1'">dynamic</Linkage-libmicrohttpd>
  </PropertyGroup>

  <PropertyGroup Condition="$(Configuration.IndexOf('LEXE')) != -1">
    <Linkage-czmq>ltcg</Linkage-czmq>
    <Linkage-libzmq>ltcg</Linkage-libzmq>
    <Linkage-uuid Condition="'$(HAVE_UUID)'=='1'">ltcg</Linkage-uuid>
    <Linkage-systemd Condition="'$(HAVE_SYSTEMD)'=='1'">ltcg</Linkage-systemd>
    <Linkage-lz4 Condition="'$(HAVE_LZ4)'=='1'">ltcg</Linkage-lz4>
    <Linkage-libcurl Condition="'$(HAVE_LIBCURL)'=='1'">ltcg</Linkage-libcurl>
    <Linkage-nss Condition="'$(HAVE_NSS)'=='1'">ltcg</Linkage-nss>
    <Linkage-libmicrohttpd Condition="'$(HAVE_LIBMICROHTTPD)'=='1'">ltcg</Linkage-libmicrohttpd>
  </PropertyGroup>

  <PropertyGroup Condition="$(Configuration.IndexOf('SEXE')) != -1">
    <Linkage-czmq>static</Linkage-czmq>
    <Linkage-libzmq>static</Linkage-libzmq>
    <Linkage-uuid Condition="'$(HAVE_UUID)'=='1'">static</Linkage-uuid>
    <Linkage-systemd Condition="'$(HAVE_SYSTEMD)'=='1'">static</Linkage-systemd>
    <Linkage-lz4 Condition="'$(HAVE_LZ4)'=='1'">static</Linkage-lz4>
    <Linkage-libcurl Condition="'$(HAVE_LIBCURL)'=='1'">static</Linkage-libcurl>
    <Linkage-nss Condition="'$(HAVE_NSS)'=='1'">static</Linkage-nss>
    <Linkage-libmicrohttpd Condition="'$(HAVE_LIBMICROHTTPD)'=='1'">static</Linkage-libmicrohttpd>
  </PropertyGroup>

  <!-- Messages -->
  <Target Name="LinkageInfo" BeforeTargets="PrepareForBuild">
    <Message Text="Linkage-czmq: $(Linkage-czmq)" Importance="high"/>
    <Message Text="Linkage-libzmq: $(Linkage-libzmq)" Importance="high" />
    <Message Text="Linkage-uuid: $(Linkage-uuid)" Importance="high" Condition="'$(HAVE_UUID)'=='1'" />
    <Message Text="Linkage-systemd: $(Linkage-systemd)" Importance="high" Condition="'$(HAVE_SYSTEMD)'=='1'" />
    <Message Text="Linkage-lz4: $(Linkage-lz4)" Importance="high" Condition="'$(HAVE_LZ4)'=='1'" />
    <Message Text="Linkage-libcurl: $(Linkage-libcurl)" Importance="high" Condition="'$(HAVE_LIBCURL)'=='1'" />
    <Message Text="Linkage-nss: $(Linkage-nss)" Importance="high" Condition="'$(HAVE_NSS)'=='1'" />
    <Message Text="Linkage-libmicrohttpd: $(Linkage-libmicrohttpd)" Importance="high" Condition="'$(HAVE_LIBMICROHTTPD)'=='1'" />
  </Target>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
</Project>
