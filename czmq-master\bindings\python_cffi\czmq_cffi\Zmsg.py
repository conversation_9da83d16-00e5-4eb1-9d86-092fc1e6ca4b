################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class Zmsg(object):
    """
    working with multipart messages
    """

    def __init__(self):
        """
        Create a new empty message object
        """
        p = utils.lib.zmsg_new()
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zmsg_destroy_py)

    @staticmethod
    def recv(source):
        """
        Receive message from socket, returns zmsg_t object or NULL if the recv
        was interrupted. Does a blocking recv. If you want to not block then use
        the zloop class or zmsg_recv_nowait or zmq_poll to check for socket input
        before receiving.
        """
        return utils.lib.zmsg_recv(source._p)

    @staticmethod
    def load(file):
        """
        Load/append an open file into new message, return the message.
        Returns NULL if the message could not be loaded.
        """
        return utils.lib.zmsg_load(file)

    @staticmethod
    def decode(frame):
        """
        Decodes a serialized message frame created by zmsg_encode () and returns
        a new zmsg_t object. Returns NULL if the frame was badly formatted or
        there was insufficient memory to work.
        """
        return utils.lib.zmsg_decode(frame._p)

    @staticmethod
    def new_signal(status):
        """
        Generate a signal message encoding the given status. A signal is a short
        message carrying a 1-byte success/failure code (by convention, 0 means
        OK). Signals are encoded to be distinguishable from "normal" messages.
        """
        return utils.lib.zmsg_new_signal(status)

    @staticmethod
    def send(self_p, dest):
        """
        Send message to destination socket, and destroy the message after sending
        it successfully. If the message has no frames, sends nothing but destroys
        the message anyhow. Nullifies the caller's reference to the message (as
        it is a destructor).
        """
        return utils.lib.zmsg_send(utils.ffi.new("zmsg_t **", self_p._p), dest._p)

    @staticmethod
    def sendm(self_p, dest):
        """
        Send message to destination socket as part of a multipart sequence, and
        destroy the message after sending it successfully. Note that after a
        zmsg_sendm, you must call zmsg_send or another method that sends a final
        message part. If the message has no frames, sends nothing but destroys
        the message anyhow. Nullifies the caller's reference to the message (as
        it is a destructor).
        """
        return utils.lib.zmsg_sendm(utils.ffi.new("zmsg_t **", self_p._p), dest._p)

    def size(self):
        """
        Return size of message, i.e. number of frames (0 or more).
        """
        return utils.lib.zmsg_size(self._p)

    def content_size(self):
        """
        Return total size of all frames in message.
        """
        return utils.lib.zmsg_content_size(self._p)

    def routing_id(self):
        """
        Return message routing ID, if the message came from a ZMQ_SERVER socket.
        Else returns zero.
        """
        return utils.lib.zmsg_routing_id(self._p)

    def set_routing_id(self, routing_id):
        """
        Set routing ID on message. This is used if/when the message is sent to a
        ZMQ_SERVER socket.
        """
        utils.lib.zmsg_set_routing_id(self._p, routing_id)

    def prepend(self, frame_p):
        """
        Push frame to the front of the message, i.e. before all other frames.
        Message takes ownership of frame, will destroy it when message is sent.
        Returns 0 on success, -1 on error. Deprecates zmsg_push, which did not
        nullify the caller's frame reference.
        """
        return utils.lib.zmsg_prepend(self._p, utils.ffi.new("zframe_t **", frame_p._p))

    def append(self, frame_p):
        """
        Add frame to the end of the message, i.e. after all other frames.
        Message takes ownership of frame, will destroy it when message is sent.
        Returns 0 on success. Deprecates zmsg_add, which did not nullify the
        caller's frame reference.
        """
        return utils.lib.zmsg_append(self._p, utils.ffi.new("zframe_t **", frame_p._p))

    def pop(self):
        """
        Remove first frame from message, if any. Returns frame, or NULL.
        """
        return utils.lib.zmsg_pop(self._p)

    def pushmem(self, data, size):
        """
        Push block of memory to front of message, as a new frame.
        Returns 0 on success, -1 on error.
        """
        return utils.lib.zmsg_pushmem(self._p, data, size)

    def addmem(self, data, size):
        """
        Add block of memory to the end of the message, as a new frame.
        Returns 0 on success, -1 on error.
        """
        return utils.lib.zmsg_addmem(self._p, data, size)

    def pushstr(self, string):
        """
        Push string as new frame to front of message.
        Returns 0 on success, -1 on error.
        """
        return utils.lib.zmsg_pushstr(self._p, utils.to_bytes(string))

    def addstr(self, string):
        """
        Push string as new frame to end of message.
        Returns 0 on success, -1 on error.
        """
        return utils.lib.zmsg_addstr(self._p, utils.to_bytes(string))

    def pushstrf(self, format, *format_args):
        """
        Push formatted string as new frame to front of message.
        Returns 0 on success, -1 on error.
        """
        return utils.lib.zmsg_pushstrf(self._p, format, *format_args)

    def addstrf(self, format, *format_args):
        """
        Push formatted string as new frame to end of message.
        Returns 0 on success, -1 on error.
        """
        return utils.lib.zmsg_addstrf(self._p, format, *format_args)

    def popstr(self):
        """
        Pop frame off front of message, return as fresh string. If there were
        no more frames in the message, returns NULL.
        """
        return utils.lib.zmsg_popstr(self._p)

    def addmsg(self, msg_p):
        """
        Push encoded message as a new frame. Message takes ownership of
        submessage, so the original is destroyed in this call. Returns 0 on
        success, -1 on error.
        """
        return utils.lib.zmsg_addmsg(self._p, utils.ffi.new("zmsg_t **", msg_p._p))

    def popmsg(self):
        """
        Remove first submessage from message, if any. Returns zmsg_t, or NULL if
        decoding was not successful.
        """
        return utils.lib.zmsg_popmsg(self._p)

    def remove(self, frame):
        """
        Remove specified frame from list, if present. Does not destroy frame.
        """
        utils.lib.zmsg_remove(self._p, frame._p)

    def first(self):
        """
        Set cursor to first frame in message. Returns frame, or NULL, if the
        message is empty. Use this to navigate the frames as a list.
        """
        return utils.lib.zmsg_first(self._p)

    def next(self):
        """
        Return the next frame. If there are no more frames, returns NULL. To move
        to the first frame call zmsg_first(). Advances the cursor.
        """
        return utils.lib.zmsg_next(self._p)

    def last(self):
        """
        Return the last frame. If there are no frames, returns NULL.
        """
        return utils.lib.zmsg_last(self._p)

    def save(self, file):
        """
        Save message to an open file, return 0 if OK, else -1. The message is
        saved as a series of frames, each with length and data. Note that the
        file is NOT guaranteed to be portable between operating systems, not
        versions of CZMQ. The file format is at present undocumented and liable
        to arbitrary change.
        """
        return utils.lib.zmsg_save(self._p, file)

    def encode(self):
        """
        Serialize multipart message to a single message frame. Use this method
        to send structured messages across transports that do not support
        multipart data. Allocates and returns a new frame containing the
        serialized message. To decode a serialized message frame, use
        zmsg_decode ().
        """
        return utils.lib.zmsg_encode(self._p)

    def dup(self):
        """
        Create copy of message, as new message object. Returns a fresh zmsg_t
        object. If message is null, or memory was exhausted, returns null.
        """
        return utils.lib.zmsg_dup(self._p)

    def print_py(self):
        """
        Send message to zsys log sink (may be stdout, or system facility as
        configured by zsys_set_logstream).
        Long messages are truncated.
        """
        utils.lib.zmsg_print(self._p)

    def print_n(self, size):
        """
        Send message to zsys log sink (may be stdout, or system facility as
        configured by zsys_set_logstream).
        Message length is specified; no truncation unless length is zero.
        Backwards compatible with zframe_print when length is zero.
        """
        utils.lib.zmsg_print_n(self._p, size)

    def eq(self, other):
        """
        Return true if the two messages have the same number of frames and each
        frame in the first message is identical to the corresponding frame in the
        other message. As with zframe_eq, return false if either message is NULL.
        """
        return utils.lib.zmsg_eq(self._p, other._p)

    def signal(self):
        """
        Return signal value, 0 or greater, if message is a signal, -1 if not.
        """
        return utils.lib.zmsg_signal(self._p)

    @staticmethod
    def is_py(self):
        """
        Probe the supplied object, and report if it looks like a zmsg_t.
        """
        return utils.lib.zmsg_is(self._p)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zmsg_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
