

#include "sys_utl.h"
//#include "const.h"
//#include "mqttclient.h"
#include "ace/OS.h"
#include "eventtype.h"
#include "fdc/fdcdef.h"
#include "net_api.h"
#include "zlib.h"
#include "zutil.h"
#include "utl/directory.h"
#include <time.h>
#include <iostream>
#include <string>

#include "CJsonObject.h"
#include "zlib.h"
using namespace ECON;
using namespace FDC;
using namespace neb;
using namespace std;

bool readFile(const char * fname, char * buf, int len)
{
	FILE * pf = fopen(fname, "r");
	if(!pf) {
		printf("r fopen [%s] failed\n", fname);
		return false;
	}

	char * st = fgets(buf, len-1, pf);
	fclose(pf);
	if(!st) {
		printf("fgets [%s] failed\n", fname);
		return false;
	}

	printf("fgets [%s] ok\n", fname);
	return true;
}

void test1()
{
    char versions[4096] = {0};
    readFile("/opt/ota/versions.json", versions, sizeof(versions));    
    
    std::string s;
    s.assign((char *)versions);
    CJsonObject obj(s);
    std::string ss;
    if(obj.IsEmpty()) {
        printf("versions.json parse error\n");
    }
    else {
        if(!obj.Get("PPC", ss)) {
            printf("versions.json get pcs-version failed\n");
        }
        else {
            printf("from versions.json get pcs-version: %s\n", ss.c_str());
		}

    }
}

bool test2(const char * fname)
{
    CJsonObject obj;
	FileManage fmgr(fname);

	FILE * pf = fopen(fmgr.get_filename(), "r");
	if(!pf) {
		printf("re fopen [%s] failed", fname);
		return false;
	}

	fseek(pf, 0, SEEK_END);  		// 将文件指针移动到文件末尾
	long file_size = ftell(pf);  	// 获取文件大小
	fseek(pf, 0, SEEK_SET);  		// 将文件指针移动回文件开头

	char * buf = (char *)malloc(file_size+1);
	memset(buf, 0, file_size+1);
	
	//char * st = fgets(buf, file_size, pf);
	char * st = fgets(buf, file_size+1, pf);
	fclose(pf);
	if(!st) {
		printf("re fgets [%s] failed", fname);
		free(buf);
		return false;
	}
	printf("read file[%s] get content[%s]\n", fname, buf);

	buf[file_size] = 0;
	if(!obj.Parse(buf)) {
		printf("re parse [%s] failed [%s]", fname, obj.GetErrMsg().c_str());
		free(buf);
		return false;
	}

	printf("re fgets [%s] ok", fname);
	free(buf);

    return true;
}

void getElemnet()
{
	string strKey;
	while (obj.GetKey(strKey)) {
		CJsonObject child;
		if (obj.Get(strKey, child)) {
			if (strcmp(strKey.c_str(), "ECON_TARGET")) {
				printf("get element %s\n", strKey.c_str());
				break;
			}
			else
		}
		
	}
}

bool test3(const char * fname)
{
    CJsonObject obj;
	FileManage fmgr(fname);

	FILE * pf = fopen(fmgr.get_filename(), "r");
	if(!pf) {
		printf("re fopen [%s] failed", fname);
		return false;
	}

	fseek(pf, 0, SEEK_END);  		// 将文件指针移动到文件末尾
	long file_size = ftell(pf);  	// 获取文件大小
	fseek(pf, 0, SEEK_SET);  		// 将文件指针移动回文件开头

	char * buf = (char *)malloc(file_size+1);
	memset(buf, 0, file_size+1);
	
	//char * st = fgets(buf, file_size, pf);
	fread(buf, 1, file_size, pf);
	fclose(pf);

	//printf("read file[%s] get content[%s]\n", fname, buf);

	if(!obj.Parse(buf)) {
		printf("re parse [%s] failed [%s]", fname, obj.GetErrMsg().c_str());
		free(buf);
		return false;
	}

	printf("re fgets [%s] ok\n", fname);
	free(buf);

	//--------------------- 	begin   -------------------------------


    return true;
}

int main(int argc, char * argv[])
{
    //test1();

    //test2(argv[1]);

	test3(argv[1]);

    return 0;
}