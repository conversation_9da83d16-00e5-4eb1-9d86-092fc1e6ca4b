

#include "sys_utl.h"
//#include "const.h"
//#include "mqttclient.h"
#include "ace/OS.h"
#include "eventtype.h"
#include "fdc/fdcdef.h"
#include "net_api.h"
#include "zlib.h"
#include "zutil.h"
#include "utl/directory.h"
#include <time.h>
#include <iostream>
#include <string>

#include "CJsonObject.h"
#include "zlib.h"
using namespace ECON;
using namespace FDC;
using namespace neb;
using namespace std;

bool readFile(const char * fname, char * buf, int len)
{
	FILE * pf = fopen(fname, "r");
	if(!pf) {
		printf("r fopen [%s] failed\n", fname);
		return false;
	}

	char * st = fgets(buf, len-1, pf);
	fclose(pf);
	if(!st) {
		printf("fgets [%s] failed\n", fname);
		return false;
	}

	printf("fgets [%s] ok\n", fname);
	return true;
}

void test1()
{
    char versions[4096] = {0};
    readFile("/opt/ota/versions.json", versions, sizeof(versions));    
    
    std::string s;
    s.assign((char *)versions);
    CJsonObject obj(s);
    std::string ss;
    if(obj.IsEmpty()) {
        printf("versions.json parse error\n");
    }
    else {
        if(!obj.Get("PPC", ss)) {
            printf("versions.json get pcs-version failed\n");
        }
        else {
            printf("from versions.json get pcs-version: %s\n", ss.c_str());
		}

    }
}

bool test2(const char * fname)
{
    CJsonObject obj;
	FileManage fmgr(fname);

	FILE * pf = fopen(fmgr.get_filename(), "r");
	if(!pf) {
		printf("re fopen [%s] failed", fname);
		return false;
	}

	fseek(pf, 0, SEEK_END);  		// 将文件指针移动到文件末尾
	long file_size = ftell(pf);  	// 获取文件大小
	fseek(pf, 0, SEEK_SET);  		// 将文件指针移动回文件开头

	char * buf = (char *)malloc(file_size+1);
	memset(buf, 0, file_size+1);
	
	//char * st = fgets(buf, file_size, pf);
	char * st = fgets(buf, file_size+1, pf);
	fclose(pf);
	if(!st) {
		printf("re fgets [%s] failed", fname);
		free(buf);
		return false;
	}
	printf("read file[%s] get content[%s]\n", fname, buf);

	buf[file_size] = 0;
	if(!obj.Parse(buf)) {
		printf("re parse [%s] failed [%s]", fname, obj.GetErrMsg().c_str());
		free(buf);
		return false;
	}

	printf("re fgets [%s] ok", fname);
	free(buf);

    return true;
}


#include <iostream>
#include <string>
#include <fstream>
#include <sstream>
#include <vector>

// 叶子节点数据结构体
struct LeafNodeData {
    string KEY;        // 数据类型，如 "string", "int", "float", "bool"
    string DATATYPE;   // 数据来源类型，如 "fixed", "yc", "custom"
    string GETWAY;     // 获取方式
    string FIXEDVAL;   // 固定值
    string GROUPNO;    // 组号
    string DITNO;      // 设备号

    // 构造函数
    LeafNodeData() : KEY(""), DATATYPE(""), GETWAY(""), FIXEDVAL(""), GROUPNO(""), DITNO("") {}

    // 打印函数
    void print() const {
        cout << "  KEY: " << KEY << endl;
        cout << "  DATATYPE: " << DATATYPE << endl;
        cout << "  GETWAY: " << GETWAY << endl;
        cout << "  FIXEDVAL: " << FIXEDVAL << endl;
        cout << "  GROUPNO: " << GROUPNO << endl;
        cout << "  DITNO: " << DITNO << endl;
    }
};

// 分割字符串函数
void splitString(const string& str, char delimiter, string parts[], int maxParts) {
    int partIndex = 0;
    int start = 0;

    for (int i = 0; i <= (int)str.length() && partIndex < maxParts; i++) {
        if (i == (int)str.length() || str[i] == delimiter) {
            if (partIndex < maxParts) {
                parts[partIndex] = str.substr(start, i - start);
                partIndex++;
            }
            start = i + 1;
        }
    }

    // 填充剩余部分为空字符串
    for (int i = partIndex; i < maxParts; i++) {
        parts[i] = "";
    }
}

// 解析叶子节点的value字符串
LeafNodeData parseLeafValue(const string& value) {
    LeafNodeData data;
    string parts[6];

    // 使用^分割字符串
    splitString(value, '^', parts, 6);

    data.KEY = parts[0];
    data.DATATYPE = parts[1];
    data.GETWAY = parts[2];
    data.FIXEDVAL = parts[3];
    data.GROUPNO = parts[4];
    data.DITNO = parts[5];

    return data;
}

// 检查是否为叶子节点（值为字符串且包含^分隔符）
bool isLeafNode(const CJsonObject& obj, const string& key) {
    string value;
    if (obj.Get(key, value)) {
        // 检查是否包含^字符，这是叶子节点的特征
        return value.find('^') != string::npos;
    }
    return false;
}

// 递归处理JSON，查找并处理叶子节点
void processJsonRecursive(CJsonObject& obj, const string& currentPath = "") {
    CJsonObject temp = obj;
    temp.ResetTraversing();

    string key;
    while (temp.GetKey(key)) {
        string fullPath = currentPath.empty() ? key : currentPath + "." + key;

        if (isLeafNode(obj, key)) {
            // 这是一个叶子节点，处理它
            string value;
            if (obj.Get(key, value)) {
                cout << "Processing leaf node: " << fullPath << endl;
                cout << "Original value: " << value << endl;

                // 解析叶子节点数据
                LeafNodeData leafData = parseLeafValue(value);
                cout << "Parsed data:" << endl;
                leafData.print();

                // 将叶子节点的值替换为1
                obj.Replace(key, 1);
                cout << "Replaced value with: 1" << endl;
                cout << string(50, '-') << endl;
            }
        } else {
            // 尝试获取子对象
            CJsonObject child;
            if (obj.Get(key, child)) {
                if (child.IsArray()) {
                    // 处理数组
                    int arraySize = child.GetArraySize();
                    for (int i = 0; i < arraySize; i++) {
                        CJsonObject arrayItem;
                        if (child.Get(i, arrayItem)) {
                            char indexStr[16];
                            sprintf(indexStr, "[%d]", i);
                            string arrayPath = fullPath + string(indexStr);

                            // 递归处理数组元素
                            processJsonRecursive(arrayItem, arrayPath);

                            // 将修改后的数组元素放回数组
                            child.Replace(i, arrayItem);
                        }
                    }
                    // 将修改后的数组放回父对象
                    obj.Replace(key, child);
                } else {
                    // 递归处理子对象
                    processJsonRecursive(child, fullPath);
                    // 将修改后的子对象放回父对象
                    obj.Replace(key, child);
                }
            }
        }
    }
}


// 简单的递归函数，查找ECON_TARGET
void findEconTarget(const CJsonObject& obj, const string& parentKey = "root") {
    // 创建临时对象用于遍历keys
    CJsonObject temp = obj;
    temp.ResetTraversing();
    
    string key;
    while (temp.GetKey(key)) {
        if (key == "ECON_TARGET") {
            // 找到了ECON_TARGET，获取其值
            string value;
            if (obj.Get(key, value)) {
                cout << "Found ECON_TARGET!" << endl;
                cout << "  Parent key: " << parentKey << endl;
                cout << "  Value: " << value << endl;
                cout << "  ---" << endl;
            }
        } else {
            // 尝试获取子对象
            CJsonObject child;
            if (obj.Get(key, child)) {
                if (child.IsArray()) {
                    // 处理数组
                    CJsonObject mutableChild = child;
                    int size = mutableChild.GetArraySize();
                    for (int i = 0; i < size; i++) {
                        CJsonObject arrayItem;
                        if (child.Get(i, arrayItem)) {
							char stri[4] = {0};
							sprintf(stri, "%d", i);
                            string arrayKey = key + "[" + string(stri) + "]";
                            findEconTarget(arrayItem, arrayKey);
                        }
                    }
                } else {
                    // 递归处理子对象
                    findEconTarget(child, key);
                }
            }
        }
    }
}

bool test3(const char * fname)
{
	FileManage fmgr(fname);
	const char * fullname = fmgr.get_filename();

    // 读取JSON文件
    ifstream file(fullname);
    if (!file.is_open()) {
        cout << "Cannot open pcs.json file!" << endl;
        return -1;
    }
    
    // 读取文件内容
    stringstream buffer;
    buffer << file.rdbuf();
    string jsonContent = buffer.str();
    file.close();
    
    cout << "JSON content loaded:" << endl;
    cout << jsonContent << endl;
    cout << "\n" << string(50, '=') << "\n" << endl;
    
    // 解析JSON
    CJsonObject rootObj(jsonContent);
    if (rootObj.IsEmpty()) {
        cout << "Failed to parse JSON!" << endl;
        cout << "Error: " << rootObj.GetErrMsg() << endl;
        return -1;
    }
    
    cout << "Searching for ECON_TARGET..." << endl;
    cout << string(30, '-') << endl;
    
    // 开始查找
    findEconTarget(rootObj);
    
    cout << string(30, '-') << endl;
    cout << "Search completed!" << endl;
    
    return 0;
}

int main(int argc, char * argv[])
{
    //test1();

    //test2(argv[1]);

	test3(argv[1]);

    return 0;
}