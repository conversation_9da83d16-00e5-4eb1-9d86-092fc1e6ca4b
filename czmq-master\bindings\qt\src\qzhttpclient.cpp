/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZhttpClient::QZhttpClient (zhttp_client_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Create a new http client
QZhttpClient::QZhttpClient (bool verbose, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zhttp_client_new (verbose);
}

///
//  Destroy an http client
QZhttpClient::~QZhttpClient ()
{
    zhttp_client_destroy (&self);
}

///
//  Self test of this class.
void QZhttpClient::test (bool verbose)
{
    zhttp_client_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
