################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class ZhttpServerOptions(object):
    """
    zhttp server.
    """

    def __init__(self):
        """
        Create a new zhttp_server_options.
        """
        p = utils.lib.zhttp_server_options_new()
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zhttp_server_options_destroy_py)

    @staticmethod
    def from_config(config):
        """
        Create options from config tree.
        """
        return utils.lib.zhttp_server_options_from_config(config._p)

    def port(self):
        """
        Get the server listening port.
        """
        return utils.lib.zhttp_server_options_port(self._p)

    def set_port(self, port):
        """
        Set the server listening port
        """
        utils.lib.zhttp_server_options_set_port(self._p, port)

    def backend_address(self):
        """
        Get the address sockets should connect to in order to receive requests.
        """
        return utils.lib.zhttp_server_options_backend_address(self._p)

    def set_backend_address(self, address):
        """
        Set the address sockets should connect to in order to receive requests.
        """
        utils.lib.zhttp_server_options_set_backend_address(self._p, utils.to_bytes(address))

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zhttp_server_options_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
