<?xml version="1.0" encoding="utf-8"?>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup Label="Globals">
    <_PropertySheetDisplayName>CZMQ Import Settings</_PropertySheetDisplayName>
  </PropertyGroup>

  <!-- User Interface -->
  <ItemGroup Label="BuildOptionsExtension">
    <PropertyPageSchema Include="$(MSBuildThisFileDirectory)czmq.import.xml" />
  </ItemGroup>

  <!-- Linkage -->
  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalIncludeDirectories>$(ProjectDir)..\..\..\..\..\czmq\include\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions Condition="'$(Linkage-czmq)' == 'static' Or '$(Linkage-czmq)' == 'ltcg'">CZMQ_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies Condition="'$(Linkage-czmq)' != ''">libczmq.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories Condition="$(Configuration.IndexOf('Debug')) != -1">$(ProjectDir)..\..\..\..\..\czmq\bin\$(PlatformName)\Debug\$(PlatformToolset)\$(Linkage-czmq)\;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalLibraryDirectories Condition="$(Configuration.IndexOf('Release')) != -1">$(ProjectDir)..\..\..\..\..\czmq\bin\$(PlatformName)\Release\$(PlatformToolset)\$(Linkage-czmq)\;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>

  <!-- Copy -->
  <Target Name="Linkage-czmq-dynamic" AfterTargets="AfterBuild" Condition="'$(Linkage-czmq)' == 'dynamic'">
    <Copy Condition="$(Configuration.IndexOf('Debug')) != -1"
          SourceFiles="$(ProjectDir)..\..\..\..\..\czmq\bin\$(PlatformName)\Debug\$(PlatformToolset)\dynamic\libczmq.dll"
          DestinationFiles="$(TargetDir)libczmq.dll"
          SkipUnchangedFiles="true" />
    <Copy Condition="$(Configuration.IndexOf('Debug')) != -1"
          SourceFiles="$(ProjectDir)..\..\..\..\..\czmq\bin\$(PlatformName)\Debug\$(PlatformToolset)\dynamic\libczmq.pdb"
          DestinationFiles="$(TargetDir)libczmq.pdb"
          SkipUnchangedFiles="true" />
    <Copy Condition="$(Configuration.IndexOf('Release')) != -1"
          SourceFiles="$(ProjectDir)..\..\..\..\..\czmq\bin\$(PlatformName)\Release\$(PlatformToolset)\dynamic\libczmq.dll"
          DestinationFiles="$(TargetDir)libczmq.dll"
          SkipUnchangedFiles="true" />
  </Target>

  <!-- Messages -->
  <Target Name="czmq-info" BeforeTargets="AfterBuild" Condition="'$(Linkage-czmq)' == 'dynamic'">
    <Message Text="Copying libczmq.dll -&gt; $(TargetDir)libczmq.dll" Importance="high"/>
    <Message Text="Copying libczmq.pdb -&gt; $(TargetDir)libczmq.pdb" Importance="high" Condition="$(Configuration.IndexOf('Debug')) != -1" />
  </Target>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
</Project>
