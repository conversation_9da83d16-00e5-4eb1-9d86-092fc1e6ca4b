/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZiflist::QZiflist (ziflist_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Get a list of network interfaces currently defined on the system
QZiflist::QZiflist (QObject *qObjParent) : QObject (qObjParent)
{
    this->self = ziflist_new ();
}

///
//  Destroy a ziflist instance
QZiflist::~QZiflist ()
{
    ziflist_destroy (&self);
}

///
//  Reload network interfaces from system
void QZiflist::reload ()
{
    ziflist_reload (self);

}

///
//  Return the number of network interfaces on system
size_t QZiflist::size ()
{
    size_t rv = ziflist_size (self);
    return rv;
}

///
//  Get first network interface, return NULL if there are none
const QString QZiflist::first ()
{
    const QString rv = QString (ziflist_first (self));
    return rv;
}

///
//  Get next network interface, return NULL if we hit the last one
const QString QZiflist::next ()
{
    const QString rv = QString (ziflist_next (self));
    return rv;
}

///
//  Return the current interface IP address as a printable string
const QString QZiflist::address ()
{
    const QString rv = QString (ziflist_address (self));
    return rv;
}

///
//  Return the current interface broadcast address as a printable string
const QString QZiflist::broadcast ()
{
    const QString rv = QString (ziflist_broadcast (self));
    return rv;
}

///
//  Return the current interface network mask as a printable string
const QString QZiflist::netmask ()
{
    const QString rv = QString (ziflist_netmask (self));
    return rv;
}

///
//  Return the current interface MAC address as a printable string
const QString QZiflist::mac ()
{
    const QString rv = QString (ziflist_mac (self));
    return rv;
}

///
//  Return the list of interfaces.
void QZiflist::print ()
{
    ziflist_print (self);

}

///
//  Get a list of network interfaces currently defined on the system
//  Includes IPv6 interfaces
QZiflist * QZiflist::newIpv6 ()
{
    QZiflist *rv = new QZiflist (ziflist_new_ipv6 ());
    return rv;
}

///
//  Reload network interfaces from system, including IPv6
void QZiflist::reloadIpv6 ()
{
    ziflist_reload_ipv6 (self);

}

///
//  Return true if the current interface uses IPv6
bool QZiflist::isIpv6 ()
{
    bool rv = ziflist_is_ipv6 (self);
    return rv;
}

///
//  Self test of this class.
void QZiflist::test (bool verbose)
{
    ziflist_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
