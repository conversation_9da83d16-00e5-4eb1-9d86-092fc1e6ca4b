/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#ifndef Q_ZHTTP_SERVER_H
#define Q_ZHTTP_SERVER_H

#include "qczmq.h"

class QT_CZMQ_EXPORT QZhttpServer : public QObject
{
    Q_OBJECT
public:

    //  Copy-construct to return the proper wrapped c types
    QZhttpServer (zhttp_server_t *self, QObject *qObjParent = 0);

    //  Create a new http server
    explicit QZhttpServer (QZhttpServerOptions *options, QObject *qObjParent = 0);

    //  Destroy an http server
    ~QZhttpServer ();

    //  Return the port the server is listening on.
    int port ();

    //  Self test of this class.
    static void test (bool verbose);

    zhttp_server_t *self;
};
#endif //  Q_ZHTTP_SERVER_H
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
