################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class Ztimerset(object):
    """
    timer set
    """

    def __init__(self):
        """
        Create new timer set.
        """
        p = utils.lib.ztimerset_new()
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.ztimerset_destroy_py)

    def add(self, interval, handler, arg):
        """
        Add a timer to the set. Returns timer id if OK, -1 on failure.
        """
        return utils.lib.ztimerset_add(self._p, interval, handler, arg._p)

    def cancel(self, timer_id):
        """
        Cancel a timer. Returns 0 if OK, -1 on failure.
        """
        return utils.lib.ztimerset_cancel(self._p, timer_id)

    def set_interval(self, timer_id, interval):
        """
        Set timer interval. Returns 0 if OK, -1 on failure.
        This method is slow, canceling the timer and adding a new one yield better performance.
        """
        return utils.lib.ztimerset_set_interval(self._p, timer_id, interval)

    def reset(self, timer_id):
        """
        Reset timer to start interval counting from current time. Returns 0 if OK, -1 on failure.
        This method is slow, canceling the timer and adding a new one yield better performance.
        """
        return utils.lib.ztimerset_reset(self._p, timer_id)

    def timeout(self):
        """
        Return the time until the next interval.
        Should be used as timeout parameter for the zpoller wait method.
        The timeout is in msec.
        """
        return utils.lib.ztimerset_timeout(self._p)

    def execute(self):
        """
        Invoke callback function of all timers which their interval has elapsed.
        Should be call after zpoller wait method.
        Returns 0 if OK, -1 on failure.
        """
        return utils.lib.ztimerset_execute(self._p)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.ztimerset_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
