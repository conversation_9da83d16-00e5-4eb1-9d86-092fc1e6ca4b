################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
include(../common.pri)
INCLUDEPATH += $$PWD
DEPENDPATH += $$PWD

qczmq-uselib:!qczmq-buildlib {
    LIBS += -L$$QCZMQ_LIBDIR -l$$QCZMQ_LIBNAME
} else {
    HEADERS       += \
                     $$PWD/qczmq.h \
                     $$PWD/qzactor.h \
                     $$PWD/qzargs.h \
                     $$PWD/qzarmour.h \
                     $$PWD/qzcert.h \
                     $$PWD/qzcertstore.h \
                     $$PWD/qzchunk.h \
                     $$PWD/qzclock.h \
                     $$PWD/qzconfig.h \
                     $$PWD/qzdigest.h \
                     $$PWD/qzdir.h \
                     $$PWD/qzdirpatch.h \
                     $$PWD/qzfile.h \
                     $$PWD/qzframe.h \
                     $$PWD/qzhash.h \
                     $$PWD/qzhashx.h \
                     $$PWD/qziflist.h \
                     $$PWD/qzlist.h \
                     $$PWD/qzlistx.h \
                     $$PWD/qzloop.h \
                     $$PWD/qzmsg.h \
                     $$PWD/qzpoller.h \
                     $$PWD/qzproc.h \
                     $$PWD/qzsock.h \
                     $$PWD/qzstr.h \
                     $$PWD/qzsys.h \
                     $$PWD/qztimerset.h \
                     $$PWD/qztrie.h \
                     $$PWD/qzuuid.h \
                     $$PWD/qzhttpclient.h \
                     $$PWD/qzhttpserver.h \
                     $$PWD/qzhttpserveroptions.h \
                     $$PWD/qzhttprequest.h \
                     $$PWD/qzhttpresponse.h \
                     $$PWD/qzosc.h

    SOURCES       += \
                     $$PWD/qzactor.cpp \
                     $$PWD/qzargs.cpp \
                     $$PWD/qzarmour.cpp \
                     $$PWD/qzcert.cpp \
                     $$PWD/qzcertstore.cpp \
                     $$PWD/qzchunk.cpp \
                     $$PWD/qzclock.cpp \
                     $$PWD/qzconfig.cpp \
                     $$PWD/qzdigest.cpp \
                     $$PWD/qzdir.cpp \
                     $$PWD/qzdirpatch.cpp \
                     $$PWD/qzfile.cpp \
                     $$PWD/qzframe.cpp \
                     $$PWD/qzhash.cpp \
                     $$PWD/qzhashx.cpp \
                     $$PWD/qziflist.cpp \
                     $$PWD/qzlist.cpp \
                     $$PWD/qzlistx.cpp \
                     $$PWD/qzloop.cpp \
                     $$PWD/qzmsg.cpp \
                     $$PWD/qzpoller.cpp \
                     $$PWD/qzproc.cpp \
                     $$PWD/qzsock.cpp \
                     $$PWD/qzstr.cpp \
                     $$PWD/qzsys.cpp \
                     $$PWD/qztimerset.cpp \
                     $$PWD/qztrie.cpp \
                     $$PWD/qzuuid.cpp \
                     $$PWD/qzhttpclient.cpp \
                     $$PWD/qzhttpserver.cpp \
                     $$PWD/qzhttpserveroptions.cpp \
                     $$PWD/qzhttprequest.cpp \
                     $$PWD/qzhttpresponse.cpp \
                     $$PWD/qzosc.cpp
}

win32 {
    qczmq-buildlib:shared:DEFINES += QT_CZMQ_EXPORT
    else:qczmq-uselib:DEFINES += QT_CZMQ_IMPORT
}
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
