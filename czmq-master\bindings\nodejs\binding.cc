/*  =========================================================================
    CZMQ Node.js binding implementation

    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
    =========================================================================
*/

#include "binding.h"

using namespace v8;
using namespace Nan;

NAN_MODULE_INIT (Zargs::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zargs").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "progname", _progname);
    Nan::SetPrototypeMethod (tpl, "arguments", _arguments);
    Nan::SetPrototypeMethod (tpl, "first", _first);
    Nan::SetPrototypeMethod (tpl, "next", _next);
    Nan::SetPrototypeMethod (tpl, "paramFirst", _param_first);
    Nan::SetPrototypeMethod (tpl, "paramNext", _param_next);
    Nan::SetPrototypeMethod (tpl, "paramName", _param_name);
    Nan::SetPrototypeMethod (tpl, "get", _get);
    Nan::SetPrototypeMethod (tpl, "getx", _getx);
    Nan::SetPrototypeMethod (tpl, "has", _has);
    Nan::SetPrototypeMethod (tpl, "hasx", _hasx);
    Nan::SetPrototypeMethod (tpl, "print", _print);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zargs").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zargs::Zargs (int argc, char **argv) {
    self = zargs_new ((int) argc, (char **)&argv);
}

Zargs::Zargs (zargs_t *self_) {
    self = self_;
}

Zargs::~Zargs () {
}

NAN_METHOD (Zargs::New) {
    assert (info.IsConstructCall ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `argc`");

    //int argc; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int argc;


    if (info [0]->IsNumber ())
    {
          argc = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`argc` must be a number");
    char *argv;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `argv`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`argv` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String argv_utf8 (info [1].As<String>());
    argv = *argv_utf8;
         //} //bjornw end
    Zargs *zargs = new Zargs ((int) argc, (char **)&argv);
    if (zargs) {
        zargs->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zargs::destroy) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    zargs_destroy (&zargs->self);
}


NAN_METHOD (Zargs::defined) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zargs->self != NULL));
}

NAN_METHOD (Zargs::_progname) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    char *result = (char *) zargs_progname (zargs->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zargs::_arguments) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    size_t result = zargs_arguments (zargs->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zargs::_first) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    char *result = (char *) zargs_first (zargs->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zargs::_next) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    char *result = (char *) zargs_next (zargs->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zargs::_param_first) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    char *result = (char *) zargs_param_first (zargs->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zargs::_param_next) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    char *result = (char *) zargs_param_next (zargs->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zargs::_param_name) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    char *result = (char *) zargs_param_name (zargs->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zargs::_get) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    char *name;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `name`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`name` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String name_utf8 (info [0].As<String>());
    name = *name_utf8;
         //} //bjornw end
    char *result = (char *) zargs_get (zargs->self, (const char *)name);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zargs::_getx) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    char *name;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `name`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`name` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String name_utf8 (info [0].As<String>());
    name = *name_utf8;
         //} //bjornw end
    char *result = (char *) zargs_getx (zargs->self, (const char *)name);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zargs::_has) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    char *name;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `name`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`name` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String name_utf8 (info [0].As<String>());
    name = *name_utf8;
         //} //bjornw end
    bool result = zargs_has (zargs->self, (const char *)name);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zargs::_hasx) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    char *name;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `name`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`name` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String name_utf8 (info [0].As<String>());
    name = *name_utf8;
         //} //bjornw end
    bool result = zargs_hasx (zargs->self, (const char *)name);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zargs::_print) {
    Zargs *zargs = Nan::ObjectWrap::Unwrap <Zargs> (info.Holder ());
    zargs_print (zargs->self);
}

NAN_METHOD (Zargs::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zargs_test ((bool) verbose);
}

Nan::Persistent <Function> &Zargs::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zarmour::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zarmour").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "encode", _encode);
    Nan::SetPrototypeMethod (tpl, "decode", _decode);
    Nan::SetPrototypeMethod (tpl, "mode", _mode);
    Nan::SetPrototypeMethod (tpl, "modeStr", _mode_str);
    Nan::SetPrototypeMethod (tpl, "setMode", _set_mode);
    Nan::SetPrototypeMethod (tpl, "pad", _pad);
    Nan::SetPrototypeMethod (tpl, "setPad", _set_pad);
    Nan::SetPrototypeMethod (tpl, "padChar", _pad_char);
    Nan::SetPrototypeMethod (tpl, "setPadChar", _set_pad_char);
    Nan::SetPrototypeMethod (tpl, "lineBreaks", _line_breaks);
    Nan::SetPrototypeMethod (tpl, "setLineBreaks", _set_line_breaks);
    Nan::SetPrototypeMethod (tpl, "lineLength", _line_length);
    Nan::SetPrototypeMethod (tpl, "setLineLength", _set_line_length);
    Nan::SetPrototypeMethod (tpl, "print", _print);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zarmour").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zarmour::Zarmour (void) {
    self = zarmour_new ();
}

Zarmour::Zarmour (zarmour_t *self_) {
    self = self_;
}

Zarmour::~Zarmour () {
}

NAN_METHOD (Zarmour::New) {
    assert (info.IsConstructCall ());
    Zarmour *zarmour = new Zarmour ();
    if (zarmour) {
        zarmour->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zarmour::destroy) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    zarmour_destroy (&zarmour->self);
}


NAN_METHOD (Zarmour::defined) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zarmour->self != NULL));
}

NAN_METHOD (Zarmour::_encode) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *data = (const byte *) node::Buffer::Data (buffer_node);
    size_t size = node::Buffer::Length (buffer_node);
    char *result = (char *) zarmour_encode (zarmour->self, (const byte *)data, (size_t) size);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zarmour::_decode) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    char *data;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `data`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`data` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String data_utf8 (info [0].As<String>());
    data = *data_utf8;
         //} //bjornw end
    zchunk_t *result = zarmour_decode (zarmour->self, (const char *)data);
    Zchunk *zchunk_result = new Zchunk (result);
    if (zchunk_result) {
    //  Don't yet know how to return a new object
    //      zchunk->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zarmour::_mode) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    int result = zarmour_mode (zarmour->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zarmour::_mode_str) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    char *result = (char *) zarmour_mode_str (zarmour->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zarmour::_set_mode) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `mode`");

    //int mode; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int mode;


    if (info [0]->IsNumber ())
    {
          mode = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`mode` must be a number");
    zarmour_set_mode (zarmour->self, (int) mode);
}

NAN_METHOD (Zarmour::_pad) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    bool result = zarmour_pad (zarmour->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zarmour::_set_pad) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `pad`");

    //bool pad; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool pad;


    if (info [0]->IsBoolean ())
    {
          pad = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`pad` must be a Boolean");
    zarmour_set_pad (zarmour->self, (bool) pad);
}

NAN_METHOD (Zarmour::_pad_char) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    char result [2] = " ";
    result [0] = zarmour_pad_char (zarmour->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zarmour::_set_pad_char) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    char pad_char;
    if (info [0]->IsUndefined ())
    {
        return Nan::ThrowTypeError ("method requires a `pad char`");
    }
    else if (!info [0]->IsString ())
    {
        return Nan::ThrowTypeError ("`pad char` must be a string");
    }
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String pad_char_utf8 (info [0].As<String>());

    if (strlen (*pad_char_utf8) != 1)
        return Nan::ThrowTypeError ("`pad char` must be a single character");
    pad_char = (*pad_char_utf8) [0];
    //} // bjornw end
    zarmour_set_pad_char (zarmour->self, (char) pad_char);
}

NAN_METHOD (Zarmour::_line_breaks) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    bool result = zarmour_line_breaks (zarmour->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zarmour::_set_line_breaks) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `line breaks`");

    //bool line_breaks; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool line_breaks;


    if (info [0]->IsBoolean ())
    {
          line_breaks = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`line breaks` must be a Boolean");
    zarmour_set_line_breaks (zarmour->self, (bool) line_breaks);
}

NAN_METHOD (Zarmour::_line_length) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    size_t result = zarmour_line_length (zarmour->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zarmour::_set_line_length) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `line length`");
    else
    if (!info [0]->IsNumber ())
        return Nan::ThrowTypeError ("`line length` must be a number");
    size_t line_length = Nan::To<int64_t>(info [0]).FromJust ();
    zarmour_set_line_length (zarmour->self, (size_t) line_length);
}

NAN_METHOD (Zarmour::_print) {
    Zarmour *zarmour = Nan::ObjectWrap::Unwrap <Zarmour> (info.Holder ());
    zarmour_print (zarmour->self);
}

NAN_METHOD (Zarmour::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zarmour_test ((bool) verbose);
}

Nan::Persistent <Function> &Zarmour::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zcert::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zcert").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "publicKey", _public_key);
    Nan::SetPrototypeMethod (tpl, "secretKey", _secret_key);
    Nan::SetPrototypeMethod (tpl, "publicTxt", _public_txt);
    Nan::SetPrototypeMethod (tpl, "secretTxt", _secret_txt);
    Nan::SetPrototypeMethod (tpl, "setMeta", _set_meta);
    Nan::SetPrototypeMethod (tpl, "unsetMeta", _unset_meta);
    Nan::SetPrototypeMethod (tpl, "meta", _meta);
    Nan::SetPrototypeMethod (tpl, "metaKeys", _meta_keys);
    Nan::SetPrototypeMethod (tpl, "save", _save);
    Nan::SetPrototypeMethod (tpl, "savePublic", _save_public);
    Nan::SetPrototypeMethod (tpl, "saveSecret", _save_secret);
    Nan::SetPrototypeMethod (tpl, "apply", _apply);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "eq", _eq);
    Nan::SetPrototypeMethod (tpl, "print", _print);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zcert").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zcert::Zcert (void) {
    self = zcert_new ();
}

Zcert::Zcert (zcert_t *self_) {
    self = self_;
}

Zcert::~Zcert () {
}

NAN_METHOD (Zcert::New) {
    assert (info.IsConstructCall ());
    Zcert *zcert = new Zcert ();
    if (zcert) {
        zcert->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zcert::destroy) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    zcert_destroy (&zcert->self);
}


NAN_METHOD (Zcert::defined) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zcert->self != NULL));
}

NAN_METHOD (Zcert::_public_key) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    const char *result = (const char *) zcert_public_key (zcert->self);
    info.GetReturnValue().Set (Nan::CopyBuffer (result, 32).ToLocalChecked ());
}

NAN_METHOD (Zcert::_secret_key) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    const char *result = (const char *) zcert_secret_key (zcert->self);
    info.GetReturnValue().Set (Nan::CopyBuffer (result, 32).ToLocalChecked ());
}

NAN_METHOD (Zcert::_public_txt) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    char *result = (char *) zcert_public_txt (zcert->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zcert::_secret_txt) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    char *result = (char *) zcert_secret_txt (zcert->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zcert::_set_meta) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    char *name;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `name`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`name` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String name_utf8 (info [0].As<String>());
    name = *name_utf8;
         //} //bjornw end
    char *format;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [1].As<String>());
    format = *format_utf8;
         //} //bjornw end
    zcert_set_meta (zcert->self, (const char *)name, "%s", format);
}

NAN_METHOD (Zcert::_unset_meta) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    char *name;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `name`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`name` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String name_utf8 (info [0].As<String>());
    name = *name_utf8;
         //} //bjornw end
    zcert_unset_meta (zcert->self, (const char *)name);
}

NAN_METHOD (Zcert::_meta) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    char *name;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `name`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`name` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String name_utf8 (info [0].As<String>());
    name = *name_utf8;
         //} //bjornw end
    char *result = (char *) zcert_meta (zcert->self, (const char *)name);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zcert::_meta_keys) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    zlist_t *result = zcert_meta_keys (zcert->self);
    Zlist *zlist_result = new Zlist (result);
    if (zlist_result) {
    //  Don't yet know how to return a new object
    //      zlist->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zcert::_save) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    int result = zcert_save (zcert->self, (const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zcert::_save_public) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    int result = zcert_save_public (zcert->self, (const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zcert::_save_secret) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    int result = zcert_save_secret (zcert->self, (const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zcert::_apply) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    Zsock *socket = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    zcert_apply (zcert->self, socket->self);
}

NAN_METHOD (Zcert::_dup) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    zcert_t *result = zcert_dup (zcert->self);
    Zcert *zcert_result = new Zcert (result);
    if (zcert_result) {
    //  Don't yet know how to return a new object
    //      zcert->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zcert::_eq) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    Zcert *compare = Nan::ObjectWrap::Unwrap<Zcert>(info [0].As<Object>());
    bool result = zcert_eq (zcert->self, compare->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zcert::_print) {
    Zcert *zcert = Nan::ObjectWrap::Unwrap <Zcert> (info.Holder ());
    zcert_print (zcert->self);
}

NAN_METHOD (Zcert::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zcert_test ((bool) verbose);
}

Nan::Persistent <Function> &Zcert::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zcertstore::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zcertstore").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "lookup", _lookup);
    Nan::SetPrototypeMethod (tpl, "insert", _insert);
    Nan::SetPrototypeMethod (tpl, "empty", _empty);
    Nan::SetPrototypeMethod (tpl, "print", _print);
    Nan::SetPrototypeMethod (tpl, "certs", _certs);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zcertstore").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zcertstore::Zcertstore (const char *location) {
    self = zcertstore_new ((const char *)location);
}

Zcertstore::Zcertstore (zcertstore_t *self_) {
    self = self_;
}

Zcertstore::~Zcertstore () {
}

NAN_METHOD (Zcertstore::New) {
    assert (info.IsConstructCall ());
    char *location;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `location`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`location` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String location_utf8 (info [0].As<String>());
    location = *location_utf8;
         //} //bjornw end
    Zcertstore *zcertstore = new Zcertstore ((const char *)location);
    if (zcertstore) {
        zcertstore->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zcertstore::destroy) {
    Zcertstore *zcertstore = Nan::ObjectWrap::Unwrap <Zcertstore> (info.Holder ());
    zcertstore_destroy (&zcertstore->self);
}


NAN_METHOD (Zcertstore::defined) {
    Zcertstore *zcertstore = Nan::ObjectWrap::Unwrap <Zcertstore> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zcertstore->self != NULL));
}

NAN_METHOD (Zcertstore::_lookup) {
    Zcertstore *zcertstore = Nan::ObjectWrap::Unwrap <Zcertstore> (info.Holder ());
    char *public_key;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `public key`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`public key` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String public_key_utf8 (info [0].As<String>());
    public_key = *public_key_utf8;
         //} //bjornw end
    zcert_t *result = zcertstore_lookup (zcertstore->self, (const char *)public_key);
    Zcert *zcert_result = new Zcert (result);
    if (zcert_result) {
    //  Don't yet know how to return a new object
    //      zcert->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zcertstore::_insert) {
    Zcertstore *zcertstore = Nan::ObjectWrap::Unwrap <Zcertstore> (info.Holder ());
    Zcert *cert_p = Nan::ObjectWrap::Unwrap<Zcert>(info [0].As<Object>());
    zcertstore_insert (zcertstore->self, &cert_p->self);
}

NAN_METHOD (Zcertstore::_empty) {
    Zcertstore *zcertstore = Nan::ObjectWrap::Unwrap <Zcertstore> (info.Holder ());
    zcertstore_empty (zcertstore->self);
}

NAN_METHOD (Zcertstore::_print) {
    Zcertstore *zcertstore = Nan::ObjectWrap::Unwrap <Zcertstore> (info.Holder ());
    zcertstore_print (zcertstore->self);
}

NAN_METHOD (Zcertstore::_certs) {
    Zcertstore *zcertstore = Nan::ObjectWrap::Unwrap <Zcertstore> (info.Holder ());
    zlistx_t *result = zcertstore_certs (zcertstore->self);
    Zlistx *zlistx_result = new Zlistx (result);
    if (zlistx_result) {
    //  Don't yet know how to return a new object
    //      zlistx->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zcertstore::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zcertstore_test ((bool) verbose);
}

Nan::Persistent <Function> &Zcertstore::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zchunk::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zchunk").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "resize", _resize);
    Nan::SetPrototypeMethod (tpl, "size", _size);
    Nan::SetPrototypeMethod (tpl, "maxSize", _max_size);
    Nan::SetPrototypeMethod (tpl, "data", _data);
    Nan::SetPrototypeMethod (tpl, "set", _set);
    Nan::SetPrototypeMethod (tpl, "append", _append);
    Nan::SetPrototypeMethod (tpl, "extend", _extend);
    Nan::SetPrototypeMethod (tpl, "consume", _consume);
    Nan::SetPrototypeMethod (tpl, "exhausted", _exhausted);
    Nan::SetPrototypeMethod (tpl, "slurp", _slurp);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "strhex", _strhex);
    Nan::SetPrototypeMethod (tpl, "strdup", _strdup);
    Nan::SetPrototypeMethod (tpl, "streq", _streq);
    Nan::SetPrototypeMethod (tpl, "pack", _pack);
    Nan::SetPrototypeMethod (tpl, "packx", _packx);
    Nan::SetPrototypeMethod (tpl, "unpack", _unpack);
    Nan::SetPrototypeMethod (tpl, "digest", _digest);
    Nan::SetPrototypeMethod (tpl, "print", _print);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zchunk").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zchunk::Zchunk (const void *data, size_t size) {
    self = zchunk_new ((const void *)data, (size_t) size);
}

Zchunk::Zchunk (zchunk_t *self_) {
    self = self_;
}

Zchunk::~Zchunk () {
}

NAN_METHOD (Zchunk::New) {
    assert (info.IsConstructCall ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *data = (const byte *) node::Buffer::Data (buffer_node);
    size_t size = node::Buffer::Length (buffer_node);
    Zchunk *zchunk = new Zchunk ((const void *)data, (size_t) size);
    if (zchunk) {
        zchunk->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zchunk::destroy) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    zchunk_destroy (&zchunk->self);
}


NAN_METHOD (Zchunk::defined) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zchunk->self != NULL));
}

NAN_METHOD (Zchunk::_resize) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `size`");
    else
    if (!info [0]->IsNumber ())
        return Nan::ThrowTypeError ("`size` must be a number");
    size_t size = Nan::To<int64_t>(info [0]).FromJust ();
    zchunk_resize (zchunk->self, (size_t) size);
}

NAN_METHOD (Zchunk::_size) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    size_t result = zchunk_size (zchunk->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zchunk::_max_size) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    size_t result = zchunk_max_size (zchunk->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zchunk::_data) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    const char *result = (const char *) zchunk_data (zchunk->self);
    info.GetReturnValue().Set (Nan::CopyBuffer (result, zchunk_size (zchunk->self)).ToLocalChecked ());
}

NAN_METHOD (Zchunk::_set) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *data = (const byte *) node::Buffer::Data (buffer_node);
    size_t size = node::Buffer::Length (buffer_node);
    size_t result = zchunk_set (zchunk->self, (const void *)data, (size_t) size);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zchunk::_append) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *data = (const byte *) node::Buffer::Data (buffer_node);
    size_t size = node::Buffer::Length (buffer_node);
    size_t result = zchunk_append (zchunk->self, (const void *)data, (size_t) size);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zchunk::_extend) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *data = (const byte *) node::Buffer::Data (buffer_node);
    size_t size = node::Buffer::Length (buffer_node);
    size_t result = zchunk_extend (zchunk->self, (const void *)data, (size_t) size);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zchunk::_consume) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    Zchunk *source = Nan::ObjectWrap::Unwrap<Zchunk>(info [0].As<Object>());
    size_t result = zchunk_consume (zchunk->self, source->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zchunk::_exhausted) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    bool result = zchunk_exhausted (zchunk->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zchunk::_slurp) {
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `maxsize`");
    else
    if (!info [1]->IsNumber ())
        return Nan::ThrowTypeError ("`maxsize` must be a number");
    size_t maxsize = Nan::To<int64_t>(info [1]).FromJust ();
    zchunk_t *result = zchunk_slurp ((const char *)filename, (size_t) maxsize);
    Zchunk *zchunk_result = new Zchunk (result);
    if (zchunk_result) {
    //  Don't yet know how to return a new object
    //      zchunk->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zchunk::_dup) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    zchunk_t *result = zchunk_dup (zchunk->self);
    Zchunk *zchunk_result = new Zchunk (result);
    if (zchunk_result) {
    //  Don't yet know how to return a new object
    //      zchunk->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zchunk::_strhex) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    char *result = (char *) zchunk_strhex (zchunk->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zchunk::_strdup) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    char *result = (char *) zchunk_strdup (zchunk->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zchunk::_streq) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    char *string;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `string`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`string` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String string_utf8 (info [0].As<String>());
    string = *string_utf8;
         //} //bjornw end
    bool result = zchunk_streq (zchunk->self, (const char *)string);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zchunk::_pack) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    zframe_t *result = zchunk_pack (zchunk->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zchunk::_packx) {
    Zchunk *self_p = Nan::ObjectWrap::Unwrap<Zchunk>(info [0].As<Object>());
    zframe_t *result = zchunk_packx (&self_p->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zchunk::_unpack) {
    Zframe *frame = Nan::ObjectWrap::Unwrap<Zframe>(info [0].As<Object>());
    zchunk_t *result = zchunk_unpack (frame->self);
    Zchunk *zchunk_result = new Zchunk (result);
    if (zchunk_result) {
    //  Don't yet know how to return a new object
    //      zchunk->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zchunk::_digest) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    char *result = (char *) zchunk_digest (zchunk->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zchunk::_print) {
    Zchunk *zchunk = Nan::ObjectWrap::Unwrap <Zchunk> (info.Holder ());
    zchunk_print (zchunk->self);
}

NAN_METHOD (Zchunk::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zchunk_test ((bool) verbose);
}

Nan::Persistent <Function> &Zchunk::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zclock::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zclock").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "sleep", _sleep);
    Nan::SetPrototypeMethod (tpl, "time", _time);
    Nan::SetPrototypeMethod (tpl, "mono", _mono);
    Nan::SetPrototypeMethod (tpl, "usecs", _usecs);
    Nan::SetPrototypeMethod (tpl, "timestr", _timestr);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zclock").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zclock::Zclock () {
}

Zclock::~Zclock () {
}

NAN_METHOD (Zclock::New) {
    assert (info.IsConstructCall ());
    Zclock *zclock = new Zclock ();
    if (zclock) {
        zclock->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zclock::_sleep) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `msecs`");

    //int msecs; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int msecs;


    if (info [0]->IsNumber ())
    {
          msecs = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`msecs` must be a number");
    zclock_sleep ((int) msecs);
}

NAN_METHOD (Zclock::_time) {
    int64_t result = zclock_time ();
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zclock::_mono) {
    int64_t result = zclock_mono ();
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zclock::_usecs) {
    int64_t result = zclock_usecs ();
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zclock::_timestr) {
    char *result = (char *) zclock_timestr ();
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zclock::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zclock_test ((bool) verbose);
}

Nan::Persistent <Function> &Zclock::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zconfig::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zconfig").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "name", _name);
    Nan::SetPrototypeMethod (tpl, "value", _value);
    Nan::SetPrototypeMethod (tpl, "put", _put);
    Nan::SetPrototypeMethod (tpl, "putf", _putf);
    Nan::SetPrototypeMethod (tpl, "get", _get);
    Nan::SetPrototypeMethod (tpl, "setName", _set_name);
    Nan::SetPrototypeMethod (tpl, "setValue", _set_value);
    Nan::SetPrototypeMethod (tpl, "child", _child);
    Nan::SetPrototypeMethod (tpl, "next", _next);
    Nan::SetPrototypeMethod (tpl, "locate", _locate);
    Nan::SetPrototypeMethod (tpl, "atDepth", _at_depth);
    Nan::SetPrototypeMethod (tpl, "setComment", _set_comment);
    Nan::SetPrototypeMethod (tpl, "comments", _comments);
    Nan::SetPrototypeMethod (tpl, "save", _save);
    Nan::SetPrototypeMethod (tpl, "savef", _savef);
    Nan::SetPrototypeMethod (tpl, "filename", _filename);
    Nan::SetPrototypeMethod (tpl, "reload", _reload);
    Nan::SetPrototypeMethod (tpl, "chunkLoad", _chunk_load);
    Nan::SetPrototypeMethod (tpl, "chunkSave", _chunk_save);
    Nan::SetPrototypeMethod (tpl, "strLoad", _str_load);
    Nan::SetPrototypeMethod (tpl, "strSave", _str_save);
    Nan::SetPrototypeMethod (tpl, "hasChanged", _has_changed);
    Nan::SetPrototypeMethod (tpl, "removeSubtree", _remove_subtree);
    Nan::SetPrototypeMethod (tpl, "remove", _remove);
    Nan::SetPrototypeMethod (tpl, "print", _print);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zconfig").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zconfig::Zconfig (const char *name, zconfig_t *parent) {
    self = zconfig_new ((const char *)name, parent);
}

Zconfig::Zconfig (zconfig_t *self_) {
    self = self_;
}

Zconfig::~Zconfig () {
}

NAN_METHOD (Zconfig::New) {
    assert (info.IsConstructCall ());
    char *name;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `name`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`name` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String name_utf8 (info [0].As<String>());
    name = *name_utf8;
         //} //bjornw end
    Zconfig *parent = Nan::ObjectWrap::Unwrap<Zconfig>(info [1].As<Object>());
    Zconfig *zconfig = new Zconfig ((const char *)name, parent->self);
    if (zconfig) {
        zconfig->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zconfig::destroy) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    zconfig_destroy (&zconfig->self);
}


NAN_METHOD (Zconfig::defined) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zconfig->self != NULL));
}

NAN_METHOD (Zconfig::_dup) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    zconfig_t *result = zconfig_dup (zconfig->self);
    Zconfig *zconfig_result = new Zconfig (result);
    if (zconfig_result) {
    //  Don't yet know how to return a new object
    //      zconfig->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zconfig::_name) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *result = (char *) zconfig_name (zconfig->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zconfig::_value) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *result = (char *) zconfig_value (zconfig->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zconfig::_put) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *path;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `path`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`path` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String path_utf8 (info [0].As<String>());
    path = *path_utf8;
         //} //bjornw end
    char *value;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `value`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`value` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String value_utf8 (info [1].As<String>());
    value = *value_utf8;
         //} //bjornw end
    zconfig_put (zconfig->self, (const char *)path, (const char *)value);
}

NAN_METHOD (Zconfig::_putf) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *path;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `path`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`path` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String path_utf8 (info [0].As<String>());
    path = *path_utf8;
         //} //bjornw end
    char *format;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [1].As<String>());
    format = *format_utf8;
         //} //bjornw end
    zconfig_putf (zconfig->self, (const char *)path, "%s", format);
}

NAN_METHOD (Zconfig::_get) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *path;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `path`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`path` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String path_utf8 (info [0].As<String>());
    path = *path_utf8;
         //} //bjornw end
    char *default_value;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `default value`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`default value` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String default_value_utf8 (info [1].As<String>());
    default_value = *default_value_utf8;
         //} //bjornw end
    char *result = (char *) zconfig_get (zconfig->self, (const char *)path, (const char *)default_value);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zconfig::_set_name) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *name;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `name`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`name` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String name_utf8 (info [0].As<String>());
    name = *name_utf8;
         //} //bjornw end
    zconfig_set_name (zconfig->self, (const char *)name);
}

NAN_METHOD (Zconfig::_set_value) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    zconfig_set_value (zconfig->self, "%s", format);
}

NAN_METHOD (Zconfig::_child) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    zconfig_t *result = zconfig_child (zconfig->self);
    Zconfig *zconfig_result = new Zconfig (result);
    if (zconfig_result) {
    //  Don't yet know how to return a new object
    //      zconfig->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zconfig::_next) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    zconfig_t *result = zconfig_next (zconfig->self);
    Zconfig *zconfig_result = new Zconfig (result);
    if (zconfig_result) {
    //  Don't yet know how to return a new object
    //      zconfig->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zconfig::_locate) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *path;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `path`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`path` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String path_utf8 (info [0].As<String>());
    path = *path_utf8;
         //} //bjornw end
    zconfig_t *result = zconfig_locate (zconfig->self, (const char *)path);
    Zconfig *zconfig_result = new Zconfig (result);
    if (zconfig_result) {
    //  Don't yet know how to return a new object
    //      zconfig->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zconfig::_at_depth) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `level`");

    //int level; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int level;


    if (info [0]->IsNumber ())
    {
          level = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`level` must be a number");
    zconfig_t *result = zconfig_at_depth (zconfig->self, (int) level);
    Zconfig *zconfig_result = new Zconfig (result);
    if (zconfig_result) {
    //  Don't yet know how to return a new object
    //      zconfig->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zconfig::_set_comment) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    zconfig_set_comment (zconfig->self, "%s", format);
}

NAN_METHOD (Zconfig::_comments) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    zlist_t *result = zconfig_comments (zconfig->self);
    Zlist *zlist_result = new Zlist (result);
    if (zlist_result) {
    //  Don't yet know how to return a new object
    //      zlist->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zconfig::_save) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    int result = zconfig_save (zconfig->self, (const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zconfig::_savef) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    int result = zconfig_savef (zconfig->self, "%s", format);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zconfig::_filename) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *result = (char *) zconfig_filename (zconfig->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zconfig::_reload) {
    Zconfig *self_p = Nan::ObjectWrap::Unwrap<Zconfig>(info [0].As<Object>());
    int result = zconfig_reload (&self_p->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zconfig::_chunk_load) {
    Zchunk *chunk = Nan::ObjectWrap::Unwrap<Zchunk>(info [0].As<Object>());
    zconfig_t *result = zconfig_chunk_load (chunk->self);
    Zconfig *zconfig_result = new Zconfig (result);
    if (zconfig_result) {
    //  Don't yet know how to return a new object
    //      zconfig->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zconfig::_chunk_save) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    zchunk_t *result = zconfig_chunk_save (zconfig->self);
    Zchunk *zchunk_result = new Zchunk (result);
    if (zchunk_result) {
    //  Don't yet know how to return a new object
    //      zchunk->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zconfig::_str_load) {
    char *string;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `string`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`string` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String string_utf8 (info [0].As<String>());
    string = *string_utf8;
         //} //bjornw end
    zconfig_t *result = zconfig_str_load ((const char *)string);
    Zconfig *zconfig_result = new Zconfig (result);
    if (zconfig_result) {
    //  Don't yet know how to return a new object
    //      zconfig->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zconfig::_str_save) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    char *result = (char *) zconfig_str_save (zconfig->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zconfig::_has_changed) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    bool result = zconfig_has_changed (zconfig->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zconfig::_remove_subtree) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    zconfig_remove_subtree (zconfig->self);
}

NAN_METHOD (Zconfig::_remove) {
    Zconfig *self_p = Nan::ObjectWrap::Unwrap<Zconfig>(info [0].As<Object>());
    zconfig_remove (&self_p->self);
}

NAN_METHOD (Zconfig::_print) {
    Zconfig *zconfig = Nan::ObjectWrap::Unwrap <Zconfig> (info.Holder ());
    zconfig_print (zconfig->self);
}

NAN_METHOD (Zconfig::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zconfig_test ((bool) verbose);
}

Nan::Persistent <Function> &Zconfig::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zdigest::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zdigest").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "update", _update);
    Nan::SetPrototypeMethod (tpl, "data", _data);
    Nan::SetPrototypeMethod (tpl, "size", _size);
    Nan::SetPrototypeMethod (tpl, "string", _string);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zdigest").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zdigest::Zdigest (void) {
    self = zdigest_new ();
}

Zdigest::Zdigest (zdigest_t *self_) {
    self = self_;
}

Zdigest::~Zdigest () {
}

NAN_METHOD (Zdigest::New) {
    assert (info.IsConstructCall ());
    Zdigest *zdigest = new Zdigest ();
    if (zdigest) {
        zdigest->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zdigest::destroy) {
    Zdigest *zdigest = Nan::ObjectWrap::Unwrap <Zdigest> (info.Holder ());
    zdigest_destroy (&zdigest->self);
}


NAN_METHOD (Zdigest::defined) {
    Zdigest *zdigest = Nan::ObjectWrap::Unwrap <Zdigest> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zdigest->self != NULL));
}

NAN_METHOD (Zdigest::_update) {
    Zdigest *zdigest = Nan::ObjectWrap::Unwrap <Zdigest> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *buffer = (const byte *) node::Buffer::Data (buffer_node);
    size_t length = node::Buffer::Length (buffer_node);
    zdigest_update (zdigest->self, (const byte *)buffer, (size_t) length);
}

NAN_METHOD (Zdigest::_data) {
    Zdigest *zdigest = Nan::ObjectWrap::Unwrap <Zdigest> (info.Holder ());
    const char *result = (const char *) zdigest_data (zdigest->self);
    info.GetReturnValue().Set (Nan::CopyBuffer (result, zdigest_size (zdigest->self)).ToLocalChecked ());
}

NAN_METHOD (Zdigest::_size) {
    Zdigest *zdigest = Nan::ObjectWrap::Unwrap <Zdigest> (info.Holder ());
    size_t result = zdigest_size (zdigest->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zdigest::_string) {
    Zdigest *zdigest = Nan::ObjectWrap::Unwrap <Zdigest> (info.Holder ());
    char *result = (char *) zdigest_string (zdigest->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zdigest::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zdigest_test ((bool) verbose);
}

Nan::Persistent <Function> &Zdigest::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zdir::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zdir").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "path", _path);
    Nan::SetPrototypeMethod (tpl, "modified", _modified);
    Nan::SetPrototypeMethod (tpl, "cursize", _cursize);
    Nan::SetPrototypeMethod (tpl, "count", _count);
    Nan::SetPrototypeMethod (tpl, "list", _list);
    Nan::SetPrototypeMethod (tpl, "listPaths", _list_paths);
    Nan::SetPrototypeMethod (tpl, "remove", _remove);
    Nan::SetPrototypeMethod (tpl, "diff", _diff);
    Nan::SetPrototypeMethod (tpl, "resync", _resync);
    Nan::SetPrototypeMethod (tpl, "cache", _cache);
    Nan::SetPrototypeMethod (tpl, "print", _print);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zdir").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zdir::Zdir (const char *path, const char *parent) {
    self = zdir_new ((const char *)path, (const char *)parent);
}

Zdir::Zdir (zdir_t *self_) {
    self = self_;
}

Zdir::~Zdir () {
}

NAN_METHOD (Zdir::New) {
    assert (info.IsConstructCall ());
    char *path;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `path`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`path` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String path_utf8 (info [0].As<String>());
    path = *path_utf8;
         //} //bjornw end
    char *parent;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `parent`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`parent` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String parent_utf8 (info [1].As<String>());
    parent = *parent_utf8;
         //} //bjornw end
    Zdir *zdir = new Zdir ((const char *)path, (const char *)parent);
    if (zdir) {
        zdir->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zdir::destroy) {
    Zdir *zdir = Nan::ObjectWrap::Unwrap <Zdir> (info.Holder ());
    zdir_destroy (&zdir->self);
}


NAN_METHOD (Zdir::defined) {
    Zdir *zdir = Nan::ObjectWrap::Unwrap <Zdir> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zdir->self != NULL));
}

NAN_METHOD (Zdir::_path) {
    Zdir *zdir = Nan::ObjectWrap::Unwrap <Zdir> (info.Holder ());
    char *result = (char *) zdir_path (zdir->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zdir::_modified) {
    Zdir *zdir = Nan::ObjectWrap::Unwrap <Zdir> (info.Holder ());
    time_t result = zdir_modified (zdir->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zdir::_cursize) {
    Zdir *zdir = Nan::ObjectWrap::Unwrap <Zdir> (info.Holder ());
    off_t result = zdir_cursize (zdir->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zdir::_count) {
    Zdir *zdir = Nan::ObjectWrap::Unwrap <Zdir> (info.Holder ());
    size_t result = zdir_count (zdir->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zdir::_list) {
    Zdir *zdir = Nan::ObjectWrap::Unwrap <Zdir> (info.Holder ());
    zlist_t *result = zdir_list (zdir->self);
    Zlist *zlist_result = new Zlist (result);
    if (zlist_result) {
    //  Don't yet know how to return a new object
    //      zlist->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zdir::_list_paths) {
    Zdir *zdir = Nan::ObjectWrap::Unwrap <Zdir> (info.Holder ());
    zlist_t *result = zdir_list_paths (zdir->self);
    Zlist *zlist_result = new Zlist (result);
    if (zlist_result) {
    //  Don't yet know how to return a new object
    //      zlist->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zdir::_remove) {
    Zdir *zdir = Nan::ObjectWrap::Unwrap <Zdir> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `force`");

    //bool force; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool force;


    if (info [0]->IsBoolean ())
    {
          force = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`force` must be a Boolean");
    zdir_remove (zdir->self, (bool) force);
}

NAN_METHOD (Zdir::_diff) {
    Zdir *older = Nan::ObjectWrap::Unwrap<Zdir>(info [0].As<Object>());
    Zdir *newer = Nan::ObjectWrap::Unwrap<Zdir>(info [1].As<Object>());
    char *alias;
    if (info [2]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `alias`");
    else
    if (!info [2]->IsString ())
        return Nan::ThrowTypeError ("`alias` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String alias_utf8 (info [2].As<String>());
    alias = *alias_utf8;
         //} //bjornw end
    zlist_t *result = zdir_diff (older->self, newer->self, (const char *)alias);
    Zlist *zlist_result = new Zlist (result);
    if (zlist_result) {
    //  Don't yet know how to return a new object
    //      zlist->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zdir::_resync) {
    Zdir *zdir = Nan::ObjectWrap::Unwrap <Zdir> (info.Holder ());
    char *alias;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `alias`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`alias` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String alias_utf8 (info [0].As<String>());
    alias = *alias_utf8;
         //} //bjornw end
    zlist_t *result = zdir_resync (zdir->self, (const char *)alias);
    Zlist *zlist_result = new Zlist (result);
    if (zlist_result) {
    //  Don't yet know how to return a new object
    //      zlist->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zdir::_cache) {
    Zdir *zdir = Nan::ObjectWrap::Unwrap <Zdir> (info.Holder ());
    zhash_t *result = zdir_cache (zdir->self);
    Zhash *zhash_result = new Zhash (result);
    if (zhash_result) {
    //  Don't yet know how to return a new object
    //      zhash->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zdir::_print) {
    Zdir *zdir = Nan::ObjectWrap::Unwrap <Zdir> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `indent`");

    //int indent; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int indent;


    if (info [0]->IsNumber ())
    {
          indent = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`indent` must be a number");
    zdir_print (zdir->self, (int) indent);
}

NAN_METHOD (Zdir::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zdir_test ((bool) verbose);
}

Nan::Persistent <Function> &Zdir::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (ZdirPatch::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("ZdirPatch").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "path", _path);
    Nan::SetPrototypeMethod (tpl, "file", _file);
    Nan::SetPrototypeMethod (tpl, "op", _op);
    Nan::SetPrototypeMethod (tpl, "vpath", _vpath);
    Nan::SetPrototypeMethod (tpl, "digestSet", _digest_set);
    Nan::SetPrototypeMethod (tpl, "digest", _digest);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("ZdirPatch").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

ZdirPatch::ZdirPatch (const char *path, zfile_t *file, int op, const char *alias) {
    self = zdir_patch_new ((const char *)path, file, (int) op, (const char *)alias);
}

ZdirPatch::ZdirPatch (zdir_patch_t *self_) {
    self = self_;
}

ZdirPatch::~ZdirPatch () {
}

NAN_METHOD (ZdirPatch::New) {
    assert (info.IsConstructCall ());
    char *path;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `path`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`path` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String path_utf8 (info [0].As<String>());
    path = *path_utf8;
         //} //bjornw end
    Zfile *file = Nan::ObjectWrap::Unwrap<Zfile>(info [1].As<Object>());
    if (info [2]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `op`");

    //int op; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int op;


    if (info [2]->IsNumber ())
    {
          op = Nan::To<int>(info [2]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`op` must be a number");
    char *alias;
    if (info [3]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `alias`");
    else
    if (!info [3]->IsString ())
        return Nan::ThrowTypeError ("`alias` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String alias_utf8 (info [3].As<String>());
    alias = *alias_utf8;
         //} //bjornw end
    ZdirPatch *zdir_patch = new ZdirPatch ((const char *)path, file->self, (int) op, (const char *)alias);
    if (zdir_patch) {
        zdir_patch->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (ZdirPatch::destroy) {
    ZdirPatch *zdir_patch = Nan::ObjectWrap::Unwrap <ZdirPatch> (info.Holder ());
    zdir_patch_destroy (&zdir_patch->self);
}


NAN_METHOD (ZdirPatch::defined) {
    ZdirPatch *zdir_patch = Nan::ObjectWrap::Unwrap <ZdirPatch> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zdir_patch->self != NULL));
}

NAN_METHOD (ZdirPatch::_dup) {
    ZdirPatch *zdir_patch = Nan::ObjectWrap::Unwrap <ZdirPatch> (info.Holder ());
    zdir_patch_t *result = zdir_patch_dup (zdir_patch->self);
    ZdirPatch *zdir_patch_result = new ZdirPatch (result);
    if (zdir_patch_result) {
    //  Don't yet know how to return a new object
    //      zdir_patch->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (ZdirPatch::_path) {
    ZdirPatch *zdir_patch = Nan::ObjectWrap::Unwrap <ZdirPatch> (info.Holder ());
    char *result = (char *) zdir_patch_path (zdir_patch->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (ZdirPatch::_file) {
    ZdirPatch *zdir_patch = Nan::ObjectWrap::Unwrap <ZdirPatch> (info.Holder ());
    zfile_t *result = zdir_patch_file (zdir_patch->self);
    Zfile *zfile_result = new Zfile (result);
    if (zfile_result) {
    //  Don't yet know how to return a new object
    //      zfile->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (ZdirPatch::_op) {
    ZdirPatch *zdir_patch = Nan::ObjectWrap::Unwrap <ZdirPatch> (info.Holder ());
    int result = zdir_patch_op (zdir_patch->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (ZdirPatch::_vpath) {
    ZdirPatch *zdir_patch = Nan::ObjectWrap::Unwrap <ZdirPatch> (info.Holder ());
    char *result = (char *) zdir_patch_vpath (zdir_patch->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (ZdirPatch::_digest_set) {
    ZdirPatch *zdir_patch = Nan::ObjectWrap::Unwrap <ZdirPatch> (info.Holder ());
    zdir_patch_digest_set (zdir_patch->self);
}

NAN_METHOD (ZdirPatch::_digest) {
    ZdirPatch *zdir_patch = Nan::ObjectWrap::Unwrap <ZdirPatch> (info.Holder ());
    char *result = (char *) zdir_patch_digest (zdir_patch->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (ZdirPatch::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zdir_patch_test ((bool) verbose);
}

Nan::Persistent <Function> &ZdirPatch::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zfile::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zfile").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "filename", _filename);
    Nan::SetPrototypeMethod (tpl, "restat", _restat);
    Nan::SetPrototypeMethod (tpl, "modified", _modified);
    Nan::SetPrototypeMethod (tpl, "cursize", _cursize);
    Nan::SetPrototypeMethod (tpl, "isDirectory", _is_directory);
    Nan::SetPrototypeMethod (tpl, "isRegular", _is_regular);
    Nan::SetPrototypeMethod (tpl, "isReadable", _is_readable);
    Nan::SetPrototypeMethod (tpl, "isWriteable", _is_writeable);
    Nan::SetPrototypeMethod (tpl, "isStable", _is_stable);
    Nan::SetPrototypeMethod (tpl, "hasChanged", _has_changed);
    Nan::SetPrototypeMethod (tpl, "remove", _remove);
    Nan::SetPrototypeMethod (tpl, "input", _input);
    Nan::SetPrototypeMethod (tpl, "output", _output);
    Nan::SetPrototypeMethod (tpl, "read", _read);
    Nan::SetPrototypeMethod (tpl, "eof", _eof);
    Nan::SetPrototypeMethod (tpl, "write", _write);
    Nan::SetPrototypeMethod (tpl, "readln", _readln);
    Nan::SetPrototypeMethod (tpl, "close", _close);
    Nan::SetPrototypeMethod (tpl, "digest", _digest);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zfile").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zfile::Zfile (const char *path, const char *name) {
    self = zfile_new ((const char *)path, (const char *)name);
}

Zfile::Zfile (zfile_t *self_) {
    self = self_;
}

Zfile::~Zfile () {
}

NAN_METHOD (Zfile::New) {
    assert (info.IsConstructCall ());
    char *path;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `path`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`path` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String path_utf8 (info [0].As<String>());
    path = *path_utf8;
         //} //bjornw end
    char *name;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `name`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`name` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String name_utf8 (info [1].As<String>());
    name = *name_utf8;
         //} //bjornw end
    Zfile *zfile = new Zfile ((const char *)path, (const char *)name);
    if (zfile) {
        zfile->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zfile::destroy) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    zfile_destroy (&zfile->self);
}


NAN_METHOD (Zfile::defined) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zfile->self != NULL));
}

NAN_METHOD (Zfile::_dup) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    zfile_t *result = zfile_dup (zfile->self);
    Zfile *zfile_result = new Zfile (result);
    if (zfile_result) {
    //  Don't yet know how to return a new object
    //      zfile->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zfile::_filename) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    char *path;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `path`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`path` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String path_utf8 (info [0].As<String>());
    path = *path_utf8;
         //} //bjornw end
    char *result = (char *) zfile_filename (zfile->self, (const char *)path);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zfile::_restat) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    zfile_restat (zfile->self);
}

NAN_METHOD (Zfile::_modified) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    time_t result = zfile_modified (zfile->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zfile::_cursize) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    off_t result = zfile_cursize (zfile->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zfile::_is_directory) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    bool result = zfile_is_directory (zfile->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zfile::_is_regular) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    bool result = zfile_is_regular (zfile->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zfile::_is_readable) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    bool result = zfile_is_readable (zfile->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zfile::_is_writeable) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    bool result = zfile_is_writeable (zfile->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zfile::_is_stable) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    bool result = zfile_is_stable (zfile->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zfile::_has_changed) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    bool result = zfile_has_changed (zfile->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zfile::_remove) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    zfile_remove (zfile->self);
}

NAN_METHOD (Zfile::_input) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    int result = zfile_input (zfile->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zfile::_output) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    int result = zfile_output (zfile->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zfile::_read) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `bytes`");
    else
    if (!info [0]->IsNumber ())
        return Nan::ThrowTypeError ("`bytes` must be a number");
    size_t bytes = Nan::To<int64_t>(info [0]).FromJust ();
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `offset`");

    //off_t offset; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int64_t offset;


    if (info [1]->IsNumber ())
    {
          offset = Nan::To<int64_t>(info [1]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`offset` must be a number");
    zchunk_t *result = zfile_read (zfile->self, (size_t) bytes, (off_t) offset);
    Zchunk *zchunk_result = new Zchunk (result);
    if (zchunk_result) {
    //  Don't yet know how to return a new object
    //      zchunk->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zfile::_eof) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    bool result = zfile_eof (zfile->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zfile::_write) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    Zchunk *chunk = Nan::ObjectWrap::Unwrap<Zchunk>(info [0].As<Object>());
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `offset`");

    //off_t offset; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int64_t offset;


    if (info [1]->IsNumber ())
    {
          offset = Nan::To<int64_t>(info [1]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`offset` must be a number");
    int result = zfile_write (zfile->self, chunk->self, (off_t) offset);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zfile::_readln) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    char *result = (char *) zfile_readln (zfile->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zfile::_close) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    zfile_close (zfile->self);
}

NAN_METHOD (Zfile::_digest) {
    Zfile *zfile = Nan::ObjectWrap::Unwrap <Zfile> (info.Holder ());
    char *result = (char *) zfile_digest (zfile->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zfile::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zfile_test ((bool) verbose);
}

Nan::Persistent <Function> &Zfile::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zframe::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zframe").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "send", _send);
    Nan::SetPrototypeMethod (tpl, "size", _size);
    Nan::SetPrototypeMethod (tpl, "data", _data);
    Nan::SetPrototypeMethod (tpl, "meta", _meta);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "strhex", _strhex);
    Nan::SetPrototypeMethod (tpl, "strdup", _strdup);
    Nan::SetPrototypeMethod (tpl, "streq", _streq);
    Nan::SetPrototypeMethod (tpl, "more", _more);
    Nan::SetPrototypeMethod (tpl, "setMore", _set_more);
    Nan::SetPrototypeMethod (tpl, "routingId", _routing_id);
    Nan::SetPrototypeMethod (tpl, "setRoutingId", _set_routing_id);
    Nan::SetPrototypeMethod (tpl, "group", _group);
    Nan::SetPrototypeMethod (tpl, "setGroup", _set_group);
    Nan::SetPrototypeMethod (tpl, "eq", _eq);
    Nan::SetPrototypeMethod (tpl, "reset", _reset);
    Nan::SetPrototypeMethod (tpl, "print", _print);
    Nan::SetPrototypeMethod (tpl, "printN", _print_n);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zframe").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zframe::Zframe (const void *data, size_t size) {
    self = zframe_new ((const void *)data, (size_t) size);
}

Zframe::Zframe (zframe_t *self_) {
    self = self_;
}

Zframe::~Zframe () {
}

NAN_METHOD (Zframe::New) {
    assert (info.IsConstructCall ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *data = (const byte *) node::Buffer::Data (buffer_node);
    size_t size = node::Buffer::Length (buffer_node);
    Zframe *zframe = new Zframe ((const void *)data, (size_t) size);
    if (zframe) {
        zframe->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zframe::destroy) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    zframe_destroy (&zframe->self);
}


NAN_METHOD (Zframe::defined) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zframe->self != NULL));
}

NAN_METHOD (Zframe::_send) {
    Zframe *self_p = Nan::ObjectWrap::Unwrap<Zframe>(info [0].As<Object>());
    Zsock *dest = Nan::ObjectWrap::Unwrap<Zsock>(info [1].As<Object>());
    if (info [2]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `flags`");

    //int flags; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int flags;


    if (info [2]->IsNumber ())
    {
          flags = Nan::To<int>(info [2]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`flags` must be a number");
    int result = zframe_send (&self_p->self, dest->self, (int) flags);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zframe::_size) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    size_t result = zframe_size (zframe->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zframe::_data) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    const char *result = (const char *) zframe_data (zframe->self);
    info.GetReturnValue().Set (Nan::CopyBuffer (result, zframe_size (zframe->self)).ToLocalChecked ());
}

NAN_METHOD (Zframe::_meta) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    char *property;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `property`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`property` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String property_utf8 (info [0].As<String>());
    property = *property_utf8;
         //} //bjornw end
    char *result = (char *) zframe_meta (zframe->self, (const char *)property);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zframe::_dup) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    zframe_t *result = zframe_dup (zframe->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zframe::_strhex) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    char *result = (char *) zframe_strhex (zframe->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zframe::_strdup) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    char *result = (char *) zframe_strdup (zframe->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zframe::_streq) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    char *string;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `string`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`string` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String string_utf8 (info [0].As<String>());
    string = *string_utf8;
         //} //bjornw end
    bool result = zframe_streq (zframe->self, (const char *)string);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zframe::_more) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    int result = zframe_more (zframe->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zframe::_set_more) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `more`");

    //int more; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int more;


    if (info [0]->IsNumber ())
    {
          more = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`more` must be a number");
    zframe_set_more (zframe->self, (int) more);
}

NAN_METHOD (Zframe::_routing_id) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    uint32_t result = zframe_routing_id (zframe->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zframe::_set_routing_id) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `routing id`");

    //uint32_t routing_id; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    uint32_t routing_id;


    if (info [0]->IsNumber ())
    {
          routing_id = Nan::To<uint32_t>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`routing id` must be a number");
    zframe_set_routing_id (zframe->self, (uint32_t) routing_id);
}

NAN_METHOD (Zframe::_group) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    char *result = (char *) zframe_group (zframe->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zframe::_set_group) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    char *group;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `group`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`group` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String group_utf8 (info [0].As<String>());
    group = *group_utf8;
         //} //bjornw end
    int result = zframe_set_group (zframe->self, (const char *)group);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zframe::_eq) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    Zframe *other = Nan::ObjectWrap::Unwrap<Zframe>(info [0].As<Object>());
    bool result = zframe_eq (zframe->self, other->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zframe::_reset) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *data = (const byte *) node::Buffer::Data (buffer_node);
    size_t size = node::Buffer::Length (buffer_node);
    zframe_reset (zframe->self, (const void *)data, (size_t) size);
}

NAN_METHOD (Zframe::_print) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    char *prefix;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `prefix`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`prefix` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String prefix_utf8 (info [0].As<String>());
    prefix = *prefix_utf8;
         //} //bjornw end
    zframe_print (zframe->self, (const char *)prefix);
}

NAN_METHOD (Zframe::_print_n) {
    Zframe *zframe = Nan::ObjectWrap::Unwrap <Zframe> (info.Holder ());
    char *prefix;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `prefix`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`prefix` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String prefix_utf8 (info [0].As<String>());
    prefix = *prefix_utf8;
         //} //bjornw end
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `length`");
    else
    if (!info [1]->IsNumber ())
        return Nan::ThrowTypeError ("`length` must be a number");
    size_t length = Nan::To<int64_t>(info [1]).FromJust ();
    zframe_print_n (zframe->self, (const char *)prefix, (size_t) length);
}

NAN_METHOD (Zframe::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zframe_test ((bool) verbose);
}

Nan::Persistent <Function> &Zframe::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zhash::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zhash").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "delete", _delete);
    Nan::SetPrototypeMethod (tpl, "rename", _rename);
    Nan::SetPrototypeMethod (tpl, "size", _size);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "keys", _keys);
    Nan::SetPrototypeMethod (tpl, "cursor", _cursor);
    Nan::SetPrototypeMethod (tpl, "comment", _comment);
    Nan::SetPrototypeMethod (tpl, "pack", _pack);
    Nan::SetPrototypeMethod (tpl, "save", _save);
    Nan::SetPrototypeMethod (tpl, "load", _load);
    Nan::SetPrototypeMethod (tpl, "refresh", _refresh);
    Nan::SetPrototypeMethod (tpl, "autofree", _autofree);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zhash").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zhash::Zhash (void) {
    self = zhash_new ();
}

Zhash::Zhash (zhash_t *self_) {
    self = self_;
}

Zhash::~Zhash () {
}

NAN_METHOD (Zhash::New) {
    assert (info.IsConstructCall ());
    Zhash *zhash = new Zhash ();
    if (zhash) {
        zhash->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zhash::destroy) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    zhash_destroy (&zhash->self);
}


NAN_METHOD (Zhash::defined) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zhash->self != NULL));
}

NAN_METHOD (Zhash::_delete) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    char *key;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `key`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`key` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String key_utf8 (info [0].As<String>());
    key = *key_utf8;
         //} //bjornw end
    zhash_delete (zhash->self, (const char *)key);
}

NAN_METHOD (Zhash::_rename) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    char *old_key;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `old key`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`old key` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String old_key_utf8 (info [0].As<String>());
    old_key = *old_key_utf8;
         //} //bjornw end
    char *new_key;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `new key`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`new key` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String new_key_utf8 (info [1].As<String>());
    new_key = *new_key_utf8;
         //} //bjornw end
    int result = zhash_rename (zhash->self, (const char *)old_key, (const char *)new_key);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zhash::_size) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    size_t result = zhash_size (zhash->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zhash::_dup) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    zhash_t *result = zhash_dup (zhash->self);
    Zhash *zhash_result = new Zhash (result);
    if (zhash_result) {
    //  Don't yet know how to return a new object
    //      zhash->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zhash::_keys) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    zlist_t *result = zhash_keys (zhash->self);
    Zlist *zlist_result = new Zlist (result);
    if (zlist_result) {
    //  Don't yet know how to return a new object
    //      zlist->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zhash::_cursor) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    char *result = (char *) zhash_cursor (zhash->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zhash::_comment) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    zhash_comment (zhash->self, "%s", format);
}

NAN_METHOD (Zhash::_pack) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    zframe_t *result = zhash_pack (zhash->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zhash::_save) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    int result = zhash_save (zhash->self, (const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zhash::_load) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    int result = zhash_load (zhash->self, (const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zhash::_refresh) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    int result = zhash_refresh (zhash->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zhash::_autofree) {
    Zhash *zhash = Nan::ObjectWrap::Unwrap <Zhash> (info.Holder ());
    zhash_autofree (zhash->self);
}

NAN_METHOD (Zhash::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zhash_test ((bool) verbose);
}

Nan::Persistent <Function> &Zhash::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zhashx::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zhashx").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "purge", _purge);
    Nan::SetPrototypeMethod (tpl, "size", _size);
    Nan::SetPrototypeMethod (tpl, "keys", _keys);
    Nan::SetPrototypeMethod (tpl, "values", _values);
    Nan::SetPrototypeMethod (tpl, "comment", _comment);
    Nan::SetPrototypeMethod (tpl, "save", _save);
    Nan::SetPrototypeMethod (tpl, "load", _load);
    Nan::SetPrototypeMethod (tpl, "refresh", _refresh);
    Nan::SetPrototypeMethod (tpl, "pack", _pack);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "dupV2", _dup_v2);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zhashx").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zhashx::Zhashx (void) {
    self = zhashx_new ();
}

Zhashx::Zhashx (zhashx_t *self_) {
    self = self_;
}

Zhashx::~Zhashx () {
}

NAN_METHOD (Zhashx::New) {
    assert (info.IsConstructCall ());
    Zhashx *zhashx = new Zhashx ();
    if (zhashx) {
        zhashx->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zhashx::destroy) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    zhashx_destroy (&zhashx->self);
}


NAN_METHOD (Zhashx::defined) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zhashx->self != NULL));
}

NAN_METHOD (Zhashx::_purge) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    zhashx_purge (zhashx->self);
}

NAN_METHOD (Zhashx::_size) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    size_t result = zhashx_size (zhashx->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zhashx::_keys) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    zlistx_t *result = zhashx_keys (zhashx->self);
    Zlistx *zlistx_result = new Zlistx (result);
    if (zlistx_result) {
    //  Don't yet know how to return a new object
    //      zlistx->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zhashx::_values) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    zlistx_t *result = zhashx_values (zhashx->self);
    Zlistx *zlistx_result = new Zlistx (result);
    if (zlistx_result) {
    //  Don't yet know how to return a new object
    //      zlistx->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zhashx::_comment) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    zhashx_comment (zhashx->self, "%s", format);
}

NAN_METHOD (Zhashx::_save) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    int result = zhashx_save (zhashx->self, (const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zhashx::_load) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    int result = zhashx_load (zhashx->self, (const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zhashx::_refresh) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    int result = zhashx_refresh (zhashx->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zhashx::_pack) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    zframe_t *result = zhashx_pack (zhashx->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zhashx::_dup) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    zhashx_t *result = zhashx_dup (zhashx->self);
    Zhashx *zhashx_result = new Zhashx (result);
    if (zhashx_result) {
    //  Don't yet know how to return a new object
    //      zhashx->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zhashx::_dup_v2) {
    Zhashx *zhashx = Nan::ObjectWrap::Unwrap <Zhashx> (info.Holder ());
    zhashx_t *result = zhashx_dup_v2 (zhashx->self);
    Zhashx *zhashx_result = new Zhashx (result);
    if (zhashx_result) {
    //  Don't yet know how to return a new object
    //      zhashx->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zhashx::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zhashx_test ((bool) verbose);
}

Nan::Persistent <Function> &Zhashx::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Ziflist::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Ziflist").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "reload", _reload);
    Nan::SetPrototypeMethod (tpl, "size", _size);
    Nan::SetPrototypeMethod (tpl, "first", _first);
    Nan::SetPrototypeMethod (tpl, "next", _next);
    Nan::SetPrototypeMethod (tpl, "address", _address);
    Nan::SetPrototypeMethod (tpl, "broadcast", _broadcast);
    Nan::SetPrototypeMethod (tpl, "netmask", _netmask);
    Nan::SetPrototypeMethod (tpl, "mac", _mac);
    Nan::SetPrototypeMethod (tpl, "print", _print);
    Nan::SetPrototypeMethod (tpl, "newIpv6", _new_ipv6);
    Nan::SetPrototypeMethod (tpl, "reloadIpv6", _reload_ipv6);
    Nan::SetPrototypeMethod (tpl, "isIpv6", _is_ipv6);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Ziflist").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Ziflist::Ziflist (void) {
    self = ziflist_new ();
}

Ziflist::Ziflist (ziflist_t *self_) {
    self = self_;
}

Ziflist::~Ziflist () {
}

NAN_METHOD (Ziflist::New) {
    assert (info.IsConstructCall ());
    Ziflist *ziflist = new Ziflist ();
    if (ziflist) {
        ziflist->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Ziflist::destroy) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    ziflist_destroy (&ziflist->self);
}


NAN_METHOD (Ziflist::defined) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (ziflist->self != NULL));
}

NAN_METHOD (Ziflist::_reload) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    ziflist_reload (ziflist->self);
}

NAN_METHOD (Ziflist::_size) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    size_t result = ziflist_size (ziflist->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Ziflist::_first) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    char *result = (char *) ziflist_first (ziflist->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Ziflist::_next) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    char *result = (char *) ziflist_next (ziflist->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Ziflist::_address) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    char *result = (char *) ziflist_address (ziflist->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Ziflist::_broadcast) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    char *result = (char *) ziflist_broadcast (ziflist->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Ziflist::_netmask) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    char *result = (char *) ziflist_netmask (ziflist->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Ziflist::_mac) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    char *result = (char *) ziflist_mac (ziflist->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Ziflist::_print) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    ziflist_print (ziflist->self);
}

NAN_METHOD (Ziflist::_new_ipv6) {
    ziflist_t *result = ziflist_new_ipv6 ();
    Ziflist *ziflist_result = new Ziflist (result);
    if (ziflist_result) {
    //  Don't yet know how to return a new object
    //      ziflist->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Ziflist::_reload_ipv6) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    ziflist_reload_ipv6 (ziflist->self);
}

NAN_METHOD (Ziflist::_is_ipv6) {
    Ziflist *ziflist = Nan::ObjectWrap::Unwrap <Ziflist> (info.Holder ());
    bool result = ziflist_is_ipv6 (ziflist->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Ziflist::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    ziflist_test ((bool) verbose);
}

Nan::Persistent <Function> &Ziflist::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zlist::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zlist").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "purge", _purge);
    Nan::SetPrototypeMethod (tpl, "size", _size);
    Nan::SetPrototypeMethod (tpl, "autofree", _autofree);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zlist").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zlist::Zlist (void) {
    self = zlist_new ();
}

Zlist::Zlist (zlist_t *self_) {
    self = self_;
}

Zlist::~Zlist () {
}

NAN_METHOD (Zlist::New) {
    assert (info.IsConstructCall ());
    Zlist *zlist = new Zlist ();
    if (zlist) {
        zlist->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zlist::destroy) {
    Zlist *zlist = Nan::ObjectWrap::Unwrap <Zlist> (info.Holder ());
    zlist_destroy (&zlist->self);
}


NAN_METHOD (Zlist::defined) {
    Zlist *zlist = Nan::ObjectWrap::Unwrap <Zlist> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zlist->self != NULL));
}

NAN_METHOD (Zlist::_dup) {
    Zlist *zlist = Nan::ObjectWrap::Unwrap <Zlist> (info.Holder ());
    zlist_t *result = zlist_dup (zlist->self);
    Zlist *zlist_result = new Zlist (result);
    if (zlist_result) {
    //  Don't yet know how to return a new object
    //      zlist->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zlist::_purge) {
    Zlist *zlist = Nan::ObjectWrap::Unwrap <Zlist> (info.Holder ());
    zlist_purge (zlist->self);
}

NAN_METHOD (Zlist::_size) {
    Zlist *zlist = Nan::ObjectWrap::Unwrap <Zlist> (info.Holder ());
    size_t result = zlist_size (zlist->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zlist::_autofree) {
    Zlist *zlist = Nan::ObjectWrap::Unwrap <Zlist> (info.Holder ());
    zlist_autofree (zlist->self);
}

NAN_METHOD (Zlist::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zlist_test ((bool) verbose);
}

Nan::Persistent <Function> &Zlist::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zlistx::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zlistx").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "size", _size);
    Nan::SetPrototypeMethod (tpl, "purge", _purge);
    Nan::SetPrototypeMethod (tpl, "sort", _sort);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "pack", _pack);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zlistx").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zlistx::Zlistx (void) {
    self = zlistx_new ();
}

Zlistx::Zlistx (zlistx_t *self_) {
    self = self_;
}

Zlistx::~Zlistx () {
}

NAN_METHOD (Zlistx::New) {
    assert (info.IsConstructCall ());
    Zlistx *zlistx = new Zlistx ();
    if (zlistx) {
        zlistx->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zlistx::destroy) {
    Zlistx *zlistx = Nan::ObjectWrap::Unwrap <Zlistx> (info.Holder ());
    zlistx_destroy (&zlistx->self);
}


NAN_METHOD (Zlistx::defined) {
    Zlistx *zlistx = Nan::ObjectWrap::Unwrap <Zlistx> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zlistx->self != NULL));
}

NAN_METHOD (Zlistx::_size) {
    Zlistx *zlistx = Nan::ObjectWrap::Unwrap <Zlistx> (info.Holder ());
    size_t result = zlistx_size (zlistx->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zlistx::_purge) {
    Zlistx *zlistx = Nan::ObjectWrap::Unwrap <Zlistx> (info.Holder ());
    zlistx_purge (zlistx->self);
}

NAN_METHOD (Zlistx::_sort) {
    Zlistx *zlistx = Nan::ObjectWrap::Unwrap <Zlistx> (info.Holder ());
    zlistx_sort (zlistx->self);
}

NAN_METHOD (Zlistx::_dup) {
    Zlistx *zlistx = Nan::ObjectWrap::Unwrap <Zlistx> (info.Holder ());
    zlistx_t *result = zlistx_dup (zlistx->self);
    Zlistx *zlistx_result = new Zlistx (result);
    if (zlistx_result) {
    //  Don't yet know how to return a new object
    //      zlistx->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zlistx::_pack) {
    Zlistx *zlistx = Nan::ObjectWrap::Unwrap <Zlistx> (info.Holder ());
    zframe_t *result = zlistx_pack (zlistx->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zlistx::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zlistx_test ((bool) verbose);
}

Nan::Persistent <Function> &Zlistx::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zloop::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zloop").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "readerEnd", _reader_end);
    Nan::SetPrototypeMethod (tpl, "readerSetTolerant", _reader_set_tolerant);
    Nan::SetPrototypeMethod (tpl, "timerEnd", _timer_end);
    Nan::SetPrototypeMethod (tpl, "setTicketDelay", _set_ticket_delay);
    Nan::SetPrototypeMethod (tpl, "setMaxTimers", _set_max_timers);
    Nan::SetPrototypeMethod (tpl, "setVerbose", _set_verbose);
    Nan::SetPrototypeMethod (tpl, "setNonstop", _set_nonstop);
    Nan::SetPrototypeMethod (tpl, "start", _start);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zloop").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zloop::Zloop (void) {
    self = zloop_new ();
}

Zloop::Zloop (zloop_t *self_) {
    self = self_;
}

Zloop::~Zloop () {
}

NAN_METHOD (Zloop::New) {
    assert (info.IsConstructCall ());
    Zloop *zloop = new Zloop ();
    if (zloop) {
        zloop->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zloop::destroy) {
    Zloop *zloop = Nan::ObjectWrap::Unwrap <Zloop> (info.Holder ());
    zloop_destroy (&zloop->self);
}


NAN_METHOD (Zloop::defined) {
    Zloop *zloop = Nan::ObjectWrap::Unwrap <Zloop> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zloop->self != NULL));
}

NAN_METHOD (Zloop::_reader_end) {
    Zloop *zloop = Nan::ObjectWrap::Unwrap <Zloop> (info.Holder ());
    Zsock *sock = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    zloop_reader_end (zloop->self, sock->self);
}

NAN_METHOD (Zloop::_reader_set_tolerant) {
    Zloop *zloop = Nan::ObjectWrap::Unwrap <Zloop> (info.Holder ());
    Zsock *sock = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    zloop_reader_set_tolerant (zloop->self, sock->self);
}

NAN_METHOD (Zloop::_timer_end) {
    Zloop *zloop = Nan::ObjectWrap::Unwrap <Zloop> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `timer id`");

    //int timer_id; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int timer_id;


    if (info [0]->IsNumber ())
    {
          timer_id = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`timer id` must be a number");
    int result = zloop_timer_end (zloop->self, (int) timer_id);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zloop::_set_ticket_delay) {
    Zloop *zloop = Nan::ObjectWrap::Unwrap <Zloop> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `ticket delay`");
    else
    if (!info [0]->IsNumber ())
        return Nan::ThrowTypeError ("`ticket delay` must be a number");
    size_t ticket_delay = Nan::To<int64_t>(info [0]).FromJust ();
    zloop_set_ticket_delay (zloop->self, (size_t) ticket_delay);
}

NAN_METHOD (Zloop::_set_max_timers) {
    Zloop *zloop = Nan::ObjectWrap::Unwrap <Zloop> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `max timers`");
    else
    if (!info [0]->IsNumber ())
        return Nan::ThrowTypeError ("`max timers` must be a number");
    size_t max_timers = Nan::To<int64_t>(info [0]).FromJust ();
    zloop_set_max_timers (zloop->self, (size_t) max_timers);
}

NAN_METHOD (Zloop::_set_verbose) {
    Zloop *zloop = Nan::ObjectWrap::Unwrap <Zloop> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zloop_set_verbose (zloop->self, (bool) verbose);
}

NAN_METHOD (Zloop::_set_nonstop) {
    Zloop *zloop = Nan::ObjectWrap::Unwrap <Zloop> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `nonstop`");

    //bool nonstop; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool nonstop;


    if (info [0]->IsBoolean ())
    {
          nonstop = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`nonstop` must be a Boolean");
    zloop_set_nonstop (zloop->self, (bool) nonstop);
}

NAN_METHOD (Zloop::_start) {
    Zloop *zloop = Nan::ObjectWrap::Unwrap <Zloop> (info.Holder ());
    int result = zloop_start (zloop->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zloop::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zloop_test ((bool) verbose);
}

Nan::Persistent <Function> &Zloop::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zmsg::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zmsg").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "send", _send);
    Nan::SetPrototypeMethod (tpl, "sendm", _sendm);
    Nan::SetPrototypeMethod (tpl, "size", _size);
    Nan::SetPrototypeMethod (tpl, "contentSize", _content_size);
    Nan::SetPrototypeMethod (tpl, "routingId", _routing_id);
    Nan::SetPrototypeMethod (tpl, "setRoutingId", _set_routing_id);
    Nan::SetPrototypeMethod (tpl, "prepend", _prepend);
    Nan::SetPrototypeMethod (tpl, "append", _append);
    Nan::SetPrototypeMethod (tpl, "pop", _pop);
    Nan::SetPrototypeMethod (tpl, "pushmem", _pushmem);
    Nan::SetPrototypeMethod (tpl, "addmem", _addmem);
    Nan::SetPrototypeMethod (tpl, "pushstr", _pushstr);
    Nan::SetPrototypeMethod (tpl, "addstr", _addstr);
    Nan::SetPrototypeMethod (tpl, "pushstrf", _pushstrf);
    Nan::SetPrototypeMethod (tpl, "addstrf", _addstrf);
    Nan::SetPrototypeMethod (tpl, "popstr", _popstr);
    Nan::SetPrototypeMethod (tpl, "addmsg", _addmsg);
    Nan::SetPrototypeMethod (tpl, "popmsg", _popmsg);
    Nan::SetPrototypeMethod (tpl, "remove", _remove);
    Nan::SetPrototypeMethod (tpl, "first", _first);
    Nan::SetPrototypeMethod (tpl, "next", _next);
    Nan::SetPrototypeMethod (tpl, "last", _last);
    Nan::SetPrototypeMethod (tpl, "encode", _encode);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "print", _print);
    Nan::SetPrototypeMethod (tpl, "printN", _print_n);
    Nan::SetPrototypeMethod (tpl, "eq", _eq);
    Nan::SetPrototypeMethod (tpl, "signal", _signal);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zmsg").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zmsg::Zmsg (void) {
    self = zmsg_new ();
}

Zmsg::Zmsg (zmsg_t *self_) {
    self = self_;
}

Zmsg::~Zmsg () {
}

NAN_METHOD (Zmsg::New) {
    assert (info.IsConstructCall ());
    Zmsg *zmsg = new Zmsg ();
    if (zmsg) {
        zmsg->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zmsg::destroy) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    zmsg_destroy (&zmsg->self);
}


NAN_METHOD (Zmsg::defined) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zmsg->self != NULL));
}

NAN_METHOD (Zmsg::_send) {
    Zmsg *self_p = Nan::ObjectWrap::Unwrap<Zmsg>(info [0].As<Object>());
    Zsock *dest = Nan::ObjectWrap::Unwrap<Zsock>(info [1].As<Object>());
    int result = zmsg_send (&self_p->self, dest->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_sendm) {
    Zmsg *self_p = Nan::ObjectWrap::Unwrap<Zmsg>(info [0].As<Object>());
    Zsock *dest = Nan::ObjectWrap::Unwrap<Zsock>(info [1].As<Object>());
    int result = zmsg_sendm (&self_p->self, dest->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_size) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    size_t result = zmsg_size (zmsg->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_content_size) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    size_t result = zmsg_content_size (zmsg->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_routing_id) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    uint32_t result = zmsg_routing_id (zmsg->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_set_routing_id) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `routing id`");

    //uint32_t routing_id; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    uint32_t routing_id;


    if (info [0]->IsNumber ())
    {
          routing_id = Nan::To<uint32_t>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`routing id` must be a number");
    zmsg_set_routing_id (zmsg->self, (uint32_t) routing_id);
}

NAN_METHOD (Zmsg::_prepend) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    Zframe *frame_p = Nan::ObjectWrap::Unwrap<Zframe>(info [0].As<Object>());
    int result = zmsg_prepend (zmsg->self, &frame_p->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_append) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    Zframe *frame_p = Nan::ObjectWrap::Unwrap<Zframe>(info [0].As<Object>());
    int result = zmsg_append (zmsg->self, &frame_p->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_pop) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    zframe_t *result = zmsg_pop (zmsg->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zmsg::_pushmem) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *data = (const byte *) node::Buffer::Data (buffer_node);
    size_t size = node::Buffer::Length (buffer_node);
    int result = zmsg_pushmem (zmsg->self, (const void *)data, (size_t) size);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_addmem) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *data = (const byte *) node::Buffer::Data (buffer_node);
    size_t size = node::Buffer::Length (buffer_node);
    int result = zmsg_addmem (zmsg->self, (const void *)data, (size_t) size);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_pushstr) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    char *string;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `string`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`string` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String string_utf8 (info [0].As<String>());
    string = *string_utf8;
         //} //bjornw end
    int result = zmsg_pushstr (zmsg->self, (const char *)string);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_addstr) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    char *string;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `string`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`string` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String string_utf8 (info [0].As<String>());
    string = *string_utf8;
         //} //bjornw end
    int result = zmsg_addstr (zmsg->self, (const char *)string);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_pushstrf) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    int result = zmsg_pushstrf (zmsg->self, "%s", format);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_addstrf) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    int result = zmsg_addstrf (zmsg->self, "%s", format);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_popstr) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    char *result = (char *) zmsg_popstr (zmsg->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zmsg::_addmsg) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    Zmsg *msg_p = Nan::ObjectWrap::Unwrap<Zmsg>(info [0].As<Object>());
    int result = zmsg_addmsg (zmsg->self, &msg_p->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_popmsg) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    zmsg_t *result = zmsg_popmsg (zmsg->self);
    Zmsg *zmsg_result = new Zmsg (result);
    if (zmsg_result) {
    //  Don't yet know how to return a new object
    //      zmsg->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zmsg::_remove) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    Zframe *frame = Nan::ObjectWrap::Unwrap<Zframe>(info [0].As<Object>());
    zmsg_remove (zmsg->self, frame->self);
}

NAN_METHOD (Zmsg::_first) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    zframe_t *result = zmsg_first (zmsg->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zmsg::_next) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    zframe_t *result = zmsg_next (zmsg->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zmsg::_last) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    zframe_t *result = zmsg_last (zmsg->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zmsg::_encode) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    zframe_t *result = zmsg_encode (zmsg->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zmsg::_dup) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    zmsg_t *result = zmsg_dup (zmsg->self);
    Zmsg *zmsg_result = new Zmsg (result);
    if (zmsg_result) {
    //  Don't yet know how to return a new object
    //      zmsg->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zmsg::_print) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    zmsg_print (zmsg->self);
}

NAN_METHOD (Zmsg::_print_n) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `size`");
    else
    if (!info [0]->IsNumber ())
        return Nan::ThrowTypeError ("`size` must be a number");
    size_t size = Nan::To<int64_t>(info [0]).FromJust ();
    zmsg_print_n (zmsg->self, (size_t) size);
}

NAN_METHOD (Zmsg::_eq) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    Zmsg *other = Nan::ObjectWrap::Unwrap<Zmsg>(info [0].As<Object>());
    bool result = zmsg_eq (zmsg->self, other->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zmsg::_signal) {
    Zmsg *zmsg = Nan::ObjectWrap::Unwrap <Zmsg> (info.Holder ());
    int result = zmsg_signal (zmsg->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zmsg::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zmsg_test ((bool) verbose);
}

Nan::Persistent <Function> &Zmsg::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zpoller::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zpoller").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "add", _add);
    Nan::SetPrototypeMethod (tpl, "setNonstop", _set_nonstop);
    Nan::SetPrototypeMethod (tpl, "expired", _expired);
    Nan::SetPrototypeMethod (tpl, "terminated", _terminated);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zpoller").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zpoller::Zpoller (zsock_t *reader) {
    self = zpoller_new (reader);
}

Zpoller::Zpoller (zpoller_t *self_) {
    self = self_;
}

Zpoller::~Zpoller () {
}

NAN_METHOD (Zpoller::New) {
    assert (info.IsConstructCall ());
    Zsock *reader = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    Zpoller *zpoller = new Zpoller (reader->self);
    if (zpoller) {
        zpoller->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zpoller::destroy) {
    Zpoller *zpoller = Nan::ObjectWrap::Unwrap <Zpoller> (info.Holder ());
    zpoller_destroy (&zpoller->self);
}


NAN_METHOD (Zpoller::defined) {
    Zpoller *zpoller = Nan::ObjectWrap::Unwrap <Zpoller> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zpoller->self != NULL));
}

NAN_METHOD (Zpoller::_add) {
    Zpoller *zpoller = Nan::ObjectWrap::Unwrap <Zpoller> (info.Holder ());
    Zsock *reader = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    int result = zpoller_add (zpoller->self, reader->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zpoller::_set_nonstop) {
    Zpoller *zpoller = Nan::ObjectWrap::Unwrap <Zpoller> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `nonstop`");

    //bool nonstop; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool nonstop;


    if (info [0]->IsBoolean ())
    {
          nonstop = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`nonstop` must be a Boolean");
    zpoller_set_nonstop (zpoller->self, (bool) nonstop);
}

NAN_METHOD (Zpoller::_expired) {
    Zpoller *zpoller = Nan::ObjectWrap::Unwrap <Zpoller> (info.Holder ());
    bool result = zpoller_expired (zpoller->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zpoller::_terminated) {
    Zpoller *zpoller = Nan::ObjectWrap::Unwrap <Zpoller> (info.Holder ());
    bool result = zpoller_terminated (zpoller->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zpoller::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zpoller_test ((bool) verbose);
}

Nan::Persistent <Function> &Zpoller::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zproc::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zproc").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "args", _args);
    Nan::SetPrototypeMethod (tpl, "setArgs", _set_args);
    Nan::SetPrototypeMethod (tpl, "setArgsx", _set_argsx);
    Nan::SetPrototypeMethod (tpl, "setEnv", _set_env);
    Nan::SetPrototypeMethod (tpl, "run", _run);
    Nan::SetPrototypeMethod (tpl, "returncode", _returncode);
    Nan::SetPrototypeMethod (tpl, "pid", _pid);
    Nan::SetPrototypeMethod (tpl, "running", _running);
    Nan::SetPrototypeMethod (tpl, "wait", _wait);
    Nan::SetPrototypeMethod (tpl, "shutdown", _shutdown);
    Nan::SetPrototypeMethod (tpl, "kill", _kill);
    Nan::SetPrototypeMethod (tpl, "setVerbose", _set_verbose);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zproc").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zproc::Zproc (void) {
    self = zproc_new ();
}

Zproc::Zproc (zproc_t *self_) {
    self = self_;
}

Zproc::~Zproc () {
}

NAN_METHOD (Zproc::New) {
    assert (info.IsConstructCall ());
    Zproc *zproc = new Zproc ();
    if (zproc) {
        zproc->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zproc::destroy) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    zproc_destroy (&zproc->self);
}


NAN_METHOD (Zproc::defined) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zproc->self != NULL));
}

NAN_METHOD (Zproc::_args) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    zlist_t *result = zproc_args (zproc->self);
    Zlist *zlist_result = new Zlist (result);
    if (zlist_result) {
    //  Don't yet know how to return a new object
    //      zlist->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zproc::_set_args) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    Zlist *arguments = Nan::ObjectWrap::Unwrap<Zlist>(info [0].As<Object>());
    zproc_set_args (zproc->self, &arguments->self);
}

NAN_METHOD (Zproc::_set_argsx) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    char *arguments;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `arguments`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`arguments` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String arguments_utf8 (info [0].As<String>());
    arguments = *arguments_utf8;
         //} //bjornw end
    zproc_set_argsx (zproc->self, (const char *)arguments);
}

NAN_METHOD (Zproc::_set_env) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    Zhash *arguments = Nan::ObjectWrap::Unwrap<Zhash>(info [0].As<Object>());
    zproc_set_env (zproc->self, &arguments->self);
}

NAN_METHOD (Zproc::_run) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    int result = zproc_run (zproc->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zproc::_returncode) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    int result = zproc_returncode (zproc->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zproc::_pid) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    int result = zproc_pid (zproc->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zproc::_running) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    bool result = zproc_running (zproc->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zproc::_wait) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `timeout`");

    //int timeout; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int timeout;


    if (info [0]->IsNumber ())
    {
          timeout = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`timeout` must be a number");
    int result = zproc_wait (zproc->self, (int) timeout);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zproc::_shutdown) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `timeout`");

    //int timeout; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int timeout;


    if (info [0]->IsNumber ())
    {
          timeout = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`timeout` must be a number");
    zproc_shutdown (zproc->self, (int) timeout);
}

NAN_METHOD (Zproc::_kill) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `signal`");

    //int signal; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int signal;


    if (info [0]->IsNumber ())
    {
          signal = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`signal` must be a number");
    zproc_kill (zproc->self, (int) signal);
}

NAN_METHOD (Zproc::_set_verbose) {
    Zproc *zproc = Nan::ObjectWrap::Unwrap <Zproc> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zproc_set_verbose (zproc->self, (bool) verbose);
}

NAN_METHOD (Zproc::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zproc_test ((bool) verbose);
}

Nan::Persistent <Function> &Zproc::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zsock::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zsock").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "bind", _bind);
    Nan::SetPrototypeMethod (tpl, "endpoint", _endpoint);
    Nan::SetPrototypeMethod (tpl, "unbind", _unbind);
    Nan::SetPrototypeMethod (tpl, "connect", _connect);
    Nan::SetPrototypeMethod (tpl, "disconnect", _disconnect);
    Nan::SetPrototypeMethod (tpl, "attach", _attach);
    Nan::SetPrototypeMethod (tpl, "typeStr", _type_str);
    Nan::SetPrototypeMethod (tpl, "send", _send);
    Nan::SetPrototypeMethod (tpl, "recv", _recv);
    Nan::SetPrototypeMethod (tpl, "bsend", _bsend);
    Nan::SetPrototypeMethod (tpl, "brecv", _brecv);
    Nan::SetPrototypeMethod (tpl, "routingId", _routing_id);
    Nan::SetPrototypeMethod (tpl, "setRoutingId", _set_routing_id);
    Nan::SetPrototypeMethod (tpl, "setUnbounded", _set_unbounded);
    Nan::SetPrototypeMethod (tpl, "wait", _wait);
    Nan::SetPrototypeMethod (tpl, "flush", _flush);
    Nan::SetPrototypeMethod (tpl, "join", _join);
    Nan::SetPrototypeMethod (tpl, "leave", _leave);
    Nan::SetPrototypeMethod (tpl, "hasIn", _has_in);
    Nan::SetPrototypeMethod (tpl, "priority", _priority);
    Nan::SetPrototypeMethod (tpl, "setPriority", _set_priority);
    Nan::SetPrototypeMethod (tpl, "reconnectStop", _reconnect_stop);
    Nan::SetPrototypeMethod (tpl, "setReconnectStop", _set_reconnect_stop);
    Nan::SetPrototypeMethod (tpl, "setOnlyFirstSubscribe", _set_only_first_subscribe);
    Nan::SetPrototypeMethod (tpl, "setHelloMsg", _set_hello_msg);
    Nan::SetPrototypeMethod (tpl, "setDisconnectMsg", _set_disconnect_msg);
    Nan::SetPrototypeMethod (tpl, "setWssTrustSystem", _set_wss_trust_system);
    Nan::SetPrototypeMethod (tpl, "setWssHostname", _set_wss_hostname);
    Nan::SetPrototypeMethod (tpl, "setWssTrustPem", _set_wss_trust_pem);
    Nan::SetPrototypeMethod (tpl, "setWssCertPem", _set_wss_cert_pem);
    Nan::SetPrototypeMethod (tpl, "setWssKeyPem", _set_wss_key_pem);
    Nan::SetPrototypeMethod (tpl, "outBatchSize", _out_batch_size);
    Nan::SetPrototypeMethod (tpl, "setOutBatchSize", _set_out_batch_size);
    Nan::SetPrototypeMethod (tpl, "inBatchSize", _in_batch_size);
    Nan::SetPrototypeMethod (tpl, "setInBatchSize", _set_in_batch_size);
    Nan::SetPrototypeMethod (tpl, "socksPassword", _socks_password);
    Nan::SetPrototypeMethod (tpl, "setSocksPassword", _set_socks_password);
    Nan::SetPrototypeMethod (tpl, "socksUsername", _socks_username);
    Nan::SetPrototypeMethod (tpl, "setSocksUsername", _set_socks_username);
    Nan::SetPrototypeMethod (tpl, "setXpubManualLastValue", _set_xpub_manual_last_value);
    Nan::SetPrototypeMethod (tpl, "routerNotify", _router_notify);
    Nan::SetPrototypeMethod (tpl, "setRouterNotify", _set_router_notify);
    Nan::SetPrototypeMethod (tpl, "multicastLoop", _multicast_loop);
    Nan::SetPrototypeMethod (tpl, "setMulticastLoop", _set_multicast_loop);
    Nan::SetPrototypeMethod (tpl, "metadata", _metadata);
    Nan::SetPrototypeMethod (tpl, "setMetadata", _set_metadata);
    Nan::SetPrototypeMethod (tpl, "loopbackFastpath", _loopback_fastpath);
    Nan::SetPrototypeMethod (tpl, "setLoopbackFastpath", _set_loopback_fastpath);
    Nan::SetPrototypeMethod (tpl, "zapEnforceDomain", _zap_enforce_domain);
    Nan::SetPrototypeMethod (tpl, "setZapEnforceDomain", _set_zap_enforce_domain);
    Nan::SetPrototypeMethod (tpl, "gssapiPrincipalNametype", _gssapi_principal_nametype);
    Nan::SetPrototypeMethod (tpl, "setGssapiPrincipalNametype", _set_gssapi_principal_nametype);
    Nan::SetPrototypeMethod (tpl, "gssapiServicePrincipalNametype", _gssapi_service_principal_nametype);
    Nan::SetPrototypeMethod (tpl, "setGssapiServicePrincipalNametype", _set_gssapi_service_principal_nametype);
    Nan::SetPrototypeMethod (tpl, "bindtodevice", _bindtodevice);
    Nan::SetPrototypeMethod (tpl, "setBindtodevice", _set_bindtodevice);
    Nan::SetPrototypeMethod (tpl, "heartbeatIvl", _heartbeat_ivl);
    Nan::SetPrototypeMethod (tpl, "setHeartbeatIvl", _set_heartbeat_ivl);
    Nan::SetPrototypeMethod (tpl, "heartbeatTtl", _heartbeat_ttl);
    Nan::SetPrototypeMethod (tpl, "setHeartbeatTtl", _set_heartbeat_ttl);
    Nan::SetPrototypeMethod (tpl, "heartbeatTimeout", _heartbeat_timeout);
    Nan::SetPrototypeMethod (tpl, "setHeartbeatTimeout", _set_heartbeat_timeout);
    Nan::SetPrototypeMethod (tpl, "useFd", _use_fd);
    Nan::SetPrototypeMethod (tpl, "setUseFd", _set_use_fd);
    Nan::SetPrototypeMethod (tpl, "setXpubManual", _set_xpub_manual);
    Nan::SetPrototypeMethod (tpl, "setXpubWelcomeMsg", _set_xpub_welcome_msg);
    Nan::SetPrototypeMethod (tpl, "setStreamNotify", _set_stream_notify);
    Nan::SetPrototypeMethod (tpl, "invertMatching", _invert_matching);
    Nan::SetPrototypeMethod (tpl, "setInvertMatching", _set_invert_matching);
    Nan::SetPrototypeMethod (tpl, "setXpubVerboser", _set_xpub_verboser);
    Nan::SetPrototypeMethod (tpl, "connectTimeout", _connect_timeout);
    Nan::SetPrototypeMethod (tpl, "setConnectTimeout", _set_connect_timeout);
    Nan::SetPrototypeMethod (tpl, "tcpMaxrt", _tcp_maxrt);
    Nan::SetPrototypeMethod (tpl, "setTcpMaxrt", _set_tcp_maxrt);
    Nan::SetPrototypeMethod (tpl, "threadSafe", _thread_safe);
    Nan::SetPrototypeMethod (tpl, "multicastMaxtpdu", _multicast_maxtpdu);
    Nan::SetPrototypeMethod (tpl, "setMulticastMaxtpdu", _set_multicast_maxtpdu);
    Nan::SetPrototypeMethod (tpl, "vmciBufferSize", _vmci_buffer_size);
    Nan::SetPrototypeMethod (tpl, "setVmciBufferSize", _set_vmci_buffer_size);
    Nan::SetPrototypeMethod (tpl, "vmciBufferMinSize", _vmci_buffer_min_size);
    Nan::SetPrototypeMethod (tpl, "setVmciBufferMinSize", _set_vmci_buffer_min_size);
    Nan::SetPrototypeMethod (tpl, "vmciBufferMaxSize", _vmci_buffer_max_size);
    Nan::SetPrototypeMethod (tpl, "setVmciBufferMaxSize", _set_vmci_buffer_max_size);
    Nan::SetPrototypeMethod (tpl, "vmciConnectTimeout", _vmci_connect_timeout);
    Nan::SetPrototypeMethod (tpl, "setVmciConnectTimeout", _set_vmci_connect_timeout);
    Nan::SetPrototypeMethod (tpl, "tos", _tos);
    Nan::SetPrototypeMethod (tpl, "setTos", _set_tos);
    Nan::SetPrototypeMethod (tpl, "setRouterHandover", _set_router_handover);
    Nan::SetPrototypeMethod (tpl, "setConnectRid", _set_connect_rid);
    Nan::SetPrototypeMethod (tpl, "setConnectRidBin", _set_connect_rid_bin);
    Nan::SetPrototypeMethod (tpl, "handshakeIvl", _handshake_ivl);
    Nan::SetPrototypeMethod (tpl, "setHandshakeIvl", _set_handshake_ivl);
    Nan::SetPrototypeMethod (tpl, "socksProxy", _socks_proxy);
    Nan::SetPrototypeMethod (tpl, "setSocksProxy", _set_socks_proxy);
    Nan::SetPrototypeMethod (tpl, "setXpubNodrop", _set_xpub_nodrop);
    Nan::SetPrototypeMethod (tpl, "setRouterMandatory", _set_router_mandatory);
    Nan::SetPrototypeMethod (tpl, "setProbeRouter", _set_probe_router);
    Nan::SetPrototypeMethod (tpl, "setReqRelaxed", _set_req_relaxed);
    Nan::SetPrototypeMethod (tpl, "setReqCorrelate", _set_req_correlate);
    Nan::SetPrototypeMethod (tpl, "setConflate", _set_conflate);
    Nan::SetPrototypeMethod (tpl, "zapDomain", _zap_domain);
    Nan::SetPrototypeMethod (tpl, "setZapDomain", _set_zap_domain);
    Nan::SetPrototypeMethod (tpl, "mechanism", _mechanism);
    Nan::SetPrototypeMethod (tpl, "plainServer", _plain_server);
    Nan::SetPrototypeMethod (tpl, "setPlainServer", _set_plain_server);
    Nan::SetPrototypeMethod (tpl, "plainUsername", _plain_username);
    Nan::SetPrototypeMethod (tpl, "setPlainUsername", _set_plain_username);
    Nan::SetPrototypeMethod (tpl, "plainPassword", _plain_password);
    Nan::SetPrototypeMethod (tpl, "setPlainPassword", _set_plain_password);
    Nan::SetPrototypeMethod (tpl, "curveServer", _curve_server);
    Nan::SetPrototypeMethod (tpl, "setCurveServer", _set_curve_server);
    Nan::SetPrototypeMethod (tpl, "curvePublickey", _curve_publickey);
    Nan::SetPrototypeMethod (tpl, "setCurvePublickey", _set_curve_publickey);
    Nan::SetPrototypeMethod (tpl, "setCurvePublickeyBin", _set_curve_publickey_bin);
    Nan::SetPrototypeMethod (tpl, "curveSecretkey", _curve_secretkey);
    Nan::SetPrototypeMethod (tpl, "setCurveSecretkey", _set_curve_secretkey);
    Nan::SetPrototypeMethod (tpl, "setCurveSecretkeyBin", _set_curve_secretkey_bin);
    Nan::SetPrototypeMethod (tpl, "curveServerkey", _curve_serverkey);
    Nan::SetPrototypeMethod (tpl, "setCurveServerkey", _set_curve_serverkey);
    Nan::SetPrototypeMethod (tpl, "setCurveServerkeyBin", _set_curve_serverkey_bin);
    Nan::SetPrototypeMethod (tpl, "gssapiServer", _gssapi_server);
    Nan::SetPrototypeMethod (tpl, "setGssapiServer", _set_gssapi_server);
    Nan::SetPrototypeMethod (tpl, "gssapiPlaintext", _gssapi_plaintext);
    Nan::SetPrototypeMethod (tpl, "setGssapiPlaintext", _set_gssapi_plaintext);
    Nan::SetPrototypeMethod (tpl, "gssapiPrincipal", _gssapi_principal);
    Nan::SetPrototypeMethod (tpl, "setGssapiPrincipal", _set_gssapi_principal);
    Nan::SetPrototypeMethod (tpl, "gssapiServicePrincipal", _gssapi_service_principal);
    Nan::SetPrototypeMethod (tpl, "setGssapiServicePrincipal", _set_gssapi_service_principal);
    Nan::SetPrototypeMethod (tpl, "ipv6", _ipv6);
    Nan::SetPrototypeMethod (tpl, "setIpv6", _set_ipv6);
    Nan::SetPrototypeMethod (tpl, "immediate", _immediate);
    Nan::SetPrototypeMethod (tpl, "setImmediate", _set_immediate);
    Nan::SetPrototypeMethod (tpl, "sndhwm", _sndhwm);
    Nan::SetPrototypeMethod (tpl, "setSndhwm", _set_sndhwm);
    Nan::SetPrototypeMethod (tpl, "rcvhwm", _rcvhwm);
    Nan::SetPrototypeMethod (tpl, "setRcvhwm", _set_rcvhwm);
    Nan::SetPrototypeMethod (tpl, "maxmsgsize", _maxmsgsize);
    Nan::SetPrototypeMethod (tpl, "setMaxmsgsize", _set_maxmsgsize);
    Nan::SetPrototypeMethod (tpl, "multicastHops", _multicast_hops);
    Nan::SetPrototypeMethod (tpl, "setMulticastHops", _set_multicast_hops);
    Nan::SetPrototypeMethod (tpl, "setXpubVerbose", _set_xpub_verbose);
    Nan::SetPrototypeMethod (tpl, "tcpKeepalive", _tcp_keepalive);
    Nan::SetPrototypeMethod (tpl, "setTcpKeepalive", _set_tcp_keepalive);
    Nan::SetPrototypeMethod (tpl, "tcpKeepaliveIdle", _tcp_keepalive_idle);
    Nan::SetPrototypeMethod (tpl, "setTcpKeepaliveIdle", _set_tcp_keepalive_idle);
    Nan::SetPrototypeMethod (tpl, "tcpKeepaliveCnt", _tcp_keepalive_cnt);
    Nan::SetPrototypeMethod (tpl, "setTcpKeepaliveCnt", _set_tcp_keepalive_cnt);
    Nan::SetPrototypeMethod (tpl, "tcpKeepaliveIntvl", _tcp_keepalive_intvl);
    Nan::SetPrototypeMethod (tpl, "setTcpKeepaliveIntvl", _set_tcp_keepalive_intvl);
    Nan::SetPrototypeMethod (tpl, "tcpAcceptFilter", _tcp_accept_filter);
    Nan::SetPrototypeMethod (tpl, "setTcpAcceptFilter", _set_tcp_accept_filter);
    Nan::SetPrototypeMethod (tpl, "lastEndpoint", _last_endpoint);
    Nan::SetPrototypeMethod (tpl, "setRouterRaw", _set_router_raw);
    Nan::SetPrototypeMethod (tpl, "ipv4only", _ipv4only);
    Nan::SetPrototypeMethod (tpl, "setIpv4only", _set_ipv4only);
    Nan::SetPrototypeMethod (tpl, "setDelayAttachOnConnect", _set_delay_attach_on_connect);
    Nan::SetPrototypeMethod (tpl, "hwm", _hwm);
    Nan::SetPrototypeMethod (tpl, "setHwm", _set_hwm);
    Nan::SetPrototypeMethod (tpl, "swap", _swap);
    Nan::SetPrototypeMethod (tpl, "setSwap", _set_swap);
    Nan::SetPrototypeMethod (tpl, "affinity", _affinity);
    Nan::SetPrototypeMethod (tpl, "setAffinity", _set_affinity);
    Nan::SetPrototypeMethod (tpl, "identity", _identity);
    Nan::SetPrototypeMethod (tpl, "setIdentity", _set_identity);
    Nan::SetPrototypeMethod (tpl, "rate", _rate);
    Nan::SetPrototypeMethod (tpl, "setRate", _set_rate);
    Nan::SetPrototypeMethod (tpl, "recoveryIvl", _recovery_ivl);
    Nan::SetPrototypeMethod (tpl, "setRecoveryIvl", _set_recovery_ivl);
    Nan::SetPrototypeMethod (tpl, "recoveryIvlMsec", _recovery_ivl_msec);
    Nan::SetPrototypeMethod (tpl, "setRecoveryIvlMsec", _set_recovery_ivl_msec);
    Nan::SetPrototypeMethod (tpl, "mcastLoop", _mcast_loop);
    Nan::SetPrototypeMethod (tpl, "setMcastLoop", _set_mcast_loop);
    Nan::SetPrototypeMethod (tpl, "rcvtimeo", _rcvtimeo);
    Nan::SetPrototypeMethod (tpl, "setRcvtimeo", _set_rcvtimeo);
    Nan::SetPrototypeMethod (tpl, "sndtimeo", _sndtimeo);
    Nan::SetPrototypeMethod (tpl, "setSndtimeo", _set_sndtimeo);
    Nan::SetPrototypeMethod (tpl, "sndbuf", _sndbuf);
    Nan::SetPrototypeMethod (tpl, "setSndbuf", _set_sndbuf);
    Nan::SetPrototypeMethod (tpl, "rcvbuf", _rcvbuf);
    Nan::SetPrototypeMethod (tpl, "setRcvbuf", _set_rcvbuf);
    Nan::SetPrototypeMethod (tpl, "linger", _linger);
    Nan::SetPrototypeMethod (tpl, "setLinger", _set_linger);
    Nan::SetPrototypeMethod (tpl, "reconnectIvl", _reconnect_ivl);
    Nan::SetPrototypeMethod (tpl, "setReconnectIvl", _set_reconnect_ivl);
    Nan::SetPrototypeMethod (tpl, "reconnectIvlMax", _reconnect_ivl_max);
    Nan::SetPrototypeMethod (tpl, "setReconnectIvlMax", _set_reconnect_ivl_max);
    Nan::SetPrototypeMethod (tpl, "backlog", _backlog);
    Nan::SetPrototypeMethod (tpl, "setBacklog", _set_backlog);
    Nan::SetPrototypeMethod (tpl, "setSubscribe", _set_subscribe);
    Nan::SetPrototypeMethod (tpl, "setUnsubscribe", _set_unsubscribe);
    Nan::SetPrototypeMethod (tpl, "type", _type);
    Nan::SetPrototypeMethod (tpl, "rcvmore", _rcvmore);
    Nan::SetPrototypeMethod (tpl, "events", _events);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zsock").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zsock::Zsock (int type) {
    self = zsock_new ((int) type);
}

Zsock::Zsock (zsock_t *self_) {
    self = self_;
}

Zsock::~Zsock () {
}

NAN_METHOD (Zsock::New) {
    assert (info.IsConstructCall ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `type`");

    //int type; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int type;

    if (info [0]->IsString ()) {
        Nan::Utf8String type_utf8 (info [0].As<String>());
            // bjornw: check if we need to remove scope here
        char *type_name = *type_utf8;
        for (char *type_ptr = type_name; *type_ptr; type_ptr++)
            *type_ptr = tolower (*type_ptr);
        if (streq (type_name, "pair"))
            type = ZMQ_PAIR;
        else
        if (streq (type_name, "pub"))
            type = ZMQ_PUB;
        else
        if (streq (type_name, "sub"))
            type = ZMQ_SUB;
        else
        if (streq (type_name, "req"))
            type = ZMQ_REQ;
        else
        if (streq (type_name, "rep"))
            type = ZMQ_REP;
        else
        if (streq (type_name, "dealer"))
            type = ZMQ_DEALER;
        else
        if (streq (type_name, "router"))
            type = ZMQ_ROUTER;
        else
        if (streq (type_name, "pull"))
            type = ZMQ_PULL;
        else
        if (streq (type_name, "push"))
            type = ZMQ_PUSH;
        else
        if (streq (type_name, "xpub"))
            type = ZMQ_XPUB;
        else
        if (streq (type_name, "xsub"))
            type = ZMQ_XSUB;
        else
        if (streq (type_name, "stream"))
            type = 11;
        else
        if (streq (type_name, "server"))
            type = 12;
        else
        if (streq (type_name, "client"))
            type = 13;
        else
        if (streq (type_name, "radio"))
            type = 14;
        else
        if (streq (type_name, "dish"))
            type = 15;
        else
        if (streq (type_name, "gather"))
            type = 16;
        else
        if (streq (type_name, "scatter"))
            type = 17;
        else
        if (streq (type_name, "dgram"))
            type = 18;
        else
            return Nan::ThrowTypeError ("`type` not a valid string");
    }
    else

    if (info [0]->IsNumber ())
    {
          type = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`type` must be a number or string");
    Zsock *zsock = new Zsock ((int) type);
    if (zsock) {
        zsock->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zsock::destroy) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    zsock_destroy (&zsock->self);
}


NAN_METHOD (Zsock::defined) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zsock->self != NULL));
}

NAN_METHOD (Zsock::_bind) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    int result = zsock_bind (zsock->self, "%s", format);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_endpoint) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_endpoint (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_unbind) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    int result = zsock_unbind (zsock->self, "%s", format);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_connect) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    int result = zsock_connect (zsock->self, "%s", format);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_disconnect) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    int result = zsock_disconnect (zsock->self, "%s", format);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_attach) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *endpoints;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `endpoints`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`endpoints` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String endpoints_utf8 (info [0].As<String>());
    endpoints = *endpoints_utf8;
         //} //bjornw end
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `serverish`");

    //bool serverish; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool serverish;


    if (info [1]->IsBoolean ())
    {
          serverish = Nan::To<bool>(info [1]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`serverish` must be a Boolean");
    int result = zsock_attach (zsock->self, (const char *)endpoints, (bool) serverish);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_type_str) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_type_str (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_send) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *picture;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `picture`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`picture` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String picture_utf8 (info [0].As<String>());
    picture = *picture_utf8;
         //} //bjornw end
    int result = zsock_send (zsock->self, (const char *)picture);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_recv) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *picture;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `picture`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`picture` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String picture_utf8 (info [0].As<String>());
    picture = *picture_utf8;
         //} //bjornw end
    int result = zsock_recv (zsock->self, (const char *)picture);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_bsend) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *picture;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `picture`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`picture` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String picture_utf8 (info [0].As<String>());
    picture = *picture_utf8;
         //} //bjornw end
    int result = zsock_bsend (zsock->self, (const char *)picture);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_brecv) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *picture;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `picture`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`picture` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String picture_utf8 (info [0].As<String>());
    picture = *picture_utf8;
         //} //bjornw end
    int result = zsock_brecv (zsock->self, (const char *)picture);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_routing_id) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    uint32_t result = zsock_routing_id (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_routing_id) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `routing id`");

    //uint32_t routing_id; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    uint32_t routing_id;


    if (info [0]->IsNumber ())
    {
          routing_id = Nan::To<uint32_t>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`routing id` must be a number");
    zsock_set_routing_id (zsock->self, (uint32_t) routing_id);
}

NAN_METHOD (Zsock::_set_unbounded) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    zsock_set_unbounded (zsock->self);
}

NAN_METHOD (Zsock::_wait) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_wait (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_flush) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    zsock_flush (zsock->self);
}

NAN_METHOD (Zsock::_join) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *group;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `group`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`group` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String group_utf8 (info [0].As<String>());
    group = *group_utf8;
         //} //bjornw end
    int result = zsock_join (zsock->self, (const char *)group);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_leave) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *group;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `group`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`group` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String group_utf8 (info [0].As<String>());
    group = *group_utf8;
         //} //bjornw end
    int result = zsock_leave (zsock->self, (const char *)group);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_has_in) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    bool result = zsock_has_in (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zsock::_priority) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_priority (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_priority) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `priority`");

    //int priority; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int priority;


    if (info [0]->IsNumber ())
    {
          priority = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`priority` must be a number");
    zsock_set_priority (zsock->self, (int) priority);
}

NAN_METHOD (Zsock::_reconnect_stop) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_reconnect_stop (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_reconnect_stop) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `reconnect stop`");

    //int reconnect_stop; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int reconnect_stop;


    if (info [0]->IsNumber ())
    {
          reconnect_stop = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`reconnect stop` must be a number");
    zsock_set_reconnect_stop (zsock->self, (int) reconnect_stop);
}

NAN_METHOD (Zsock::_set_only_first_subscribe) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `only first subscribe`");

    //int only_first_subscribe; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int only_first_subscribe;


    if (info [0]->IsNumber ())
    {
          only_first_subscribe = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`only first subscribe` must be a number");
    zsock_set_only_first_subscribe (zsock->self, (int) only_first_subscribe);
}

NAN_METHOD (Zsock::_set_hello_msg) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    Zframe *hello_msg = Nan::ObjectWrap::Unwrap<Zframe>(info [0].As<Object>());
    zsock_set_hello_msg (zsock->self, hello_msg->self);
}

NAN_METHOD (Zsock::_set_disconnect_msg) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    Zframe *disconnect_msg = Nan::ObjectWrap::Unwrap<Zframe>(info [0].As<Object>());
    zsock_set_disconnect_msg (zsock->self, disconnect_msg->self);
}

NAN_METHOD (Zsock::_set_wss_trust_system) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `wss trust system`");

    //int wss_trust_system; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int wss_trust_system;


    if (info [0]->IsNumber ())
    {
          wss_trust_system = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`wss trust system` must be a number");
    zsock_set_wss_trust_system (zsock->self, (int) wss_trust_system);
}

NAN_METHOD (Zsock::_set_wss_hostname) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *wss_hostname;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `wss hostname`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`wss hostname` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String wss_hostname_utf8 (info [0].As<String>());
    wss_hostname = *wss_hostname_utf8;
         //} //bjornw end
    zsock_set_wss_hostname (zsock->self, (const char *)wss_hostname);
}

NAN_METHOD (Zsock::_set_wss_trust_pem) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *wss_trust_pem;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `wss trust pem`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`wss trust pem` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String wss_trust_pem_utf8 (info [0].As<String>());
    wss_trust_pem = *wss_trust_pem_utf8;
         //} //bjornw end
    zsock_set_wss_trust_pem (zsock->self, (const char *)wss_trust_pem);
}

NAN_METHOD (Zsock::_set_wss_cert_pem) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *wss_cert_pem;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `wss cert pem`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`wss cert pem` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String wss_cert_pem_utf8 (info [0].As<String>());
    wss_cert_pem = *wss_cert_pem_utf8;
         //} //bjornw end
    zsock_set_wss_cert_pem (zsock->self, (const char *)wss_cert_pem);
}

NAN_METHOD (Zsock::_set_wss_key_pem) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *wss_key_pem;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `wss key pem`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`wss key pem` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String wss_key_pem_utf8 (info [0].As<String>());
    wss_key_pem = *wss_key_pem_utf8;
         //} //bjornw end
    zsock_set_wss_key_pem (zsock->self, (const char *)wss_key_pem);
}

NAN_METHOD (Zsock::_out_batch_size) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_out_batch_size (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_out_batch_size) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `out batch size`");

    //int out_batch_size; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int out_batch_size;


    if (info [0]->IsNumber ())
    {
          out_batch_size = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`out batch size` must be a number");
    zsock_set_out_batch_size (zsock->self, (int) out_batch_size);
}

NAN_METHOD (Zsock::_in_batch_size) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_in_batch_size (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_in_batch_size) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `in batch size`");

    //int in_batch_size; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int in_batch_size;


    if (info [0]->IsNumber ())
    {
          in_batch_size = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`in batch size` must be a number");
    zsock_set_in_batch_size (zsock->self, (int) in_batch_size);
}

NAN_METHOD (Zsock::_socks_password) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_socks_password (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_socks_password) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *socks_password;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `socks password`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`socks password` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String socks_password_utf8 (info [0].As<String>());
    socks_password = *socks_password_utf8;
         //} //bjornw end
    zsock_set_socks_password (zsock->self, (const char *)socks_password);
}

NAN_METHOD (Zsock::_socks_username) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_socks_username (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_socks_username) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *socks_username;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `socks username`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`socks username` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String socks_username_utf8 (info [0].As<String>());
    socks_username = *socks_username_utf8;
         //} //bjornw end
    zsock_set_socks_username (zsock->self, (const char *)socks_username);
}

NAN_METHOD (Zsock::_set_xpub_manual_last_value) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `xpub manual last value`");

    //int xpub_manual_last_value; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int xpub_manual_last_value;


    if (info [0]->IsNumber ())
    {
          xpub_manual_last_value = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`xpub manual last value` must be a number");
    zsock_set_xpub_manual_last_value (zsock->self, (int) xpub_manual_last_value);
}

NAN_METHOD (Zsock::_router_notify) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_router_notify (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_router_notify) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `router notify`");

    //int router_notify; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int router_notify;


    if (info [0]->IsNumber ())
    {
          router_notify = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`router notify` must be a number");
    zsock_set_router_notify (zsock->self, (int) router_notify);
}

NAN_METHOD (Zsock::_multicast_loop) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_multicast_loop (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_multicast_loop) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `multicast loop`");

    //int multicast_loop; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int multicast_loop;


    if (info [0]->IsNumber ())
    {
          multicast_loop = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`multicast loop` must be a number");
    zsock_set_multicast_loop (zsock->self, (int) multicast_loop);
}

NAN_METHOD (Zsock::_metadata) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_metadata (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_metadata) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *metadata;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `metadata`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`metadata` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String metadata_utf8 (info [0].As<String>());
    metadata = *metadata_utf8;
         //} //bjornw end
    zsock_set_metadata (zsock->self, (const char *)metadata);
}

NAN_METHOD (Zsock::_loopback_fastpath) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_loopback_fastpath (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_loopback_fastpath) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `loopback fastpath`");

    //int loopback_fastpath; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int loopback_fastpath;


    if (info [0]->IsNumber ())
    {
          loopback_fastpath = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`loopback fastpath` must be a number");
    zsock_set_loopback_fastpath (zsock->self, (int) loopback_fastpath);
}

NAN_METHOD (Zsock::_zap_enforce_domain) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_zap_enforce_domain (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_zap_enforce_domain) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `zap enforce domain`");

    //int zap_enforce_domain; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int zap_enforce_domain;


    if (info [0]->IsNumber ())
    {
          zap_enforce_domain = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`zap enforce domain` must be a number");
    zsock_set_zap_enforce_domain (zsock->self, (int) zap_enforce_domain);
}

NAN_METHOD (Zsock::_gssapi_principal_nametype) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_gssapi_principal_nametype (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_gssapi_principal_nametype) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `gssapi principal nametype`");

    //int gssapi_principal_nametype; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int gssapi_principal_nametype;


    if (info [0]->IsNumber ())
    {
          gssapi_principal_nametype = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`gssapi principal nametype` must be a number");
    zsock_set_gssapi_principal_nametype (zsock->self, (int) gssapi_principal_nametype);
}

NAN_METHOD (Zsock::_gssapi_service_principal_nametype) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_gssapi_service_principal_nametype (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_gssapi_service_principal_nametype) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `gssapi service principal nametype`");

    //int gssapi_service_principal_nametype; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int gssapi_service_principal_nametype;


    if (info [0]->IsNumber ())
    {
          gssapi_service_principal_nametype = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`gssapi service principal nametype` must be a number");
    zsock_set_gssapi_service_principal_nametype (zsock->self, (int) gssapi_service_principal_nametype);
}

NAN_METHOD (Zsock::_bindtodevice) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_bindtodevice (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_bindtodevice) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *bindtodevice;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `bindtodevice`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`bindtodevice` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String bindtodevice_utf8 (info [0].As<String>());
    bindtodevice = *bindtodevice_utf8;
         //} //bjornw end
    zsock_set_bindtodevice (zsock->self, (const char *)bindtodevice);
}

NAN_METHOD (Zsock::_heartbeat_ivl) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_heartbeat_ivl (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_heartbeat_ivl) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `heartbeat ivl`");

    //int heartbeat_ivl; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int heartbeat_ivl;


    if (info [0]->IsNumber ())
    {
          heartbeat_ivl = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`heartbeat ivl` must be a number");
    zsock_set_heartbeat_ivl (zsock->self, (int) heartbeat_ivl);
}

NAN_METHOD (Zsock::_heartbeat_ttl) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_heartbeat_ttl (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_heartbeat_ttl) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `heartbeat ttl`");

    //int heartbeat_ttl; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int heartbeat_ttl;


    if (info [0]->IsNumber ())
    {
          heartbeat_ttl = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`heartbeat ttl` must be a number");
    zsock_set_heartbeat_ttl (zsock->self, (int) heartbeat_ttl);
}

NAN_METHOD (Zsock::_heartbeat_timeout) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_heartbeat_timeout (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_heartbeat_timeout) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `heartbeat timeout`");

    //int heartbeat_timeout; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int heartbeat_timeout;


    if (info [0]->IsNumber ())
    {
          heartbeat_timeout = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`heartbeat timeout` must be a number");
    zsock_set_heartbeat_timeout (zsock->self, (int) heartbeat_timeout);
}

NAN_METHOD (Zsock::_use_fd) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_use_fd (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_use_fd) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `use fd`");

    //int use_fd; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int use_fd;


    if (info [0]->IsNumber ())
    {
          use_fd = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`use fd` must be a number");
    zsock_set_use_fd (zsock->self, (int) use_fd);
}

NAN_METHOD (Zsock::_set_xpub_manual) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `xpub manual`");

    //int xpub_manual; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int xpub_manual;


    if (info [0]->IsNumber ())
    {
          xpub_manual = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`xpub manual` must be a number");
    zsock_set_xpub_manual (zsock->self, (int) xpub_manual);
}

NAN_METHOD (Zsock::_set_xpub_welcome_msg) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *xpub_welcome_msg;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `xpub welcome msg`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`xpub welcome msg` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String xpub_welcome_msg_utf8 (info [0].As<String>());
    xpub_welcome_msg = *xpub_welcome_msg_utf8;
         //} //bjornw end
    zsock_set_xpub_welcome_msg (zsock->self, (const char *)xpub_welcome_msg);
}

NAN_METHOD (Zsock::_set_stream_notify) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `stream notify`");

    //int stream_notify; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int stream_notify;


    if (info [0]->IsNumber ())
    {
          stream_notify = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`stream notify` must be a number");
    zsock_set_stream_notify (zsock->self, (int) stream_notify);
}

NAN_METHOD (Zsock::_invert_matching) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_invert_matching (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_invert_matching) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `invert matching`");

    //int invert_matching; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int invert_matching;


    if (info [0]->IsNumber ())
    {
          invert_matching = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`invert matching` must be a number");
    zsock_set_invert_matching (zsock->self, (int) invert_matching);
}

NAN_METHOD (Zsock::_set_xpub_verboser) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `xpub verboser`");

    //int xpub_verboser; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int xpub_verboser;


    if (info [0]->IsNumber ())
    {
          xpub_verboser = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`xpub verboser` must be a number");
    zsock_set_xpub_verboser (zsock->self, (int) xpub_verboser);
}

NAN_METHOD (Zsock::_connect_timeout) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_connect_timeout (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_connect_timeout) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `connect timeout`");

    //int connect_timeout; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int connect_timeout;


    if (info [0]->IsNumber ())
    {
          connect_timeout = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`connect timeout` must be a number");
    zsock_set_connect_timeout (zsock->self, (int) connect_timeout);
}

NAN_METHOD (Zsock::_tcp_maxrt) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_tcp_maxrt (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_tcp_maxrt) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `tcp maxrt`");

    //int tcp_maxrt; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int tcp_maxrt;


    if (info [0]->IsNumber ())
    {
          tcp_maxrt = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`tcp maxrt` must be a number");
    zsock_set_tcp_maxrt (zsock->self, (int) tcp_maxrt);
}

NAN_METHOD (Zsock::_thread_safe) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_thread_safe (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_multicast_maxtpdu) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_multicast_maxtpdu (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_multicast_maxtpdu) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `multicast maxtpdu`");

    //int multicast_maxtpdu; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int multicast_maxtpdu;


    if (info [0]->IsNumber ())
    {
          multicast_maxtpdu = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`multicast maxtpdu` must be a number");
    zsock_set_multicast_maxtpdu (zsock->self, (int) multicast_maxtpdu);
}

NAN_METHOD (Zsock::_vmci_buffer_size) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_vmci_buffer_size (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_vmci_buffer_size) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `vmci buffer size`");

    //int vmci_buffer_size; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int vmci_buffer_size;


    if (info [0]->IsNumber ())
    {
          vmci_buffer_size = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`vmci buffer size` must be a number");
    zsock_set_vmci_buffer_size (zsock->self, (int) vmci_buffer_size);
}

NAN_METHOD (Zsock::_vmci_buffer_min_size) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_vmci_buffer_min_size (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_vmci_buffer_min_size) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `vmci buffer min size`");

    //int vmci_buffer_min_size; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int vmci_buffer_min_size;


    if (info [0]->IsNumber ())
    {
          vmci_buffer_min_size = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`vmci buffer min size` must be a number");
    zsock_set_vmci_buffer_min_size (zsock->self, (int) vmci_buffer_min_size);
}

NAN_METHOD (Zsock::_vmci_buffer_max_size) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_vmci_buffer_max_size (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_vmci_buffer_max_size) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `vmci buffer max size`");

    //int vmci_buffer_max_size; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int vmci_buffer_max_size;


    if (info [0]->IsNumber ())
    {
          vmci_buffer_max_size = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`vmci buffer max size` must be a number");
    zsock_set_vmci_buffer_max_size (zsock->self, (int) vmci_buffer_max_size);
}

NAN_METHOD (Zsock::_vmci_connect_timeout) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_vmci_connect_timeout (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_vmci_connect_timeout) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `vmci connect timeout`");

    //int vmci_connect_timeout; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int vmci_connect_timeout;


    if (info [0]->IsNumber ())
    {
          vmci_connect_timeout = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`vmci connect timeout` must be a number");
    zsock_set_vmci_connect_timeout (zsock->self, (int) vmci_connect_timeout);
}

NAN_METHOD (Zsock::_tos) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_tos (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_tos) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `tos`");

    //int tos; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int tos;


    if (info [0]->IsNumber ())
    {
          tos = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`tos` must be a number");
    zsock_set_tos (zsock->self, (int) tos);
}

NAN_METHOD (Zsock::_set_router_handover) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `router handover`");

    //int router_handover; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int router_handover;


    if (info [0]->IsNumber ())
    {
          router_handover = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`router handover` must be a number");
    zsock_set_router_handover (zsock->self, (int) router_handover);
}

NAN_METHOD (Zsock::_set_connect_rid) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *connect_rid;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `connect rid`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`connect rid` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String connect_rid_utf8 (info [0].As<String>());
    connect_rid = *connect_rid_utf8;
         //} //bjornw end
    zsock_set_connect_rid (zsock->self, (const char *)connect_rid);
}

NAN_METHOD (Zsock::_set_connect_rid_bin) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *connect_rid = (const byte *) node::Buffer::Data (buffer_node);
    zsock_set_connect_rid_bin (zsock->self, (const byte *)connect_rid);
}

NAN_METHOD (Zsock::_handshake_ivl) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_handshake_ivl (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_handshake_ivl) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `handshake ivl`");

    //int handshake_ivl; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int handshake_ivl;


    if (info [0]->IsNumber ())
    {
          handshake_ivl = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`handshake ivl` must be a number");
    zsock_set_handshake_ivl (zsock->self, (int) handshake_ivl);
}

NAN_METHOD (Zsock::_socks_proxy) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_socks_proxy (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_socks_proxy) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *socks_proxy;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `socks proxy`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`socks proxy` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String socks_proxy_utf8 (info [0].As<String>());
    socks_proxy = *socks_proxy_utf8;
         //} //bjornw end
    zsock_set_socks_proxy (zsock->self, (const char *)socks_proxy);
}

NAN_METHOD (Zsock::_set_xpub_nodrop) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `xpub nodrop`");

    //int xpub_nodrop; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int xpub_nodrop;


    if (info [0]->IsNumber ())
    {
          xpub_nodrop = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`xpub nodrop` must be a number");
    zsock_set_xpub_nodrop (zsock->self, (int) xpub_nodrop);
}

NAN_METHOD (Zsock::_set_router_mandatory) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `router mandatory`");

    //int router_mandatory; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int router_mandatory;


    if (info [0]->IsNumber ())
    {
          router_mandatory = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`router mandatory` must be a number");
    zsock_set_router_mandatory (zsock->self, (int) router_mandatory);
}

NAN_METHOD (Zsock::_set_probe_router) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `probe router`");

    //int probe_router; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int probe_router;


    if (info [0]->IsNumber ())
    {
          probe_router = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`probe router` must be a number");
    zsock_set_probe_router (zsock->self, (int) probe_router);
}

NAN_METHOD (Zsock::_set_req_relaxed) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `req relaxed`");

    //int req_relaxed; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int req_relaxed;


    if (info [0]->IsNumber ())
    {
          req_relaxed = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`req relaxed` must be a number");
    zsock_set_req_relaxed (zsock->self, (int) req_relaxed);
}

NAN_METHOD (Zsock::_set_req_correlate) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `req correlate`");

    //int req_correlate; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int req_correlate;


    if (info [0]->IsNumber ())
    {
          req_correlate = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`req correlate` must be a number");
    zsock_set_req_correlate (zsock->self, (int) req_correlate);
}

NAN_METHOD (Zsock::_set_conflate) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `conflate`");

    //int conflate; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int conflate;


    if (info [0]->IsNumber ())
    {
          conflate = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`conflate` must be a number");
    zsock_set_conflate (zsock->self, (int) conflate);
}

NAN_METHOD (Zsock::_zap_domain) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_zap_domain (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_zap_domain) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *zap_domain;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `zap domain`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`zap domain` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String zap_domain_utf8 (info [0].As<String>());
    zap_domain = *zap_domain_utf8;
         //} //bjornw end
    zsock_set_zap_domain (zsock->self, (const char *)zap_domain);
}

NAN_METHOD (Zsock::_mechanism) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_mechanism (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_plain_server) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_plain_server (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_plain_server) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `plain server`");

    //int plain_server; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int plain_server;


    if (info [0]->IsNumber ())
    {
          plain_server = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`plain server` must be a number");
    zsock_set_plain_server (zsock->self, (int) plain_server);
}

NAN_METHOD (Zsock::_plain_username) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_plain_username (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_plain_username) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *plain_username;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `plain username`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`plain username` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String plain_username_utf8 (info [0].As<String>());
    plain_username = *plain_username_utf8;
         //} //bjornw end
    zsock_set_plain_username (zsock->self, (const char *)plain_username);
}

NAN_METHOD (Zsock::_plain_password) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_plain_password (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_plain_password) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *plain_password;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `plain password`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`plain password` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String plain_password_utf8 (info [0].As<String>());
    plain_password = *plain_password_utf8;
         //} //bjornw end
    zsock_set_plain_password (zsock->self, (const char *)plain_password);
}

NAN_METHOD (Zsock::_curve_server) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_curve_server (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_curve_server) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `curve server`");

    //int curve_server; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int curve_server;


    if (info [0]->IsNumber ())
    {
          curve_server = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`curve server` must be a number");
    zsock_set_curve_server (zsock->self, (int) curve_server);
}

NAN_METHOD (Zsock::_curve_publickey) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_curve_publickey (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_curve_publickey) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *curve_publickey;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `curve publickey`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`curve publickey` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String curve_publickey_utf8 (info [0].As<String>());
    curve_publickey = *curve_publickey_utf8;
         //} //bjornw end
    zsock_set_curve_publickey (zsock->self, (const char *)curve_publickey);
}

NAN_METHOD (Zsock::_set_curve_publickey_bin) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *curve_publickey = (const byte *) node::Buffer::Data (buffer_node);
    zsock_set_curve_publickey_bin (zsock->self, (const byte *)curve_publickey);
}

NAN_METHOD (Zsock::_curve_secretkey) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_curve_secretkey (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_curve_secretkey) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *curve_secretkey;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `curve secretkey`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`curve secretkey` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String curve_secretkey_utf8 (info [0].As<String>());
    curve_secretkey = *curve_secretkey_utf8;
         //} //bjornw end
    zsock_set_curve_secretkey (zsock->self, (const char *)curve_secretkey);
}

NAN_METHOD (Zsock::_set_curve_secretkey_bin) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *curve_secretkey = (const byte *) node::Buffer::Data (buffer_node);
    zsock_set_curve_secretkey_bin (zsock->self, (const byte *)curve_secretkey);
}

NAN_METHOD (Zsock::_curve_serverkey) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_curve_serverkey (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_curve_serverkey) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *curve_serverkey;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `curve serverkey`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`curve serverkey` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String curve_serverkey_utf8 (info [0].As<String>());
    curve_serverkey = *curve_serverkey_utf8;
         //} //bjornw end
    zsock_set_curve_serverkey (zsock->self, (const char *)curve_serverkey);
}

NAN_METHOD (Zsock::_set_curve_serverkey_bin) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *curve_serverkey = (const byte *) node::Buffer::Data (buffer_node);
    zsock_set_curve_serverkey_bin (zsock->self, (const byte *)curve_serverkey);
}

NAN_METHOD (Zsock::_gssapi_server) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_gssapi_server (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_gssapi_server) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `gssapi server`");

    //int gssapi_server; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int gssapi_server;


    if (info [0]->IsNumber ())
    {
          gssapi_server = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`gssapi server` must be a number");
    zsock_set_gssapi_server (zsock->self, (int) gssapi_server);
}

NAN_METHOD (Zsock::_gssapi_plaintext) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_gssapi_plaintext (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_gssapi_plaintext) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `gssapi plaintext`");

    //int gssapi_plaintext; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int gssapi_plaintext;


    if (info [0]->IsNumber ())
    {
          gssapi_plaintext = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`gssapi plaintext` must be a number");
    zsock_set_gssapi_plaintext (zsock->self, (int) gssapi_plaintext);
}

NAN_METHOD (Zsock::_gssapi_principal) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_gssapi_principal (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_gssapi_principal) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *gssapi_principal;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `gssapi principal`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`gssapi principal` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String gssapi_principal_utf8 (info [0].As<String>());
    gssapi_principal = *gssapi_principal_utf8;
         //} //bjornw end
    zsock_set_gssapi_principal (zsock->self, (const char *)gssapi_principal);
}

NAN_METHOD (Zsock::_gssapi_service_principal) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_gssapi_service_principal (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_gssapi_service_principal) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *gssapi_service_principal;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `gssapi service principal`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`gssapi service principal` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String gssapi_service_principal_utf8 (info [0].As<String>());
    gssapi_service_principal = *gssapi_service_principal_utf8;
         //} //bjornw end
    zsock_set_gssapi_service_principal (zsock->self, (const char *)gssapi_service_principal);
}

NAN_METHOD (Zsock::_ipv6) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_ipv6 (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_ipv6) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `ipv6`");

    //int ipv6; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int ipv6;


    if (info [0]->IsNumber ())
    {
          ipv6 = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`ipv6` must be a number");
    zsock_set_ipv6 (zsock->self, (int) ipv6);
}

NAN_METHOD (Zsock::_immediate) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_immediate (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_immediate) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `immediate`");

    //int immediate; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int immediate;


    if (info [0]->IsNumber ())
    {
          immediate = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`immediate` must be a number");
    zsock_set_immediate (zsock->self, (int) immediate);
}

NAN_METHOD (Zsock::_sndhwm) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_sndhwm (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_sndhwm) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `sndhwm`");

    //int sndhwm; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int sndhwm;


    if (info [0]->IsNumber ())
    {
          sndhwm = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`sndhwm` must be a number");
    zsock_set_sndhwm (zsock->self, (int) sndhwm);
}

NAN_METHOD (Zsock::_rcvhwm) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_rcvhwm (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_rcvhwm) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `rcvhwm`");

    //int rcvhwm; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int rcvhwm;


    if (info [0]->IsNumber ())
    {
          rcvhwm = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`rcvhwm` must be a number");
    zsock_set_rcvhwm (zsock->self, (int) rcvhwm);
}

NAN_METHOD (Zsock::_maxmsgsize) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_maxmsgsize (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_maxmsgsize) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `maxmsgsize`");

    //int maxmsgsize; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int maxmsgsize;


    if (info [0]->IsNumber ())
    {
          maxmsgsize = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`maxmsgsize` must be a number");
    zsock_set_maxmsgsize (zsock->self, (int) maxmsgsize);
}

NAN_METHOD (Zsock::_multicast_hops) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_multicast_hops (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_multicast_hops) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `multicast hops`");

    //int multicast_hops; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int multicast_hops;


    if (info [0]->IsNumber ())
    {
          multicast_hops = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`multicast hops` must be a number");
    zsock_set_multicast_hops (zsock->self, (int) multicast_hops);
}

NAN_METHOD (Zsock::_set_xpub_verbose) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `xpub verbose`");

    //int xpub_verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int xpub_verbose;


    if (info [0]->IsNumber ())
    {
          xpub_verbose = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`xpub verbose` must be a number");
    zsock_set_xpub_verbose (zsock->self, (int) xpub_verbose);
}

NAN_METHOD (Zsock::_tcp_keepalive) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_tcp_keepalive (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_tcp_keepalive) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `tcp keepalive`");

    //int tcp_keepalive; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int tcp_keepalive;


    if (info [0]->IsNumber ())
    {
          tcp_keepalive = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`tcp keepalive` must be a number");
    zsock_set_tcp_keepalive (zsock->self, (int) tcp_keepalive);
}

NAN_METHOD (Zsock::_tcp_keepalive_idle) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_tcp_keepalive_idle (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_tcp_keepalive_idle) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `tcp keepalive idle`");

    //int tcp_keepalive_idle; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int tcp_keepalive_idle;


    if (info [0]->IsNumber ())
    {
          tcp_keepalive_idle = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`tcp keepalive idle` must be a number");
    zsock_set_tcp_keepalive_idle (zsock->self, (int) tcp_keepalive_idle);
}

NAN_METHOD (Zsock::_tcp_keepalive_cnt) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_tcp_keepalive_cnt (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_tcp_keepalive_cnt) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `tcp keepalive cnt`");

    //int tcp_keepalive_cnt; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int tcp_keepalive_cnt;


    if (info [0]->IsNumber ())
    {
          tcp_keepalive_cnt = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`tcp keepalive cnt` must be a number");
    zsock_set_tcp_keepalive_cnt (zsock->self, (int) tcp_keepalive_cnt);
}

NAN_METHOD (Zsock::_tcp_keepalive_intvl) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_tcp_keepalive_intvl (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_tcp_keepalive_intvl) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `tcp keepalive intvl`");

    //int tcp_keepalive_intvl; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int tcp_keepalive_intvl;


    if (info [0]->IsNumber ())
    {
          tcp_keepalive_intvl = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`tcp keepalive intvl` must be a number");
    zsock_set_tcp_keepalive_intvl (zsock->self, (int) tcp_keepalive_intvl);
}

NAN_METHOD (Zsock::_tcp_accept_filter) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_tcp_accept_filter (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_tcp_accept_filter) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *tcp_accept_filter;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `tcp accept filter`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`tcp accept filter` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String tcp_accept_filter_utf8 (info [0].As<String>());
    tcp_accept_filter = *tcp_accept_filter_utf8;
         //} //bjornw end
    zsock_set_tcp_accept_filter (zsock->self, (const char *)tcp_accept_filter);
}

NAN_METHOD (Zsock::_last_endpoint) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_last_endpoint (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_router_raw) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `router raw`");

    //int router_raw; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int router_raw;


    if (info [0]->IsNumber ())
    {
          router_raw = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`router raw` must be a number");
    zsock_set_router_raw (zsock->self, (int) router_raw);
}

NAN_METHOD (Zsock::_ipv4only) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_ipv4only (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_ipv4only) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `ipv4only`");

    //int ipv4only; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int ipv4only;


    if (info [0]->IsNumber ())
    {
          ipv4only = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`ipv4only` must be a number");
    zsock_set_ipv4only (zsock->self, (int) ipv4only);
}

NAN_METHOD (Zsock::_set_delay_attach_on_connect) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `delay attach on connect`");

    //int delay_attach_on_connect; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int delay_attach_on_connect;


    if (info [0]->IsNumber ())
    {
          delay_attach_on_connect = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`delay attach on connect` must be a number");
    zsock_set_delay_attach_on_connect (zsock->self, (int) delay_attach_on_connect);
}

NAN_METHOD (Zsock::_hwm) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_hwm (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_hwm) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `hwm`");

    //int hwm; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int hwm;


    if (info [0]->IsNumber ())
    {
          hwm = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`hwm` must be a number");
    zsock_set_hwm (zsock->self, (int) hwm);
}

NAN_METHOD (Zsock::_swap) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_swap (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_swap) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `swap`");

    //int swap; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int swap;


    if (info [0]->IsNumber ())
    {
          swap = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`swap` must be a number");
    zsock_set_swap (zsock->self, (int) swap);
}

NAN_METHOD (Zsock::_affinity) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_affinity (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_affinity) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `affinity`");

    //int affinity; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int affinity;


    if (info [0]->IsNumber ())
    {
          affinity = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`affinity` must be a number");
    zsock_set_affinity (zsock->self, (int) affinity);
}

NAN_METHOD (Zsock::_identity) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *result = (char *) zsock_identity (zsock->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsock::_set_identity) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *identity;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `identity`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`identity` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String identity_utf8 (info [0].As<String>());
    identity = *identity_utf8;
         //} //bjornw end
    zsock_set_identity (zsock->self, (const char *)identity);
}

NAN_METHOD (Zsock::_rate) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_rate (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_rate) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `rate`");

    //int rate; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int rate;


    if (info [0]->IsNumber ())
    {
          rate = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`rate` must be a number");
    zsock_set_rate (zsock->self, (int) rate);
}

NAN_METHOD (Zsock::_recovery_ivl) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_recovery_ivl (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_recovery_ivl) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `recovery ivl`");

    //int recovery_ivl; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int recovery_ivl;


    if (info [0]->IsNumber ())
    {
          recovery_ivl = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`recovery ivl` must be a number");
    zsock_set_recovery_ivl (zsock->self, (int) recovery_ivl);
}

NAN_METHOD (Zsock::_recovery_ivl_msec) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_recovery_ivl_msec (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_recovery_ivl_msec) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `recovery ivl msec`");

    //int recovery_ivl_msec; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int recovery_ivl_msec;


    if (info [0]->IsNumber ())
    {
          recovery_ivl_msec = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`recovery ivl msec` must be a number");
    zsock_set_recovery_ivl_msec (zsock->self, (int) recovery_ivl_msec);
}

NAN_METHOD (Zsock::_mcast_loop) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_mcast_loop (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_mcast_loop) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `mcast loop`");

    //int mcast_loop; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int mcast_loop;


    if (info [0]->IsNumber ())
    {
          mcast_loop = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`mcast loop` must be a number");
    zsock_set_mcast_loop (zsock->self, (int) mcast_loop);
}

NAN_METHOD (Zsock::_rcvtimeo) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_rcvtimeo (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_rcvtimeo) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `rcvtimeo`");

    //int rcvtimeo; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int rcvtimeo;


    if (info [0]->IsNumber ())
    {
          rcvtimeo = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`rcvtimeo` must be a number");
    zsock_set_rcvtimeo (zsock->self, (int) rcvtimeo);
}

NAN_METHOD (Zsock::_sndtimeo) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_sndtimeo (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_sndtimeo) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `sndtimeo`");

    //int sndtimeo; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int sndtimeo;


    if (info [0]->IsNumber ())
    {
          sndtimeo = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`sndtimeo` must be a number");
    zsock_set_sndtimeo (zsock->self, (int) sndtimeo);
}

NAN_METHOD (Zsock::_sndbuf) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_sndbuf (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_sndbuf) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `sndbuf`");

    //int sndbuf; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int sndbuf;


    if (info [0]->IsNumber ())
    {
          sndbuf = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`sndbuf` must be a number");
    zsock_set_sndbuf (zsock->self, (int) sndbuf);
}

NAN_METHOD (Zsock::_rcvbuf) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_rcvbuf (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_rcvbuf) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `rcvbuf`");

    //int rcvbuf; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int rcvbuf;


    if (info [0]->IsNumber ())
    {
          rcvbuf = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`rcvbuf` must be a number");
    zsock_set_rcvbuf (zsock->self, (int) rcvbuf);
}

NAN_METHOD (Zsock::_linger) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_linger (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_linger) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `linger`");

    //int linger; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int linger;


    if (info [0]->IsNumber ())
    {
          linger = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`linger` must be a number");
    zsock_set_linger (zsock->self, (int) linger);
}

NAN_METHOD (Zsock::_reconnect_ivl) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_reconnect_ivl (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_reconnect_ivl) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `reconnect ivl`");

    //int reconnect_ivl; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int reconnect_ivl;


    if (info [0]->IsNumber ())
    {
          reconnect_ivl = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`reconnect ivl` must be a number");
    zsock_set_reconnect_ivl (zsock->self, (int) reconnect_ivl);
}

NAN_METHOD (Zsock::_reconnect_ivl_max) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_reconnect_ivl_max (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_reconnect_ivl_max) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `reconnect ivl max`");

    //int reconnect_ivl_max; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int reconnect_ivl_max;


    if (info [0]->IsNumber ())
    {
          reconnect_ivl_max = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`reconnect ivl max` must be a number");
    zsock_set_reconnect_ivl_max (zsock->self, (int) reconnect_ivl_max);
}

NAN_METHOD (Zsock::_backlog) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_backlog (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_set_backlog) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `backlog`");

    //int backlog; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int backlog;


    if (info [0]->IsNumber ())
    {
          backlog = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`backlog` must be a number");
    zsock_set_backlog (zsock->self, (int) backlog);
}

NAN_METHOD (Zsock::_set_subscribe) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *subscribe;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `subscribe`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`subscribe` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String subscribe_utf8 (info [0].As<String>());
    subscribe = *subscribe_utf8;
         //} //bjornw end
    zsock_set_subscribe (zsock->self, (const char *)subscribe);
}

NAN_METHOD (Zsock::_set_unsubscribe) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    char *unsubscribe;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `unsubscribe`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`unsubscribe` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String unsubscribe_utf8 (info [0].As<String>());
    unsubscribe = *unsubscribe_utf8;
         //} //bjornw end
    zsock_set_unsubscribe (zsock->self, (const char *)unsubscribe);
}

NAN_METHOD (Zsock::_type) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_type (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_rcvmore) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_rcvmore (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_events) {
    Zsock *zsock = Nan::ObjectWrap::Unwrap <Zsock> (info.Holder ());
    int result = zsock_events (zsock->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsock::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zsock_test ((bool) verbose);
}

Nan::Persistent <Function> &Zsock::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zstr::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zstr").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "recv", _recv);
    Nan::SetPrototypeMethod (tpl, "recvx", _recvx);
    Nan::SetPrototypeMethod (tpl, "recvCompress", _recv_compress);
    Nan::SetPrototypeMethod (tpl, "send", _send);
    Nan::SetPrototypeMethod (tpl, "sendm", _sendm);
    Nan::SetPrototypeMethod (tpl, "sendf", _sendf);
    Nan::SetPrototypeMethod (tpl, "sendfm", _sendfm);
    Nan::SetPrototypeMethod (tpl, "sendx", _sendx);
    Nan::SetPrototypeMethod (tpl, "sendCompress", _send_compress);
    Nan::SetPrototypeMethod (tpl, "sendmCompress", _sendm_compress);
    Nan::SetPrototypeMethod (tpl, "str", _str);
    Nan::SetPrototypeMethod (tpl, "free", _free);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zstr").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zstr::Zstr () {
}

Zstr::~Zstr () {
}

NAN_METHOD (Zstr::New) {
    assert (info.IsConstructCall ());
    Zstr *zstr = new Zstr ();
    if (zstr) {
        zstr->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zstr::_recv) {
    Zsock *source = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    char *result = (char *) zstr_recv (source->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zstr::_recvx) {
    Zsock *source = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    char *string_p;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `string_p`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`string_p` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String string_p_utf8 (info [1].As<String>());
    string_p = *string_p_utf8;
         //} //bjornw end
    int result = zstr_recvx (source->self, (char **)&string_p);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zstr::_recv_compress) {
    Zsock *source = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    char *result = (char *) zstr_recv_compress (source->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zstr::_send) {
    Zsock *dest = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    char *string;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `string`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`string` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String string_utf8 (info [1].As<String>());
    string = *string_utf8;
         //} //bjornw end
    int result = zstr_send (dest->self, (const char *)string);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zstr::_sendm) {
    Zsock *dest = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    char *string;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `string`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`string` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String string_utf8 (info [1].As<String>());
    string = *string_utf8;
         //} //bjornw end
    int result = zstr_sendm (dest->self, (const char *)string);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zstr::_sendf) {
    Zsock *dest = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    char *format;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [1].As<String>());
    format = *format_utf8;
         //} //bjornw end
    int result = zstr_sendf (dest->self, "%s", format);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zstr::_sendfm) {
    Zsock *dest = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    char *format;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [1].As<String>());
    format = *format_utf8;
         //} //bjornw end
    int result = zstr_sendfm (dest->self, "%s", format);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zstr::_sendx) {
    Zsock *dest = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    char *string;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `string`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`string` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String string_utf8 (info [1].As<String>());
    string = *string_utf8;
         //} //bjornw end
    int result = zstr_sendx (dest->self, (const char *)string);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zstr::_send_compress) {
    Zsock *dest = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    char *string;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `string`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`string` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String string_utf8 (info [1].As<String>());
    string = *string_utf8;
         //} //bjornw end
    int result = zstr_send_compress (dest->self, (const char *)string);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zstr::_sendm_compress) {
    Zsock *dest = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    char *string;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `string`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`string` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String string_utf8 (info [1].As<String>());
    string = *string_utf8;
         //} //bjornw end
    int result = zstr_sendm_compress (dest->self, (const char *)string);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zstr::_str) {
    Zsock *source = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    char *result = (char *) zstr_str (source->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zstr::_free) {
    char *string_p;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `string_p`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`string_p` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String string_p_utf8 (info [0].As<String>());
    string_p = *string_p_utf8;
         //} //bjornw end
    zstr_free ((char **)&string_p);
}

NAN_METHOD (Zstr::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zstr_test ((bool) verbose);
}

Nan::Persistent <Function> &Zstr::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zsys::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zsys").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "shutdown", _shutdown);
    Nan::SetPrototypeMethod (tpl, "sockname", _sockname);
    Nan::SetPrototypeMethod (tpl, "createPipe", _create_pipe);
    Nan::SetPrototypeMethod (tpl, "handlerReset", _handler_reset);
    Nan::SetPrototypeMethod (tpl, "catchInterrupts", _catch_interrupts);
    Nan::SetPrototypeMethod (tpl, "isInterrupted", _is_interrupted);
    Nan::SetPrototypeMethod (tpl, "setInterrupted", _set_interrupted);
    Nan::SetPrototypeMethod (tpl, "fileExists", _file_exists);
    Nan::SetPrototypeMethod (tpl, "fileModified", _file_modified);
    Nan::SetPrototypeMethod (tpl, "fileMode", _file_mode);
    Nan::SetPrototypeMethod (tpl, "fileDelete", _file_delete);
    Nan::SetPrototypeMethod (tpl, "fileStable", _file_stable);
    Nan::SetPrototypeMethod (tpl, "dirCreate", _dir_create);
    Nan::SetPrototypeMethod (tpl, "dirDelete", _dir_delete);
    Nan::SetPrototypeMethod (tpl, "dirChange", _dir_change);
    Nan::SetPrototypeMethod (tpl, "fileModePrivate", _file_mode_private);
    Nan::SetPrototypeMethod (tpl, "fileModeDefault", _file_mode_default);
    Nan::SetPrototypeMethod (tpl, "version", _version);
    Nan::SetPrototypeMethod (tpl, "sprintfHint", _sprintf_hint);
    Nan::SetPrototypeMethod (tpl, "sprintf", _sprintf);
    Nan::SetPrototypeMethod (tpl, "socketError", _socket_error);
    Nan::SetPrototypeMethod (tpl, "hostname", _hostname);
    Nan::SetPrototypeMethod (tpl, "daemonize", _daemonize);
    Nan::SetPrototypeMethod (tpl, "runAs", _run_as);
    Nan::SetPrototypeMethod (tpl, "hasCurve", _has_curve);
    Nan::SetPrototypeMethod (tpl, "setIoThreads", _set_io_threads);
    Nan::SetPrototypeMethod (tpl, "setThreadSchedPolicy", _set_thread_sched_policy);
    Nan::SetPrototypeMethod (tpl, "setThreadPriority", _set_thread_priority);
    Nan::SetPrototypeMethod (tpl, "setThreadNamePrefix", _set_thread_name_prefix);
    Nan::SetPrototypeMethod (tpl, "threadNamePrefix", _thread_name_prefix);
    Nan::SetPrototypeMethod (tpl, "setThreadNamePrefixStr", _set_thread_name_prefix_str);
    Nan::SetPrototypeMethod (tpl, "threadNamePrefixStr", _thread_name_prefix_str);
    Nan::SetPrototypeMethod (tpl, "threadAffinityCpuAdd", _thread_affinity_cpu_add);
    Nan::SetPrototypeMethod (tpl, "threadAffinityCpuRemove", _thread_affinity_cpu_remove);
    Nan::SetPrototypeMethod (tpl, "setMaxSockets", _set_max_sockets);
    Nan::SetPrototypeMethod (tpl, "socketLimit", _socket_limit);
    Nan::SetPrototypeMethod (tpl, "setMaxMsgsz", _set_max_msgsz);
    Nan::SetPrototypeMethod (tpl, "maxMsgsz", _max_msgsz);
    Nan::SetPrototypeMethod (tpl, "setZeroCopyRecv", _set_zero_copy_recv);
    Nan::SetPrototypeMethod (tpl, "zeroCopyRecv", _zero_copy_recv);
    Nan::SetPrototypeMethod (tpl, "setFileStableAgeMsec", _set_file_stable_age_msec);
    Nan::SetPrototypeMethod (tpl, "fileStableAgeMsec", _file_stable_age_msec);
    Nan::SetPrototypeMethod (tpl, "setLinger", _set_linger);
    Nan::SetPrototypeMethod (tpl, "setSndhwm", _set_sndhwm);
    Nan::SetPrototypeMethod (tpl, "setRcvhwm", _set_rcvhwm);
    Nan::SetPrototypeMethod (tpl, "setPipehwm", _set_pipehwm);
    Nan::SetPrototypeMethod (tpl, "pipehwm", _pipehwm);
    Nan::SetPrototypeMethod (tpl, "setIpv6", _set_ipv6);
    Nan::SetPrototypeMethod (tpl, "ipv6", _ipv6);
    Nan::SetPrototypeMethod (tpl, "ipv6Available", _ipv6_available);
    Nan::SetPrototypeMethod (tpl, "setInterface", _set_interface);
    Nan::SetPrototypeMethod (tpl, "interface", _interface);
    Nan::SetPrototypeMethod (tpl, "setIpv6Address", _set_ipv6_address);
    Nan::SetPrototypeMethod (tpl, "ipv6Address", _ipv6_address);
    Nan::SetPrototypeMethod (tpl, "setIpv6McastAddress", _set_ipv6_mcast_address);
    Nan::SetPrototypeMethod (tpl, "ipv6McastAddress", _ipv6_mcast_address);
    Nan::SetPrototypeMethod (tpl, "setIpv4McastAddress", _set_ipv4_mcast_address);
    Nan::SetPrototypeMethod (tpl, "ipv4McastAddress", _ipv4_mcast_address);
    Nan::SetPrototypeMethod (tpl, "setAutoUseFd", _set_auto_use_fd);
    Nan::SetPrototypeMethod (tpl, "autoUseFd", _auto_use_fd);
    Nan::SetPrototypeMethod (tpl, "zprintf", _zprintf);
    Nan::SetPrototypeMethod (tpl, "zprintfError", _zprintf_error);
    Nan::SetPrototypeMethod (tpl, "zplprintf", _zplprintf);
    Nan::SetPrototypeMethod (tpl, "zplprintfError", _zplprintf_error);
    Nan::SetPrototypeMethod (tpl, "setLogident", _set_logident);
    Nan::SetPrototypeMethod (tpl, "setLogsender", _set_logsender);
    Nan::SetPrototypeMethod (tpl, "setLogsystem", _set_logsystem);
    Nan::SetPrototypeMethod (tpl, "error", _error);
    Nan::SetPrototypeMethod (tpl, "warning", _warning);
    Nan::SetPrototypeMethod (tpl, "notice", _notice);
    Nan::SetPrototypeMethod (tpl, "info", _info);
    Nan::SetPrototypeMethod (tpl, "debug", _debug);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zsys").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zsys::Zsys () {
}

Zsys::~Zsys () {
}

NAN_METHOD (Zsys::New) {
    assert (info.IsConstructCall ());
    Zsys *zsys = new Zsys ();
    if (zsys) {
        zsys->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zsys::_shutdown) {
    zsys_shutdown ();
}

NAN_METHOD (Zsys::_sockname) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `socktype`");

    //int socktype; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int socktype;


    if (info [0]->IsNumber ())
    {
          socktype = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`socktype` must be a number");
    char *result = (char *) zsys_sockname ((int) socktype);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_create_pipe) {
    Zsock *backend_p = Nan::ObjectWrap::Unwrap<Zsock>(info [0].As<Object>());
    zsock_t *result = zsys_create_pipe (&backend_p->self);
    Zsock *zsock_result = new Zsock (result);
    if (zsock_result) {
    //  Don't yet know how to return a new object
    //      zsock->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zsys::_handler_reset) {
    zsys_handler_reset ();
}

NAN_METHOD (Zsys::_catch_interrupts) {
    zsys_catch_interrupts ();
}

NAN_METHOD (Zsys::_is_interrupted) {
    bool result = zsys_is_interrupted ();
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zsys::_set_interrupted) {
    zsys_set_interrupted ();
}

NAN_METHOD (Zsys::_file_exists) {
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    bool result = zsys_file_exists ((const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zsys::_file_modified) {
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    time_t result = zsys_file_modified ((const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_file_mode) {
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    int result = zsys_file_mode ((const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_file_delete) {
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    int result = zsys_file_delete ((const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_file_stable) {
    char *filename;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `filename`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`filename` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String filename_utf8 (info [0].As<String>());
    filename = *filename_utf8;
         //} //bjornw end
    bool result = zsys_file_stable ((const char *)filename);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zsys::_dir_create) {
    char *pathname;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `pathname`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`pathname` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String pathname_utf8 (info [0].As<String>());
    pathname = *pathname_utf8;
         //} //bjornw end
    int result = zsys_dir_create ((const char *)pathname);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_dir_delete) {
    char *pathname;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `pathname`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`pathname` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String pathname_utf8 (info [0].As<String>());
    pathname = *pathname_utf8;
         //} //bjornw end
    int result = zsys_dir_delete ((const char *)pathname);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_dir_change) {
    char *pathname;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `pathname`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`pathname` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String pathname_utf8 (info [0].As<String>());
    pathname = *pathname_utf8;
         //} //bjornw end
    int result = zsys_dir_change ((const char *)pathname);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_file_mode_private) {
    zsys_file_mode_private ();
}

NAN_METHOD (Zsys::_file_mode_default) {
    zsys_file_mode_default ();
}

NAN_METHOD (Zsys::_version) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `major`");

    //int * major; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int major;


    if (info [0]->IsNumber ())
    {
          major = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`major` must be a number");
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `minor`");

    //int * minor; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int minor;


    if (info [1]->IsNumber ())
    {
          minor = Nan::To<int>(info [1]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`minor` must be a number");
    if (info [2]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `patch`");

    //int * patch; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int patch;


    if (info [2]->IsNumber ())
    {
          patch = Nan::To<int>(info [2]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`patch` must be a number");
    zsys_version ((int *) &major, (int *) &minor, (int *) &patch);
}

NAN_METHOD (Zsys::_sprintf_hint) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `hint`");

    //int hint; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int hint;


    if (info [0]->IsNumber ())
    {
          hint = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`hint` must be a number");
    char *format;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [1].As<String>());
    format = *format_utf8;
         //} //bjornw end
    char *result = (char *) zsys_sprintf_hint ((int) hint, (const char *)format);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_sprintf) {
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    char *result = (char *) zsys_sprintf ((const char *)format);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_socket_error) {
    char *reason;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `reason`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`reason` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String reason_utf8 (info [0].As<String>());
    reason = *reason_utf8;
         //} //bjornw end
    zsys_socket_error ((const char *)reason);
}

NAN_METHOD (Zsys::_hostname) {
    char *result = (char *) zsys_hostname ();
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_daemonize) {
    char *workdir;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `workdir`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`workdir` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String workdir_utf8 (info [0].As<String>());
    workdir = *workdir_utf8;
         //} //bjornw end
    int result = zsys_daemonize ((const char *)workdir);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_run_as) {
    char *lockfile;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `lockfile`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`lockfile` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String lockfile_utf8 (info [0].As<String>());
    lockfile = *lockfile_utf8;
         //} //bjornw end
    char *group;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `group`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`group` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String group_utf8 (info [1].As<String>());
    group = *group_utf8;
         //} //bjornw end
    char *user;
    if (info [2]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `user`");
    else
    if (!info [2]->IsString ())
        return Nan::ThrowTypeError ("`user` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String user_utf8 (info [2].As<String>());
    user = *user_utf8;
         //} //bjornw end
    int result = zsys_run_as ((const char *)lockfile, (const char *)group, (const char *)user);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_has_curve) {
    bool result = zsys_has_curve ();
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zsys::_set_io_threads) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `io_threads`");
    else
    if (!info [0]->IsNumber ())
        return Nan::ThrowTypeError ("`io_threads` must be a number");
    size_t io_threads = Nan::To<int64_t>(info [0]).FromJust ();
    zsys_set_io_threads ((size_t) io_threads);
}

NAN_METHOD (Zsys::_set_thread_sched_policy) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `policy`");

    //int policy; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int policy;


    if (info [0]->IsNumber ())
    {
          policy = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`policy` must be a number");
    zsys_set_thread_sched_policy ((int) policy);
}

NAN_METHOD (Zsys::_set_thread_priority) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `priority`");

    //int priority; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int priority;


    if (info [0]->IsNumber ())
    {
          priority = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`priority` must be a number");
    zsys_set_thread_priority ((int) priority);
}

NAN_METHOD (Zsys::_set_thread_name_prefix) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `prefix`");

    //int prefix; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int prefix;


    if (info [0]->IsNumber ())
    {
          prefix = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`prefix` must be a number");
    zsys_set_thread_name_prefix ((int) prefix);
}

NAN_METHOD (Zsys::_thread_name_prefix) {
    int result = zsys_thread_name_prefix ();
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_set_thread_name_prefix_str) {
    char *prefix;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `prefix`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`prefix` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String prefix_utf8 (info [0].As<String>());
    prefix = *prefix_utf8;
         //} //bjornw end
    zsys_set_thread_name_prefix_str ((const char *)prefix);
}

NAN_METHOD (Zsys::_thread_name_prefix_str) {
    char *result = (char *) zsys_thread_name_prefix_str ();
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_thread_affinity_cpu_add) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `cpu`");

    //int cpu; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int cpu;


    if (info [0]->IsNumber ())
    {
          cpu = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`cpu` must be a number");
    zsys_thread_affinity_cpu_add ((int) cpu);
}

NAN_METHOD (Zsys::_thread_affinity_cpu_remove) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `cpu`");

    //int cpu; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int cpu;


    if (info [0]->IsNumber ())
    {
          cpu = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`cpu` must be a number");
    zsys_thread_affinity_cpu_remove ((int) cpu);
}

NAN_METHOD (Zsys::_set_max_sockets) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `max sockets`");
    else
    if (!info [0]->IsNumber ())
        return Nan::ThrowTypeError ("`max sockets` must be a number");
    size_t max_sockets = Nan::To<int64_t>(info [0]).FromJust ();
    zsys_set_max_sockets ((size_t) max_sockets);
}

NAN_METHOD (Zsys::_socket_limit) {
    size_t result = zsys_socket_limit ();
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_set_max_msgsz) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `max msgsz`");

    //int max_msgsz; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int max_msgsz;


    if (info [0]->IsNumber ())
    {
          max_msgsz = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`max msgsz` must be a number");
    zsys_set_max_msgsz ((int) max_msgsz);
}

NAN_METHOD (Zsys::_max_msgsz) {
    int result = zsys_max_msgsz ();
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_set_zero_copy_recv) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `zero copy`");

    //int zero_copy; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int zero_copy;


    if (info [0]->IsNumber ())
    {
          zero_copy = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`zero copy` must be a number");
    zsys_set_zero_copy_recv ((int) zero_copy);
}

NAN_METHOD (Zsys::_zero_copy_recv) {
    int result = zsys_zero_copy_recv ();
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_set_file_stable_age_msec) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `file stable age msec`");

    //int64_t file_stable_age_msec; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int64_t file_stable_age_msec;


    if (info [0]->IsNumber ())
    {
          file_stable_age_msec = Nan::To<int64_t>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`file stable age msec` must be a number");
    zsys_set_file_stable_age_msec ((int64_t) file_stable_age_msec);
}

NAN_METHOD (Zsys::_file_stable_age_msec) {
    int64_t result = zsys_file_stable_age_msec ();
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_set_linger) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `linger`");
    else
    if (!info [0]->IsNumber ())
        return Nan::ThrowTypeError ("`linger` must be a number");
    size_t linger = Nan::To<int64_t>(info [0]).FromJust ();
    zsys_set_linger ((size_t) linger);
}

NAN_METHOD (Zsys::_set_sndhwm) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `sndhwm`");
    else
    if (!info [0]->IsNumber ())
        return Nan::ThrowTypeError ("`sndhwm` must be a number");
    size_t sndhwm = Nan::To<int64_t>(info [0]).FromJust ();
    zsys_set_sndhwm ((size_t) sndhwm);
}

NAN_METHOD (Zsys::_set_rcvhwm) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `rcvhwm`");
    else
    if (!info [0]->IsNumber ())
        return Nan::ThrowTypeError ("`rcvhwm` must be a number");
    size_t rcvhwm = Nan::To<int64_t>(info [0]).FromJust ();
    zsys_set_rcvhwm ((size_t) rcvhwm);
}

NAN_METHOD (Zsys::_set_pipehwm) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `pipehwm`");
    else
    if (!info [0]->IsNumber ())
        return Nan::ThrowTypeError ("`pipehwm` must be a number");
    size_t pipehwm = Nan::To<int64_t>(info [0]).FromJust ();
    zsys_set_pipehwm ((size_t) pipehwm);
}

NAN_METHOD (Zsys::_pipehwm) {
    size_t result = zsys_pipehwm ();
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_set_ipv6) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `ipv6`");

    //int ipv6; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int ipv6;


    if (info [0]->IsNumber ())
    {
          ipv6 = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`ipv6` must be a number");
    zsys_set_ipv6 ((int) ipv6);
}

NAN_METHOD (Zsys::_ipv6) {
    int result = zsys_ipv6 ();
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_ipv6_available) {
    bool result = zsys_ipv6_available ();
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zsys::_set_interface) {
    char *value;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `value`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`value` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String value_utf8 (info [0].As<String>());
    value = *value_utf8;
         //} //bjornw end
    zsys_set_interface ((const char *)value);
}

NAN_METHOD (Zsys::_interface) {
    char *result = (char *) zsys_interface ();
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_set_ipv6_address) {
    char *value;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `value`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`value` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String value_utf8 (info [0].As<String>());
    value = *value_utf8;
         //} //bjornw end
    zsys_set_ipv6_address ((const char *)value);
}

NAN_METHOD (Zsys::_ipv6_address) {
    char *result = (char *) zsys_ipv6_address ();
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_set_ipv6_mcast_address) {
    char *value;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `value`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`value` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String value_utf8 (info [0].As<String>());
    value = *value_utf8;
         //} //bjornw end
    zsys_set_ipv6_mcast_address ((const char *)value);
}

NAN_METHOD (Zsys::_ipv6_mcast_address) {
    char *result = (char *) zsys_ipv6_mcast_address ();
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_set_ipv4_mcast_address) {
    char *value;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `value`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`value` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String value_utf8 (info [0].As<String>());
    value = *value_utf8;
         //} //bjornw end
    zsys_set_ipv4_mcast_address ((const char *)value);
}

NAN_METHOD (Zsys::_ipv4_mcast_address) {
    char *result = (char *) zsys_ipv4_mcast_address ();
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_set_auto_use_fd) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `auto use fd`");

    //int auto_use_fd; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int auto_use_fd;


    if (info [0]->IsNumber ())
    {
          auto_use_fd = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`auto use fd` must be a number");
    zsys_set_auto_use_fd ((int) auto_use_fd);
}

NAN_METHOD (Zsys::_auto_use_fd) {
    int result = zsys_auto_use_fd ();
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zsys::_zprintf) {
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    Zhash *args = Nan::ObjectWrap::Unwrap<Zhash>(info [1].As<Object>());
    char *result = (char *) zsys_zprintf ((const char *)format, args->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_zprintf_error) {
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    Zhash *args = Nan::ObjectWrap::Unwrap<Zhash>(info [1].As<Object>());
    char *result = (char *) zsys_zprintf_error ((const char *)format, args->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_zplprintf) {
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    Zconfig *args = Nan::ObjectWrap::Unwrap<Zconfig>(info [1].As<Object>());
    char *result = (char *) zsys_zplprintf ((const char *)format, args->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_zplprintf_error) {
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    Zconfig *args = Nan::ObjectWrap::Unwrap<Zconfig>(info [1].As<Object>());
    char *result = (char *) zsys_zplprintf_error ((const char *)format, args->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zsys::_set_logident) {
    char *value;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `value`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`value` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String value_utf8 (info [0].As<String>());
    value = *value_utf8;
         //} //bjornw end
    zsys_set_logident ((const char *)value);
}

NAN_METHOD (Zsys::_set_logsender) {
    char *endpoint;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `endpoint`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`endpoint` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String endpoint_utf8 (info [0].As<String>());
    endpoint = *endpoint_utf8;
         //} //bjornw end
    zsys_set_logsender ((const char *)endpoint);
}

NAN_METHOD (Zsys::_set_logsystem) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `logsystem`");

    //bool logsystem; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool logsystem;


    if (info [0]->IsBoolean ())
    {
          logsystem = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`logsystem` must be a Boolean");
    zsys_set_logsystem ((bool) logsystem);
}

NAN_METHOD (Zsys::_error) {
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    zsys_error ((const char *)format);
}

NAN_METHOD (Zsys::_warning) {
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    zsys_warning ((const char *)format);
}

NAN_METHOD (Zsys::_notice) {
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    zsys_notice ((const char *)format);
}

NAN_METHOD (Zsys::_info) {
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    zsys_info ((const char *)format);
}

NAN_METHOD (Zsys::_debug) {
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    zsys_debug ((const char *)format);
}

NAN_METHOD (Zsys::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zsys_test ((bool) verbose);
}

Nan::Persistent <Function> &Zsys::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Ztimerset::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Ztimerset").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "cancel", _cancel);
    Nan::SetPrototypeMethod (tpl, "setInterval", _set_interval);
    Nan::SetPrototypeMethod (tpl, "reset", _reset);
    Nan::SetPrototypeMethod (tpl, "timeout", _timeout);
    Nan::SetPrototypeMethod (tpl, "execute", _execute);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Ztimerset").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Ztimerset::Ztimerset (void) {
    self = ztimerset_new ();
}

Ztimerset::Ztimerset (ztimerset_t *self_) {
    self = self_;
}

Ztimerset::~Ztimerset () {
}

NAN_METHOD (Ztimerset::New) {
    assert (info.IsConstructCall ());
    Ztimerset *ztimerset = new Ztimerset ();
    if (ztimerset) {
        ztimerset->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Ztimerset::destroy) {
    Ztimerset *ztimerset = Nan::ObjectWrap::Unwrap <Ztimerset> (info.Holder ());
    ztimerset_destroy (&ztimerset->self);
}


NAN_METHOD (Ztimerset::defined) {
    Ztimerset *ztimerset = Nan::ObjectWrap::Unwrap <Ztimerset> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (ztimerset->self != NULL));
}

NAN_METHOD (Ztimerset::_cancel) {
    Ztimerset *ztimerset = Nan::ObjectWrap::Unwrap <Ztimerset> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `timer id`");

    //int timer_id; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int timer_id;


    if (info [0]->IsNumber ())
    {
          timer_id = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`timer id` must be a number");
    int result = ztimerset_cancel (ztimerset->self, (int) timer_id);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Ztimerset::_set_interval) {
    Ztimerset *ztimerset = Nan::ObjectWrap::Unwrap <Ztimerset> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `timer id`");

    //int timer_id; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int timer_id;


    if (info [0]->IsNumber ())
    {
          timer_id = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`timer id` must be a number");
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `interval`");
    else
    if (!info [1]->IsNumber ())
        return Nan::ThrowTypeError ("`interval` must be a number");
    size_t interval = Nan::To<int64_t>(info [1]).FromJust ();
    int result = ztimerset_set_interval (ztimerset->self, (int) timer_id, (size_t) interval);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Ztimerset::_reset) {
    Ztimerset *ztimerset = Nan::ObjectWrap::Unwrap <Ztimerset> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `timer id`");

    //int timer_id; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int timer_id;


    if (info [0]->IsNumber ())
    {
          timer_id = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`timer id` must be a number");
    int result = ztimerset_reset (ztimerset->self, (int) timer_id);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Ztimerset::_timeout) {
    Ztimerset *ztimerset = Nan::ObjectWrap::Unwrap <Ztimerset> (info.Holder ());
    int result = ztimerset_timeout (ztimerset->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Ztimerset::_execute) {
    Ztimerset *ztimerset = Nan::ObjectWrap::Unwrap <Ztimerset> (info.Holder ());
    int result = ztimerset_execute (ztimerset->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Ztimerset::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    ztimerset_test ((bool) verbose);
}

Nan::Persistent <Function> &Ztimerset::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Ztrie::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Ztrie").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "removeRoute", _remove_route);
    Nan::SetPrototypeMethod (tpl, "matches", _matches);
    Nan::SetPrototypeMethod (tpl, "hitParameterCount", _hit_parameter_count);
    Nan::SetPrototypeMethod (tpl, "hitParameters", _hit_parameters);
    Nan::SetPrototypeMethod (tpl, "hitAsteriskMatch", _hit_asterisk_match);
    Nan::SetPrototypeMethod (tpl, "print", _print);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Ztrie").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Ztrie::Ztrie (char delimiter) {
    self = ztrie_new ((char) delimiter);
}

Ztrie::Ztrie (ztrie_t *self_) {
    self = self_;
}

Ztrie::~Ztrie () {
}

NAN_METHOD (Ztrie::New) {
    assert (info.IsConstructCall ());
    char delimiter;
    if (info [0]->IsUndefined ())
    {
        return Nan::ThrowTypeError ("method requires a `delimiter`");
    }
    else if (!info [0]->IsString ())
    {
        return Nan::ThrowTypeError ("`delimiter` must be a string");
    }
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String delimiter_utf8 (info [0].As<String>());

    if (strlen (*delimiter_utf8) != 1)
        return Nan::ThrowTypeError ("`delimiter` must be a single character");
    delimiter = (*delimiter_utf8) [0];
    //} // bjornw end
    Ztrie *ztrie = new Ztrie ((char) delimiter);
    if (ztrie) {
        ztrie->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Ztrie::destroy) {
    Ztrie *ztrie = Nan::ObjectWrap::Unwrap <Ztrie> (info.Holder ());
    ztrie_destroy (&ztrie->self);
}


NAN_METHOD (Ztrie::defined) {
    Ztrie *ztrie = Nan::ObjectWrap::Unwrap <Ztrie> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (ztrie->self != NULL));
}

NAN_METHOD (Ztrie::_remove_route) {
    Ztrie *ztrie = Nan::ObjectWrap::Unwrap <Ztrie> (info.Holder ());
    char *path;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `path`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`path` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String path_utf8 (info [0].As<String>());
    path = *path_utf8;
         //} //bjornw end
    int result = ztrie_remove_route (ztrie->self, (const char *)path);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Ztrie::_matches) {
    Ztrie *ztrie = Nan::ObjectWrap::Unwrap <Ztrie> (info.Holder ());
    char *path;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `path`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`path` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String path_utf8 (info [0].As<String>());
    path = *path_utf8;
         //} //bjornw end
    bool result = ztrie_matches (ztrie->self, (const char *)path);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Ztrie::_hit_parameter_count) {
    Ztrie *ztrie = Nan::ObjectWrap::Unwrap <Ztrie> (info.Holder ());
    size_t result = ztrie_hit_parameter_count (ztrie->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Ztrie::_hit_parameters) {
    Ztrie *ztrie = Nan::ObjectWrap::Unwrap <Ztrie> (info.Holder ());
    zhashx_t *result = ztrie_hit_parameters (ztrie->self);
    Zhashx *zhashx_result = new Zhashx (result);
    if (zhashx_result) {
    //  Don't yet know how to return a new object
    //      zhashx->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Ztrie::_hit_asterisk_match) {
    Ztrie *ztrie = Nan::ObjectWrap::Unwrap <Ztrie> (info.Holder ());
    char *result = (char *) ztrie_hit_asterisk_match (ztrie->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Ztrie::_print) {
    Ztrie *ztrie = Nan::ObjectWrap::Unwrap <Ztrie> (info.Holder ());
    ztrie_print (ztrie->self);
}

NAN_METHOD (Ztrie::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    ztrie_test ((bool) verbose);
}

Nan::Persistent <Function> &Ztrie::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zuuid::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zuuid").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "set", _set);
    Nan::SetPrototypeMethod (tpl, "setStr", _set_str);
    Nan::SetPrototypeMethod (tpl, "data", _data);
    Nan::SetPrototypeMethod (tpl, "size", _size);
    Nan::SetPrototypeMethod (tpl, "str", _str);
    Nan::SetPrototypeMethod (tpl, "strCanonical", _str_canonical);
    Nan::SetPrototypeMethod (tpl, "export", _export);
    Nan::SetPrototypeMethod (tpl, "eq", _eq);
    Nan::SetPrototypeMethod (tpl, "neq", _neq);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zuuid").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zuuid::Zuuid (void) {
    self = zuuid_new ();
}

Zuuid::Zuuid (zuuid_t *self_) {
    self = self_;
}

Zuuid::~Zuuid () {
}

NAN_METHOD (Zuuid::New) {
    assert (info.IsConstructCall ());
    Zuuid *zuuid = new Zuuid ();
    if (zuuid) {
        zuuid->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zuuid::destroy) {
    Zuuid *zuuid = Nan::ObjectWrap::Unwrap <Zuuid> (info.Holder ());
    zuuid_destroy (&zuuid->self);
}


NAN_METHOD (Zuuid::defined) {
    Zuuid *zuuid = Nan::ObjectWrap::Unwrap <Zuuid> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zuuid->self != NULL));
}

NAN_METHOD (Zuuid::_set) {
    Zuuid *zuuid = Nan::ObjectWrap::Unwrap <Zuuid> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *source = (const byte *) node::Buffer::Data (buffer_node);
    zuuid_set (zuuid->self, (const byte *)source);
}

NAN_METHOD (Zuuid::_set_str) {
    Zuuid *zuuid = Nan::ObjectWrap::Unwrap <Zuuid> (info.Holder ());
    char *source;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `source`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`source` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String source_utf8 (info [0].As<String>());
    source = *source_utf8;
         //} //bjornw end
    int result = zuuid_set_str (zuuid->self, (const char *)source);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zuuid::_data) {
    Zuuid *zuuid = Nan::ObjectWrap::Unwrap <Zuuid> (info.Holder ());
    const char *result = (const char *) zuuid_data (zuuid->self);
    info.GetReturnValue().Set (Nan::CopyBuffer (result, zuuid_size (zuuid->self)).ToLocalChecked ());
}

NAN_METHOD (Zuuid::_size) {
    Zuuid *zuuid = Nan::ObjectWrap::Unwrap <Zuuid> (info.Holder ());
    size_t result = zuuid_size (zuuid->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zuuid::_str) {
    Zuuid *zuuid = Nan::ObjectWrap::Unwrap <Zuuid> (info.Holder ());
    char *result = (char *) zuuid_str (zuuid->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zuuid::_str_canonical) {
    Zuuid *zuuid = Nan::ObjectWrap::Unwrap <Zuuid> (info.Holder ());
    char *result = (char *) zuuid_str_canonical (zuuid->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zuuid::_export) {
    Zuuid *zuuid = Nan::ObjectWrap::Unwrap <Zuuid> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *target = (const byte *) node::Buffer::Data (buffer_node);
    zuuid_export (zuuid->self, (byte *)target);
}

NAN_METHOD (Zuuid::_eq) {
    Zuuid *zuuid = Nan::ObjectWrap::Unwrap <Zuuid> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *compare = (const byte *) node::Buffer::Data (buffer_node);
    bool result = zuuid_eq (zuuid->self, (const byte *)compare);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zuuid::_neq) {
    Zuuid *zuuid = Nan::ObjectWrap::Unwrap <Zuuid> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a argument to provide data");
    Local<Object> buffer_node = info [0].As<Object> ();
    const byte *compare = (const byte *) node::Buffer::Data (buffer_node);
    bool result = zuuid_neq (zuuid->self, (const byte *)compare);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (Zuuid::_dup) {
    Zuuid *zuuid = Nan::ObjectWrap::Unwrap <Zuuid> (info.Holder ());
    zuuid_t *result = zuuid_dup (zuuid->self);
    Zuuid *zuuid_result = new Zuuid (result);
    if (zuuid_result) {
    //  Don't yet know how to return a new object
    //      zuuid->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zuuid::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zuuid_test ((bool) verbose);
}

Nan::Persistent <Function> &Zuuid::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (ZhttpClient::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("ZhttpClient").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("ZhttpClient").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

ZhttpClient::ZhttpClient (bool verbose) {
    self = zhttp_client_new ((bool) verbose);
}

ZhttpClient::ZhttpClient (zhttp_client_t *self_) {
    self = self_;
}

ZhttpClient::~ZhttpClient () {
}

NAN_METHOD (ZhttpClient::New) {
    assert (info.IsConstructCall ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    ZhttpClient *zhttp_client = new ZhttpClient ((bool) verbose);
    if (zhttp_client) {
        zhttp_client->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (ZhttpClient::destroy) {
    ZhttpClient *zhttp_client = Nan::ObjectWrap::Unwrap <ZhttpClient> (info.Holder ());
    zhttp_client_destroy (&zhttp_client->self);
}


NAN_METHOD (ZhttpClient::defined) {
    ZhttpClient *zhttp_client = Nan::ObjectWrap::Unwrap <ZhttpClient> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zhttp_client->self != NULL));
}

NAN_METHOD (ZhttpClient::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zhttp_client_test ((bool) verbose);
}

Nan::Persistent <Function> &ZhttpClient::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (ZhttpServer::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("ZhttpServer").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "port", _port);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("ZhttpServer").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

ZhttpServer::ZhttpServer (zhttp_server_options_t *options) {
    self = zhttp_server_new (options);
}

ZhttpServer::ZhttpServer (zhttp_server_t *self_) {
    self = self_;
}

ZhttpServer::~ZhttpServer () {
}

NAN_METHOD (ZhttpServer::New) {
    assert (info.IsConstructCall ());
    ZhttpServerOptions *options = Nan::ObjectWrap::Unwrap<ZhttpServerOptions>(info [0].As<Object>());
    ZhttpServer *zhttp_server = new ZhttpServer (options->self);
    if (zhttp_server) {
        zhttp_server->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (ZhttpServer::destroy) {
    ZhttpServer *zhttp_server = Nan::ObjectWrap::Unwrap <ZhttpServer> (info.Holder ());
    zhttp_server_destroy (&zhttp_server->self);
}


NAN_METHOD (ZhttpServer::defined) {
    ZhttpServer *zhttp_server = Nan::ObjectWrap::Unwrap <ZhttpServer> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zhttp_server->self != NULL));
}

NAN_METHOD (ZhttpServer::_port) {
    ZhttpServer *zhttp_server = Nan::ObjectWrap::Unwrap <ZhttpServer> (info.Holder ());
    int result = zhttp_server_port (zhttp_server->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (ZhttpServer::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zhttp_server_test ((bool) verbose);
}

Nan::Persistent <Function> &ZhttpServer::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (ZhttpServerOptions::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("ZhttpServerOptions").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "port", _port);
    Nan::SetPrototypeMethod (tpl, "setPort", _set_port);
    Nan::SetPrototypeMethod (tpl, "backendAddress", _backend_address);
    Nan::SetPrototypeMethod (tpl, "setBackendAddress", _set_backend_address);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("ZhttpServerOptions").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

ZhttpServerOptions::ZhttpServerOptions (void) {
    self = zhttp_server_options_new ();
}

ZhttpServerOptions::ZhttpServerOptions (zhttp_server_options_t *self_) {
    self = self_;
}

ZhttpServerOptions::~ZhttpServerOptions () {
}

NAN_METHOD (ZhttpServerOptions::New) {
    assert (info.IsConstructCall ());
    ZhttpServerOptions *zhttp_server_options = new ZhttpServerOptions ();
    if (zhttp_server_options) {
        zhttp_server_options->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (ZhttpServerOptions::destroy) {
    ZhttpServerOptions *zhttp_server_options = Nan::ObjectWrap::Unwrap <ZhttpServerOptions> (info.Holder ());
    zhttp_server_options_destroy (&zhttp_server_options->self);
}


NAN_METHOD (ZhttpServerOptions::defined) {
    ZhttpServerOptions *zhttp_server_options = Nan::ObjectWrap::Unwrap <ZhttpServerOptions> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zhttp_server_options->self != NULL));
}

NAN_METHOD (ZhttpServerOptions::_port) {
    ZhttpServerOptions *zhttp_server_options = Nan::ObjectWrap::Unwrap <ZhttpServerOptions> (info.Holder ());
    int result = zhttp_server_options_port (zhttp_server_options->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (ZhttpServerOptions::_set_port) {
    ZhttpServerOptions *zhttp_server_options = Nan::ObjectWrap::Unwrap <ZhttpServerOptions> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `port`");

    //int port; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int port;


    if (info [0]->IsNumber ())
    {
          port = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`port` must be a number");
    zhttp_server_options_set_port (zhttp_server_options->self, (int) port);
}

NAN_METHOD (ZhttpServerOptions::_backend_address) {
    ZhttpServerOptions *zhttp_server_options = Nan::ObjectWrap::Unwrap <ZhttpServerOptions> (info.Holder ());
    char *result = (char *) zhttp_server_options_backend_address (zhttp_server_options->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (ZhttpServerOptions::_set_backend_address) {
    ZhttpServerOptions *zhttp_server_options = Nan::ObjectWrap::Unwrap <ZhttpServerOptions> (info.Holder ());
    char *address;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `address`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`address` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String address_utf8 (info [0].As<String>());
    address = *address_utf8;
         //} //bjornw end
    zhttp_server_options_set_backend_address (zhttp_server_options->self, (const char *)address);
}

NAN_METHOD (ZhttpServerOptions::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zhttp_server_options_test ((bool) verbose);
}

Nan::Persistent <Function> &ZhttpServerOptions::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (ZhttpRequest::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("ZhttpRequest").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "method", _method);
    Nan::SetPrototypeMethod (tpl, "setMethod", _set_method);
    Nan::SetPrototypeMethod (tpl, "url", _url);
    Nan::SetPrototypeMethod (tpl, "setUrl", _set_url);
    Nan::SetPrototypeMethod (tpl, "contentType", _content_type);
    Nan::SetPrototypeMethod (tpl, "setContentType", _set_content_type);
    Nan::SetPrototypeMethod (tpl, "contentLength", _content_length);
    Nan::SetPrototypeMethod (tpl, "headers", _headers);
    Nan::SetPrototypeMethod (tpl, "content", _content);
    Nan::SetPrototypeMethod (tpl, "getContent", _get_content);
    Nan::SetPrototypeMethod (tpl, "setContent", _set_content);
    Nan::SetPrototypeMethod (tpl, "setContentConst", _set_content_const);
    Nan::SetPrototypeMethod (tpl, "resetContent", _reset_content);
    Nan::SetPrototypeMethod (tpl, "setUsername", _set_username);
    Nan::SetPrototypeMethod (tpl, "setPassword", _set_password);
    Nan::SetPrototypeMethod (tpl, "match", _match);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("ZhttpRequest").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

ZhttpRequest::ZhttpRequest (void) {
    self = zhttp_request_new ();
}

ZhttpRequest::ZhttpRequest (zhttp_request_t *self_) {
    self = self_;
}

ZhttpRequest::~ZhttpRequest () {
}

NAN_METHOD (ZhttpRequest::New) {
    assert (info.IsConstructCall ());
    ZhttpRequest *zhttp_request = new ZhttpRequest ();
    if (zhttp_request) {
        zhttp_request->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (ZhttpRequest::destroy) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    zhttp_request_destroy (&zhttp_request->self);
}


NAN_METHOD (ZhttpRequest::defined) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zhttp_request->self != NULL));
}

NAN_METHOD (ZhttpRequest::_method) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *result = (char *) zhttp_request_method (zhttp_request->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (ZhttpRequest::_set_method) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *method;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `method`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`method` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String method_utf8 (info [0].As<String>());
    method = *method_utf8;
         //} //bjornw end
    zhttp_request_set_method (zhttp_request->self, (const char *)method);
}

NAN_METHOD (ZhttpRequest::_url) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *result = (char *) zhttp_request_url (zhttp_request->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (ZhttpRequest::_set_url) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *url;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `url`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`url` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String url_utf8 (info [0].As<String>());
    url = *url_utf8;
         //} //bjornw end
    zhttp_request_set_url (zhttp_request->self, (const char *)url);
}

NAN_METHOD (ZhttpRequest::_content_type) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *result = (char *) zhttp_request_content_type (zhttp_request->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (ZhttpRequest::_set_content_type) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *content_type;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `content type`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`content type` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String content_type_utf8 (info [0].As<String>());
    content_type = *content_type_utf8;
         //} //bjornw end
    zhttp_request_set_content_type (zhttp_request->self, (const char *)content_type);
}

NAN_METHOD (ZhttpRequest::_content_length) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    size_t result = zhttp_request_content_length (zhttp_request->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (ZhttpRequest::_headers) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    zhash_t *result = zhttp_request_headers (zhttp_request->self);
    Zhash *zhash_result = new Zhash (result);
    if (zhash_result) {
    //  Don't yet know how to return a new object
    //      zhash->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (ZhttpRequest::_content) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *result = (char *) zhttp_request_content (zhttp_request->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (ZhttpRequest::_get_content) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *result = (char *) zhttp_request_get_content (zhttp_request->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (ZhttpRequest::_set_content) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *content;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `content`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`content` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String content_utf8 (info [0].As<String>());
    content = *content_utf8;
         //} //bjornw end
    zhttp_request_set_content (zhttp_request->self, (char **)&content);
}

NAN_METHOD (ZhttpRequest::_set_content_const) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *content;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `content`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`content` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String content_utf8 (info [0].As<String>());
    content = *content_utf8;
         //} //bjornw end
    zhttp_request_set_content_const (zhttp_request->self, (const char *)content);
}

NAN_METHOD (ZhttpRequest::_reset_content) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    zhttp_request_reset_content (zhttp_request->self);
}

NAN_METHOD (ZhttpRequest::_set_username) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *username;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `username`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`username` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String username_utf8 (info [0].As<String>());
    username = *username_utf8;
         //} //bjornw end
    zhttp_request_set_username (zhttp_request->self, (const char *)username);
}

NAN_METHOD (ZhttpRequest::_set_password) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *password;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `password`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`password` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String password_utf8 (info [0].As<String>());
    password = *password_utf8;
         //} //bjornw end
    zhttp_request_set_password (zhttp_request->self, (const char *)password);
}

NAN_METHOD (ZhttpRequest::_match) {
    ZhttpRequest *zhttp_request = Nan::ObjectWrap::Unwrap <ZhttpRequest> (info.Holder ());
    char *method;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `method`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`method` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String method_utf8 (info [0].As<String>());
    method = *method_utf8;
         //} //bjornw end
    char *path;
    if (info [1]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `path`");
    else
    if (!info [1]->IsString ())
        return Nan::ThrowTypeError ("`path` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String path_utf8 (info [1].As<String>());
    path = *path_utf8;
         //} //bjornw end
    bool result = zhttp_request_match (zhttp_request->self, (const char *)method, (const char *)path);
    info.GetReturnValue ().Set (Nan::New<Boolean>(result));
}

NAN_METHOD (ZhttpRequest::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zhttp_request_test ((bool) verbose);
}

Nan::Persistent <Function> &ZhttpRequest::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (ZhttpResponse::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("ZhttpResponse").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "contentType", _content_type);
    Nan::SetPrototypeMethod (tpl, "setContentType", _set_content_type);
    Nan::SetPrototypeMethod (tpl, "statusCode", _status_code);
    Nan::SetPrototypeMethod (tpl, "setStatusCode", _set_status_code);
    Nan::SetPrototypeMethod (tpl, "headers", _headers);
    Nan::SetPrototypeMethod (tpl, "contentLength", _content_length);
    Nan::SetPrototypeMethod (tpl, "content", _content);
    Nan::SetPrototypeMethod (tpl, "getContent", _get_content);
    Nan::SetPrototypeMethod (tpl, "setContent", _set_content);
    Nan::SetPrototypeMethod (tpl, "setContentConst", _set_content_const);
    Nan::SetPrototypeMethod (tpl, "resetContent", _reset_content);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("ZhttpResponse").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

ZhttpResponse::ZhttpResponse (void) {
    self = zhttp_response_new ();
}

ZhttpResponse::ZhttpResponse (zhttp_response_t *self_) {
    self = self_;
}

ZhttpResponse::~ZhttpResponse () {
}

NAN_METHOD (ZhttpResponse::New) {
    assert (info.IsConstructCall ());
    ZhttpResponse *zhttp_response = new ZhttpResponse ();
    if (zhttp_response) {
        zhttp_response->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (ZhttpResponse::destroy) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    zhttp_response_destroy (&zhttp_response->self);
}


NAN_METHOD (ZhttpResponse::defined) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zhttp_response->self != NULL));
}

NAN_METHOD (ZhttpResponse::_content_type) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    char *result = (char *) zhttp_response_content_type (zhttp_response->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (ZhttpResponse::_set_content_type) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    char *value;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `value`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`value` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String value_utf8 (info [0].As<String>());
    value = *value_utf8;
         //} //bjornw end
    zhttp_response_set_content_type (zhttp_response->self, (const char *)value);
}

NAN_METHOD (ZhttpResponse::_status_code) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    uint32_t result = zhttp_response_status_code (zhttp_response->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (ZhttpResponse::_set_status_code) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `status_code`");

    //uint32_t status_code; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    uint32_t status_code;


    if (info [0]->IsNumber ())
    {
          status_code = Nan::To<uint32_t>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`status_code` must be a number");
    zhttp_response_set_status_code (zhttp_response->self, (uint32_t) status_code);
}

NAN_METHOD (ZhttpResponse::_headers) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    zhash_t *result = zhttp_response_headers (zhttp_response->self);
    Zhash *zhash_result = new Zhash (result);
    if (zhash_result) {
    //  Don't yet know how to return a new object
    //      zhash->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (ZhttpResponse::_content_length) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    size_t result = zhttp_response_content_length (zhttp_response->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (ZhttpResponse::_content) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    char *result = (char *) zhttp_response_content (zhttp_response->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (ZhttpResponse::_get_content) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    char *result = (char *) zhttp_response_get_content (zhttp_response->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (ZhttpResponse::_set_content) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    char *content;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `content`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`content` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String content_utf8 (info [0].As<String>());
    content = *content_utf8;
         //} //bjornw end
    zhttp_response_set_content (zhttp_response->self, (char **)&content);
}

NAN_METHOD (ZhttpResponse::_set_content_const) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    char *content;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `content`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`content` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String content_utf8 (info [0].As<String>());
    content = *content_utf8;
         //} //bjornw end
    zhttp_response_set_content_const (zhttp_response->self, (const char *)content);
}

NAN_METHOD (ZhttpResponse::_reset_content) {
    ZhttpResponse *zhttp_response = Nan::ObjectWrap::Unwrap <ZhttpResponse> (info.Holder ());
    zhttp_response_reset_content (zhttp_response->self);
}

NAN_METHOD (ZhttpResponse::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zhttp_response_test ((bool) verbose);
}

Nan::Persistent <Function> &ZhttpResponse::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


NAN_MODULE_INIT (Zosc::Init) {
    Nan::HandleScope scope;

    // Prepare constructor template
    Local <FunctionTemplate> tpl = Nan::New <FunctionTemplate> (New);
    tpl->SetClassName (Nan::New ("Zosc").ToLocalChecked ());
    tpl->InstanceTemplate ()->SetInternalFieldCount (1);

    // Prototypes
    Nan::SetPrototypeMethod (tpl, "destroy", destroy);
    Nan::SetPrototypeMethod (tpl, "defined", defined);
    Nan::SetPrototypeMethod (tpl, "size", _size);
    Nan::SetPrototypeMethod (tpl, "data", _data);
    Nan::SetPrototypeMethod (tpl, "address", _address);
    Nan::SetPrototypeMethod (tpl, "format", _format);
    Nan::SetPrototypeMethod (tpl, "append", _append);
    Nan::SetPrototypeMethod (tpl, "retr", _retr);
    Nan::SetPrototypeMethod (tpl, "dup", _dup);
    Nan::SetPrototypeMethod (tpl, "pack", _pack);
    Nan::SetPrototypeMethod (tpl, "packx", _packx);
    Nan::SetPrototypeMethod (tpl, "unpack", _unpack);
    Nan::SetPrototypeMethod (tpl, "dump", _dump);
    Nan::SetPrototypeMethod (tpl, "print", _print);
    Nan::SetPrototypeMethod (tpl, "popInt32", _pop_int32);
    Nan::SetPrototypeMethod (tpl, "popInt64", _pop_int64);
    Nan::SetPrototypeMethod (tpl, "popFloat", _pop_float);
    Nan::SetPrototypeMethod (tpl, "popDouble", _pop_double);
    Nan::SetPrototypeMethod (tpl, "popString", _pop_string);
    Nan::SetPrototypeMethod (tpl, "popChar", _pop_char);
    Nan::SetPrototypeMethod (tpl, "popBool", _pop_bool);
    Nan::SetPrototypeMethod (tpl, "popMidi", _pop_midi);
    Nan::SetPrototypeMethod (tpl, "test", _test);

    constructor ().Reset (Nan::GetFunction (tpl).ToLocalChecked ());
    Nan::Set (target, Nan::New ("Zosc").ToLocalChecked (),
    Nan::GetFunction (tpl).ToLocalChecked ());
}

Zosc::Zosc (const char *address) {
    self = zosc_new ((const char *)address);
}

Zosc::Zosc (zosc_t *self_) {
    self = self_;
}

Zosc::~Zosc () {
}

NAN_METHOD (Zosc::New) {
    assert (info.IsConstructCall ());
    char *address;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `address`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`address` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String address_utf8 (info [0].As<String>());
    address = *address_utf8;
         //} //bjornw end
    Zosc *zosc = new Zosc ((const char *)address);
    if (zosc) {
        zosc->Wrap (info.This ());
        info.GetReturnValue ().Set (info.This ());
    }
}

NAN_METHOD (Zosc::destroy) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    zosc_destroy (&zosc->self);
}


NAN_METHOD (Zosc::defined) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    info.GetReturnValue ().Set (Nan::New (zosc->self != NULL));
}

NAN_METHOD (Zosc::_size) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    size_t result = zosc_size (zosc->self);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zosc::_data) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    const char *result = (const char *) zosc_data (zosc->self);
    info.GetReturnValue().Set (Nan::CopyBuffer (result, zosc_size (zosc->self)).ToLocalChecked ());
}

NAN_METHOD (Zosc::_address) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    char *result = (char *) zosc_address (zosc->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zosc::_format) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    char *result = (char *) zosc_format (zosc->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zosc::_append) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    int result = zosc_append (zosc->self, (const char *)format);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zosc::_retr) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    char *format;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `format`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`format` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String format_utf8 (info [0].As<String>());
    format = *format_utf8;
         //} //bjornw end
    int result = zosc_retr (zosc->self, (const char *)format);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zosc::_dup) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    zosc_t *result = zosc_dup (zosc->self);
    Zosc *zosc_result = new Zosc (result);
    if (zosc_result) {
    //  Don't yet know how to return a new object
    //      zosc->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zosc::_pack) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    zframe_t *result = zosc_pack (zosc->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zosc::_packx) {
    Zosc *self_p = Nan::ObjectWrap::Unwrap<Zosc>(info [0].As<Object>());
    zframe_t *result = zosc_packx (&self_p->self);
    Zframe *zframe_result = new Zframe (result);
    if (zframe_result) {
    //  Don't yet know how to return a new object
    //      zframe->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zosc::_unpack) {
    Zframe *frame = Nan::ObjectWrap::Unwrap<Zframe>(info [0].As<Object>());
    zosc_t *result = zosc_unpack (frame->self);
    Zosc *zosc_result = new Zosc (result);
    if (zosc_result) {
    //  Don't yet know how to return a new object
    //      zosc->Wrap (info.This ());
    //      info.GetReturnValue ().Set (info.This ());
        info.GetReturnValue ().Set (Nan::New<Boolean>(true));
    }
}

NAN_METHOD (Zosc::_dump) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    char *result = (char *) zosc_dump (zosc->self);
    info.GetReturnValue ().Set (Nan::New (result).ToLocalChecked ());
}

NAN_METHOD (Zosc::_print) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    zosc_print (zosc->self);
}

NAN_METHOD (Zosc::_pop_int32) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `val`");

    //int * val; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int val;


    if (info [0]->IsNumber ())
    {
          val = Nan::To<int>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`val` must be a number");
    int result = zosc_pop_int32 (zosc->self, (int *) &val);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zosc::_pop_int64) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `val`");

    //int64_t * val; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    int64_t val;


    if (info [0]->IsNumber ())
    {
          val = Nan::To<int64_t>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`val` must be a number");
    int result = zosc_pop_int64 (zosc->self, (int64_t *) &val);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zosc::_pop_float) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `val`");

    //float * val; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    double val;


    if (info [0]->IsDouble ())
    {
          val = Nan::To<double>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`val` must be a number");
    int result = zosc_pop_float (zosc->self, (float *) &val);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zosc::_pop_double) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `val`");

    //double * val; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    double val;


    if (info [0]->IsDouble ())
    {
          val = Nan::To<double>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`val` must be a number");
    int result = zosc_pop_double (zosc->self, (double *) &val);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zosc::_pop_string) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    char *val;
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `val`");
    else
    if (!info [0]->IsString ())
        return Nan::ThrowTypeError ("`val` must be a string");
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String val_utf8 (info [0].As<String>());
    val = *val_utf8;
         //} //bjornw end
    int result = zosc_pop_string (zosc->self, (char **)&val);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zosc::_pop_char) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    char val;
    if (info [0]->IsUndefined ())
    {
        return Nan::ThrowTypeError ("method requires a `val`");
    }
    else if (!info [0]->IsString ())
    {
        return Nan::ThrowTypeError ("`val` must be a string");
    }
    //else { // bjornw: remove brackets to keep scope
    Nan::Utf8String val_utf8 (info [0].As<String>());

    if (strlen (*val_utf8) != 1)
        return Nan::ThrowTypeError ("`val` must be a single character");
    val = (*val_utf8) [0];
    //} // bjornw end
    int result = zosc_pop_char (zosc->self, (char *) &val);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zosc::_pop_bool) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `val`");

    //bool * val; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool val;


    if (info [0]->IsBoolean ())
    {
          val = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`val` must be a Boolean");
    int result = zosc_pop_bool (zosc->self, (bool *) &val);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zosc::_pop_midi) {
    Zosc *zosc = Nan::ObjectWrap::Unwrap <Zosc> (info.Holder ());
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `val`");

    //uint32_t * val; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    uint32_t val;


    if (info [0]->IsNumber ())
    {
          val = Nan::To<uint32_t>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`val` must be a number");
    int result = zosc_pop_midi (zosc->self, (uint32_t *) &val);
    info.GetReturnValue ().Set (Nan::New<Number>(result));
}

NAN_METHOD (Zosc::_test) {
    if (info [0]->IsUndefined ())
        return Nan::ThrowTypeError ("method requires a `verbose`");

    //bool verbose; // bjornw typedef - if using c_type, then you get 'int * major' but it needs to be 'int major'. later using the FromJust() returns an int
    bool verbose;


    if (info [0]->IsBoolean ())
    {
          verbose = Nan::To<bool>(info [0]).FromJust ();
    }
    else
        return Nan::ThrowTypeError ("`verbose` must be a Boolean");
    zosc_test ((bool) verbose);
}

Nan::Persistent <Function> &Zosc::constructor () {
    static Nan::Persistent <Function> my_constructor;
    return my_constructor;
}


extern "C" NAN_MODULE_INIT (czmq_initialize)
{
    Zargs::Init (target);
    Zarmour::Init (target);
    Zcert::Init (target);
    Zcertstore::Init (target);
    Zchunk::Init (target);
    Zclock::Init (target);
    Zconfig::Init (target);
    Zdigest::Init (target);
    Zdir::Init (target);
    ZdirPatch::Init (target);
    Zfile::Init (target);
    Zframe::Init (target);
    Zhash::Init (target);
    Zhashx::Init (target);
    Ziflist::Init (target);
    Zlist::Init (target);
    Zlistx::Init (target);
    Zloop::Init (target);
    Zmsg::Init (target);
    Zpoller::Init (target);
    Zproc::Init (target);
    Zsock::Init (target);
    Zstr::Init (target);
    Zsys::Init (target);
    Ztimerset::Init (target);
    Ztrie::Init (target);
    Zuuid::Init (target);
    ZhttpClient::Init (target);
    ZhttpServer::Init (target);
    ZhttpServerOptions::Init (target);
    ZhttpRequest::Init (target);
    ZhttpResponse::Init (target);
    Zosc::Init (target);
}

NODE_MODULE (czmq, czmq_initialize)
