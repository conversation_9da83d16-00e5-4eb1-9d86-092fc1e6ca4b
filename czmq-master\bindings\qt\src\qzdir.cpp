/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZdir::QZdir (zdir_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Create a new directory item that loads in the full tree of the specified
//  path, optionally located under some parent path. If parent is "-", then
//  loads only the top-level directory, and does not use parent as a path.
QZdir::QZdir (const QString &path, const QString &parent, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zdir_new (path.toUtf8().data(), parent.toUtf8().data());
}

///
//  Destroy a directory tree and all children it contains.
QZdir::~QZdir ()
{
    zdir_destroy (&self);
}

///
//  Return directory path
const QString QZdir::path ()
{
    const QString rv = QString (zdir_path (self));
    return rv;
}

///
//  Return last modification time for directory.
time_t QZdir::modified ()
{
    time_t rv = zdir_modified (self);
    return rv;
}

///
//  Return total hierarchy size, in bytes of data contained in all files
//  in the directory tree.
off_t QZdir::cursize ()
{
    off_t rv = zdir_cursize (self);
    return rv;
}

///
//  Return directory count
size_t QZdir::count ()
{
    size_t rv = zdir_count (self);
    return rv;
}

///
//  Returns a sorted list of zfile objects; Each entry in the list is a pointer
//  to a zfile_t item already allocated in the zdir tree. Do not destroy the
//  original zdir tree until you are done with this list.
QZlist * QZdir::list ()
{
    QZlist *rv = new QZlist (zdir_list (self));
    return rv;
}

///
//  Returns a sorted list of char*; Each entry in the list is a path of a file
//  or directory contained in self.
QZlist * QZdir::listPaths ()
{
    QZlist *rv = new QZlist (zdir_list_paths (self));
    return rv;
}

///
//  Remove directory, optionally including all files that it contains, at
//  all levels. If force is false, will only remove the directory if empty.
//  If force is true, will remove all files and all subdirectories.
void QZdir::remove (bool force)
{
    zdir_remove (self, force);

}

///
//  Calculate differences between two versions of a directory tree.
//  Returns a list of zdir_patch_t patches. Either older or newer may
//  be null, indicating the directory is empty/absent. If alias is set,
//  generates virtual filename (minus path, plus alias).
QZlist * QZdir::diff (QZdir *older, QZdir *newer, const QString &alias)
{
    QZlist *rv = new QZlist (zdir_diff (older->self, newer->self, alias.toUtf8().data()));
    return rv;
}

///
//  Return full contents of directory as a zdir_patch list.
QZlist * QZdir::resync (const QString &alias)
{
    QZlist *rv = new QZlist (zdir_resync (self, alias.toUtf8().data()));
    return rv;
}

///
//  Load directory cache; returns a hash table containing the SHA-1 digests
//  of every file in the tree. The cache is saved between runs in .cache.
QZhash * QZdir::cache ()
{
    QZhash *rv = new QZhash (zdir_cache (self));
    return rv;
}

///
//  Print contents of directory to open stream
void QZdir::fprint (FILE *file, int indent)
{
    zdir_fprint (self, file, indent);

}

///
//  Print contents of directory to stdout
void QZdir::print (int indent)
{
    zdir_print (self, indent);

}

///
//  Create a new zdir_watch actor instance:
//
//      zactor_t *watch = zactor_new (zdir_watch, NULL);
//
//  Destroy zdir_watch instance:
//
//      zactor_destroy (&watch);
//
//  Enable verbose logging of commands and activity:
//
//      zstr_send (watch, "VERBOSE");
//
//  Subscribe to changes to a directory path:
//
//      zsock_send (watch, "ss", "SUBSCRIBE", "directory_path");
//
//  Unsubscribe from changes to a directory path:
//
//      zsock_send (watch, "ss", "UNSUBSCRIBE", "directory_path");
//
//  Receive directory changes:
//      zsock_recv (watch, "sp", &path, &patches);
//
//      // Delete the received data.
//      free (path);
//      zlist_destroy (&patches);
void QZdir::watch (QZsock *pipe, void *unused)
{
    zdir_watch (pipe->self, unused);

}

///
//  Self test of this class.
void QZdir::test (bool verbose)
{
    zdir_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
