/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZhttpResponse::QZhttpResponse (zhttp_response_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Create a new zhttp_response.
QZhttpResponse::QZhttpResponse (QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zhttp_response_new ();
}

///
//  Destroy the zhttp_response.
QZhttpResponse::~QZhttpResponse ()
{
    zhttp_response_destroy (&self);
}

///
//  Send a response to a request.
//  Returns 0 if successful and -1 otherwise.
int QZhttpResponse::send (QZsock *sock, void **connection)
{
    int rv = zhttp_response_send (self, sock->self, connection);
    return rv;
}

///
//  Receive a response from zhttp_client.
//  On success return 0, -1 otherwise.
//
//  Recv returns the two user arguments which was provided with the request.
//  The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an argument.
int QZhttpResponse::recv (QZhttpClient *client, void **arg, void **arg2)
{
    int rv = zhttp_response_recv (self, client->self, arg, arg2);
    return rv;
}

///
//  Get the response content type
const QString QZhttpResponse::contentType ()
{
    const QString rv = QString (zhttp_response_content_type (self));
    return rv;
}

///
//  Set the content type of the response.
void QZhttpResponse::setContentType (const QString &value)
{
    zhttp_response_set_content_type (self, value.toUtf8().data());

}

///
//  Get the status code of the response.
quint32 QZhttpResponse::statusCode ()
{
    uint32_t rv = zhttp_response_status_code (self);
    return rv;
}

///
//  Set the status code of the response.
void QZhttpResponse::setStatusCode (quint32 statusCode)
{
    zhttp_response_set_status_code (self, (uint32_t) statusCode);

}

///
//  Get the headers of the response.
QZhash * QZhttpResponse::headers ()
{
    QZhash *rv = new QZhash (zhttp_response_headers (self));
    return rv;
}

///
//  Get the content length of the response
size_t QZhttpResponse::contentLength ()
{
    size_t rv = zhttp_response_content_length (self);
    return rv;
}

///
//  Get the content of the response.
const QString QZhttpResponse::content ()
{
    const QString rv = QString (zhttp_response_content (self));
    return rv;
}

///
//  Get the content of the response.
QString QZhttpResponse::getContent ()
{
    char *retStr_ = zhttp_response_get_content (self);
    QString rv = QString (retStr_);
    zstr_free (&retStr_);
    return rv;
}

///
//  Set the content of the response.
//  The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
void QZhttpResponse::setContentConst (const QString &content)
{
    zhttp_response_set_content_const (self, content.toUtf8().data());

}

///
//  Set the content to NULL
void QZhttpResponse::resetContent ()
{
    zhttp_response_reset_content (self);

}

///
//  Self test of this class.
void QZhttpResponse::test (bool verbose)
{
    zhttp_response_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
