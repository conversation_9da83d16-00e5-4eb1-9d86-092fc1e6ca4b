################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from setuptools import setup

setup(
    name = "czmq",
    version = "4.2.1",
    license = "mplv2",
    description = """Python bindings of: the high-level c binding for 0mq""",
    url = "https://github.com/zeromq/czmq",
    packages = ["czmq"],
    install_requires = [
    ],
)
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
