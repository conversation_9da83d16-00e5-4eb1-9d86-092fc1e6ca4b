/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#include <stdio.h>
#include <stdlib.h>
#include <jni.h>
#include "czmq.h"
#include "org_zeromq_czmq_ZhttpClient.h"

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_ZhttpClient__1_1new (JNIEnv *env, jclass c, j<PERSON>lean verbose)
{
    //  Disable CZMQ signal handling; allow Java to deal with it
    zsys_handler_set (NULL);
    jlong new_ = (jlong) (intptr_t) zhttp_client_new ((bool) verbose);
    return new_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpClient__1_1destroy (JNIEnv *env, jclass c, jlong self)
{
    zhttp_client_destroy ((zhttp_client_t **) &self);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpClient__1_1test (JNIEnv *env, jclass c, jboolean verbose)
{
    zhttp_client_test ((bool) verbose);
}

