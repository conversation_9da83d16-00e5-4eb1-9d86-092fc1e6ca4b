################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

module CZMQ
  module FFI

    # simple trie for tokenizable strings
    # @note This class is 100% generated using zproject.
    class Ztrie
      # Raised when one tries to use an instance of {Ztrie} after
      # the internal pointer to the native object has been nullified.
      class DestroyedError < RuntimeError; end

      # Boilerplate for self pointer, initializer, and finalizer
      class << self
        alias :__new :new
      end
      # Attaches the pointer _ptr_ to this instance and defines a finalizer for
      # it if necessary.
      # @param ptr [::FFI::Pointer]
      # @param finalize [Boolean]
      def initialize(ptr, finalize = true)
        @ptr = ptr
        if @ptr.null?
          @ptr = nil # Remove null pointers so we don't have to test for them.
        elsif finalize
          @finalizer = self.class.create_finalizer_for @ptr
          ObjectSpace.define_finalizer self, @finalizer
        end
      end
      # @param ptr [::FFI::Pointer]
      # @return [Proc]
      def self.create_finalizer_for(ptr)
        ptr_ptr = ::FFI::MemoryPointer.new :pointer

        Proc.new do
          ptr_ptr.write_pointer ptr
          ::CZMQ::FFI.ztrie_destroy ptr_ptr
        end
      end
      # @return [Boolean]
      def null?
        !@ptr or @ptr.null?
      end
      # Return internal pointer
      # @return [::FFI::Pointer]
      def __ptr
        raise DestroyedError unless @ptr
        @ptr
      end
      # So external Libraries can just pass the Object to a FFI function which expects a :pointer
      alias_method :to_ptr, :__ptr
      # Nullify internal pointer and return pointer pointer.
      # @note This detaches the current instance from the native object
      #   and thus makes it unusable.
      # @return [::FFI::MemoryPointer] the pointer pointing to a pointer
      #   pointing to the native object
      def __ptr_give_ref
        raise DestroyedError unless @ptr
        ptr_ptr = ::FFI::MemoryPointer.new :pointer
        ptr_ptr.write_pointer @ptr
        __undef_finalizer if @finalizer
        @ptr = nil
        ptr_ptr
      end
      # Undefines the finalizer for this object.
      # @note Only use this if you need to and can guarantee that the native
      #   object will be freed by other means.
      # @return [void]
      def __undef_finalizer
        ObjectSpace.undefine_finalizer self
        @finalizer = nil
      end

      # Create a new callback of the following type:
      # Callback function for ztrie_node to destroy node data.
      #     typedef void (ztrie_destroy_data_fn) (
      #         void **data);
      #
      # @note WARNING: If your Ruby code doesn't retain a reference to the
      #   FFI::Function object after passing it to a C function call,
      #   it may be garbage collected while C still holds the pointer,
      #   potentially resulting in a segmentation fault.
      def self.destroy_data_fn
        ::FFI::Function.new :void, [:pointer], blocking: true do |data|
          result = yield data
          result
        end
      end

      # Creates a new ztrie.
      # @param delimiter [::FFI::Pointer, #to_ptr]
      # @return [CZMQ::Ztrie]
      def self.new(delimiter)
        ptr = ::CZMQ::FFI.ztrie_new(delimiter)
        __new ptr
      end

      # Destroy the ztrie.
      #
      # @return [void]
      def destroy()
        return unless @ptr
        self_p = __ptr_give_ref
        result = ::CZMQ::FFI.ztrie_destroy(self_p)
        result
      end

      # Inserts a new route into the tree and attaches the data. Returns -1
      # if the route already exists, otherwise 0. This method takes ownership of
      # the provided data if a destroy_data_fn is provided.
      #
      # @param path [String, #to_s, nil]
      # @param data [::FFI::Pointer, #to_ptr]
      # @param destroy_data_fn [::FFI::Pointer, #to_ptr]
      # @return [Integer]
      def insert_route(path, data, destroy_data_fn)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ztrie_insert_route(self_p, path, data, destroy_data_fn)
        result
      end

      # Removes a route from the trie and destroys its data. Returns -1 if the
      # route does not exists, otherwise 0.
      # the start of the list call zlist_first (). Advances the cursor.
      #
      # @param path [String, #to_s, nil]
      # @return [Integer]
      def remove_route(path)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ztrie_remove_route(self_p, path)
        result
      end

      # Returns true if the path matches a route in the tree, otherwise false.
      #
      # @param path [String, #to_s, nil]
      # @return [Boolean]
      def matches(path)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ztrie_matches(self_p, path)
        result
      end

      # Returns the data of a matched route from last ztrie_matches. If the path
      # did not match, returns NULL. Do not delete the data as it's owned by
      # ztrie.
      #
      # @return [::FFI::Pointer]
      def hit_data()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ztrie_hit_data(self_p)
        result
      end

      # Returns the count of parameters that a matched route has.
      #
      # @return [Integer]
      def hit_parameter_count()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ztrie_hit_parameter_count(self_p)
        result
      end

      # Returns the parameters of a matched route with named regexes from last
      # ztrie_matches. If the path did not match or the route did not contain any
      # named regexes, returns NULL.
      #
      # @return [Zhashx]
      def hit_parameters()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ztrie_hit_parameters(self_p)
        result = Zhashx.__new result, false
        result
      end

      # Returns the asterisk matched part of a route, if there has been no match
      # or no asterisk match, returns NULL.
      #
      # @return [String]
      def hit_asterisk_match()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ztrie_hit_asterisk_match(self_p)
        result
      end

      # Print the trie
      #
      # @return [void]
      def print()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ztrie_print(self_p)
        result
      end

      # Self test of this class.
      #
      # @param verbose [Boolean]
      # @return [void]
      def self.test(verbose)
        verbose = !(0==verbose||!verbose) # boolean
        result = ::CZMQ::FFI.ztrie_test(verbose)
        result
      end
    end
  end
end

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
