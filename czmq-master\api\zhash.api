<class name = "zhash" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    generic type-free hash container (simple)

    <callback_type name = "free_fn">
        Callback function for zhash_freefn method
        <argument name = "data" type = "anything" />
    </callback_type>

    <constructor>
        Create a new, empty hash container
    </constructor>

    <destructor>
        Destroy a hash container and all items in it
    </destructor>

    <method name = "insert">
        Insert item into hash table with specified key and item.
        If key is already present returns -1 and leaves existing item unchanged
        Returns 0 on success.
        <argument name = "key" type = "string" />
        <argument name = "item" type = "anything" />
        <return type = "integer" />
    </method>

    <method name = "update">
        Update item into hash table with specified key and item.
        If key is already present, destroys old item and inserts new one.
        Use free_fn method to ensure deallocator is properly called on item.
        <argument name = "key" type = "string" />
        <argument name = "item" type = "anything" />
    </method>

    <method name = "delete">
        Remove an item specified by key from the hash table. If there was no such
        item, this function does nothing.
        <argument name = "key" type = "string" />
    </method>

    <method name = "lookup">
        Return the item at the specified key, or null
        <argument name = "key" type = "string" />
        <return type = "anything" />
    </method>

    <method name = "rename">
        Reindexes an item from an old key to a new key. If there was no such
        item, does nothing. Returns 0 if successful, else -1.
        <argument name = "old key" type = "string" />
        <argument name = "new key" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "freefn">
        Set a free function for the specified hash table item. When the item is
        destroyed, the free function, if any, is called on that item.
        Use this when hash items are dynamically allocated, to ensure that
        you don't have memory leaks. You can pass 'free' or NULL as a free_fn.
        Returns the item, or NULL if there is no such item.
        <argument name = "key" type = "string" />
        <argument name = "free_fn" type = "zhash_free_fn" callback = "1" />
        <return type = "anything" />
    </method>

    <method name = "size">
        Return the number of keys/items in the hash table
        <return type = "size" />
    </method>

    <method name = "dup">
        Make copy of hash table; if supplied table is null, returns null.
        Does not copy items themselves. Rebuilds new table so may be slow on
        very large tables. NOTE: only works with item values that are strings
        since there's no other way to know how to duplicate the item value.
        <return type = "zhash" fresh = "1" />
    </method>

    <method name = "keys">
        Return keys for items in table
        <return type = "zlist" fresh = "1" />
    </method>

    <method name = "first">
        Simple iterator; returns first item in hash table, in no given order,
        or NULL if the table is empty. This method is simpler to use than the
        foreach() method, which is deprecated. To access the key for this item
        use zhash_cursor(). NOTE: do NOT modify the table while iterating.
        <return type = "anything" />
    </method>

    <method name = "next">
        Simple iterator; returns next item in hash table, in no given order,
        or NULL if the last item was already returned. Use this together with
        zhash_first() to process all items in a hash table. If you need the
        items in sorted order, use zhash_keys() and then zlist_sort(). To
        access the key for this item use zhash_cursor(). NOTE: do NOT modify
        the table while iterating.
        <return type = "anything" />
    </method>

    <method name = "cursor">
        After a successful first/next method, returns the key for the item that
        was returned. This is a constant string that you may not modify or
        deallocate, and which lasts as long as the item in the hash. After an
        unsuccessful first/next, returns NULL.
        <return type = "string" />
    </method>

    <method name = "comment">
        Add a comment to hash table before saving to disk. You can add as many
        comment lines as you like. These comment lines are discarded when loading
        the file. If you use a null format, all comments are deleted.
        <argument name = "format" type = "format" />
    </method>

    <method name = "pack">
        Serialize hash table to a binary frame that can be sent in a message.
        The packed format is compatible with the 'dictionary' type defined in
        http://rfc.zeromq.org/spec:35/FILEMQ, and implemented by zproto:

           ; A list of name/value pairs
           dictionary      = dict-count *( dict-name dict-value )
           dict-count      = number-4
           dict-value      = longstr
           dict-name       = string

           ; Strings are always length + text contents
           longstr         = number-4 *VCHAR
           string          = number-1 *VCHAR

           ; Numbers are unsigned integers in network byte order
           number-1        = 1OCTET
           number-4        = 4OCTET

        Comments are not included in the packed data. Item values MUST be
        strings.
        <return type = "zframe" fresh = "1" />
    </method>

    <constructor name = "unpack">
        Unpack binary frame into a new hash table. Packed data must follow format
        defined by zhash_pack. Hash table is set to autofree. An empty frame
        unpacks to an empty hash table.
        <argument name = "frame" type = "zframe" />
    </constructor>

    <method name = "save">
        Save hash table to a text file in name=value format. Hash values must be
        printable strings; keys may not contain '=' character. Returns 0 if OK,
        else -1 if a file error occurred.
        <argument name = "filename" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "load">
        Load hash table from a text file in name=value format; hash table must
        already exist. Hash values must printable strings; keys may not contain
        '=' character. Returns 0 if OK, else -1 if a file was not readable.
        <argument name = "filename" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "refresh">
        When a hash table was loaded from a file by zhash_load, this method will
        reload the file if it has been modified since, and is "stable", i.e. not
        still changing. Returns 0 if OK, -1 if there was an error reloading the 
        file.
        <return type = "integer" />
    </method>

    <method name = "autofree">
        Set hash for automatic value destruction. Note that this assumes that
        values are NULL-terminated strings. Do not use with different types.
    </method>
</class>
