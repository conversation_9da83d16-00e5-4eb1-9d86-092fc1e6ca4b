<class name = "zuuid" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    UUID support class

    <constructor>
        Create a new UUID object.
    </constructor>

    <destructor>
        Destroy a specified UUID object.
    </destructor>

    <constructor name = "new from">
        Create UUID object from supplied ZUUID_LEN-octet value.
        <argument name = "source" type = "buffer" />
    </constructor>

    <method name = "set">
        Set UUID to new supplied ZUUID_LEN-octet value.
        <argument name = "source" type = "buffer" />
    </method>

    <method name = "set str">
        Set UUID to new supplied string value skipping '-' and '{' '}'
        optional delimiters. Return 0 if OK, else returns -1.
        <argument name = "source" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "data">
        Return UUID binary data.
        <return type = "buffer" size = ".size" />
    </method>

    <method name = "size">
        Return UUID binary size
        <return type = "size" />
    </method>

    <method name = "str">
        Returns UUID as string
        <return type = "string" />
    </method>

    <method name = "str canonical">
        Return UUID in the canonical string format: 8-4-4-4-12, in lower
        case. Caller does not modify or free returned value. See
        http://en.wikipedia.org/wiki/Universally_unique_identifier
        <return type = "string" />
    </method>

    <method name = "export">
        Store UUID blob in target array
        <argument name = "target" type = "buffer" mutable = "1" />
    </method>

    <method name = "eq">
        Check if UUID is same as supplied value
        <argument name = "compare" type = "buffer" />
        <return type = "boolean" />
    </method>

    <method name = "neq">
        Check if UUID is different from supplied value
        <argument name = "compare" type = "buffer" />
        <return type = "boolean" />
    </method>

    <method name = "dup">
        Make copy of UUID object; if uuid is null, or memory was exhausted,
        returns null.
        <return type = "zuuid" />
    </method>
</class>
