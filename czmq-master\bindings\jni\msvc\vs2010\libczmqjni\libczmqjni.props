<?xml version="1.0" encoding="utf-8"?>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup Label="Globals">
    <_PropertySheetDisplayName>CZMQ Common Settings</_PropertySheetDisplayName>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <RunCodeAnalysis>false</RunCodeAnalysis>
  </PropertyGroup>

  <PropertyGroup>
    <_PropertySheetDisplayName>Output Settings</_PropertySheetDisplayName>
    <OutDir>..\..\bin\$(PlatformName)\$(DebugOrRelease)\$(PlatformToolset)\$(DefaultLinkage)\</OutDir>
    <IntDir>..\..\obj\$(PlatformName)\$(DebugOrRelease)\$(PlatformToolset)\$(DefaultLinkage)\</IntDir>
    <TargetDir>$(OutDir)</TargetDir>
    <TargetName>$(TargetName)</TargetName>
    <TargetPath>$(TargetDir)$(TargetName)$(TargetExt)</TargetPath>
  </PropertyGroup>

  <!-- Configuration -->

  <ItemDefinitionGroup>
    <PreBuildEvent>
      <Command>copy ..\..\platform.h ..\..\..\..\..\include\</Command>
      <Command>..\call_javah.bat</Command>
    </PreBuildEvent>
    <ClCompile>
      <AdditionalIncludeDirectories>..\..\..\..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories>$(JAVA_HOME)\include;$(JAVA_HOME)\include\win32;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories>..\..\..\..\..\..\libzmq\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <CompileAs>CompileAsC</CompileAs>
      <DisableSpecificWarnings>%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <EnablePREfast>false</EnablePREfast>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;BASE_THREADSAFE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(ConfigurationType)' == 'StaticLibrary'">CZMQJNI_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(ConfigurationType)' == 'DynamicLibrary'">CZMQJNI_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>Rpcrt4.lib;Ws2_32.lib;Iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <!-- Pull in our own library -->
      <AdditionalDependencies>libczmq.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories Condition="$(Configuration.IndexOf('Debug')) != -1">..\..\..\..\..\..\czmq\bin\$(PlatformName)\Debug\$(PlatformToolset)\dynamic\;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalLibraryDirectories Condition="$(Configuration.IndexOf('Release')) != -1">..\..\..\..\..\..\czmq\bin\$(PlatformName)\Release\$(PlatformToolset)\dynamic\;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <!-- Pull in libzmq dependency -->
      <AdditionalDependencies>libzmq.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories Condition="$(Configuration.IndexOf('Debug')) != -1">..\..\..\..\..\..\libzmq\bin\$(PlatformName)\Debug\$(PlatformToolset)\dynamic\;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalLibraryDirectories Condition="$(Configuration.IndexOf('Release')) != -1">..\..\..\..\..\..\libzmq\bin\$(PlatformName)\Release\$(PlatformToolset)\dynamic\;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>

  <!-- Messages -->
  <Target Name="CustomInfo" BeforeTargets="PrepareForBuild">
    <Message Text="Will copy ..\..\platform.h -&gt; ..\..\..\..\..\include\platform.h" Importance="high"/>
  </Target>

  <Target Name="LinkageInfo" BeforeTargets="PrepareForBuild">
  </Target>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
</Project>
