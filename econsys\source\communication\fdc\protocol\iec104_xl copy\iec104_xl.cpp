/*-------------------------------------------------------------------------*\
|                          IEC-104 主控端规约类实现文件                   |
|                          AUTHOR  BRUCE                                  |
|                          2006-03-24                                     |
\*-------------------------------------------------------------------------*/

/*-------------------------------------------------------------------------*\
REV    DATE      WHO        COMMENT
------ --------  ---------- ------------------------------------------------
1.9	   20090806  yeluxp		规约移植到新平台，读取数据接口改变

1.8    20070111  BRUCE      修改protocolrx() 将rxsomedata放在后面处理，这样
才能使rxsomedata中判断workstate的依据有效

1.7    20070111  BRUCE      修改RxSomeData() 只有在workstate已经变成free的状态
才将t1和t3计数器清零。

1.6    20061116  BRUCE      用ChannelOpened和ChannelClosed替代ResetState函数

1.5    20060920  BRUCE      增加 ResetState()函数，重置规约状态
例如，当通道断开又重新连接后可以调用该函数。

1.4    20060918  BRUCE      修改函数 NoRxAnyData()
T1-----T1超时应在 < I桢无应答 > 或者 
< T3超时后测试连接无应答 > 后发生

1.3    20060918  BRUCE      响应对方测试报文

1.2    20060917  BRUCE      增加了发送接受计数检测，在接受I桢中如果对方的发送
序号和我们的接受序号不一致，终止链路

1.1    20060803  BRUCE      增加传送质量码（YCYXKWH）
0x80 ---- 未激活,0x40 ---- 无效, 0x20 ---- 人工

1.0    20060317  BRUCE      CREATE
\*-------------------------------------------------------------------------*/

#include "sys_utl.h"
#include "iec104_xl.h"
#include "ace/OS.h"

#include "fdc/fdcdef.h"
#include "utl/directory.h"
using namespace ECON;
using namespace FDC;

void CIEC104_XL::printHex(hUChar *data,unsigned short len,bool isRecvFlag)
{
	char tmpchr[1024];
	int t_printcout = 0;
	int ilen = len;
	
	if(isRecvFlag) sprintf(tmpchr,"recv:");
	else sprintf(tmpchr,"send:");
	
	char * pHex = tmpchr + 5; // 5=strlen(recv:)
	char * pEnd = tmpchr + 980;
	for (int i=0; i<ilen; i++) {
		sprintf(pHex, "%02hhX ", data[i]);
		pHex += 3;

		if(pHex > pEnd)	{//防止溢出 340*3=1020 <1024
			if(isRecvFlag) printlog(LOGE_IEC104_RX+m_link,"%s",tmpchr);
			else printlog(LOGE_IEC104_TX+m_link,"%s",tmpchr);
			pHex = tmpchr + 5;
		}
	}
	if(isRecvFlag) printlog(LOGE_IEC104_RX+m_link,"%s",tmpchr);
	else printlog(LOGE_IEC104_TX+m_link,"%s",tmpchr);
}

CIEC104_XL::CIEC104_XL()
{
	pInfo = NULL;
	m_pLink = NULL;
	m_pLinkInfo = NULL;
	m_pRoute = NULL;	
	m_pRouteInfo = NULL;
	m_pGroup = NULL;
	bInitAssociation = TRUE;//TRUE -- 主动发起链接激活 zwc
	m_bCanNextRoute = FALSE;
} 

CIEC104_XL::~CIEC104_XL()
{
}

// CLink::initProtocol 调用协议的 init函数 link.cpp
// 初始化发送、接收缓冲区，消息数组 protocol.cpp
bool CIEC104_XL::init(hInt32 link,CBuffer *pTxBuf,CBuffer *pRxBuf,CMessageArray *pMsgArray)
{
	bool ret = CProtocol::init(link,pTxBuf,pRxBuf,pMsgArray);
	return ret;
}

//是否打开
bool	CIEC104_XL::isOpen()	const
{
	if (!m_pLink)
		return false;

	return true;
}
//打开规约
bool	CIEC104_XL::open()
{
	ACE_OS::sleep(1);
	m_pLink = m_commInf.link( m_link );
	if( !m_pLink )
	{
		printlog(LOGE_IEC104_BASE + m_link,"取链路%d的链路信息失败,关闭规约",m_link);
		close();
		return false;
	}

	m_pLinkInfo = m_commInf.linkInfo( m_link );
	if ( !m_pLinkInfo )
	{
		printlog(LOGE_IEC104_BASE + m_link,"取链路%d的链路相关信息失败,关闭规约",m_link);
		close();
		return false;
	}
	
	if ( m_pLinkInfo->routeNum<=0)
	{
		printlog(LOGE_IEC104_BASE + m_link,"链路%d无关联路径,打开规约失败",m_link);
		close();
		return false;
	}

// 初始化 IEC104_INFO	info[FDC_ROUTE_IN_LINK];
	memset(&config,0,sizeof(IEC104_CONFIG));
	memset(buffer,0,BUFFER_SIZE);
	for(hUInt8 i=0;i<FDC_ROUTE_IN_LINK;i++)
	{
		info[i].resize();	// resize 对IEC104_INFO 初始化
		info[i].SendTimeFlag = TRUE;
		info[i].CallAllDataFlag = TRUE;
		info[i].CallAllKwhFlag = TRUE;
	}


// 设置 IEC104_CONFIG	config;
	if ( !readFeature() )
	{
		printlog(LOGE_IEC104_BASE + m_link,"IEC104,LinkNo %d,read feature config error!",m_link);
		close();
		return false;
	}

// 设置 m_curRoute，protocol.cpp	
	setCurRouteBySend();
	
	if ( !routeInit() )
	{
		printlog(LOGE_IEC104_BASE + m_link,"IEC104,LinkNo %d,初始化路径失败!",m_link);
		close();
		return false;
	}
	printlog((LOGE_IEC104_BASE + m_link),"<IEC104,LinkNo:%d> open sucess!", m_link);
	//initVectorYt();
	//initVectorYk();
	
	m_tvBegin =  ACE_OS::gettimeofday();
	m_tvKwhBegin = m_tvBegin;

	return true;
}
//关闭规约
void	CIEC104_XL::close()
{
	printlog(LOGE_IEC104_BASE + m_link,"<IEC104,LinkNo:%d> closed, reset info!", m_link);
	memset(&config,0,sizeof(IEC104_CONFIG));
	for(int i=0; i<FDC_ROUTE_IN_LINK; i++)
	{
		info[i].resize();
	}
	m_pLink = NULL;
	m_pLinkInfo = NULL;
	m_pRoute = NULL;	
	m_pRouteInfo = NULL;
	m_pGroup = NULL;
}

void	CIEC104_XL::run()
{
// 发送
	ProtocolTX();

// sleep 1ms
	ACE_OS::sleep(ACE_Time_Value(0, PROTOPROC_DELAY));

// 接收	
	ProtocolRX();

	if ( m_pRouteInfo != NULL)
		m_pRouteInfo->lastDataOkTime = (hUInt32)time(0);
}

bool	CIEC104_XL::routeInit()
{
	if(m_loopRoute<0||m_loopRoute>FDC_ROUTE_IN_LINK)
		return false;

	m_pRoute = m_commInf.route(m_curRoute);
	if (!m_pRoute) 		return false;
	m_pRouteInfo = m_commInf.routeInfo(m_curRoute);
	if (!m_pRouteInfo) 	return false;
	m_pGroup =m_commInf.group(m_pRoute->group);
	if (!m_pGroup) 		return false;
	
	pInfo = info + m_loopRoute;

	if(m_pRouteInfo->cmd.all_data == TRUE)
	{
		m_pRouteInfo->cmd.all_yc = TRUE;
		m_pRouteInfo->cmd.all_yx = TRUE;
		m_pRouteInfo->cmd.all_kwh = TRUE;
		m_pRouteInfo->cmd.soe = TRUE;
		m_pRouteInfo->cmd.all_data = FALSE;
	}
	return true;
}
void CIEC104_XL::initVectorYk()
{
	m_vectorYk.clear();
	CRdbOp rdbop(CRdbOp::Direct);
	char sqlstr[80] = {0};
	sprintf(sqlstr,"select * from %s where f_linkno=%d order by f_ykno",TABLE_YK,m_link);
	printlog(LOGE_IEC104_BASE + m_link,"%s",sqlstr);
	CDataset ds;
	int retNum = rdbop.exec( sqlstr,ds);
	printlog(LOGE_IEC104_BASE + m_link, "SQL:%s,ret=%d", sqlstr, retNum);
	if ( retNum <= 0 ) return;

	for(int i=0; i<retNum; i++) 
	{
		m_vectorYk.push_back(ds.field( i,"f_ykno").value().toInt32());
	}
}
void CIEC104_XL::initVectorYt()
{
	m_vectorYt.clear();
	CRdbOp rdbop(CRdbOp::Direct);
	char sqlstr[80] = {0};
	sprintf(sqlstr,"select * from %s where f_linkno=%d order by f_ytno",TABLE_YT,m_link);
	CDataset ds;
	int retNum = rdbop.exec( sqlstr,ds);
	printlog(LOGE_IEC104_BASE + m_link, "SQL:%s,ret=%d", sqlstr, retNum);
	if ( retNum <= 0 ) return;
	
	for(int i=0; i<retNum; i++) 
	{
		m_vectorYt.push_back(ds.field( i,"f_ytno").value().toInt32());
	}
}
bool	CIEC104_XL::readFeature()
{
	memset(&config,0,sizeof(IEC104_CONFIG));
	
	if(!featureFromdb())
		printlog(LOGE_IEC104_BASE + m_link
			, "装载 IEC104规约特征表 fdc_iec104_feature 失败 , LinkNo = %d , 特征名 = %s , 规约采用标准的默认特征"
			, m_link, m_pLink->protoFeature);

//设置初始值————————开始
//长度
	if(config.CommAddrLen == 0)	config.CommAddrLen = 2;
	if(config.ObjAddrLen == 0)	config.ObjAddrLen = 3;
	if(config.CotLen == 0)	config.CotLen = 2;
	
//起始地址
	// 遥信
	if(config.Yx_Start_Addr == 0)	config.Yx_Start_Addr = IEC104_OBJ_16_ADDR_YX_START;
	if(config.Yx_End_Addr == 0)		config.Yx_End_Addr = IEC104_OBJ_16_ADDR_YX_END;
	
	if(config.Prot_Start_Addr == 0)	config.Prot_Start_Addr = IEC104_OBJ_16_ADDR_PROT_START;
	if(config.Prot_End_Addr == 0)	config.Prot_End_Addr = IEC104_OBJ_16_ADDR_PROT_END;

	// 遥测
	if(config.Yc_Start_Addr == 0)	config.Yc_Start_Addr = IEC104_OBJ_16_ADDR_YC_START;
	if(config.Yc_End_Addr == 0)		config.Yc_End_Addr = IEC104_OBJ_16_ADDR_YC_END;

	// 参数
	if(config.Para_Start_Addr == 0)	config.Para_Start_Addr = IEC104_OBJ_16_ADDR_PARA_START;
	if(config.Para_End_Addr == 0)	config.Para_End_Addr = IEC104_OBJ_16_ADDR_PARA_END;

	// 遥控
	if(config.Yk_Start_Addr == 0)	config.Yk_Start_Addr = IEC104_OBJ_16_ADDR_YK_START;
	if(config.Yk_End_Addr == 0)		config.Yk_End_Addr = IEC104_OBJ_16_ADDR_YK_END;

	// 遥调/设点
	if(config.Set_Start_Addr == 0)	config.Set_Start_Addr = IEC104_OBJ_16_ADDR_SET_START;
	if(config.Set_End_Addr == 0)	config.Set_End_Addr = IEC104_OBJ_16_ADDR_SET_END;

	// 遥脉/电度
	if(config.Kwh_Start_Addr == 0)	config.Kwh_Start_Addr = IEC104_OBJ_16_ADDR_KWH_START;
	if(config.Kwh_End_Addr == 0)	config.Kwh_End_Addr = IEC104_OBJ_16_ADDR_KWH_END;
	
	if(config.Step_Start_Addr == 0)	config.Step_Start_Addr = IEC104_OBJ_16_ADDR_STEP_START;
	if(config.Step_End_Addr == 0)	config.Step_End_Addr = IEC104_OBJ_16_ADDR_STEP_END;
	
	if(config.Bin_Start_Addr == 0)	config.Bin_Start_Addr = IEC104_OBJ_16_ADDR_BIN_START;
	if(config.Bin_End_Addr == 0)	config.Bin_End_Addr = IEC104_OBJ_16_ADDR_BIN_END;
	
	if(config.Rtu_State_Addr == 0)	config.Rtu_State_Addr = IEC104_OBJ_16_ADDR_RTU_STATE;
	if(config.File_Start_Addr == 0)	config.File_Start_Addr = IEC104_OBJ_16_ADDR_FILE_START;
	if(config.File_End_Addr == 0)	config.File_End_Addr = IEC104_OBJ_16_ADDR_FILE_END;
	if(config.Revert_Type == 0)		config.Revert_Type = P_ME_NC_1;
	if(config.Fix_Set == 0)			config.Fix_Set = P_ME_NC_1;
	if(config.Fix_Ack == 0)			config.Fix_Ack = P_AC_NA_1;
	if(config.Fix_Start_Addr == 0)  config.Fix_Start_Addr = IEC104_OBJ_16_ADDR_FIX_START;
	if(config.Fix_End_Addr == 0)	config.Fix_End_Addr	= IEC104_OBJ_16_ADDR_FIX_END;

	if(config.DataTran_type == 0)	config.DataTran_type = IEC104_OBJ_16_TRANSTYPE;
	//文件召唤时间间隔及文件保存地址
	if(config.File_Read_Interval == 0)	config.File_Read_Interval = IEC104_OBJ_16_INTERVAL_FILE_READ;
	//if(config.File_Call_Way_Flag == 0)	config.File_Call_Way_Flag = IEC104_OBJ_16_CALLWAYFLAG;

	// 计划曲线
	if(config.Plan_Start_Addr == 0)	config.Plan_Start_Addr = IEC104_OBJ_16_ADDR_PLAN_START;
	if(config.Plan_End_Addr == 0)	config.Plan_End_Addr = IEC104_OBJ_16_ADDR_PLAN_END;

	ECON::CString filePath;

// 一般不配置，走这里
// 创建config.File_Store_Addr目录 dat/iec104file 
	filePath = config.File_Store_Addr;
	if(filePath.length() == 0)
	{
		filePath = ACE_OS::getenv("SYS_ROOT");
#ifdef WIN32
		filePath += "\\dat\\iec104file\\";
#else
		filePath += "/dat/iec104file/";
#endif
		CDirectory  dir(filePath);
		if(!dir.isExist())
			dir.mkdir(filePath);
		ACE_OS::strncpy(config.File_Store_Addr, filePath.c_str(),filePath.length() );
	}
	
	//设置初始值————————结束

//计算基本信息————————开始
	config.YcMaxNum = config.Yc_End_Addr - config.Yc_Start_Addr + 1;
	config.YxMaxNum = config.Yx_End_Addr - config.Yx_Start_Addr + 1;
	config.KwhMaxNum = config.Kwh_End_Addr - config.Kwh_Start_Addr + 1;
	config.YkMaxNum = config.Yk_End_Addr - config.Yk_Start_Addr + 1;
	
	//计算基本信息————————结束
	return	TRUE;

}
bool	CIEC104_XL::featureFromdb()
{
	if ( !m_pLink )
		return FALSE;
	
// fdc_link表配置的 f_protocol_feature字段为空
	if(m_pLink->protoFeature[0] == 0)
		return FALSE;
	
	FDC_PROTOCOL *pProtocol = m_commInf.protocol(m_pLink->protocol);

	m_pRdbOp = new CRdbOp(CRdbOp::Direct);
	if ( !m_pRdbOp )
	{
		printlog(LOGE_IEC104_BASE + m_link,"CIEC104_XL::featureFromdb() : can't new CRdbOp object,load IEC104 feature failed");
		return FALSE;
	}
	
	CSql* pSql = m_pRdbOp->createSQL();
	if ( !pSql )
	{
		printlog(LOGE_IEC104_BASE + m_link,"CIEC104_XL::featureFromdb() : m_pRdbOp->createSQL() failed");
		delete m_pRdbOp;
		return FALSE;
	}
	pSql->setOperate( CSql::OP_Select );

// 表名称：使用协议表 fdc_protocol 配置的 f_feature_Table
	pSql->setTableName(pProtocol->featureTable);
	pSql->selectAllField();

// f_feature_Table字段表 f_name = fdc_link表配置的 f_protocol_feature字段
	pSql->whereField("f_name",m_pLink->protoFeature,CSql::CP_Equal);

	printlog(LOGE_IEC104_BASE + m_link,"CIEC104_XL::featureFromdb() sql=%s", pSql->sql().c_str());
	int ret;
	CDataset ds;
	ret = m_pRdbOp->exec( *pSql,ds);

	if ( ret < 0 )
	{
		printlog(LOGE_IEC104_BASE + m_link,"CIEC104_XL::featureFromdb() : m_pRdbOp->exec( *pSql,ds) failed");
		pSql->destroy();
		delete m_pRdbOp;
		return FALSE;
	}
	else
	{
		int		value;
		char	str[32];
		//长度

		config.CommAddrLen = ds.field( 0,"comm_addr_len").value().toInt8();
		config.ObjAddrLen = ds.field( 0,"obj_addr_len").value().toInt8();
		config.CotLen = ds.field( 0,"cot_len").value().toInt8();
		//起始地址
		ACE_OS::strncpy(str,ds.field( 0,"yx_start_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Yx_Start_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"yx_end_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Yx_End_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"prot_start_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Prot_Start_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"prot_end_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Prot_End_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"yc_start_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Yc_Start_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"yc_end_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Yc_End_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"para_start_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Para_Start_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"para_end_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Para_End_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"yk_start_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Yk_Start_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"yk_end_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Yk_End_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"set_start_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Set_Start_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"set_end_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Set_End_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"kwh_start_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Kwh_Start_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"kwh_end_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Kwh_End_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"step_start_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Step_Start_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"step_end_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Step_End_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"bin_start_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Bin_Start_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"bin_end_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Bin_End_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"rtu_state_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.Rtu_State_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"file_start_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.File_Start_Addr = value;
		ACE_OS::strncpy(str,ds.field( 0,"file_end_addr").value().toString().c_str(),8);
		if(sscanf(str,"%X",&value) <= 0)	value = 0;
		config.File_End_Addr = value;

		//自动文件传输配置
		config.File_Call_Way_Flag = ds.field( 0,"file_call_way_flag").value().toInt8();
		config.File_Read_Interval = ds.field( 0,"file_read_inteval").value().toInt16();
		ACE_OS::strncpy(config.File_Store_Addr,ds.field( 0,"file_store_addr").value().toString().c_str(),FILE_STORE_ADDR_LEN);
		config.File_Dir = ds.field( 0,"file_dir").value().toInt16();
		//此节点所处的位置
		config.DataTran_type = ds.field(0,"datatran_type").value().toInt8();
		printlog(LOGE_IEC104_BASE + m_link,"装载 IEC104规约特征表 fdc_iec104_feature 成功 , LinkNo = %d , 特征名 = %s",m_link,m_pLink->protoFeature);
		pSql->destroy();
		delete m_pRdbOp;
	}
	return TRUE;

}
void	CIEC104_XL::ProtocolRX()
{
	int		ret;
	hInt16	len;
	len = length();		//CProtocol::length 接收缓冲区可用字节数

// 定时器++
	NoRxAnyData();
	
	while( len >= 6 )
	{
		get(buffer,2);			//CProtocol::get
		if(buffer[0] != 0x68)		// 启动字符 0x68
		{
			back(1);
			len = length();
			continue;
		}
		len -= 2;
		RxDataSize = buffer[1];		// APDU长度，ASDU字节数+4(控制域)

/******** 正常报文    ********/
		if(len >= RxDataSize)
		{
			msgType.value = 0;
			
		// 判断报文类型,
		// 处理 U 帧和 S 帧
			ret = SearchAPCI();

		// I帧 数据帧
			if ( ret == IEC104_APCI_I )
			{
				// 处理 I 帧
				SearchASDU();
			}
			
			else if(ret == -1)
			{
				msgType.clnbyte.err = 1;
				printlog((LOGE_IEC104_RX+m_link), "IEC104-错误帧");
				m_pRouteInfo->errFrmCnt++;
			}
			
			// bruce ++++++++++++++++++++++++++++
			RxSomeData();  
			
			// bruce ++++++++++++++++++++++++++++
			msgType.clnbyte.pars = 1;
			addMsg(&expMsg,buffer,RxDataSize+2,msgType.value);
		}

/******** 错误帧    ********/
		else	
		{
			get(buffer+2,len);//读取并丢弃
			msgType.clnbyte.err = 1;
			printlog((LOGE_IEC104_RX+m_link), "IEC104-错误帧,接收长度%d小于指定长度%d",len,(int)RxDataSize);
			expMsg = "IEC104-错误帧,接收长度:" + tValue.from( (int)len );
			expMsg += "小于指定长度";
			expMsg += tValue.from( (int)RxDataSize );
			m_pRouteInfo->errFrmCnt++;
			msgType.clnbyte.pars = 1;
			addMsg(&expMsg,buffer,len+2,msgType.value);
		}
		
		len = length();
	}

	DoneRead();			//CProtocol::DoneRead
	
}

void	CIEC104_XL::ProtocolTX()
{
// 检查超时
	CheckTimeOut();
	
	CheckTxFlag();

	switch(pInfo->LinkState)	// 链路状态
	{
/********    未连接    ********/
// 初始化为0 第一次走这里
	case	STATE_UNBOUND:
		pInfo->T4++;

		// 发送一个启动命令
		if (pInfo->WorkState != STATE_WAIT_STARTDT_CON) 
		{
// send-1 发送一个启动命令
			if(Tx_APDU_U(APCI_U_STARTDT_ACT) == TRUE)
			{
				printlog((LOGE_IEC104_TX+m_link), "Tx -----------> 启动链接<激活>");

				// 进入 等待启动链路确认 状态
				pInfo->WorkState = STATE_WAIT_STARTDT_CON;
			}
		}
		break;
		
/********    正常连接    ********/
	case	STATE_IDLE:
		{
			//处理“停止链路——激活”
			// Send_U_STOPDT_ACT_Flag 发送U帧停止链路激活标志
			if(pInfo->WorkState!=STATE_WAIT_STOPDT_CON && pInfo->Send_U_STOPDT_ACT_Flag==TRUE)
			{
				if(Tx_APDU_U(APCI_U_STOPDT_ACT) == TRUE)
				{
					pInfo->Send_U_STOPDT_ACT_Flag = FALSE;
					pInfo->WorkState = STATE_WAIT_STOPDT_CON;
					pInfo->T5 = 0;
				}
			}
			// else if(pInfo->WorkState == STATE_WAIT_YK_EXE_CON)
			// {
			// 	Tx_CtrlYkAckCmd();
			// 	pInfo->WorkState = STATE_FREE;
			// }
			// else if(pInfo->WorkState == STATE_WAIT_YT_EXE_CON)
			// {
			// 	Tx_CtrlYtAckCmd();
			// 	pInfo->WorkState = STATE_FREE;
			// }
			else	if(pInfo->WorkState == STATE_WAIT_FILE_DIR)
			{
				Tx_FileCmd();
			}
			else    if(pInfo->WorkState != STATE_FREE)//等待状态
			{
				if ( pInfo->Send_S_ACK_Flag == TRUE && pInfo->W >= 8)	
				{
					pInfo->W = 0;
					printlog((LOGE_IEC104_TX+m_link), "Tx ---------> K值越限， 发送 S 格式<ACK>");					//处理“S帧确认”
					Tx_APDU_S();
				}
				pInfo->T5 ++;
				if (pInfo->T5 >= TIMEOUT_T5)
				{
					pInfo->WorkState = STATE_FREE;
					pInfo->T5 = 0;
					break;
				}
			}

		// 空闲
			else	if(pInfo->WorkState == STATE_FREE)
			{
				//处理测试链接<确认>
				if (pInfo->Send_U_TESTFR_CON_Flag)
				{
					if(Tx_APDU_U(APCI_U_TESTFR_CON) == TRUE)
					{
						printlog((LOGE_IEC104_TX+m_link), "Tx ---------> 测试连接<确认>");
						pInfo->Send_U_TESTFR_CON_Flag = FALSE;
					}
					break;
				}
				
				//处理遥控/摇调命令
				if(Ctrl_YKYT() == TRUE)
				{	
					printlog((LOGE_IEC104_TX+m_link), "处理遥控或者摇调命令");
					break;
				}
				
				//处理“测试链路——激活”
				if(pInfo->Send_U_TESTFR_ACT_Flag == TRUE)
				{
					printlog((LOGE_IEC104_TX+m_link), "Tx ---------> 测试链接<激活>");
					if(Tx_APDU_U(APCI_U_TESTFR_ACT) == TRUE)
					{
						pInfo->Send_U_TESTFR_ACT_Flag = FALSE;
						pInfo->WorkState = STATE_WAIT_TESTDT_CON;
						pInfo->T5 = 0;
					}
				}
				//处理“S帧确认”
				else	if(pInfo->Send_S_ACK_Flag == TRUE)	
				{
					printlog((LOGE_IEC104_TX+m_link), "Tx ---------> S 格式<ACK>");
					Tx_APDU_S();
				}
//处理“数据传输”
// 发送总召唤
// send-2
// send-3
				else
				{
					Tx_DataCmd();
				}				
				break;
			}
			break;
		}
	default:
		break;
	}
}

void	CIEC104_XL::InitInfo()
{
	if (pInfo == NULL) return;

	pInfo->T1 = 0;
	pInfo->T2 = 0;
	pInfo->T3 = 0;
	pInfo->T4 = 0;
	pInfo->T5 = 0;
	pInfo->LinkState = STATE_UNBOUND;
	pInfo->WorkState = STATE_FREE;
	pInfo->NS = 0;
	pInfo->NR = 0;
	pInfo->SendTimeFlag    = TRUE;
	pInfo->CallAllDataFlag = TRUE;
	pInfo->CallAllKwhFlag =  TRUE;
	
	
	pInfo->Send_S_ACK_Flag = FALSE;
	pInfo->Send_U_TESTFR_ACT_Flag = FALSE;
	pInfo->Send_U_STOPDT_ACT_Flag = FALSE;
	pInfo->W = 0;
}
void	CIEC104_XL::CheckTimeOut()
{
// 等待启动链接确认
	if ( pInfo->WorkState==STATE_WAIT_STARTDT_CON)
	{
		if(pInfo->T4 > TIMEOUT_T4)
		{
			printlog((LOGE_IEC104_TX+m_link), "启动连接激活超时占用关闭,TIMEOUT_T0");
			pInfo->T4 = 0;
		}
		return;
	}

	if(pInfo->T1 > TIMEOUT_T1)
	{
		printlog((LOGE_IEC104_TX+m_link), "超时占用关闭, %d > TIMEOUT_T1(%d)", pInfo->T1, TIMEOUT_T1);
		InitInfo();
	}
	
	else	if(pInfo->T3 > TIMEOUT_T3 && !pInfo->Send_U_TESTFR_ACT_Flag)
	{
		printlog((LOGE_IEC104_TX+m_link), "等待双方进行数据传输定时器超时（超时启动测试过程）TIMEOUT_T3");
		pInfo->Send_U_TESTFR_ACT_Flag = TRUE;
	}
	
	else	if(pInfo->T2 > TIMEOUT_T2 && !pInfo->Send_S_ACK_Flag)
	{
		printlog((LOGE_IEC104_TX+m_link), "等待本端信息处理完成定时器超时（超时发送S帧）TIMEOUT_T2");
		pInfo->Send_S_ACK_Flag = TRUE;
	}
}
void	CIEC104_XL::CheckTxFlag()
{
	if(m_pRouteInfo->cmd.timesync == TRUE)
	{
		pInfo->SendTimeFlag = TRUE;
		m_pRouteInfo->cmd.timesync   = FALSE;
	}
	
	if(m_pRouteInfo->cmd.all_data==TRUE )
	//if(m_pRouteInfo->cmd.all_yc ==TRUE || m_pRouteInfo->cmd.all_yx==TRUE)
	{
		pInfo->CallAllDataFlag = TRUE;
		m_pRouteInfo->cmd.all_data = FALSE;
		//m_pRouteInfo->cmd.all_yc = FALSE;
		//m_pRouteInfo->cmd.all_yx = FALSE;
	}

	//m_pRouteInfo->cmd.all_kwh 在link.cpp内被注释，所以此处需要自己判断
	// CLoadPara::assignRoute loadpara.cpp
	time_t allkwh = m_pRoute->scanInterval.all_kwh.hostValue(); 
	if(allkwh > 0) 
	{
		ACE_Time_Value tv = ACE_OS::gettimeofday() - m_tvKwhBegin;
		if(tv.sec() > allkwh) {
			pInfo->CallAllKwhFlag = TRUE;
			m_tvKwhBegin = ACE_OS::gettimeofday();
		}
	}

	// if(m_pRouteInfo->cmd.all_kwh == TRUE)
	// {
	// 	pInfo->CallAllKwhFlag = TRUE;
	// 	m_pRouteInfo->cmd.all_kwh = FALSE;
	// }
	//if(m_pRouteInfo->cmd.file_call == TRUE)
	//{
	//	pInfo->CallFileFlag = TRUE;
	//	m_pRouteInfo->cmd.file_call = FALSE;
	//}
	switch(config.File_Call_Way_Flag)
	{
	case 0:
		break;
	case 1:
		tvInterval = ACE_OS::gettimeofday() - m_tvBegin;
		if (tvInterval.sec() > (config.File_Read_Interval *60) )
		{
			pInfo->CallFileFlag = TRUE;
			m_tvBegin = ACE_OS::gettimeofday();
		}
		break;
	case 2:
		GetTime( &m_curtime );
		if ( m_curtime.hour == config.File_Read_Interval/10000 &&
			 m_curtime.min == config.File_Read_Interval/100 &&
			 m_curtime.sec == config.File_Read_Interval%100
			)
		{
			pInfo->CallFileFlag = TRUE;
		}
		break;
	default:
		break;
	}

	// 发送计划曲线，289个点
	//es_strategy.cpp xd -> iec104
	if ((m_commInf.systemInfo())->loadParaFlag & 0x20000) {//暂定0x20000作为通知标志
		//收到装载jk策略标志
		(m_commInf.systemInfo())->loadParaFlag &= ~0x20000;
		pInfo->SendPlanFlag = TRUE;	
		m_planHasSendNum = 0;	
		printlog(LOGE_IEC104_BASE+m_link, "Recv plan cmd !!! now send plan data ...");
	}
	
}


// 发送个一个I帧，数据帧
bool	CIEC104_XL::Tx_APDU_I(hUInt16 len)
{
	int length = remainLength();
	
	if( length < len+6)
	{
		printlog(LOGE_IEC104_TX+m_link, "Tx_buffer_remain length=%d", length);
		return	FALSE;
	}
	//printlog(LOGE_IEC104_TX+m_link, "Tx_APDU_I NS=%d", pInfo->NS);
	//printlog(LOGE_IEC104_TX+m_link, "Tx_buffer_remain length=%d", length);
	buffer[0] = 0x68;
	buffer[1] = len + 4;
	buffer[2] = (hUChar)((pInfo->NS % 128) << 1);
	buffer[3] = (hUChar)(pInfo->NS / 128);
	buffer[4] = (hUChar)((pInfo->NR % 128) << 1);
	buffer[5] = (hUChar)(pInfo->NR / 128);
	put(buffer,len+6);		// 放到发送缓冲区
	
	printHex(buffer, len+6, false);
	pInfo->NS = (pInfo->NS + 1) % (hUInt16)0x8000;
	pInfo->Send_S_ACK_Flag = FALSE;
	pInfo->T1 = 0;
	pInfo->T2 = 0;
	pInfo->T3 = 0;

	msgType.value = 0;
	msgType.clnbyte.pars = 1;
	msgType.clnbyte.dir = 1;
	//printlog((LOGE_IEC104_TX+m_link),"-->%s",expMsg.c_str());
	addMsg(&expMsg,buffer,len+6,msgType.value);//添加报文

	return	TRUE;
}

/*
发送一个U帧
#define	APCI_U_STARTDT_ACT	0x04	//启动链路——激活
#define	APCI_U_STARTDT_CON	0x08	//启动链路——确认
#define	APCI_U_STOPDT_ACT	0x10	//停止链路——激活
#define	APCI_U_STOPDT_CON	0x20	//停止链路——确认
#define	APCI_U_TESTFR_ACT	0x40	//测试链路——激活
#define	APCI_U_TESTFR_CON	0x80	//测试链路——确认
*/
bool	CIEC104_XL::Tx_APDU_U(hUChar type)
{
	int length = remainLength();
	if( length < 6 )	return	FALSE;

// APCI 6字节	
	buffer[0] = 0x68;
	buffer[1] = 4;	// 后面需要再加上ASDU字节数
	buffer[2] = type | 0x03;
	buffer[3] = 0;
	buffer[4] = 0;
	buffer[5] = 0;
	put(buffer,6);

	pInfo->T1 = 0;
	pInfo->T3 = 0;

	expMsg = "U格式报文:";
	if (type & 0x80)
		expMsg += "测试确认";
	else if (type & 0x40)
		expMsg += "测试生效";
	else if (type & 0x20)
		expMsg += "停止确认";
	else if (type & 0x10)
		expMsg += "停止生效";
	else if (type & 0x08)
		expMsg += "启动确认";
	else if (type & 0x04)
		expMsg += "启动生效";

	msgType.value = 0;
	msgType.clnbyte.pars = 1;
	msgType.clnbyte.dir = 1;
	
	addMsg(&expMsg,buffer,6, msgType.value);	//添加报文 protocol.cpp

	return	TRUE;
}

// 发送一个S帧，ack
bool	CIEC104_XL::Tx_APDU_S()
{
	int length = remainLength();
	if( length < 6)	return	FALSE;
	buffer[0] = 0x68;
	buffer[1] = 4;
	buffer[2] = 1;
	buffer[3] = 0;
	buffer[4] = (hUChar)((pInfo->NR % 128) << 1);
	buffer[5] = (hUChar)(pInfo->NR / 128);
	put(buffer,6);

	pInfo->Send_S_ACK_Flag = FALSE;
	pInfo->T1 = 0;
	pInfo->T2 = 0;
	pInfo->T3 = 0;

	expMsg = "S格式确认帧，序号";
	expMsg += tValue.from( pInfo->NR ) ;
	msgType.value = 0;
	msgType.clnbyte.pars = 1;
	msgType.clnbyte.dir = 1;
	addMsg(&expMsg,buffer,6,msgType.value);//添加报文

	return	TRUE;
}


bool CIEC104_XL::Ctrl_YKYT()
{
	hUChar pCmdBuf[FDC_CTRL_LEN];
	int len = m_ctrlInf.get(m_pRoute->group, pCmdBuf, FDC_CTRL_LEN);
	if( len <= 0)
		return FALSE;
	
	ctrl_head head;
	memcpy(&head, pCmdBuf, sizeof(ctrl_head));
	
	//遥调、遥调确认
	if(head.type == CTRL_PRO_YT || head.type == CTRL_PRO_ACK_YT)
	{		
		if(Tx_AnalogOutPut(pCmdBuf) == TRUE)
		{
			printlog((LOGE_IEC104_TX+m_link), "Tx ---------> 发送摇调设点命令成功");
			return TRUE;
		}
	}

	//遥控、遥控返校
	else if(head.type == CTRL_PRO_YK || head.type ==CTRL_PRO_ACK_YK)
	{
		if(Tx_CtrlYKCmd(pCmdBuf) == TRUE)
		{
			printlog((LOGE_IEC104_TX+m_link), "Tx ---------> 发送遥控命令成功");
			return TRUE;
		}
	}

	return FALSE;
}

bool CIEC104_XL::Tx_CtrlYkAckCmd()
{
	short	no;
	unsigned char	i,type,state,cause;
	unsigned char	*p = buffer + 6;
	p[i++] = C_SC_NA_1;		//单点命令
	p[i++] = 0x01;			//结构限定词
	//传送原因
	
	
	cause = CAUSE_ACT;
	no = pInfo->cur_yk.ctrlNo;
	i += MakeCause(p+i,cause);						//传送原因
	i += MakeCommAddr(p+i);							//公共地址（一般为子站站址）
	i += MakeObjAddr(p+i,no+config.Yk_Start_Addr);	//信息体地址

	hUInt8		S_E,QU,DCS;
	S_E = 0x00;		//0,执行;
	state = pInfo->cur_yk.ctrlState;
	switch(state)
	{
	case	YK_ERR:
		DCS = 0x00;
		expMsg += "状态[未定义]" ;
		break;
	case	YK_OFF:
		DCS = 0x01;
		expMsg += "状态[分]" ;
		break;
	case	YK_ON:
		DCS = 0x02;
		expMsg += "状态[合]" ;
		break;
	case	YK_UNDEF:
		DCS = 0x03;
		expMsg += "状态[未定义]" ;
		break;
	default:
		return	FALSE;
	}
	p[i++] = S_E  | DCS;
	if(Tx_APDU_I(i) == FALSE)	return	FALSE;
}
bool CIEC104_XL::Tx_CtrlYtAckCmd()
{
	short	no;
	unsigned char	i,type,state,cause;
	unsigned char	*p = buffer + 6;
	p[i++] = C_RC_NA_1;		//步命令
	p[i++] = 0x01;			//结构限定词
	//传送原因
	
	cause = CAUSE_ACT;
	no = pInfo->cur_yt.ctrlNo;
	i += MakeCause(p+i,cause);						//传送原因
	i += MakeCommAddr(p+i);							//公共地址（一般为子站站址）
	i += MakeObjAddr(p+i,no+config.Step_Start_Addr);	//信息体地址

	hUInt8		S_E,QU,DCS;
	S_E = 0x00;		//0,执行;
	state = pInfo->cur_yt.ctrlState;
	switch(state)
	{
	case	YK_ERR:
		DCS = 0x00;
		expMsg += "状态[未定义]" ;
		break;
	case	YK_OFF:
		DCS = 0x01;
		expMsg += "状态[分]" ;
		break;
	case	YK_ON:
		DCS = 0x02;
		expMsg += "状态[合]" ;
		break;
	case	YK_UNDEF:
		DCS = 0x03;
		expMsg += "状态[未定义]" ;
		break;
	default:
		return	FALSE;
	}
	p[i++] = S_E  | DCS;
	if(Tx_APDU_I(i) == FALSE)	return	FALSE;
}
bool CIEC104_XL::Tx_CtrlYKCmd(hUChar  *pCmdBuf)
{	
	memcpy(&pInfo->cur_yk, (ctrl_pro_yk*)(pCmdBuf + sizeof(ctrl_head)), sizeof(ctrl_pro_yk));
	
	short no = pInfo->cur_yk.ctrlNo.hostValue();	
	// if (std::find(m_vectorYk.begin(), m_vectorYk.end(), no) == m_vectorYk.end())
	// {
	// 	printlog((LOGE_IEC104_TX+m_link), "遥控号[%d]不存在", no);
	// 	return false;
	// }

	unsigned char cause;
	unsigned char type = pInfo->cur_yk.ctrlType;
	unsigned char state = pInfo->cur_yk.ctrlState;
	printlog((LOGE_IEC104_TX+m_link),"发送YK遥控命令 : Group = %s , No = %d , Type = %hhu , State = %hhu"
		, m_pGroup->name, no, type, state);

	if(no < 0)	return	FALSE;

	unsigned char *p = buffer + 6;
	int i = 0;
	if(type == 10) p[i++] = C_DC_NA_1;	//双点命令 zwc 2025.01.06
	else p[i++] = C_SC_NA_1;			//单点命令
	p[i++] = 0x01;			//结构限定词
	
	switch(type)	//传送原因
	{
	case YK_SET_CMD:	//遥控选择
		cause = CAUSE_ACT;
		break;
	case YK_EXE_CMD:	//遥控执行
	case CTRL_PRO_YK:	//兼容非标类型 zwc 2023.06.26
	case 10: 			//双点遥控 zwc 2025.01.06
		cause = CAUSE_ACT;
		break;
	case YK_DEL_CMD:	//遥控撤消
		cause = CAUSE_STOPACT;
		break;
	
	default:
		return	FALSE;
	}
	i += MakeCause(p+i, cause);						//传送原因
	i += MakeCommAddr(p+i);							//公共地址（一般为子站站址）
	i += MakeObjAddr(p+i, no+config.Yk_Start_Addr);	//信息体地址

	hUInt8		S_E, QU, DCS; //遥控命令限定词
	switch(type)
	{
	case YK_EXE_CMD:	//遥控执行
	case CTRL_PRO_YK:	//兼容非标类型 zwc 2023.06.26
	case 10:			//兼容双点遥控 zwc 2025.01.06
		S_E = 0x00;		//0,执行;
		QU = 0x04;		//1短脉冲持续时间(断路器)，持续时间由被控站内的系统参数所确定
		break;
	case YK_SET_CMD:	//遥控选择
		S_E = 0x80;		//1,选择;
		QU = 0x04;		//1短脉冲持续时间(断路器)，持续时间由被控站内的系统参数所确定
		break;
	case YK_DEL_CMD:	//遥控撤消
		S_E = 0x00;		//0,执行;
		QU = 0x00;		//无另外的定义
		break;
	default:
		return	FALSE;
	}

	//if(type == CTRL_PRO_YK) state++;//兼容非标类型 zwc 2023.06.26

	switch(state)
	{
	case 0:
		if(type == 10) DCS = 0x01;//双点 状态[分] zwc 2025.01.06
		else DCS = 0x00;//状态[分]
		break;
	case 1:
		if(type == 10) DCS = 0x02;//双点 状态[合] zwc 2025.01.06
		else DCS = 0x01;//状态[合]
		break;
	default:
		printlog((LOGE_IEC104_TX+m_link), "遥控值[%hhu]异常", state);
		return	FALSE;
	}
	p[i++] = S_E  | DCS;
	if(Tx_APDU_I(i) == FALSE)	return	FALSE;
	return	TRUE;
}
/*
	68 0E 04 00 0C 00 2D 01 06 00 00 00 02 60 00 01
	68 0E 06 00 0E 00 2D 01 06 00 00 00 02 60 00 01 

bool	CIEC104_XL::Tx_CtrlCmd()
{
	if(pInfo->WorkState != STATE_FREE)	return	FALSE;
	hUChar  cmdBuf[FDC_CTRL_LEN];
	if (m_ctrlInf.get(m_pGroup->name, CTRL_PRO_YK, cmdBuf, sizeof(ctrl_head)+sizeof(ctrl_pro_yk),0) <= 0)
		return	FALSE;//m_pGroup->name

	memcpy(&pInfo->cur_yk, (ctrl_pro_yk*)(cmdBuf + sizeof(ctrl_head)), sizeof(ctrl_pro_yk));
	printlog((LOGE_IEC104_TX+m_link), "Tx ---------> 发送遥控命令");

	short	no;
	unsigned char	i,type,state,cause;

	
	no = pInfo->cur_yk.ctrlNo;
	type = pInfo->cur_yk.ctrlType;
	state = pInfo->cur_yk.ctrlState;
	char tmpstr[1024];
	sprintf(tmpstr,"遥控:组名[%s],点名[%s],点号[%d],",m_pGroup->name,pInfo->cur_yk.pointName,no);
	expMsg = tmpstr;
	//expMsg += m_pGroup->name;
	//expMsg += "],点名[]点号["
	//expMsg += tValue.from(no);
	printlog((LOGE_IEC104_TX+m_link),"发送遥控命令 : Group = %s , No = %d , Type = %d , State = %d",m_pGroup->name,no,type,state);
	if(no < 0)	return	FALSE;
	unsigned char	*p = buffer + 6;
	i = 0;
	p[i++] = C_DC_NA_1;		//双点命令
	p[i++] = 0x01;			//结构限定词
	//传送原因
	switch(type)
	{
	case YK_SET_CMD:	//遥控选择
		cause = CAUSE_ACT;
		expMsg += "类型[选择]," ;
		break;
	case YK_EXE_CMD:	//遥控执行
		cause = CAUSE_ACT;
		expMsg += "类型[执行]," ;
		break;
	case YK_DEL_CMD:	//遥控撤消
		cause = CAUSE_STOPACT;
		expMsg += "类型[撤消]," ;
		break;
	default:
		return	FALSE;
	}
	i += MakeCause(p+i,cause);						//传送原因
	i += MakeCommAddr(p+i);							//公共地址（一般为子站站址）
	i += MakeObjAddr(p+i,no+config.Yk_Start_Addr);	//信息体地址

	hUInt8		S_E,QU,DCS;
	//遥控命令限定词
	switch(type)
	{
	case YK_EXE_CMD:	//遥控执行
		S_E = 0x00;		//0,执行;
		QU = 0x04;		//1短脉冲持续时间(断路器)，持续时间由被控站内的系统参数所确定
		break;
	case YK_SET_CMD:	//遥控选择
		S_E = 0x80;		//1,选择;
		QU = 0x04;		//1短脉冲持续时间(断路器)，持续时间由被控站内的系统参数所确定
		break;
	case YK_DEL_CMD:	//遥控撤消
		S_E = 0x00;		//0,执行;
		QU = 0x00;		//无另外的定义
		break;
	default:
		return	FALSE;
	}
	switch(state)
	{
	case	YK_ERR:
		DCS = 0x00;
		expMsg += "状态[未定义]" ;
		break;
	case	YK_OFF:
		DCS = 0x01;
		expMsg += "状态[分]" ;
		break;
	case	YK_ON:
		DCS = 0x02;
		expMsg += "状态[合]" ;
		break;
	case	YK_UNDEF:
		DCS = 0x03;
		expMsg += "状态[未定义]" ;
		break;
	default:
		return	FALSE;
	}
	p[i++] = S_E || QU || DCS;
	if(Tx_APDU_I(i) == FALSE)	return	FALSE;
	switch(type)
	{
	case YK_SET_CMD:	//遥控选择
		//pInfo->WorkState = STATE_WAIT_YK_SEL_CON;
		break;
	case YK_EXE_CMD:	//遥控执行
		//pInfo->WorkState = STATE_WAIT_YK_EXE_CON;
		break;
	case YK_DEL_CMD:	//遥控撤消
		//pInfo->WorkState = STATE_WAIT_YK_DEL_CON;
		break;
	default:
		return	FALSE;
	}
	return	TRUE;
}
*/
bool	CIEC104_XL::Tx_AnalogOutPut(hUChar  *pCmdBuf)
{
	ctrl_pro_setpoint * sp = (ctrl_pro_setpoint*)(pCmdBuf + sizeof(ctrl_head));
	if(sp->ctrlType == 9) {
		memcpy(&pInfo->cur_setpoints, (ctrl_pro_setpoints*)(pCmdBuf + sizeof(ctrl_head)), sizeof(ctrl_pro_setpoints));
		return Tx_AnalogMutliOutPut();
	}
	
	memcpy(&pInfo->cur_setpoint, (ctrl_pro_setpoint*)(pCmdBuf + sizeof(ctrl_head)), sizeof(ctrl_pro_setpoint));

	int	no = pInfo->cur_setpoint.ctrlNo.hostValue();
	unsigned char type = pInfo->cur_setpoint.ctrlType;
	float ctrlValue = pInfo->cur_setpoint.floatValue.hostValue();

	printlog((LOGE_IEC104_TX+m_link),"遥调设点执行 : Group = %s , No = %d , Type = %hhu , Value = %.3f"
		, m_pGroup->name, no, type, ctrlValue);
	
	if(no < 0)	return	FALSE;
	
	unsigned char *p = buffer + 6;
	int i = 0;
	p[i++] = C_SE_NC_1;		//设定值命令，短浮点数
	p[i++] = 0x01;			//结构限定词,单个信息对象(SQ=0)

	unsigned char cause = CAUSE_ACT;
	i += MakeCause(p+i, cause);						//传送原因
	i += MakeCommAddr(p+i);							//公共地址
	i += MakeObjAddr(p+i, no+config.Set_Start_Addr);//信息体地址

	memcpy(p+i, &ctrlValue, 4);
	i+=4;
	hUInt8 QOS = 0x00;
	p[i++] = 	QOS;								//设定命令限定词QOS CP8{QL,S/E}S/E=:0执行,1选择
	if(Tx_APDU_I((hUInt16)i) == FALSE)	return	FALSE;
	return TRUE;
}
bool	CIEC104_XL::Tx_AnalogMutliOutPut()
{
	int no = pInfo->cur_setpoints.ctrlNo;
	int	num = pInfo->cur_setpoints.ctrlNum;

	printlog((LOGE_IEC104_TX+m_link),"遥调YTS批量设点 : Group = %s , Type = %hhu , Num = %d, Value(0) = %.3f"
		, m_pGroup->name, pInfo->cur_setpoints.ctrlType, num, pInfo->cur_setpoints.floatVal[0]);
	
	if(num < 0)	return	false;
	
	unsigned char *p = buffer + 6;
	int i = 0;
	p[i++] = C_SE_ND_1;		//多个设定值命令，短浮点数
	p[i++] = 0x80 | num;	//结构限定词,多个连续信息对象(SQ=1)

	unsigned char cause = CAUSE_ACT;
	i += MakeCause(p+i, cause);						//传送原因
	i += MakeCommAddr(p+i);							//公共地址
	i += MakeObjAddr(p+i, no+config.Set_Start_Addr);//信息体地址
	for(int k = 0; k < num; k++) {
		memcpy(p+i, pInfo->cur_setpoints.floatVal+k, 4); //设点值
		i+=4;
	}	
	
	hUInt8 QOS = 0x00;
	p[i++] = 	QOS;								//设定命令限定词QOS CP8{QL,S/E}S/E=:0执行,1选择
	if(Tx_APDU_I((hUInt16)i) == FALSE)	return	false;
	return true;
}


void	CIEC104_XL::Tx_FileCmd()
{
	//if(pInfo->CallOneFileFlag == TRUE && pInfo->IsCallingFile == FALSE )//选择一个文件
	//{
	//	printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 开始选择文件");
	//	if(pInfo->FileBasicInfo.size() <=0)
	//	{
	//		pInfo->CallOneFileFlag = FALSE;
	//		pInfo->WorkState = STATE_FREE;
	//		return;
	//	}
	//	if(pInfo->FileBasicInfo[pInfo->FileBasicInfo.size()-1].FileType == 1 )//目录，不处理
	//	{
	//		pInfo->FileBasicInfo.pop_back();
	//		return;
	//	}
	//	if ( !Tx_SelectFile(pInfo->FileBasicInfo[pInfo->FileBasicInfo.size()-1].FileObjAddr,
	//						pInfo->FileBasicInfo[pInfo->FileBasicInfo.size()-1].FileNo,
	//						0,
	//						SCQ_SELE_FILE ) ) 
	//		return;
	//	pInfo->IsCallingFile = TRUE;
	//	pInfo->CurFile  = pInfo->FileBasicInfo[pInfo->FileBasicInfo.size()-1];
	//	printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 选择文件,文件名为%d",pInfo->FileBasicInfo[pInfo->FileBasicInfo.size()-1].FileNo);
	//	pInfo->FileBasicInfo.pop_back();
	//	return ;
	//}
	//if (pInfo->IsCallingFile == TRUE )
	//{
	//	switch( pInfo->CallFileCmd )
	//	{
	//	case CMD_CALL_FILE:			//召唤一个文件
	//		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 开始召唤文件，文件名为 %s",pInfo->CurFile.FileNo);
	//		if ( !Tx_CallFile( pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, 0, SCQ_CALL_FILE ) )
	//			return;
	//		break;
	//	case CMD_SELE_SECTION:		//选择一个文件的节
	//		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 开始选择文件的一个节，文件名为 %s，节为 %s",pInfo->CurFile.FileNo ,pInfo->CurFile.SectionNo);
	//		if ( !Tx_SelectFile( pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, pInfo->CurFile.SectionNo, SCQ_SELE_SECTION ) )
	//			return;
	//		break;
	//	case CMD_CALL_SECTION:		//召唤一个文件的节
	//		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 开始召唤文件的一个节，文件名为 %s，节为 %s",pInfo->CurFile.FileNo ,pInfo->CurFile.SectionNo);
	//		if ( !Tx_CallSection( pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, pInfo->CurFile.SectionNo, SCQ_CALL_SECTION ) )
	//			return;
	//	    break;
	//	case CMD_CON_SECTION:			//确认文件的一个节
	//		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 确认文件的一个节，文件名为 %s，节为 %s",pInfo->CurFile.FileNo ,pInfo->CurFile.SectionNo);
	//		if ( !Tx_FileCON(pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, pInfo->CurFile.SectionNo, AFQ_CON_SECTION ) )
	//			return;
	//		break;
	//	case CMD_DENY_SECTION:		//否认文件的一个节
	//		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 否认文件的一个节 ，文件名为 %s，节为 %s",pInfo->CurFile.FileNo ,pInfo->CurFile.SectionNo);
	//		if ( !Tx_FileCON(pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, pInfo->CurFile.SectionNo, AFQ_DENY_SECTION) )
	//			return;
	//		break;
	//	case CMD_CON_FILE:			//确认文件
	//		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 确认文件，文件名为 %s",pInfo->CurFile.FileNo);
	//		if ( !Tx_FileCON(pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, pInfo->CurFile.SectionNo, AFQ_CON_FILE) )
	//			return;
	//		pInfo->WorkState = STATE_FREE;
	//		break;
	//	case CMD_DENY_FILE:			//否认文件
	//		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 否认文件，文件名为 %s",pInfo->CurFile.FileNo);
	//		if ( !Tx_FileCON(pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, pInfo->CurFile.SectionNo, AFQ_DENY_FILE) )
	//			return;
	//		pInfo->WorkState = STATE_FREE;
	//	    break;
	//	default:
	//	    break;
	//	}
	//	pInfo->CallFileCmd = CMD_DEFAULT;
	//	return ;
	//}
}

void	CIEC104_XL::Tx_DataCmd()
{
/*
open函数初始化
SendTimeFlag = TRUE;
CallAllDataFlag = TRUE;
CallAllKwhFlag = TRUE;
*/

//send-2 发送对时命令
	if(pInfo->SendTimeFlag == TRUE)
	{
		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 对时命令<激活>");
		expMsg = "Tx ----------> 对时命令<激活>";
		Tx_SyncTime();
		pInfo->WorkState = STATE_WAIT_TIME_CON;
		pInfo->T5 = 0;
		pInfo->SendTimeFlag = FALSE;
		return;	
	}

//send-3 发送召唤全数据
	if(pInfo->CallAllDataFlag==TRUE && Tx_CallAllData()==TRUE)
	{
		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 召唤全数据");
		expMsg = "Tx ----------> 召唤全数据";
		pInfo->CallAllDataFlag = FALSE;
		pInfo->WorkState = STATE_WAIT_ALLDATA_CON;
		pInfo->T5 = 0;
		return;
	}
	if(pInfo->CallAllKwhFlag==TRUE && m_pGroup->kwhNum>0 && Tx_CallAllKwh()==TRUE)
	{
		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 召唤全电度");
		expMsg = "Tx ----------> 召唤全电度";
		pInfo->CallAllKwhFlag = FALSE;
		pInfo->WorkState = STATE_WAIT_ALLKWH_CON;
		pInfo->T5 = 0;
		return;
	}
	if(pInfo->CallFileFlag==TRUE && Tx_CallFileDir() ==TRUE)
	{
		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 召唤文件目录");
		expMsg = "Tx ----------> 召唤文件目录";
		pInfo->CallFileFlag = FALSE;
		//pInfo->WorkState = STATE_WAIT_FILE_DIR;
		pInfo->T5 = 0;
	}
	if(pInfo->CallOneFileFlag == TRUE && pInfo->IsCallingFile == FALSE )//选择一个文件
	{
		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 开始选择文件");
		expMsg = "Tx ----------> 开始选择文件";
		if(pInfo->FileBasicInfo.size() <=0)
		{
			pInfo->CallOneFileFlag = FALSE;
			pInfo->WorkState = STATE_FREE;
			return;
		}
		if(pInfo->FileBasicInfo[pInfo->FileBasicInfo.size()-1].FileType == 1 )//目录，不处理
		{
			pInfo->FileBasicInfo.pop_back();
			return;
		}
		if ( !Tx_SelectFile(pInfo->FileBasicInfo[pInfo->FileBasicInfo.size()-1].FileObjAddr,
							pInfo->FileBasicInfo[pInfo->FileBasicInfo.size()-1].FileNo,
							0,
							SCQ_SELE_FILE ) ) 
			return;
		pInfo->IsCallingFile = TRUE;
		pInfo->CurFile  = pInfo->FileBasicInfo[pInfo->FileBasicInfo.size()-1];
		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 选择文件,文件名为%d",pInfo->FileBasicInfo[pInfo->FileBasicInfo.size()-1].FileNo);
		expMsg = "Tx ----------> 选择文件";
		pInfo->FileBasicInfo.pop_back();
		return ;
	}
	if (pInfo->IsCallingFile == TRUE )
	{
		switch( pInfo->CallFileCmd )
		{
		case CMD_CALL_FILE:			//召唤一个文件
			printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 开始召唤文件，文件名为 %d",pInfo->CurFile.FileNo);
			expMsg = "Tx ----------> 开始召唤文件";
			if ( !Tx_CallFile( pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, 0, SCQ_CALL_FILE ) )
				return;
			break;
		case CMD_SELE_SECTION:		//选择一个文件的节
			printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 开始选择文件的一个节，文件名为 %d，节为 %d",pInfo->CurFile.FileNo ,pInfo->CurFile.SectionNo);
			expMsg = "Tx ----------> 开始选择文件的一个节";
			if ( !Tx_SelectFile( pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, pInfo->CurFile.SectionNo, SCQ_SELE_SECTION ) )
				return;
			break;
		case CMD_CALL_SECTION:		//召唤一个文件的节
			printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 开始召唤文件的一个节，文件名为 %d，节为 %d",pInfo->CurFile.FileNo ,pInfo->CurFile.SectionNo);
			expMsg = "Tx ----------> 开始召唤文件的一个节";
			if ( !Tx_CallSection( pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, pInfo->CurFile.SectionNo, SCQ_CALL_SECTION ) )
				return;
		    break;
		case CMD_CON_SECTION:			//确认文件的一个节
			printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 确认文件的一个节，文件名为 %d，节为 %d",pInfo->CurFile.FileNo ,pInfo->CurFile.SectionNo);
			expMsg = "Tx ----------> 确认文件的一个节";
			if ( !Tx_FileCON(pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, pInfo->CurFile.SectionNo, AFQ_CON_SECTION ) )
				return;
			break;
		case CMD_DENY_SECTION:		//否认文件的一个节
			printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 否认文件的一个节 ，文件名为 %d，节为 %d",pInfo->CurFile.FileNo ,pInfo->CurFile.SectionNo);
			expMsg = "Tx ----------> 否认文件的一个节";
			if ( !Tx_FileCON(pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, pInfo->CurFile.SectionNo, AFQ_DENY_SECTION) )
				return;
			break;
		case CMD_CON_FILE:			//确认文件
			printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 确认文件，文件名为 %d",pInfo->CurFile.FileNo);
			expMsg = "Tx ----------> 确认文件";
			if ( !Tx_FileCON(pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, pInfo->CurFile.SectionNo, AFQ_CON_FILE) )
				return;
			pInfo->WorkState = STATE_FREE;
			break;
		case CMD_DENY_FILE:			//否认文件
			printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 否认文件，文件名为 %d",pInfo->CurFile.FileNo);
			expMsg = "Tx ----------> 否认文件";
			if ( !Tx_FileCON(pInfo->CurFile.FileObjAddr, pInfo->CurFile.FileNo, pInfo->CurFile.SectionNo, AFQ_DENY_FILE) )
				return;
			pInfo->WorkState = STATE_FREE;
		    break;
		default:
		    break;
		}
		pInfo->CallFileCmd = CMD_DEFAULT;
		return ;
	}

// 计划曲线发送
	if(pInfo->SendPlanFlag == TRUE)
	{
		printlog((LOGE_IEC104_TX+m_link), "Tx ----------> 计划曲线");
		expMsg = "Tx ----------> 计划曲线";
		Tx_Plan();
		//发送后置flag=false，收到响应后，修改flag=true，继续发送下一帧
		pInfo->SendPlanFlag = FALSE;
		return;	
	}
}
int		CIEC104_XL::Tx_CallAllData()
{
	hUChar	i = 0;
	hUChar	*p = buffer + 6;
	p[i++] = C_IC_NA_1;					//类型
	p[i++] = 1;							//结构限定词
	i += MakeCause(p+i,CAUSE_ACT);		//传送原因
	i += MakeCommAddr(p+i);				//公共地址（一般为子站站址）
	i += MakeObjAddr(p+i,0);			//信息体地址
	p[i++] = 0x14;						//总召唤限定词QOI
	return	Tx_APDU_I(i);
}

int		CIEC104_XL::Tx_CallAllKwh()
{
	hUChar	i = 0;
	hUChar	*p = buffer + 6;
	p[i++] = C_CI_NA_1;					//类型
	p[i++] = 1;							//结构限定词
	i += MakeCause(p+i,CAUSE_ACT);		//传送原因
	i += MakeCommAddr(p+i);				//公共地址（一般为子站站址）
	i += MakeObjAddr(p+i,0);			//信息体地址
	p[i++] = 0x45;						//请求电能总计数量QCC
	return	Tx_APDU_I(i);
}

int		CIEC104_XL::Tx_CallFileDir()
{
	hUChar	i = 0;
	hUChar	*p = buffer + 6;
	p[i++] = F_SC_NA_1;					//类型
	p[i++] = 1;							//结构限定词
	i += MakeCause(p+i,CAUSE_REQ);		//传送原因
	i += MakeCommAddr(p+i);				//公共地址（一般为子站站址）
	i += MakeObjAddr(p+i,0);			//信息体地址
	p[i++] = config.File_Dir%256;		//文件目录名称，默认为0，范围（1-65535）
	p[i++] = config.File_Dir/256;
	p[i++] = 0;							//节名称
	p[i++] = SCQ_DEFAULT;				//选择和召唤限定词(SCQ)           

	return	Tx_APDU_I(i);

}
int		CIEC104_XL::Tx_SendFixdata()
{
	if(pInfo->WorkState != STATE_FREE)	return	FALSE;
	hUInt8	i,j,frm_type,crc_val;
	hUInt16	fixNum; //定值个数，整定时有用
	hUInt16 iedNo, areaNo;

	iedNo = MakeWord(pInfo->cur_fix.dataBuf[0],pInfo->cur_fix.dataBuf[1]);
	areaNo = MakeWord(pInfo->cur_fix.dataBuf[2],pInfo->cur_fix.dataBuf[3]);
	
	switch (pInfo->cur_fix.ctrlType)
	{
	case FIX_GET:
		frm_type = config.Fix_Set;
		break;
	case FIX_SET:
		frm_type = config.Fix_Set;
		break;
	case FIX_AFIRM:
		frm_type = config.Fix_Ack;
		break;
	case FIX_CANCEL:
		frm_type = config.Fix_Ack;
		break;
	case DEV_RESET:
		frm_type = config.Revert_Type;
		break;
	default:
		return	FALSE;
	}

	unsigned char	buffer_len = 0;
	unsigned char	*p = buffer + 6; 
	switch (pInfo->cur_fix.ctrlType)
	{
	case FIX_GET:
		pInfo->CallFixFlag = FALSE;

		p[buffer_len++] = frm_type;								//类型标志符
		p[buffer_len++] = 0x01;									//结构限定词  
		buffer_len += MakeCause(p+buffer_len,CAUSE_REQ);		//传送原因
		buffer_len += MakeCommAddr(p+buffer_len);				//公共地址（一般为子站站址）
		buffer_len += MakeObjAddr(p+buffer_len,config.Fix_Start_Addr);	//信息体地址
		
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[0];	//IED号低字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[1];	//IED号高字节

		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[2];	//定值区号低字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[3];	//定值区号高字节

		p[buffer_len++] = 0; //
		p[buffer_len++] = 0; //
		p[buffer_len++] = 0; //信息体个数
		p[buffer_len++] = 0; //信息体个数
		p[buffer_len++] = 0; //信息体个数
		p[buffer_len++] = 0; //信息体个数
		break;
	case FIX_SET:
		fixNum = MakeWord(pInfo->cur_fix.dataBuf[6],pInfo->cur_fix.dataBuf[7]);
		p[buffer_len++] = frm_type;
		p[buffer_len++] = fixNum + 2;									//结构限定词     
		buffer_len += MakeCause(p+buffer_len,CAUSE_INIT);				//传送原因
		buffer_len += MakeCommAddr(p+buffer_len);						//公共地址（一般为子站站址）
		buffer_len += MakeObjAddr(p+buffer_len,config.Fix_Start_Addr);	//信息体地址

		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[0]; //IED号低字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[1]; //IED号高字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[2]; //定值区号低字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[3]; //定值区号高字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[4]; //
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[5]; //
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[6]; //信息体个数
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[7]; //信息体个数
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[8]; //信息体个数
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[9]; //信息体个数

		for(i=0,j=2;i<fixNum;i++,j++)
		{
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6-2];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6-1];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6+1];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6+2];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6+3];
		}
		break;
	case FIX_AFIRM:
		pInfo->CallFixFlag = FALSE;

		p[buffer_len++] = frm_type;										//类型标志符
		p[buffer_len++] = 0x01;											//结构限定词     
		buffer_len += MakeCause(p+buffer_len,CAUSE_ACT);				//传送原因
		buffer_len += MakeCommAddr(p+buffer_len);						//公共地址（一般为子站站址）
		buffer_len += MakeObjAddr(p+buffer_len,config.Fix_Start_Addr);	//信息体地址
		
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[0]; //IED号低字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[1]; //IED号高字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[2]; //定值区号低字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[3]; //定值区号高字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[4]; //
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[5]; //
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[6]; //信息体个数
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[7]; //信息体个数
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[8]; //信息体个数
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[9]; //信息体个数
		for(i=0,j=2;i<fixNum;i++,j++)
		{
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6-2];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6-1];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6+1];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6+2];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6+3];
		}
		break;
	case FIX_CANCEL:
		pInfo->CallFixFlag = FALSE;

		p[buffer_len++] = frm_type;										//类型标志符
		p[buffer_len++] = 0x01;											//结构限定词     
		buffer_len += MakeCause(p+buffer_len,CAUSE_STOPACT);			//传送原因
		buffer_len += MakeCommAddr(p+buffer_len);						//公共地址（一般为子站站址）
		buffer_len += MakeObjAddr(p+buffer_len,config.Fix_Start_Addr);	//信息体地址
		
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[0]; //IED号低字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[1]; //IED号高字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[2]; //定值区号低字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[3]; //定值区号高字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[4]; //
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[5]; //
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[6]; //信息体个数
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[7]; //信息体个数
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[8]; //信息体个数
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[9]; //信息体个数
		for(i=0,j=2;i<fixNum;i++,j++)
		{
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6-2];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6-1];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6+1];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6+2];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6+3];
		}
		break;
	case DEV_RESET:
		fixNum = MakeWord(pInfo->cur_fix.dataBuf[6],pInfo->cur_fix.dataBuf[7]);
		p[buffer_len++] = frm_type;
		p[buffer_len++] = 0x01;											//结构限定词     
		buffer_len += MakeCause(p+buffer_len,CAUSE_REQ);				//传送原因
		buffer_len += MakeCommAddr(p+buffer_len);						//公共地址（一般为子站站址）
		buffer_len += MakeObjAddr(p+buffer_len,config.Fix_Start_Addr);	//信息体地址

		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[0]; //IED号低字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[1]; //IED号高字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[2]; //定值区号低字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[3]; //定值区号高字节
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[4]; //
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[5]; //
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[6]; //信息体个数
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[7]; //信息体个数
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[8]; //信息体个数
		p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[9]; //信息体个数
		for(i=0,j=2;i<fixNum;i++,j++)
		{
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6-2];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6-1];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6+1];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6+2];
			p[buffer_len++] = (hUInt8)pInfo->cur_fix.dataBuf[j*6+3];
		}
		break;
	default:
		break;
	}

	pInfo->cur_fixbuf[0] = pInfo->cur_fix.dataBuf[0];
	pInfo->cur_fixbuf[1] = pInfo->cur_fix.dataBuf[1];
	pInfo->cur_fixbuf[2] = pInfo->cur_fix.dataBuf[2];
	pInfo->cur_fixbuf[3] = pInfo->cur_fix.dataBuf[3];

	printlog(1234, "Tx ---------> 发送定值");

	Tx_APDU_I(buffer_len);	
	buffer_len = 0;
		
	return FALSE;
}
int		CIEC104_XL::Tx_SelectFile(hInt32 fileObjAddr, hUInt16 fileNo, hUInt8 sectionNo, hUInt8 fileSCQ)
{
	hUChar	i = 0;
	hUChar	*p = buffer + 6;
	p[i++] = F_SC_NA_1;					//类型
	p[i++] = 1;							//结构限定词
	i += MakeCause(p+i,CAUSE_TRANSFILE);//传送原因
	i += MakeCommAddr(p+i);				//公共地址（一般为子站站址）
	i += MakeObjAddr(p+i,fileObjAddr);	//信息体地址
	p[i++] = fileNo%256;				//文件目录名称，默认为0，范围（1-65535）
	p[i++] = fileNo/256;
	p[i++] = sectionNo;					//节名称
	p[i++] = fileSCQ;				//选择和召唤限定词(SCQ)	
	return	Tx_APDU_I(i);

}
int		CIEC104_XL::Tx_CallFile(hInt32 fileObjAddr, hUInt16 fileNo, hUInt8 sectionNo, hUInt8 fileSCQ)
{
	fileSCQ = SCQ_CALL_FILE;
	return Tx_SelectFile(fileObjAddr, fileNo, sectionNo, fileSCQ);

}
int		CIEC104_XL::Tx_CallSection(hInt32 fileObjAddr, hUInt16 fileNo, hUInt8 sectionNo, hUInt8 fileSCQ)
{
	fileSCQ = SCQ_CALL_SECTION;
	return Tx_SelectFile(fileObjAddr, fileNo, sectionNo, fileSCQ);

}
int		CIEC104_XL::Tx_FileCON(hInt32 fileObjAddr, hUInt16 fileNo,hUInt8 sectionNo,hUInt8 fileAFQ)
{
	hUChar	i = 0;
	hUChar	*p = buffer + 6;
	p[i++] = F_AF_NA_1;					//类型
	p[i++] = 1;							//结构限定词
	i += MakeCause(p+i,CAUSE_TRANSFILE);//传送原因
	i += MakeCommAddr(p+i);				//公共地址（一般为子站站址）
	i += MakeObjAddr(p+i,fileObjAddr);	//信息体地址
	p[i++] = fileNo%256;				//文件目录名称，默认为0，范围（1-65535）
	p[i++] = fileNo/256;
	p[i++] = 0;							//节名称
	p[i++] = fileAFQ;					//文件认可或节认可限定词(AFQ)
	return	Tx_APDU_I(i);

}

int		CIEC104_XL::Tx_SyncTime()
{
	hUChar	i = 0;
	hUChar	*p = buffer + 6;
	p[i++] = C_CS_NA_1;					//类型
	p[i++] = 1;							//结构限定词
	i += MakeCause(p+i,CAUSE_ACT);		//传送原因 2字节
	i += MakeCommAddr(p+i);				//公共地址（一般为子站站址） 2字节
	i += MakeObjAddr(p+i,0);			//信息体地址 3字节
	ECON_SYS_TIME	cur_time;
	GetTime(&cur_time);

	int	ms = (int)cur_time.sec*1000 + (int)cur_time.ms;
	p[i++] = LoByte(ms);
	p[i++] = HiByte(ms);
	p[i++] = cur_time.min & 0x3F;
	p[i++] = cur_time.hour & 0x3F;
	p[i++] = (cur_time.wday << 5) | (cur_time.day & 0x1F);
	p[i++] = cur_time.mon & 0x0F;
	p[i++] = (cur_time.year % 100) & 0x7F;
	return	Tx_APDU_I(i);
}

int		CIEC104_XL::Tx_YkInfo()
{/*
	hUInt8	i,frm_type,state,funccode,crc_val;

	switch (pInfo->cur_yk.ctrlType)
	{
	case YK_SET_CMD:
		frm_type = config.Yk_Sel_Type;
		funccode = config.Yk_Sel_Code;
		if(pInfo->cur_yk.ctrlState == YK_ON)	state = 0xCC;
		else	if(pInfo->cur_yk.ctrlState == YK_OFF)	state = 0x33;
		else	return	FALSE;
		break;
	case YK_EXE_CMD:
		frm_type = config.Yk_Exe_Type;
		funccode = config.Yk_Exe_Code;
		state = 0xAA;
		break;
	case YK_DEL_CMD:
		frm_type = config.Yk_Del_Type;
		funccode = config.Yk_Del_Code;
		state = 0x55;
		break;
	default:
		return	FALSE;
	}
	
	// 缓冲区指针索引
	hUChar i = 0,txnum=0;
	hUChar * p = buffer + 6;

	// 双点命令
	p[i++] = C_DC_NA_1;
	i++; // 结构限定词VSQ，后面赋值 

	i += MakeCause (p+i,CAUSE_ACT);	//传送原因
	i += MakeCommAddr (p+i);			//公共地址（一般为子站站址）


	buffer[0+buffer_len] = config.CtrlWord;
	buffer[1+buffer_len] = frm_type;
	buffer[2+buffer_len] = 0x03;
	buffer[3+buffer_len] = (hUInt8)m_pRoute->localAddr;
	buffer[4+buffer_len] = (hUInt8)m_pRoute->remoteAddr;
	MakeCRC(&crc_val);
	buffer[5+buffer_len] = crc_val;
	buffer_len += 6;

	for(i=0;i<3;i++)
	{
		buffer[0+buffer_len] = funccode;
		buffer[1+buffer_len] = state;
		buffer[2+buffer_len] = (hUInt8)pInfo->cur_yk.ctrlNo;
		buffer[3+buffer_len] = buffer[1+buffer_len];
		buffer[4+buffer_len] = buffer[2+buffer_len];
		MakeCRC(&crc_val);
		buffer[5+buffer_len] = crc_val;
		buffer_len += 6;
	}
	put(buffer, buffer_len);
	buffer_len = 0;
	printlog(LOGE_CDT_BASE + m_link,"file = %s,line = %d: 发送SBO遥控命令 : groupNo = %d , ctrlNo = %d , ctrlType = %d , state = 0x%X",__FILE__,__LINE__,(int)pInfo->cur_yk.groupNo,(int)pInfo->cur_yk.ctrlNo,(int)pInfo->cur_yk.ctrlType,state);
*/
	return	TRUE;
	
}
int		CIEC104_XL::Tx_AnalogOutPutInfo()
{
	return	TRUE;
}
int		CIEC104_XL::Tx_FixInfo()
{
	return TRUE;
}

bool	CIEC104_XL::Tx_Plan()
{
	//------- get plan data from file -------
	//289个点分3帧发送，100+100+89，只在发送第一帧时读文件
	if (0 == m_planHasSendNum)
	{
		const char* filedata;
		if (NULL == (filedata = (const char *)readFile("ini/plan.txt"))) {
			printlog(LOGE_IEC104_BASE+m_link, "%s read plan data error !!!", __func__);
			return false;
		}

		char buf[1024] = {0}; \
    	snprintf(buf, sizeof(buf), "%s", filedata); \
		printlog(LOGE_IEC104_BASE+m_link, "plan data: %s", buf);
	
		const char * p = filedata;
		const char* pos = 0;
		int idx = 0;
		while ((pos = strchr(p, ',')) != NULL) {
			sscanf(p, "%f,", &m_planVal[idx]);
			p = pos + 1;
			idx++;
		}
		free((void*)filedata);
	
		if (idx != 289) {
			printlog(LOGE_IEC104_BASE+m_link, "plan data error, idx = %d", idx);
			return false;
		}
	}

	//------- assemble package -------
	unsigned char *p = buffer + 6;
	int i = 0;
	
	//TYP
	p[i++] = C_SE_TF_1;		//138  计划曲线传送，短浮点数
	
	//结构限定词VSQ SQ=1
	int sendNum = 289 - m_planHasSendNum;
	sendNum = (sendNum > 100 ? 100 : sendNum);
	p[i++] = (0x01 << 7) | sendNum;

	//COT 传送原因
	unsigned char cause = CAUSE_ACT;
	i += MakeCause(p+i, cause);					
	
	//公共地址
	i += MakeCommAddr(p+i);							
	
	//首个信息体地址
	i += MakeObjAddr(p+i, config.Plan_Start_Addr+m_planHasSendNum);

	//data float
	for(int n = m_planHasSendNum; n < m_planHasSendNum + sendNum; n++)
	{
		memcpy(p+i, &m_planVal[n], sizeof(float));
		i+=sizeof(float);
	}	

	//QL QOS
	hUInt8 QOS = 0x00;
	p[i++] = 	QOS;								//设定命令限定词QOS CP8{QL,S/E}S/E=:0执行,1选择
	
	//CP56Time2a
	unsigned char datetime[7] = {0};
	genCP56Time2a(datetime);
	memcpy(p+i, datetime, 7);
	i+=7;
	
	if(Tx_APDU_I((hUInt16)i) == FALSE)	{
		printlog(LOGE_IEC104_BASE+m_link, "send plan data error, sendNum = %d", sendNum);
		return FALSE;
	}

	printlog(LOGE_IEC104_BASE+m_link, "send plan data success, sendNum = %d", sendNum);

	return TRUE;
}

int		CIEC104_XL::MakeCommAddr(hUInt8 *buf)
{
	if(buf == NULL)	return	0;
	
	buf[0] = m_pRoute->remoteAddr % 256;
	if(config.CommAddrLen == 2)	{
		buf[1] = (m_pRoute->remoteAddr / 256)<<1;
		return 2;
	}
	return 1;
}

int		CIEC104_XL::MakeObjAddr(hUInt8 *buf,hInt32 no)
{
	if(buf == NULL)	return	0;
	int	num = 0;
	buf[num++] = no % 256;
	if(config.ObjAddrLen >= 2)	buf[num++] = (no / 256) % 256;
	if(config.ObjAddrLen >= 3)	buf[num++] = (no / 256) / 256;
	return	num;
}

int		CIEC104_XL::MakeCause(hUInt8 *buf,hUInt8 cause)
{
	if(buf == NULL)	return	0;
	int	num = 0;
	buf[num++] = cause & 0x3F;	//T=0，PN=0
	if(config.CotLen == 2)	buf[num++] = (hUInt8)m_pRoute->localAddr;
	return	num;
}

int		CIEC104_XL::GetObjAddr()
{
	int	addr;
	addr = pAppDataBuf[0];
	if (config.ObjAddrLen>=2)
	{
		addr += 256*pAppDataBuf[1];
	}
	if (config.ObjAddrLen>=3)
	{
		addr += 256*256*pAppDataBuf[2];
	}

	pAppDataBuf += config.ObjAddrLen;
	return	addr;
}
//----------------------------------------------------------------------------
void	CIEC104_XL::NoRxAnyData()
{
	//pInfo->T1++;//T1超时应在I帧无应答或者T3超时后测试连接无应答后发生
	if (pInfo->WorkState!=STATE_FREE)
	{
		pInfo->T1++;	// 等待接收对端应答信息定时器（超时占用关闭）
	}
	
	pInfo->T3++;	//等待双方进行数据传输定时器（超时启动测试过程）
}

void	CIEC104_XL::RxSomeData()
{
	if (pInfo->WorkState == STATE_FREE)// 只有收到相应的报文，例如请求全数据对方回答全数据
	{
		pInfo->T1 = 0;
	}
	pInfo->T3 = 0;
}
//-----------------------------------------------------------------------------
// 判断对方的发送桢和我们的接受计数是否一致，如果不一致重置链路 
// Bruce 2006-09-18
int		CIEC104_XL::IsNsNrMatch(hUChar * buff)
{
	int nPeerNs = (buff[0]>>1)+buff[1]*128;
	int nPeerNr = (buff[2]>>1)+buff[3]*128;

	if (pInfo->NR == nPeerNs)// && pInfo->Ns == nPeerNr)
		return TRUE;
	expMsg = "对方发送帧计数(";
	tValue.from(nPeerNs);
	expMsg += tValue;
	expMsg += ")和我们的接受计数(";
	tValue.from(pInfo->NR);
	expMsg += tValue;
	expMsg += ")不一致";
	printlog((LOGE_IEC104_RX+m_link), "Rx <---------- 对方发送帧计数(%d)和我们的接受计数(%d)不一致",nPeerNs,pInfo->NR);
	pInfo->NR = nPeerNs;
	return TRUE;
	//return FALSE;
}
//----------------------------------------------------------------------------
int		CIEC104_XL::SearchAPCI()
{
	get(buffer+2,4);	// 控制域 4字节
	printHex(buffer, 6, true);

//I格式 //数据帧I帧
	if((buffer[2]&0x01) == IEC104_APCI_I)	
	{
		if(RxDataSize <= 4)	
		{ 
			expMsg = "104-I格式信息报文，报文长度小于等于6";
			printlog((LOGE_IEC104_RX+m_link), "Rx <-------------- 104-I格式信息报文，报文长度小于等于4，返回"); 
			return	-1;
		}
	// 判断对方的发送桢和我们的接受计数是否一致，如果不一致重置链路 
		if (IsNsNrMatch(buffer+2) == FALSE)
		{
			InitInfo();//重新初始化
			return -1;
		}
		printlog((LOGE_IEC104_RX+m_link), "Rx <--- IEC-104 I 格式信息报文 接收序号:%d",(int)pInfo->NR);
		return	IEC104_APCI_I;
	}

//S格式 ack帧
	else	if((buffer[2]&0x03) == IEC104_APCI_S)
	{
		if(RxDataSize != 4)	
		{
			expMsg = "104-S格式信息报文，报文总长度不等于6";
			return	-1;
		}
		expMsg = "104-S格式确认帧";
		printlog((LOGE_IEC104_RX+m_link), "Rx <---------- IEC104-收到S帧");
		return	IEC104_APCI_S;
	}

//U格式 测试、停止、确认
	else	if((buffer[2]&0x03) == IEC104_APCI_U)
	{
		if(RxDataSize != 4)	
		{
			expMsg = "104-U格式信息报文，报文长度不等于6";
			return	-1;
		}
		switch(buffer[2] & 0xFC)	// 0xfc  1111 1100
		{
		
	// recv-1启动确认
	// 发送启动命令后，收到启动确认，走这里
		case	APCI_U_STARTDT_CON:	//0x08 = 0000 1000
			if(pInfo->LinkState==STATE_UNBOUND && pInfo->WorkState==STATE_WAIT_STARTDT_CON)
			{
				expMsg = "IEC-104 U 格式信息报文：启动链接确认";
				printlog((LOGE_IEC104_RX+m_link), "Rx <------------ 收到104-U格式信息报文：启动链接确认");
				pInfo->LinkState = STATE_IDLE;
				pInfo->WorkState = STATE_FREE;
				pInfo->T5 = 0;
			}
			break;
			
	// 停止确认
		case	APCI_U_STOPDT_CON:	// 0x20 0010 0000
			expMsg = "IEC-104-U 格式信息报文：停止链路确认";
			printlog((LOGE_IEC104_RX+m_link), "Rx <------------ 收到停止链路确认");
			if(pInfo->LinkState==STATE_IDLE && pInfo->WorkState==STATE_WAIT_STOPDT_CON)
			{
				InitInfo();
			}
			break;
			
	// 测试确认			
		case	APCI_U_TESTFR_CON:	// 0x80 = 1000 0000
			expMsg = "IEC-104-U 格式信息报文：测试链路确认";
			printlog((LOGE_IEC104_RX+m_link), "Rx <------------ 收到测试链路确认");
			if(pInfo->LinkState==STATE_IDLE && pInfo->WorkState==STATE_WAIT_TESTDT_CON)
			{
				pInfo->WorkState = STATE_FREE;
				pInfo->T5 = 0;
			}
			break;
			
	// 测试命令		
		case	APCI_U_TESTFR_ACT:	// 0x40=0100 0000
			expMsg = "IEC-104-U 格式信息报文：激活测试帧";
			printlog((LOGE_IEC104_RX+m_link), "Rx <-------------- IEC-104-U 格式信息报文：激活测试帧");
			pInfo->Send_U_TESTFR_CON_Flag = TRUE;
			if(pInfo->LinkState==STATE_IDLE)
			{
				pInfo->WorkState = STATE_FREE;
			}
			break;
			
		default:
			return	-1;
		}
		return	IEC104_APCI_U;
	}
	return	-1;

}

int		CIEC104_XL::SearchASDU()
{
//未连接
	if(pInfo->LinkState == STATE_UNBOUND)	
	{
		printlog((LOGE_IEC104_RX+m_link), "Rx <------------- 链路未启用，收到报文，不处理");
		return	FALSE;
	}
	
	pInfo->NR = (pInfo->NR + 1) % (hUInt16)0x8000;
	AppDataLen = RxDataSize - 4;
	get(buffer+6,AppDataLen);
	printHex(buffer, AppDataLen+6, true);
	pAppDataBuf = buffer + 6;
	
	IEC104_APP_HEAD		AppHead;
	if(GetAppHead(&AppHead) == FALSE)
	{
		printlog((LOGE_IEC104_RX+m_link), "Rx <------------- GetAppHead() == FALSE");
		return	FALSE;
	}
	m_pRouteInfo->okFrmCnt++;
	pInfo->W ++;
	pInfo->Send_S_ACK_Flag = TRUE;
	printlog((LOGE_IEC104_RX+m_link), "Rx <-- GetAppHead() == TRUE, AppHead.Type = %d VSQ.Num=%d", AppHead.Type,AppHead.VSQ.Num);
	//printlog((LOGE_IEC104_RX+m_link), "AppHead:Cause=%d,CommAddr=%d,PN=%d,T=%d,VSQ.Num=%d,VSQ.SQ=%d",AppHead.COT.Cause,AppHead.CommAddr,AppHead.COT.PN,AppHead.COT.T,AppHead.VSQ.Num,AppHead.VSQ.SQ);

	switch(AppHead.Type)
	{
//recv-3
//recv-6
	case	C_IC_NA_1:		//总召唤 0x64
		DoCallAllDataAck(AppHead);
		break;
	case	C_CI_NA_1:		//计数量召唤
		DoCallAllKwhAck(AppHead);
		break;
// recv-2
	case	C_CS_NA_1:		//时钟同步
		DoSyncTimeAck(AppHead);
		break;
	case	C_SC_NA_1:		//遥控（单点命令）
	case	C_DC_NA_1:		//遥控（双点命令）
		DoYkRetInfo(AppHead);
		break;
	case	C_RC_NA_1:		//调节步命令
		DoYtRetInfo(AppHead);
		break;
	case	C_SE_NA_1:		//设定值命令，规一化值
	case	C_SE_NB_1:		//设定值命令，标度化值
		break;
	case	C_SE_NC_1:		//设定值命令，短浮点数
		DoAnalogOutPutAck(AppHead);
		break;
	case C_SE_TF_1:
		DoPlanRetInfo(AppHead);
		break;
	case P_ME_NC_1:	//定值，测量值，短浮点数
	case P_AC_NA_1:
		FIX_RxProc(AppHead);
		break;
// recv-4
	case	M_SP_NA_1:		//单点信息 单点遥信
	case	M_DP_NA_1:		//双点信息 双点遥信
	case	M_PS_NA_1:		//带变位检出的成组单点信息
		YX_RxProc(AppHead);
		break;
	case	M_SP_TA_1:		//带CP24Time2a时标的单点信息
	case	M_DP_TA_1:		//带CP24Time2a时标的双点信息
	case	M_SP_TB_1:		//带CP56Time2a时标的单点信息  0x1e
	case	M_DP_TB_1:		//带CP56Time2a时标的双点信息  0x1f
		SOE_RxProc(AppHead);
		break;
// recv-5
	case	M_ME_NA_1:		//测量值，规一化值
	case	M_ME_NB_1:		//测量值，标度化值
	case	M_ME_NC_1:		//测量值，短浮点数
	case	M_ME_TA_1:		//带CP24Time2a时标的测量值，规一化值
	case	M_ME_TB_1:		//带CP24Time2a时标的测量值，标度化值
	case	M_ME_TC_1:		//带CP24Time2a时标的测量值，短浮点数
	case	M_ME_TD_1:		//带CP56Time2a时标的测量值，规一化值
	case	M_ME_TE_1:		//带CP56Time2a时标的测量值，标度化值
	case	M_ME_TF_1:		//带CP56Time2a时标的测量值，短浮点数
	case	M_ME_ND_1:		//测量值，不带品质描述词的规一化值
		YC_RxProc(AppHead);
		break;
	case	M_IT_NA_1:		//累积量
	case	M_IT_TA_1:		//带CP24Time2a时标的累积量
	case	M_IT_TB_1:		//带CP56Time2a时标的累积量
		KWH_RxProc(AppHead);
		break;
	case P_PL_NA_1:
		//PLAN_RxProc(AppHead);
		break;
	case	F_FR_NA_1:		//文件准备就绪
		printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 文件准备就绪");
		FILE_RxFileReadyProc(AppHead);
		//pInfo->WorkState = STATE_WAIT_FILE_DIR;
		break;
	case	F_SR_NA_1:		//节准备就绪
		printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 节准备就绪 文件名 %d",pInfo->CurFile.FileNo);
		FILE_RxSectionReadyProc(AppHead);
		break;
	case	F_LS_SE_1:		//最后的段
		printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 最后的段 文件名 %d",pInfo->CurFile.FileNo);
		FILE_RxLastSegmentProc(AppHead);
		break;
	case	F_LS_NA_1:		//最后的节,最后的段
		printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 最后的节 文件名 %d",pInfo->CurFile.FileNo);
		FILE_RxLastSectionProc(AppHead);
		break;
	case	F_SG_NA_1:		//段
		FILE_RxSegmentProc(AppHead);
		break;
	case	F_DR_NA_1:		//目录
		printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 接收到目录文件列表");
		FILE_RxDirProc(AppHead);
		break;
	case	P_EP_NA_1:
		printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 接收到事件");
		EVENT_RxProc(AppHead);
		break;
	default:
		break;
	}
	return	TRUE;

}

/*-----------------------------------------------------------------------*\
接收控制方向报文
\*-----------------------------------------------------------------------*/
void	CIEC104_XL::App_RxAct(IEC104_APP_HEAD &AppHead)
{
	printlog((LOGE_IEC104_RX+m_link), "----====接收到控制方向报文   %d " ,(int)AppHead.Type);
	switch(AppHead.Type)
	{
	case	C_SC_NA_1:		//遥控（单点命令）
	case	C_DC_NA_1:		//遥控（双点命令）
		DoYkAck(AppHead);
		break;
	case	C_RC_NA_1:		//调节步命令
	case	C_SE_NA_1:		//设定值命令，规一化值
	case	C_SE_NB_1:		//设定值命令，标度化值
		DoYtAck(AppHead);
		break;
	case C_CS_NA_1:		//时钟同步
		printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 时钟同步<激活>");
		pInfo->Next_I_Confirm = I_CON_SYSNTIME;
		break;
	case C_IC_NA_1:
		{
			hUInt8 detail = 0;
			if (AppDataLen > 0) detail = pAppDataBuf[AppDataLen-1];
			else detail = pAppDataBuf[0];
			pAppDataBuf ++;
			if ( detail == 0x14 ) // 总召唤限定词QOI
			{
				printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 总召唤 全部数据(YX,YC) <激活>");
				pInfo->Next_I_Confirm = I_CON_CALL_ALL_DATA_ACT;
			}
			else 
			{
				printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 总召唤 <激活>,detail = %d", detail);
			}
		}
		break;
	case C_CI_NA_1:
		{
			hUInt8 detail = 0;
			if (AppDataLen > 0) detail = pAppDataBuf[AppDataLen-1];
			else detail = pAppDataBuf[0];
			pAppDataBuf ++;
			if ( detail == 0x45 ) // 总召唤电度
			{
				printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 总召唤 电度 <激活>");
				pInfo->Next_I_Confirm = I_CON_CALL_ALL_KWH_ACT; // 
			}
			else 
			{
				printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 总召唤 <激活>,detail = %d", detail);
			}
		}
		break;
	case A_HD_NA_1:									    //add:G.Z.Wang 20060726 start
		HISDATA_RxProc(AppHead);
		break;
	case E_HD_NA_1:
		break;									    //add:G.Z.Wang 20060726 end
	default:
		printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 控制方向报文 <激活>");
		break;
	}
}
/*-----------------------------------------------------------------------*\
接收监视方向报文
\*-----------------------------------------------------------------------*/
void	CIEC104_XL::App_RxCon(IEC104_APP_HEAD &AppHead)
{
	printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 监视方向报文<确认>");
	pInfo->Send_S_ACK_Flag = TRUE;

	switch(AppHead.Type)
	{
	case	C_IC_NA_1:		//总召唤
		DoCallAllDataAck(AppHead);
		break;
	case	C_CI_NA_1:		//计数量召唤
		DoCallAllKwhAck(AppHead);
		break;
	case	C_CS_NA_1:		//时钟同步
		printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 时钟同步<确认>");
		DoSyncTimeAck(AppHead);
		break;
	case	C_SC_NA_1:		//遥控（单点命令）
	case	C_DC_NA_1:		//遥控（双点命令）
		// DoYkRetInfo(AppHead);
		break;
	case	C_RC_NA_1:		//调节步命令
	case	C_SE_NA_1:		//设定值命令，规一化值
	case	C_SE_NB_1:		//设定值命令，标度化值
		// DoYtRetInfo(AppHead);
		break;
	case	C_SE_NC_1:		//设定值命令，短浮点数
		DoAnalogOutPutAck(AppHead);
		break;
	case	M_SP_NA_1:		//单点信息
	case	M_DP_NA_1:		//双点信息
	case	M_PS_NA_1:		//带变位检出的成组单点信息
		YX_RxProc(AppHead);
		break;
	case	M_SP_TA_1:		//带CP24Time2a时标的单点信息
	case	M_DP_TA_1:		//带CP24Time2a时标的双点信息
	case	M_SP_TB_1:		//带CP56Time2a时标的单点信息
	case	M_DP_TB_1:		//带CP56Time2a时标的双点信息
		SOE_RxProc(AppHead);
		break;
	case	M_ME_NA_1:		//测量值，规一化值
	case	M_ME_NB_1:		//测量值，标度化值
	case	M_ME_NC_1:		//测量值，短浮点数
	case	M_ME_TA_1:		//带CP24Time2a时标的测量值，规一化值
	case	M_ME_TB_1:		//带CP24Time2a时标的测量值，标度化值
	case	M_ME_TC_1:		//带CP24Time2a时标的测量值，短浮点数
	case	M_ME_TD_1:		//带CP56Time2a时标的测量值，规一化值
	case	M_ME_TE_1:		//带CP56Time2a时标的测量值，标度化值
	case	M_ME_TF_1:		//带CP56Time2a时标的测量值，短浮点数
	case	M_ME_ND_1:		//测量值，不带品质描述词的规一化值
		YC_RxProc(AppHead);
		break;
	case	M_IT_NA_1:		//累积量
	case	M_IT_TA_1:		//带CP24Time2a时标的累积量
	case	M_IT_TB_1:		//带CP56Time2a时标的累积量
		KWH_RxProc(AppHead);
		break;
		/*									//delete:G.Z.Wang 20060726
		case A_HD_NA_1:
		case E_HD_NA_1:
		HISDATA_RxProc(AppHead);
		break;
		*/
	default:
		break;
	}
}
void	CIEC104_XL::DoYkAck(IEC104_APP_HEAD &head)
{
	//结构限定词
	if(head.VSQ.SQ!=0 || head.VSQ.Num!=1)	return;
	hUChar	state;
	hUInt16	no;
	hUInt16 type;
	//遥控号
	no = GetObjAddr() - config.Yk_Start_Addr;
	//传输原因
	switch(head.COT.Cause)
	{
	case	CAUSE_ACT:			//激活
	case	CAUSE_STOPACT:		//停止激活
		type = CTRL_PRO_YK;
		break;
	case	CAUSE_ACT_CON:		//激活确认
	case	CAUSE_STOPACT_CON:	//停止激活确认
		type = CTRL_PRO_ACK_YK;
		break;
	default:
		return;
	}
	//遥控命令限定词
	dtUint8		D_SCO = *(pAppDataBuf++);
	dtUint8		S_E,QU,D_SCS;
	S_E = (D_SCO & 0x80)?1:0;	//0——执行，1——选择
	printlog((LOGE_IEC104_TX+m_link),"--------DoYkAck ,%d    , type = %d",head.Type,type);
	return;
}
void CIEC104_XL::DoYtAck(IEC104_APP_HEAD &head)
{
	//结构限定词
	if(head.VSQ.SQ!=0 || head.VSQ.Num!=1)	return;
	hUChar	state;
	hUInt16	no;
	hUInt16 type;
	//遥控号
	no = GetObjAddr() - config.Yk_Start_Addr;
	//传输原因
	switch(head.COT.Cause)
	{
	case	CAUSE_ACT:			//激活
	case	CAUSE_STOPACT:		//停止激活
		type = CTRL_PRO_YT;
		break;
	case	CAUSE_ACT_CON:		//激活确认
	case	CAUSE_STOPACT_CON:	//停止激活确认
		type = CTRL_PRO_ACK_YT;
		break;
	default:
		return;
	}
	//遥控命令限定词
	dtUint8		D_SCO = *(pAppDataBuf++);
	dtUint8		S_E,QU,D_SCS;
	S_E = (D_SCO & 0x80)?1:0;	//0——执行，1——选择

	printlog((LOGE_IEC104_TX+m_link),"--------DoYtAck ,%d    , type = %d",head.Type,type);

	return;
}
//----------------------------------------------------------------------------
void	CIEC104_XL::DoCallAllDataAck(IEC104_APP_HEAD &head)
{
	//结构限定词
	if(head.VSQ.SQ!=0 || head.VSQ.Num!=1)
	{
		printlog((LOGE_IEC104_RX+m_link), "Rx <----------- head.VSQ.SQ == %d ,head.VSQ.Num = %d, 不合理",head.VSQ.SQ, head.VSQ.Num);
		return;
	}
	//传输原因
	switch(head.COT.Cause)
	{
// recv-3
	case	CAUSE_ACT_CON:
		printlog((LOGE_IEC104_RX+m_link), "Rx <----------- 全数据 激活确认");
		if(pInfo->WorkState == STATE_WAIT_ALLDATA_CON)	
		{
			pInfo->WorkState = STATE_WAIT_ALLDATA;
			pInfo->T5 = 0;
		}
		expMsg = "全数据 激活确认";
		break;
	case	CAUSE_STOPACT_CON:
		printlog((LOGE_IEC104_RX+m_link), "Rx <----------- 全数据 停止激活确认");
		if(pInfo->WorkState == STATE_WAIT_ALLDATA_CON)
		{
			pInfo->WorkState = STATE_FREE;
			pInfo->T5 = 0;
		}
		expMsg = "全数据 停止激活确认";
		break;
// recv-6
	case	CAUSE_ACT_TERM:
		printlog((LOGE_IEC104_RX+m_link), "Rx <----------- 全数据 激活终止");
		if(pInfo->WorkState == STATE_WAIT_ALLDATA)
		{
			pInfo->WorkState = STATE_FREE;		//here
			pInfo->T5 = 0;
		}
		expMsg = "全数据 激活终止";
		break;
	default:
		expMsg = "未知的确认原因 传送原因序号:";
		tValue.from(head.COT.Cause);
		expMsg += tValue;
		printlog((LOGE_IEC104_RX+m_link), "Rx <----------- head.COT.Cause = %d, 未知的确认原因",head.COT.Cause);
		break;
	}

}

void	CIEC104_XL::DoCallAllKwhAck(IEC104_APP_HEAD &head)
{
	//结构限定词
	if(head.VSQ.SQ!=0 || head.VSQ.Num!=1)	return;
	//传输原因
	switch(head.COT.Cause)
	{
	case	CAUSE_ACT_CON:
		if(pInfo->WorkState == STATE_WAIT_ALLKWH_CON)
		{
			pInfo->WorkState = STATE_WAIT_ALLKWH;
			pInfo->T5 = 0;
		}
		expMsg = "电度激活确认";
		break;
	case	CAUSE_STOPACT_CON:
		if(pInfo->WorkState == STATE_WAIT_ALLKWH_CON)
		{
			pInfo->WorkState = STATE_FREE;
			pInfo->T5 = 0;
		}
		expMsg = "电度停止激活确认";
		break;
	case	CAUSE_ACT_TERM:
		if(pInfo->WorkState == STATE_WAIT_ALLKWH)
		{
			pInfo->WorkState = STATE_FREE;
			pInfo->T5 = 0;
		}
		expMsg = "电度激活终止";
		break;
	default:
		break;
	}
}

void	CIEC104_XL::DoSyncTimeAck(IEC104_APP_HEAD &head)
{
	//结构限定词
	if(head.VSQ.SQ!=0 || head.VSQ.Num!=1)
	{
		printlog((LOGE_IEC104_RX+m_link), "Rx <------------ head.VSQ.SQ!=0 || head.VSQ.Num!=1");
		return;
	}
	//传输原因
	if( head.COT.Cause==CAUSE_ACT_CON )
	{
		expMsg = "时钟同步<确认>";
		if (pInfo->WorkState==STATE_WAIT_TIME_CON )
		{
			printlog((LOGE_IEC104_RX+m_link), "Rx <------------ 对时得到确认，将工作状态置为空闲");
			pInfo->WorkState = STATE_FREE;
			pInfo->T5 = 0;
		}
		else printlog((LOGE_IEC104_RX+m_link), "Rx <------------ 对时得到确认，工作状态不是Wait sync time confirm");
	}
	else 
	{
		expMsg = "时钟同步<否认>";
		printlog((LOGE_IEC104_RX+m_link), "Rx <------------ 对时没有得到确认");
		pInfo->WorkState = STATE_FREE;
		pInfo->T5 = 0;
	}
}

void	CIEC104_XL::DoYkRetInfo(IEC104_APP_HEAD &head)
{
	//结构限定词
	if(head.VSQ.SQ!=0 || head.VSQ.Num!=1)	return;
	hUChar	state;
	hUInt16	no;
	hUInt16 type;
	//遥控号
	no = GetObjAddr() - config.Yk_Start_Addr;
	//传输原因
	switch(head.COT.Cause)
	{
	case	CAUSE_ACT:			//激活
	case	CAUSE_STOPACT:		//停止激活
		type = CTRL_PRO_YK;
		break;
	case	CAUSE_ACT_CON:		//激活确认
	case	CAUSE_STOPACT_CON:	//停止激活确认
		type = CTRL_PRO_ACK_YK;
		break;
	default:
		return;
	}
	//遥控命令限定词
	dtUint8		D_SCO = *(pAppDataBuf++);
	dtUint8		S_E,QU,D_SCS;
	S_E = (D_SCO & 0x80)?1:0;	//0——执行，1——选择
	if(S_E == 0)
	{
		return ;//执行就不要管了 /
		//68 0E 08 00 10 00 2D 01 06 00 00 00 02 60 00 81 
		//68 0E 10 00 0A 00 2D 01 07 00 00 00 02 60 00 81
		//68 0E 0A 00 12 00 2D 01 06 00 00 00 02 60 00 01
		//68 0E 12 00 0C 00 2D 01 07 00 00 00 02 60 00 01
	}
	switch(head.Type)
	{
	case	C_SC_NA_1:			//单点命令
		QU = (D_SCO & 0x7E) >> 1;
		D_SCS = D_SCO & 0x01;
		if(D_SCS == 0)	state = YK_OFF;			//开——0
		else	state = YK_ON;					//合——1
		break;
	case	C_DC_NA_1:			//双点命令
		QU = (D_SCO & 0x7C) >> 2;
		D_SCS = D_SCO & 0x03;
		if(D_SCS == 1)	state = YK_OFF;			//开——1
		else	if(D_SCS == 2)	state = YK_ON;	//合——2
		else	state = YK_ERR;					//不允许——0、3
		break;
	default:
		return;
	}
	hUChar  cmdBuf[FDC_CTRL_LEN];
	ctrl_head m_head;
	m_head.type = type;
	printlog((LOGE_IEC104_RX+m_link), "Rx <------------ 读到了从站发送的遥控返校结果，根据结果组装遥控执行 type = %d   state = %d",(int)type,(int)state);
	if(type == CTRL_PRO_ACK_YK)  //发出执行命令
	{
		pInfo->cur_yk.ctrlNo = no;
		pInfo->cur_yk.ctrlType = YK_EXE_CMD;
		pInfo->cur_yk.ctrlState = state;
		pInfo->cur_yk.groupNo = m_pRoute->group;
		if(state == YK_ERR)	
			pInfo->cur_yk.ctrlState = FALSE;
		else
			pInfo->cur_yk.ctrlState = TRUE;
		memcpy(cmdBuf,&m_head,sizeof(ctrl_head));
		memcpy(cmdBuf+sizeof(ctrl_head),&(pInfo->cur_yk),sizeof(ctrl_pro_yk));
		printlog((LOGE_IEC104_TX+m_link),"遥控返校 : Group = %d , No = %d , State = %d ,ctrlState = %d ",m_pRoute->group,no,pInfo->cur_yk.ctrlState,pInfo->cur_yk.ctrlState);
		m_ctrlInf.add(cmdBuf,sizeof(ctrl_head)+sizeof(ctrl_pro_yk));
	}

	return;
}

void CIEC104_XL::DoYtRetInfo(IEC104_APP_HEAD &head)
{
	return;
}


void	CIEC104_XL::DoFixRetInfo(IEC104_APP_HEAD &head)
{

}
void	CIEC104_XL::DoAnalogOutPutAck(IEC104_APP_HEAD &head)
{
	
	//结构限定词
	if(head.VSQ.SQ!=0 || head.VSQ.Num!=1)	return;
	hUInt16	no;
	//序号
	no = GetObjAddr() - config.Set_Start_Addr;
	//值
	dtInt16		value;
	memcpy(&value,pAppDataBuf,2);
	pAppDataBuf += 2;
	//设点命令限定词
	dtUint8		QOS = *(pAppDataBuf++);
	//传输原因
	switch(head.COT.Cause)
	{
	case	CAUSE_ACT_CON:		//激活确认
		printlog((LOGE_IEC104_RX+m_link),"收到模拟量输出激活确认 : Rtu = %d , No = %d , Value = %d",m_pRoute->group,no,(dtLong)value);
		//cae_ctrlinf.SendYKReturnCmd(rtu_no,no,TRUE);
		break;
	case	CAUSE_STOPACT_CON:	//停止激活确认
		printlog((LOGE_IEC104_RX+m_link),"收到模拟量输出停止激活确认 : Rtu = %d , No = %d , Value = %d",m_pRoute->group,no,(dtLong)value);
		//cae_ctrlinf.SendYKReturnCmd(rtu_no,no,FALSE);
		break;
	default:
		return;
	}
	
}

// 发送计划曲线后，收到响应
void	CIEC104_XL::DoPlanRetInfo(IEC104_APP_HEAD &head)
{
	// 发送下一帧
	pInfo->SendPlanFlag = TRUE;

	//结构限定词
	// 错误帧
	// 信息体地址连续，三帧分别发送100 100 89个点位
	if(head.VSQ.SQ != 1 || (head.VSQ.Num != 100 && head.VSQ.Num != 89)) {
		pInfo->SendPlanFlag = FALSE;
		printlog((LOGE_IEC104_RX+m_link), "Rx <------------ 读到了从站返回的计划曲线, VSQ.SQ:%d VSQ.Num:%d, not equal to send packet, recv error packet!!!", head.VSQ.SQ, head.VSQ.Num);
		return;
	}

	m_planHasSendNum += head.VSQ.Num;
	
	// 289个点全部发完，结束
	if (m_planHasSendNum == 289) {
		pInfo->SendPlanFlag = FALSE;
		printlog((LOGE_IEC104_RX+m_link), "Rx <------------ 读到了从站返回的计划曲线，所有点位发送完成，结束 !!!");
		return;
	}

	return;	
}
int		CIEC104_XL::GetAppHead(IEC104_APP_HEAD *head)
{
	if(pAppDataBuf==NULL || AppDataLen==0)	return	FALSE;
	
	hUChar * appBuffHead = pAppDataBuf;
	int headLen = 0;
	if(config.CotLen == 2)		// here
	{
		memcpy(head,pAppDataBuf,4);
		pAppDataBuf += 4;
		AppDataLen -= 4;
		headLen+=4;
	}
	else
	{
		memcpy(head,pAppDataBuf,3);
		pAppDataBuf += 3;
		AppDataLen -= 3;
		headLen+=3;
	}
	hUInt16	CommAddr = 0;	
	if(config.CommAddrLen == 2)		// here
	{
		CommAddr = *pAppDataBuf;
		pAppDataBuf++;
		CommAddr += ((*pAppDataBuf)>>1)*256;
		pAppDataBuf++;
		AppDataLen -= 2;
		headLen+=2;
	}
	else
	{
		memcpy(&CommAddr,pAppDataBuf,1);
		pAppDataBuf += 1;
		AppDataLen -= 1;
		headLen+=1;
	}
	head->CommAddr = CommAddr;
	if(head->CommAddr != (hUInt16)m_pRoute->remoteAddr)	
	{
		msgType.clnbyte.err = 1;
		expMsg = "地址不匹配,报文中地址:";
		expMsg += tValue.from(head->CommAddr);
		expMsg += ",本地路径表中配置的远程地址:";
		expMsg += tValue.from(m_pRoute->remoteAddr);
		printlog((LOGE_IEC104_RX+m_link), "! 地址不匹配,报文中地址:%d,本地路径表中配置的远程地址:%d", head->CommAddr , (hUInt16)m_pRoute->remoteAddr); 
		
		//时钟同步时 取消判断远程地址(适应五沙空调)zwc
		if(head->Type == C_CS_NA_1) return TRUE;
		return	FALSE;
	}

	return	TRUE;
}

void	CIEC104_XL::YC_RxProc(IEC104_APP_HEAD &head)
{
	if(head.VSQ.Num == 0)	return;
	hUInt16	i,no,num,maxnum;
	nFloat i_val = 0;
	dtFloat32 value;
	FDC_YC_DATA		data;
	num = head.VSQ.Num;
	maxnum = m_pGroup->ycNum;
	switch(head.Type)
	{
	case	M_ME_NA_1:		//测量值，规一化值
		expMsg = "测量值，规一化值:";
	case	M_ME_NB_1:		//测量值，标度化值
		{
			expMsg = "测量值，标度化值:";
			printlog((LOGE_IEC104_RX+m_link), "RX <------------ 测量值 num=%d", num);
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yc_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 3;
					continue;
				}

				if(pAppDataBuf[2] & 0x81)	
				{
					pAppDataBuf += 3;	
					continue;
				}
				memset(&data, 0, sizeof(FDC_YC_DATA));
				data.val = (float)MakeWord(pAppDataBuf[0],(pAppDataBuf[1]&0x7F));
				if(pAppDataBuf[1] & 0x80)	data.val = -(float)(0xFFFF - MakeWord(pAppDataBuf[0],pAppDataBuf[1]) + 1);
				pAppDataBuf += 3;
				memcpy(&data.quality, &pAppDataBuf[2],1);
				m_dataInf.setYc(m_curRoute,no,data);
				expMsg += tValue.from( no );
				expMsg += "[";
				expMsg += tValue.from((hFloat)data.val);
				expMsg += "] ";
				printlog((LOGE_IEC104_RX+m_link), "Set YC[%d] = %f by route[%d] ", no,(float)data.val,m_curRoute);
			}
			break;
		}
	case	M_ME_TA_1:		//带CP24Time2a时标的测量值，规一化值
		expMsg = "带CP24Time2a时标的测量值，规一化值:";
	case	M_ME_TB_1:		//带CP24Time2a时标的测量值，标度化值
		{
			expMsg = "带CP24Time2a时标的测量值，标度化值:";
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yc_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 6;
					continue;
				}
				if(pAppDataBuf[2] & 0x81)	
				{
					pAppDataBuf += 6;	
					continue;
				}
				data.val = (float)MakeWord(pAppDataBuf[0],(pAppDataBuf[1]&0x7F));
				if(pAppDataBuf[1] & 0x80)	data.val = -(float)(0xFFFF - MakeWord(pAppDataBuf[0],pAppDataBuf[1]) + 1);
				pAppDataBuf += 3;
				//三个字节的时间信息暂时未处理
				pAppDataBuf += 3;
				m_dataInf.setYc(m_curRoute,no,data);
				expMsg += tValue.from( no );
				expMsg += "[";
				expMsg += tValue.from((hFloat)data.val);
				expMsg += "] ";
			}
			break;
		}
	case	M_ME_TD_1:	//带CP56Time2a时标的测量值，规一化值
		expMsg = "带CP56Time2a时标的测量值，规一化值:";
	case	M_ME_TE_1:	//带CP56Time2a时标的测量值，标度化值
		{
			expMsg = "带CP56Time2a时标的测量值，标度化值:";
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yc_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 10;
					continue;
				}
				if(pAppDataBuf[2] & 0x81)	
				{
					pAppDataBuf += 10;	
					continue;
				}
				data.val = (float)MakeWord(pAppDataBuf[0],(pAppDataBuf[1]&0x7F));
				if(pAppDataBuf[1] & 0x80)	data.val = -(float)(0xFFFF - MakeWord(pAppDataBuf[0],pAppDataBuf[1]) + 1);
				pAppDataBuf += 3;
				//七个字节的时间信息暂时未处理
				pAppDataBuf += 7;
				m_dataInf.setYc(m_curRoute,no,data);
				expMsg += tValue.from( no );
				expMsg += "[";
				expMsg += tValue.from((hFloat)data.val);
				expMsg += "] ";
			}
			break;
		}
	case	M_ME_NC_1:		//测量值，短浮点数
		{
			expMsg = "测量值，短浮点数:";
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yc_Start_Addr;
				else	no+=1;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					printlog(LOGE_IEC104_RX+m_link,"no=%d,maxnum=%d,config.Yc_Start_Addr=%d",no,maxnum,config.Yc_Start_Addr);
					pAppDataBuf += 5;
					continue;
				}
				// if(pAppDataBuf[4] & 0x81)	
				// {
				// 	pAppDataBuf += 5;
				// 	continue;
				// }
				data.quality = pAppDataBuf[4];//zwc mod 2022.08.22 质量码全拷贝
				
				memcpy(&value,pAppDataBuf,4);
				data.val = value;
				pAppDataBuf += 5;
				m_dataInf.setYc(m_curRoute,no,data);
				//printlog(LOGE_IEC104_RX+m_link,"route=%d,no=%d,value=%.3f",(int)m_curRoute,(int)no,(float)value);
				expMsg += tValue.from( no );
				expMsg += "[";
				sprintf(t_cValue,"%.2f",(hFloat)data.val);
				expMsg += t_cValue;
				expMsg += "] ";
			}
			break;
		}
	case	M_ME_TC_1:		//带CP24Time2a时标的测量值，短浮点数
		{
			expMsg = "带CP24Time2a时标的测量值，短浮点数:";
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yc_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 8;
					continue;
				}
				if(pAppDataBuf[4] & 0x81)	
				{
					pAppDataBuf += 5;	
					continue;
				}
				memcpy(&value,pAppDataBuf,4);
				data.val = value;
				pAppDataBuf += 5;
				//三个字节的时间信息暂时未处理
				pAppDataBuf += 3;
				m_dataInf.setYc(m_curRoute,no,data);
				printlog(LOGE_IEC104_RX+m_link,"TC route=%d,no=%d,value=%.3f",(int)m_curRoute,(int)no,(float)value);
				expMsg += tValue.from( no );
				expMsg += "[";
				expMsg += tValue.from((hFloat)data.val);
				expMsg += "] ";
			}
			break;
		}
	case	M_ME_TF_1:		//带CP56Time2a时标的测量值，短浮点数
		{
			expMsg = "带CP56Time2a时标的测量值，短浮点数:";
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yc_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 12;
					continue;
				}
				if(pAppDataBuf[4] & 0x81)	
				{
					pAppDataBuf += 5;	
					continue;
				}

				memcpy(&data.val ,pAppDataBuf,4);

				pAppDataBuf += 5;
				//七个字节的时间信息暂时未处理
				pAppDataBuf += 7;
				m_dataInf.setYc(m_curRoute,no,data);
				expMsg += tValue.from( no );
				expMsg += "[";
				expMsg += tValue.from((hFloat)data.val);
				expMsg += "] ";
			}
			break;
		}
	case	M_ME_ND_1:		//测量值，不带品质描述词的规一化值
		{
			expMsg = "测量值，不带品质描述词的规一化值:";
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yc_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 2;
					continue;
				}
				data.val = (float)MakeWord(pAppDataBuf[0],(pAppDataBuf[1]&0x7F));
				if(pAppDataBuf[1] & 0x80)	data.val = -(float)(0xFFFF - MakeWord(pAppDataBuf[0],pAppDataBuf[1]) + 1);
				pAppDataBuf += 2;
				m_dataInf.setYc(m_curRoute,no,data);
				expMsg += tValue.from( no );
				expMsg += "[";
				expMsg += tValue.from((hFloat)data.val);
				expMsg += "] ";
			}
			break;
		}
	default:
		break;
	}
}

void	CIEC104_XL::FIX_RxProc(IEC104_APP_HEAD &head)
{
	printlog((LOGE_IEC104_RX+m_link), "RX <----------------------- Fix_RxProc");
	printlog(1234, "RX <----------------------- Fix_RxProc config.DataTran_type =%d",config.DataTran_type);
	if(head.VSQ.Num == 0)	return;

	unsigned short	i,num,no;
	num = head.VSQ.Num;
	no	= GetObjAddr() - config.Fix_Start_Addr;
		
	if(config.DataTran_type == IEC104_OBJ_16_TRANSTYPE)
	{
		pInfo->cur_fix.groupNo = MakeWord(pInfo->cur_fixbuf[0] , pInfo->cur_fixbuf[1]); 
		pInfo->cur_fix.dataBuf[0] = LoByte(LoWord(m_pRoute->group)); //iedNo
		pInfo->cur_fix.dataBuf[1] = HiByte(LoWord(m_pRoute->group));
		printlog(1234, "RX <-------- Fix_RxProc  groupNo =%d",(int)pInfo->cur_fix.groupNo);
	}else
	{
		pInfo->cur_fix.dataBuf[0] = pInfo->cur_fixbuf[0]; //iedNo
		pInfo->cur_fix.dataBuf[1] = pInfo->cur_fixbuf[1];
	}
	pInfo->cur_fix.dataBuf[2] = pInfo->cur_fixbuf[2]; //areaNo
	pInfo->cur_fix.dataBuf[3] = pInfo->cur_fixbuf[3];

	pInfo->cur_fix.dataBuf[4] = pAppDataBuf[4]; //序号
	pInfo->cur_fix.dataBuf[5] = pAppDataBuf[5];

	pInfo->cur_fix.dataBuf[6] = pAppDataBuf[6]; //个数
	pInfo->cur_fix.dataBuf[7] = pAppDataBuf[7];
	pInfo->cur_fix.dataBuf[8] = pAppDataBuf[8];
	pInfo->cur_fix.dataBuf[9] = pAppDataBuf[9];


	for ( i = 2; i < num; i++ )
	{
		pInfo->cur_fix.dataBuf[6*i-2] = pAppDataBuf[6*i-2];
		pInfo->cur_fix.dataBuf[6*i-1] = pAppDataBuf[6*i-1];
		pInfo->cur_fix.dataBuf[6*i]	  = pAppDataBuf[6*i];
		pInfo->cur_fix.dataBuf[6*i+1] = pAppDataBuf[6*i+1];
		pInfo->cur_fix.dataBuf[6*i+2] = pAppDataBuf[6*i+2];
		pInfo->cur_fix.dataBuf[6*i+3] = pAppDataBuf[6*i+3];
	}
	printlog(1234, "RX <---Fix_RxProc pAppDataBuf[0] = %d",pAppDataBuf[0]);
	printlog(1234, "RX <---Fix_RxProc pAppDataBuf[1] = %d",pAppDataBuf[1]);


	if(config.DataTran_type == IEC104_OBJ_16_TRANSTYPE)
	{//此节点为数据转换
		unsigned char buf[512];
		hInt32 headLen = sizeof( ctrl_head );
		hInt32 dataLen = sizeof( ctrl_pro_fix );
		ctrl_head*	pHead = (ctrl_head*)buf;
		pHead->type = CTRL_PRO_FIX;
		ctrl_pro_fix* pData = (ctrl_pro_fix*)(buf + headLen );	

		memcpy(pData,&pInfo->cur_fix,dataLen);
		m_ctrlInf.add(buf, headLen + dataLen,0);
	}else
	{//此节点为主控段
		m_fdcCtrl.fixAck(&pInfo->cur_fix);
	}
		
	printlog(1234, "RX <----------- Fix_RxProc end ");

}
void	CIEC104_XL::YX_RxProc(IEC104_APP_HEAD &head)
{
	if(head.VSQ.Num == 0)	return;
	hUInt16	i,no,num,maxnum;
	FDC_YX_DATA		data;
	num = head.VSQ.Num;
	maxnum = m_pGroup->yxNum;
	
	switch(head.Type)
	{
	case	M_SP_NA_1:		//单点信息
		{
			expMsg = "单点遥信:";
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yx_Start_Addr;
				else	no++;

				if(no >= maxnum)
				{  // printlog(LOGE_IEC104_RX+m_link,"单点信息no==%d,maxnum==%d,config.Yx_Start_Addr==%d",no,maxnum,config.Yx_Start_Addr);
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 1;
					continue;
				}
				//if (pAppDataBuf[0] & 0x80)	//过滤无效
				//{
				//	pAppDataBuf += 1;
				//	continue ;
				//}
				data.val = pAppDataBuf[0] & 0x01;
				data.quality = pAppDataBuf[0] & 0xFE;
		// 保存数据
				m_dataInf.setYx(m_curRoute,no,data,FALSE);

				expMsg += tValue.from(no);
				if (pAppDataBuf[0] & 0x01)
					expMsg += "[合] ";
				else
					expMsg += "[分] ";

				pAppDataBuf += 1;
				//printlog(LOGE_IEC104_RX+m_link,"m_curRoute=%d,no=%d,value=%d",m_curRoute,no,data.val);
				
			}
			break;
		}
	case	M_DP_NA_1:		//双点信息
		{
			expMsg = "双点遥信:";
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yx_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 1;
					continue;
				}
				data.val = pAppDataBuf[0] & 0x03;
				
				if(data.val!=0x01 && data.val!=0x02)	continue;
				data.val -= 0x01;		
				m_dataInf.setYx(m_curRoute,no,data,FALSE);
				expMsg += tValue.from(no);
				if (data.val == 0x01)
					expMsg += "[合] ";
				else
					expMsg += "[分] ";
				
				pAppDataBuf += 1;
			}
			break;
		}
	case	M_PS_NA_1:		//带变位检出的成组单点信息
		expMsg = "带变位检出的成组单点信息,未处理";
		break;
	default:
		break;
	}
}

void	CIEC104_XL::SOE_RxProc(IEC104_APP_HEAD &head)
{
	printlog((LOGE_IEC104_RX+m_link), "Rx <------------- soe  ------------------");

	if(head.VSQ.Num == 0)	return;
	int				ms;
	dtUint8			value;
	hUInt16	i,no,num,maxnum;
	ECON_SYS_TIME		soetime;
	FDC_SOE_DATA	soedata;
	num = head.VSQ.Num;
	maxnum =m_pGroup->yxNum;
	char strtime[128]={0};
	switch(head.Type)
	{
	case	M_SP_TA_1:		//带CP24Time2a时标的单点信息
		{
			expMsg = "带CP24Time2a时标的单点信息(SOE)";
			printlog((LOGE_IEC104_RX+m_link), "带CP24Time2a时标的单点信息(SOE)");
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yx_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 4;
					continue;
				}
				value = pAppDataBuf[0] & 0x01;
				ms = MakeWord(pAppDataBuf[1],pAppDataBuf[2]);
				GetTime(&soetime);
				soetime.min = pAppDataBuf[3] & 0x3F;
				soetime.sec = ms / 1000;
				soetime.ms = ms % 1000;
				memset(&soedata,0,sizeof(FDC_SOE_DATA));
				soedata.soeTime.year = soetime.year;
				soedata.soeTime.mon = soetime.mon;
				soedata.soeTime.day = soetime.day;
				soedata.soeTime.hour = soetime.hour;
				soedata.soeTime.min = soetime.min;
				soedata.soeTime.sec = soetime.sec;
				soedata.soeTime.msec = soetime.ms;
				soedata.no = no;
				soedata.val = value;
				pAppDataBuf += 4;
				//m_dataInf.setSoe(m_curRoute,soedata,true,true);
				m_fdcsoe.setSoe(m_curRoute,soedata,false,false);
				expMsg += tValue.from(no);
				if (soedata.val == 0x01)
					expMsg += "[合] ";
				else
					expMsg += "[分] ";
				expMsg += "时间:";
				sprintf(strtime,"%04d-%02d-%02d %02d:%02d:%02d %03d  ",
					(int)soetime.year,(int)soetime.mon,(int)soetime.day,(int)soetime.hour,
					(int)soetime.min,(int)soetime.sec,(int)soetime.ms);
				expMsg += strtime;
			}
			break;
		}
	case	M_DP_NA_1:		//带CP24Time2a时标的双点信息
		{
			expMsg = "带CP24Time2a时标的双点信息(SOE)";
			printlog((LOGE_IEC104_RX+m_link), "带CP24Time2a时标的双点信息(SOE)");
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yx_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 4;
					continue;
				}
				value = pAppDataBuf[0] & 0x03;
				if(value!=0x01 && value!=0x02)	{pAppDataBuf += 4;	continue;}
				value -= 0x01;

				ms = MakeWord(pAppDataBuf[1],pAppDataBuf[2]);
				GetTime(&soetime);
				soetime.min = pAppDataBuf[3] & 0x3F;
				soetime.sec = ms / 1000;
				soetime.ms = ms % 1000;
				memset(&soedata,0,sizeof(FDC_SOE_DATA));
				soedata.soeTime.year = soetime.year;
				soedata.soeTime.mon = soetime.mon;
				soedata.soeTime.day = soetime.day;
				soedata.soeTime.hour = soetime.hour;
				soedata.soeTime.min = soetime.min;
				soedata.soeTime.sec = soetime.sec;
				soedata.soeTime.msec = soetime.ms;
				soedata.no = no;
				soedata.val = value;
				pAppDataBuf += 4;
				//m_dataInf.setSoe(m_curRoute,soedata,true,true);
				m_fdcsoe.setSoe(m_curRoute,soedata,false,false);
				expMsg += tValue.from(no);
				if (soedata.val == 0x01)
					expMsg += "[合] ";
				else
					expMsg += "[分] ";
				expMsg += "时间:";
				sprintf(strtime,"%04d-%02d-%02d %02d:%02d:%02d %03d  ",(int)soetime.year,(int)soetime.mon,(int)soetime.day,(int)soetime.hour,
					(int)soetime.min,(int)soetime.sec,(int)soetime.ms);
				expMsg += strtime;
			}
			break;
		}
	case	M_SP_TB_1:	//带CP56Time2a时标的单点信息
		{
			expMsg = "带CP56Time2a时标的单点信息(SOE)";
			printlog((LOGE_IEC104_RX+m_link), "带CP56Time2a时标的单点信息(SOE)");
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yx_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 8;
					continue;
				}
				value = pAppDataBuf[0] & 0x01;

				ms = MakeWord(pAppDataBuf[1],pAppDataBuf[2]);
				soetime.min = pAppDataBuf[3] & 0x3F;	//bit0-5 6位
				soetime.hour = pAppDataBuf[4] & 0x1F;	//bit0-4 5位
				soetime.day = pAppDataBuf[5] & 0x1F;	//bit0-4 5位
				soetime.wday = (pAppDataBuf[5]>>5) & 0x07;	//bit5-7 3位
				soetime.mon = pAppDataBuf[6] & 0x0F;		//bit0-3 4位
				soetime.year = (pAppDataBuf[7]&0x7F) + 2000;	//bit0-6 7位
				soetime.sec = ms / 1000;
				soetime.ms = ms % 1000;	


				memset(&soedata,0,sizeof(FDC_SOE_DATA));
				soedata.soeTime.year = soetime.year;
				soedata.soeTime.mon = soetime.mon;
				soedata.soeTime.day = soetime.day;
				soedata.soeTime.hour = soetime.hour;
				soedata.soeTime.min = soetime.min;
				soedata.soeTime.sec = soetime.sec;
				soedata.soeTime.msec = soetime.ms;
				soedata.no = no;
				soedata.val = value;
				pAppDataBuf += 8;
				//m_dataInf.setSoe(m_curRoute,soedata,true,true); 暂不做处理
				m_fdcsoe.setSoe(m_curRoute,soedata,false,false);		//liph 测试，做处理
				expMsg += tValue.from(no);
				if (soedata.val == 0x01)
					expMsg += "[合] ";
				else
					expMsg += "[分] ";
				expMsg += "时间:";
				sprintf(strtime,"%04d-%02d-%02d %02d:%02d:%02d %03d  ",
					(int)soetime.year,(int)soetime.mon,(int)soetime.day,(int)soetime.hour,
					(int)soetime.min,(int)soetime.sec,(int)soetime.ms);
				expMsg += strtime;
			}
			break;
		}
	case	M_DP_TB_1:	//带CP56Time2a时标的双点信息
		{
			expMsg = "带CP56Time2a时标的双点信息(SOE)";
			printlog((LOGE_IEC104_RX+m_link), "带CP56Time2a时标的双点信息(SOE)");
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = GetObjAddr() - config.Yx_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 8;
					continue;
				}
				value = pAppDataBuf[0] & 0x03;
				if(value!=0x01 && value!=0x02)	{pAppDataBuf += 8;	continue;}
				value -= 0x01;
				ms = MakeWord(pAppDataBuf[1],pAppDataBuf[2]);
				soetime.min = pAppDataBuf[3] & 0x3F;
				soetime.hour = pAppDataBuf[4] & 0x1F;
				soetime.day = pAppDataBuf[5] & 0x1F;
				soetime.wday = (pAppDataBuf[5]>>5) & 0x07;
				soetime.mon = pAppDataBuf[6] & 0x0F;
				soetime.year = (pAppDataBuf[7]&0x7F) + 2000;
				soetime.sec = ms / 1000;
				soetime.ms = ms % 1000;
				memset(&soedata,0,sizeof(FDC_SOE_DATA));
				soedata.soeTime.year = soetime.year;
				soedata.soeTime.mon = soetime.mon;
				soedata.soeTime.day = soetime.day;
				soedata.soeTime.hour = soetime.hour;
				soedata.soeTime.min = soetime.min;
				soedata.soeTime.sec = soetime.sec;
				soedata.soeTime.msec = soetime.ms;
				soedata.no = no;
				soedata.val = value;
				pAppDataBuf += 8;
				//m_dataInf.setSoe(m_curRoute,soedata,true,true);
				m_fdcsoe.setSoe(m_curRoute,soedata,false,false);
				expMsg += tValue.from(no);
				if (soedata.val == 0x01)
					expMsg += "[合] ";
				else
					expMsg += "[分] ";
				expMsg += "时间:";
				sprintf(strtime,"%04d-%02d-%02d %02d:%02d:%02d %03d  ",
					(int)soetime.year,(int)soetime.mon,(int)soetime.day,(int)soetime.hour,
					(int)soetime.min,(int)soetime.sec,(int)soetime.ms);
				expMsg += strtime;
			}
			break;
		}
	default:
		break;
	}
}

void	CIEC104_XL::HISDATA_RxProc(IEC104_APP_HEAD &head)
{
	printlog((LOGE_IEC104_RX+m_link), "RX <------------ HISDATA,But do nothing");
	return;
	/*
	//printlog(19800, "RX <-------------- 收到省调召唤历史数据命令"); 
	SYS_EVENT sysevt;
	memset(&sysevt, 0, sizeof(sysevt));
	strcpy(sysevt.group_name, "SCADA_HIS_DAT");
	memcpy(sysevt.char_info,pAppDataBuf,16);           //add:G.Z.Wang 20060724

	struct ___________________________ttime                             //add:G.Z.Wang for test start
	{
		dtInt32 a,b,c,d;
	} t;
	memcpy(&t,pAppDataBuf,16);	
	printlog(19800,"time is %d:%d:%d:%d",(int)t.a,(int)t.b,(int)t.c,(int)t.d);			//add:G.Z.Wang for test end
	printlog(19800,"time is %x:%x:%x:%x:%x:%x:%x:%x",sysevt.char_info[0],sysevt.char_info[1],sysevt.char_info[2],sysevt.char_info[3],sysevt.char_info[4],sysevt.char_info[5],sysevt.char_info[6],sysevt.char_info[7]);//add:G.Z.Wang for test
	if (head.Type == A_HD_NA_1) // 遥测
	{
		sysevt.event_type = 4001;
	}
	else if (head.Type == E_HD_NA_1) // 电度
	{
		sysevt.event_type = 4002;
	}
	evtcli.SaveEvent(&sysevt);
	*/
}

void	CIEC104_XL::KWH_RxProc(IEC104_APP_HEAD &head)
{
	if(head.VSQ.Num == 0)	return;
	hUInt16	i,no,num,maxnum,bgn;//kwh2yc
	FDC_KWH_DATA		data;
	FDC_YC_DATA		ycdata;//kwh2yc
	memset(&data, 0, sizeof(data));
	memset(&ycdata, 0, sizeof(ycdata));//kwh2yc
	num = head.VSQ.Num;
	bgn = config.Para_Start_Addr;//此特征值做为kwh转yc的起始地址 大于零有效
	maxnum = (bgn > 0 ? m_pGroup->ycNum : m_pGroup->kwhNum); //kwh2yc
	switch(head.Type)
	{
		expMsg = "累积量:";
	case	M_IT_NA_1:		//累积量
		{
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = bgn + GetObjAddr() - config.Kwh_Start_Addr;
				else	no++;
				printlog((LOGE_IEC104_RX+m_link), "kwh no=%hu, bgn=%hu", no, bgn);
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 5;
					continue;
				}
				if(pAppDataBuf[4] & 0x80)	{pAppDataBuf += 5;	continue;}
				data.val = MakeLong(MakeWord(pAppDataBuf[0],pAppDataBuf[1]),MakeWord(pAppDataBuf[2],pAppDataBuf[3]));
				pAppDataBuf += 5;
				if(bgn > 0) {
					ycdata.val = data.val;
					m_dataInf.setYc(m_curRoute,no,ycdata);
				}else {
					m_dataInf.setKwh(m_curRoute,no,data);
				}
				
				expMsg += tValue.from( no );
				expMsg += "[";
				expMsg += tValue.from((hDouble)data.val);
				expMsg += "] ";
			}
			break;
		}
	case	M_IT_TA_1:		//带CP24Time2a时标的累积量
		{
			expMsg = "带CP24Time2a时标的累积量:";
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = bgn + GetObjAddr() - config.Kwh_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 8;
					continue;
				}
				if(pAppDataBuf[4] & 0x80)	{pAppDataBuf += 8;	continue;}
				data.val = MakeLong(MakeWord(pAppDataBuf[0],pAppDataBuf[1]),MakeWord(pAppDataBuf[2],pAppDataBuf[3]));
				pAppDataBuf += 5;
				//三个字节的时间信息暂时未处理
				pAppDataBuf += 3;
				if(bgn > 0) {
					ycdata.val = data.val;
					m_dataInf.setYc(m_curRoute,no,ycdata);
				}else {
					m_dataInf.setKwh(m_curRoute,no,data);
				}
				expMsg += tValue.from( no );
				expMsg += "[";
				expMsg += tValue.from((hDouble)data.val);
				expMsg += "] ";
			}
			break;
		}
	case	M_IT_TB_1:		//带CP56Time2a时标的累积量
		{
			expMsg = "带CP56Time2a时标的累积量:";
			for(i=0;i<num;i++)
			{
				if(i==0 || head.VSQ.SQ==0)	no = bgn + GetObjAddr() - config.Kwh_Start_Addr;
				else	no++;
				if(no >= maxnum)
				{
					if(head.VSQ.SQ)	break;
					pAppDataBuf += 12;
					continue;
				}
				if(pAppDataBuf[4] & 0x80)	{pAppDataBuf += 12;	continue;}
				data.val = MakeLong(MakeWord(pAppDataBuf[0],pAppDataBuf[1]),MakeWord(pAppDataBuf[2],pAppDataBuf[3]));
				pAppDataBuf += 5;
				//七个字节的时间信息暂时未处理
				pAppDataBuf += 7;
				if(bgn > 0) {
					ycdata.val = data.val;
					m_dataInf.setYc(m_curRoute,no,ycdata);
				}else {
					m_dataInf.setKwh(m_curRoute,no,data);
				}

				expMsg += tValue.from( no );
				expMsg += "[";
				expMsg += tValue.from((hDouble)data.val);
				expMsg += "] ";
			}
			break;
		}
	default:
		break;
	}
}
void	CIEC104_XL::FILE_RxDirProc(IEC104_APP_HEAD &head)
{
	if(head.VSQ.Num == 0)	return;
	//传输原因
	if(head.COT.Cause!=CAUSE_ACT_CON)
	{
		//if(pInfo->WorkState == STATE_WAIT_FILE_DIR)
		if(pInfo->WorkState == STATE_FREE)
		{
			pInfo->WorkState = STATE_FREE;
			pInfo->T5 = 0;
		}
		return;
	}
	//else if(pInfo->WorkState == STATE_WAIT_FILE_DIR)
	else if(pInfo->WorkState == STATE_FREE)
	{
		pInfo->CallOneFileFlag = TRUE;
		pInfo->T5 = 0;
	}

	pInfo->FileBasicInfo.clear();
	hUInt16 no,num;
	num = head.VSQ.Num;
	no = GetObjAddr();
	AppDataLen -= config.ObjAddrLen;
	IEC104_FILE_BASE_INFO m_fileinfo;
	memset(&m_fileinfo,0,sizeof(IEC104_FILE_BASE_INFO));
	hUChar filestate;
	int m_DataLen = AppDataLen;
		VEC_FILE_INFO tmpF;
	while(m_DataLen>0)
	{
		if(!m_pRoute)
			m_fileinfo.GroupNo = m_pRoute->group;
		else m_fileinfo.GroupNo = 0;
		m_fileinfo.FileObjAddr  = no;//信息体地址
		m_fileinfo.FileNo  = pAppDataBuf[0] + pAppDataBuf[1]*256;
		m_fileinfo.FileLen = pAppDataBuf[2] + pAppDataBuf[3]*256 + pAppDataBuf[4]*256*256;
		filestate = pAppDataBuf[5];
		if(filestate & 0x40)
			m_fileinfo.FileType = 1;//子目录
		else
			m_fileinfo.FileType = 0;//文件
		pAppDataBuf += 6; 
		pAppDataBuf += App_CP56Time2a( pAppDataBuf ,(m_fileinfo.FileClock ));
		m_fileinfo.SectionNo = 0;
		//tmpF.push_back(m_fileinfo); 
		pInfo->FileBasicInfo.push_back(m_fileinfo); 

		AppDataLen -= 13;
		m_DataLen -=13;
	}

	printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 接受到的文件个数为%d",pInfo->FileBasicInfo.size());

}
void	CIEC104_XL::FILE_RxFileReadyProc(IEC104_APP_HEAD &head)
{
	if(head.VSQ.Num == 0)	return;
	//传输原因
	if(head.COT.Cause!=CAUSE_TRANSFILE)
		return ; 

	pInfo->CurFile.FileObjAddr = GetObjAddr();//信息体地址
	pInfo->CurFile.FileNo = pAppDataBuf[0] + pAppDataBuf[1]*256;
	pInfo->CurFile.FileLen = pAppDataBuf[2] + pAppDataBuf[3]*256 + pAppDataBuf[4]*256*256;
	if( pAppDataBuf[5] & 0x80)
	{
		pInfo->IsCallingFile = TRUE;
		return ; 
	}
	pInfo->IsCallingFile = TRUE;
	pInfo->CallFileCmd = CMD_CALL_FILE;
	
	printlog((LOGE_IEC104_RX+m_link), "Rx <--------- 文件准备就绪,文件名 %d",pInfo->CurFile.FileNo);
}
void	CIEC104_XL::FILE_RxSectionReadyProc(IEC104_APP_HEAD &head)
{
	if(head.VSQ.Num == 0)	return;
	if(head.COT.Cause!=CAUSE_TRANSFILE)
		return ; 
	pInfo->CurFile.FileObjAddr = GetObjAddr();//信息体地址
	pInfo->CurFile.FileNo = pAppDataBuf[0] + pAppDataBuf[1]*256;
	pInfo->CurFile.SectionNo = pAppDataBuf[2] ;
	pInfo->CurFile.FileLen = pAppDataBuf[3] + pAppDataBuf[4]*256 + pAppDataBuf[5]*256*256;
	if( pAppDataBuf[6] & 0x80)
	{
		return ; 
	}
	pInfo->CallFileCmd = CMD_CALL_SECTION;
}
void	CIEC104_XL::FILE_RxSegmentProc(IEC104_APP_HEAD &head)
{
	if(head.VSQ.Num == 0)	return;
	if(pInfo->CurFile.FileObjAddr != GetObjAddr()
		||(pInfo->CurFile.FileNo != pAppDataBuf[0] + pAppDataBuf[1]*256)
		||(pInfo->CurFile.SectionNo != pAppDataBuf[2])
		)
		return;
	for (int i=0;i< pAppDataBuf[3];i++)
	{
		char chbuf[10];
		sprintf(chbuf,"%c",pAppDataBuf[4+i]);
		//pInfo->FileData += chbuf;
		pInfo->FileData += (char)pAppDataBuf[4+i];
	}
}
void	CIEC104_XL::FILE_RxLastSegmentProc(IEC104_APP_HEAD &head)
{
	if(head.VSQ.Num == 0)	return;
	if(head.COT.Cause!=CAUSE_TRANSFILE)
		return ; 
	if(pInfo->CurFile.FileObjAddr != GetObjAddr())
		return;
	if((pInfo->CurFile.FileNo != pAppDataBuf[0] + pAppDataBuf[1]*256)
		||(pInfo->CurFile.SectionNo != pAppDataBuf[2])
		)
		return;
	if(pAppDataBuf[3] ==0)
		pInfo->CallFileCmd = CMD_CON_SECTION ;
	else
		pInfo->CallFileCmd = CMD_DENY_SECTION ;
}
void	CIEC104_XL::FILE_RxLastSectionProc(IEC104_APP_HEAD &head)
{
	if(head.VSQ.Num == 0)	return;
	if(head.COT.Cause!=CAUSE_TRANSFILE)
		return ; 
	if(pInfo->CurFile.FileObjAddr != GetObjAddr())
		return;
	if( (pInfo->CurFile.FileNo != pAppDataBuf[0] + pAppDataBuf[1]*256)
		||(pInfo->CurFile.SectionNo != pAppDataBuf[2])
		)
		return;

	if(pAppDataBuf[3] == 1 || pAppDataBuf[3] == 2 )
	{
		FILE_WriteData(pInfo->CurFile,pInfo->FileData);
		pInfo->CallFileCmd = CMD_CON_FILE ;
	}
	else
	{
		pInfo->CallFileCmd = CMD_CON_SECTION ;
		//pInfo->CallFileCmd = CMD_DENY_FILE ;
	}
}

/********************************************************************************
*		描述: 应用层CP56Time2a格式解码.
*		参数: 
*			  buf: 报文编码.
*		返回: 处理的字节数.
********************************************************************************/
void	CIEC104_XL::getDateTimeStr(char* datetime,int&days,int& secs)
{
	time_t sec = time(NULL);
	struct tm *systime,timeptr;
	systime = localtime((const time_t*)&sec);
	sprintf(datetime,"%4d-%02d-%02d %02d:%02d:%02d",systime->tm_year+1900,systime->tm_mon+1,systime->tm_mday,systime->tm_hour,systime->tm_min,systime->tm_sec);
	//计算距离2000年的天数和今天的秒数
	timeptr.tm_year=100;
	timeptr.tm_mon=0;
	timeptr.tm_mday=1;
	timeptr.tm_hour=0;
	timeptr.tm_min=0;
	timeptr.tm_sec=0;

	time_t ts= mktime(&timeptr);

	days = (sec-ts)/86400;
	secs = systime->tm_hour*3600+systime->tm_min*60+systime->tm_sec;

}
int		CIEC104_XL::FILE_WriteData(IEC104_FILE_BASE_INFO &CurFile,ECON::CString &FileData)
{	
	FILE	*fp;
	ECON::CString filename;
	filename = config.File_Store_Addr;
	filename += "/" ;

	char	chGroupNo[20], chFileNo[20];
	sprintf(chGroupNo,"%d",CurFile.GroupNo);
	sprintf(chFileNo,"%d",CurFile.FileNo);
	filename += chGroupNo;
	CDirectory  dir(filename.c_str());
	if(!dir.isExist())
		dir.mkdir(filename.c_str());
	filename +=  "/" ;
	filename += chFileNo ;

	fp = fopen(filename.c_str(),"w+");
	if (fp==NULL)
	{
		printlog((LOGE_IEC104_RX+m_link),"打开文件 %s 失败",filename.c_str());
		return FALSE;
	}
	int filelen = fprintf(fp,"%s",FileData.c_str());
	fclose(fp);
	FileData = "";
	if( filelen == filename.length())
	{
		printlog((LOGE_IEC104_RX+m_link),"写入文件 %s 成功",filename.c_str());
		return filelen;
	}
	return filelen;
	
}
int		CIEC104_XL::App_CP56Time2a( hUInt8 *buf ,ECON_SYS_TIME &ferttime)
{
	hUInt32 ms, minute;
	int  ptr = 0;

	ms = buf[ptr+1]*256+buf[ptr];
	ptr+=2;
	minute = buf[ptr]&0x3f;
	ptr++;

	ferttime.hour = buf[ptr]&0x1f;
	ptr++;
	ferttime.day  = buf[ptr]&0x1f;
	ptr++;
	ferttime.mon = buf[ptr]&0xf;
	ptr++;
	ferttime.year = (buf[ptr]&0x7f)+2000;
	ptr++;
	ferttime.min = minute;
	ferttime.sec = ms/1000;
	ferttime.ms = ms%1000;

	return  7;
}

// 获取当前时间并转换为CP56Time2a格式
void CIEC104_XL::genCP56Time2a(unsigned char* datetime) 
{
    time_t rawtime;
    struct tm * timeinfo;

    time(&rawtime);
    timeinfo = localtime(&rawtime);

    struct timeval tv;
    gettimeofday(&tv, NULL);

    //printf("timeinfo: %d-%d-%d %d:%d:%d:%lld\n", timeinfo->tm_year+1900, timeinfo->tm_mon+1, timeinfo->tm_mday, timeinfo->tm_hour, timeinfo->tm_min, timeinfo->tm_sec, (long long)tv.tv_usec / 1000);

    // 填充毫秒部分
    uint16_t ms = timeinfo->tm_sec * 1000 + tv.tv_usec / 1000;
	datetime[0] = ms % 256;
	datetime[1] = ms / 256;

    // 处理IVResMinute字段
    datetime[2] = timeinfo->tm_min & 0x3F;  
    // 处理SURes2Hour字段
    datetime[3] = timeinfo->tm_hour & 0x1F;  
    // 处理DOWDay字段
    datetime[4] = ((timeinfo->tm_wday == 0? 7 : timeinfo->tm_wday) << 5) | (timeinfo->tm_mday & 0x1F);  
    // 处理Res3Month字段
    datetime[5] = (timeinfo->tm_mon + 1) & 0x0F;  // 月份加1取低4位
    // 处理Res4Year字段
    datetime[6] = (timeinfo->tm_year+1900-2000) & 0x7F;  // 年份-2000，再取低7位
	
	return;

}

/*------------------------------------------------------------------------------
事件数据接收处理
*/
void	CIEC104_XL::EVENT_RxProc(IEC104_APP_HEAD &head)
{
	if ( head.VSQ.Num == 0 )	return;

	switch(head.COT.Cause)
	{
	case CAUSE_SPONT://接受数据
		{
			int no = GetObjAddr();
			if ((pInfo->RecvEventSize + head.VSQ.Num) > sizeof(SYS_EVENT))
			{
				printlog((LOGE_IEC104_RX+m_link), "Rx <----------- 接收事件数据非法pInfo->RecvEventSize:%d, head.VSQ.Num=%d",
					pInfo->RecvEventSize, head.VSQ.Num);
				expMsg = "接收事件数据非法";
				memset(&one_event, 0, sizeof(SYS_EVENT));
				pInfo->RecvEventSize = 0;
				return;
			}

			memcpy( ((unsigned char * )&one_event) + pInfo->RecvEventSize, pAppDataBuf, head.VSQ.Num);
			printlog((LOGE_IEC104_RX+m_link), "Rx <----------- 事件数据信息(no:%d, num=%d),累计接收%d", no, head.VSQ.Num,pInfo->RecvEventSize);
			expMsg = "接收事件数据信息部分";
			pInfo->RecvEventSize += head.VSQ.Num;
			break;
		}
	case CAUSE_ACT_TERM://终止
		{
			printlog((LOGE_IEC104_RX+m_link), "Rx <----------- 事件数据终结");
			if (pInfo->RecvEventSize == sizeof(SYS_EVENT))//完整的计划值
			{
				printlog((LOGE_IEC104_RX+m_link), "Rx <----------- 收到完整的事件数据");
				char tmpline[1024];
				sprintf(tmpline,"收到完整的事件数据,sort:%d,type=%d,info=%s%s",(int)one_event.event_sort,(int)one_event.event_type,one_event.char_info,one_event.tone_info);
				expMsg = tmpline;
				Set_Event();
			}
			else
			{
				expMsg = "事件数据不完整";
				printlog((LOGE_IEC104_RX+m_link), "Rx <----------- 事件数据不完整");
			}
			memset(&one_event, 0, sizeof(SYS_EVENT));
			pInfo->RecvEventSize = 0;
		}
	default:
		break;
	}
}

const void * CIEC104_XL::readFile(const char * fname)
{
	FileManage fmgr(fname);
	FILE * pf = fopen(fmgr.get_filename(), "rb");
	if(!pf) {
		printlog(LOGE_IEC104_BASE+m_link, "fopen [%s] failed", fname);
		return NULL;
	}

	fseek(pf, 0, SEEK_END);  		// 将文件指针移动到文件末尾
	long file_size = ftell(pf);  	// 获取文件大小
	fseek(pf, 0, SEEK_SET);  		// 将文件指针移动回文件开头

	char * buf = NULL;
	buf = (char *)malloc(file_size+1);
	memset(buf, 0, file_size);

	size_t rs = fread(buf, file_size, 1, pf);
	fclose(pf);
	if(rs != 1) {
		printlog(LOGE_IEC104_BASE+m_link, "fread [%s] failed", fname);
		return NULL;
	}

	printlog(LOGE_IEC104_BASE+m_link, "fread [%s] ok", fname);

	return buf;
}

/*-----------------------------------------------------------------------
传输事件
*/
void	CIEC104_XL::Set_Event()								//事件接收处理
{
	printlog((LOGE_IEC104_RX+m_link), "Rx <----------- 设置事件数据,sizeof(SYS_EVENT)=%d,sort=%d,type=%d,info=%s",sizeof(SYS_EVENT),one_event.event_sort,(int)one_event.event_type,one_event.char_info);
	CEventClient evtClnt;
	one_event.event_source = 0;//源：前置，需要往EMS写
	one_event.state_value2 = (hUInt16)m_pRoute->remoteAddr;//对端地址,即对端的vcc号
	evtClnt.SaveEvent(&one_event) ;
}

//----------------------------------------------------------------------------
extern "C" IEC104_EXPORT ECON::FDC::CProtocol * CreateProtocol()
{
	return ( new CIEC104_XL() );
}
extern "C" IEC104_EXPORT void DestroyProtocol(ECON::FDC::CProtocol *p)
{
	delete p;
}