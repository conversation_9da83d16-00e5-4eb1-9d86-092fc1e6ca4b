################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

ACLOCAL_AMFLAGS = -I config


AM_CPPFLAGS = \
    ${libzmq_CFLAGS} \
    ${uuid_CFLAGS} \
    ${systemd_CFLAGS} \
    ${lz4_CFLAGS} \
    ${libcurl_CFLAGS} \
    ${nss_CFLAGS} \
    ${libmicrohttpd_CFLAGS} \
    -I$(srcdir)/include

project_libs = ${libzmq_LIBS} ${uuid_LIBS} ${systemd_LIBS} ${lz4_LIBS} ${libcurl_LIBS} ${nss_LIBS} ${libmicrohttpd_LIBS}

SUBDIRS = doc
SUBDIRS += include
DIST_SUBDIRS = doc
DIST_SUBDIRS += include

lib_LTLIBRARIES =
bin_PROGRAMS =
noinst_PROGRAMS =
check_PROGRAMS =
noinst_LTLIBRARIES =
TESTS =
# Prepare variables that can be populated (appended) in generated Makefiles or
# manually maintained src/Makemodule-local.am
EXTRA_DIST =
CLEANFILES =
DISTCLEANFILES =

if ENABLE_DIST_CMAKEFILES
EXTRA_DIST += \
    Findlibzmq.cmake \
    Finduuid.cmake \
    Findsystemd.cmake \
    Findlz4.cmake \
    Findlibcurl.cmake \
    Findnss.cmake \
    Findlibmicrohttpd.cmake \
    src/CMakeLists-local.txt \
    builds/cmake/Modules/ClangFormat.cmake \
    builds/cmake/clang-format-check.sh.in \
    builds/cmake/Config.cmake.in \
    CMakeLists.txt
endif

EXTRA_DIST += \
    bindings \
    src/zsock_option.inc \
    src/zgossip_engine.inc \
    src/zhash_primes.inc \
    src/foreign/sha1/sha1.inc_c \
    src/foreign/sha1/sha1.h \
    src/foreign/slre/slre.inc_c \
    src/foreign/slre/slre.h \
    src/foreign/slre/readme.txt \
    src/zgossip_msg.h \
    LICENSE \
    README.txt \
    README.md \
    CONTRIBUTING.md \
    src/czmq_classes.h

# NOTE: this "include" syntax is not a "make" but an "autotools" keyword,
# see https://www.gnu.org/software/automake/manual/html_node/Include.html
include $(srcdir)/src/Makemodule.am
include $(srcdir)/src/Makemodule-local.am # Optional project-local hook

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
