################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class ZhttpRequest(object):
    """
    Http request that can be received from zhttp_server or sent to zhttp_client.
Class can be reused between send & recv calls.
Headers and Content is being destroyed after every send call.
    """

    def __init__(self):
        """
        Create a new http request.
        """
        p = utils.lib.zhttp_request_new()
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zhttp_request_destroy_py)

    def recv(self, sock):
        """
        Receive a new request from zhttp_server.
        Return the underlying connection if successful, to be used when calling zhttp_response_send.
        """
        return utils.lib.zhttp_request_recv(self._p, sock._p)

    def send(self, client, timeout, arg, arg2):
        """
        Send a request to zhttp_client.
        Url and the request path will be concatenated.
        This behavior is useful for url rewrite and reverse proxy.

        Send also allow two user provided arguments which will be returned with the response.
        The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an arg.
        """
        return utils.lib.zhttp_request_send(self._p, client._p, timeout, arg._p, arg2._p)

    def method(self):
        """
        Get the request method
        """
        return utils.lib.zhttp_request_method(self._p)

    def set_method(self, method):
        """
        Set the request method
        """
        utils.lib.zhttp_request_set_method(self._p, utils.to_bytes(method))

    def url(self):
        """
        Get the request url.
        When receiving a request from http server this is only the path part of the url.
        """
        return utils.lib.zhttp_request_url(self._p)

    def set_url(self, url):
        """
        Set the request url
        When sending a request to http client this should be full url.
        """
        utils.lib.zhttp_request_set_url(self._p, utils.to_bytes(url))

    def content_type(self):
        """
        Get the request content type
        """
        return utils.lib.zhttp_request_content_type(self._p)

    def set_content_type(self, content_type):
        """
        Set the request content type
        """
        utils.lib.zhttp_request_set_content_type(self._p, utils.to_bytes(content_type))

    def content_length(self):
        """
        Get the content length of the request
        """
        return utils.lib.zhttp_request_content_length(self._p)

    def headers(self):
        """
        Get the headers of the request
        """
        return utils.lib.zhttp_request_headers(self._p)

    def content(self):
        """
        Get the content of the request.
        """
        return utils.lib.zhttp_request_content(self._p)

    def get_content(self):
        """
        Get the content of the request.
        """
        return utils.lib.zhttp_request_get_content(self._p)

    def set_content(self, content):
        """
        Set the content of the request.
        Content must by dynamically allocated string.
        Takes ownership of the content.
        """
        utils.lib.zhttp_request_set_content(self._p, utils.to_bytes(content))

    def set_content_const(self, content):
        """
        Set the content of the request..
        The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
        """
        utils.lib.zhttp_request_set_content_const(self._p, utils.to_bytes(content))

    def reset_content(self):
        """
        Set the content to NULL
        """
        utils.lib.zhttp_request_reset_content(self._p)

    def set_username(self, username):
        """
        Set the request username
        """
        utils.lib.zhttp_request_set_username(self._p, utils.to_bytes(username))

    def set_password(self, password):
        """
        Set the request password
        """
        utils.lib.zhttp_request_set_password(self._p, utils.to_bytes(password))

    def match(self, method, path, *path_args):
        """
        Match the path of the request.
        Support wildcards with '%s' symbol inside the match string.
        Matching wildcards until the next '/', '?' or '\0'.
        On successful match the variadic arguments will be filled with the matching strings.
        On successful match the method is modifying the url field and break it into substrings.
        If you need to use the url, do it before matching or take a copy.

        User must not free the variadic arguments as they are part of the url.

        To use the percent symbol, just double it, e.g "%%something".

        Example:
        if (zhttp_request_match (request, "POST", "/send/%s/%s", &name, &id))
        """
        return utils.lib.zhttp_request_match(self._p, utils.to_bytes(method), utils.to_bytes(path), *path_args)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zhttp_request_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
