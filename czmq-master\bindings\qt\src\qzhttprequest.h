/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#ifndef Q_ZHTTP_REQUEST_H
#define Q_ZHTTP_REQUEST_H

#include "qczmq.h"

class QT_CZMQ_EXPORT QZhttpRequest : public QObject
{
    Q_OBJECT
public:

    //  Copy-construct to return the proper wrapped c types
    QZhttpRequest (zhttp_request_t *self, QObject *qObjParent = 0);

    //  Create a new http request.
    explicit QZhttpRequest (QObject *qObjParent = 0);

    //  Destroy an http request.
    ~QZhttpRequest ();

    //  Receive a new request from zhttp_server.
    //  Return the underlying connection if successful, to be used when calling zhttp_response_send.
    void * recv (QZsock *sock);

    //  Send a request to zhttp_client.
    //  Url and the request path will be concatenated.
    //  This behavior is useful for url rewrite and reverse proxy.
    //
    //  Send also allow two user provided arguments which will be returned with the response.
    //  The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an arg.
    int send (QZhttpClient *client, int timeout, void *arg, void *arg2);

    //  Get the request method
    const QString method ();

    //  Set the request method
    void setMethod (const QString &method);

    //  Get the request url.
    //  When receiving a request from http server this is only the path part of the url.
    const QString url ();

    //  Set the request url
    //  When sending a request to http client this should be full url.
    void setUrl (const QString &url);

    //  Get the request content type
    const QString contentType ();

    //  Set the request content type
    void setContentType (const QString &contentType);

    //  Get the content length of the request
    size_t contentLength ();

    //  Get the headers of the request
    QZhash * headers ();

    //  Get the content of the request.
    const QString content ();

    //  Get the content of the request.
    QString getContent ();

    //  Set the content of the request..
    //  The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
    void setContentConst (const QString &content);

    //  Set the content to NULL
    void resetContent ();

    //  Set the request username
    void setUsername (const QString &username);

    //  Set the request password
    void setPassword (const QString &password);

    //  Self test of this class.
    static void test (bool verbose);

    zhttp_request_t *self;
};
#endif //  Q_ZHTTP_REQUEST_H
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
