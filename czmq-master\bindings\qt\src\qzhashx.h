/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#ifndef Q_ZHASHX_H
#define Q_ZHASHX_H

#include "qczmq.h"

class QT_CZMQ_EXPORT QZhashx : public QObject
{
    Q_OBJECT
public:

    //  Copy-construct to return the proper wrapped c types
    QZhashx (zhashx_t *self, QObject *qObjParent = 0);

    //  Create a new, empty hash container
    explicit QZhashx (QObject *qObjParent = 0);

    //  Unpack binary frame into a new hash table. Packed data must follow format
    //  defined by zhashx_pack. Hash table is set to autofree. An empty frame
    //  unpacks to an empty hash table.
    static QZhashx* unpack (QZframe *frame, QObject *qObjParent = 0);

    //  Same as unpack but uses a user-defined deserializer function to convert
    //  a longstr back into item format.
    static QZhashx* unpackOwn (QZframe *frame, zhashx_deserializer_fn deserializer, QObject *qObjParent = 0);

    //  Destroy a hash container and all items in it
    ~QZhashx ();

    //  Insert item into hash table with specified key and item.
    //  If key is already present returns -1 and leaves existing item unchanged
    //  Returns 0 on success.
    int insert (const void *key, void *item);

    //  Update or insert item into hash table with specified key and item. If the
    //  key is already present, destroys old item and inserts new one. If you set
    //  a container item destructor, this is called on the old value. If the key
    //  was not already present, inserts a new item. Sets the hash cursor to the
    //  new item.
    void update (const void *key, void *item);

    //  Remove an item specified by key from the hash table. If there was no such
    //  item, this function does nothing.
    void deleteNoConflict (const void *key);

    //  Delete all items from the hash table. If the key destructor is
    //  set, calls it on every key. If the item destructor is set, calls
    //  it on every item.
    void purge ();

    //  Return the item at the specified key, or null
    void * lookup (const void *key);

    //  Reindexes an item from an old key to a new key. If there was no such
    //  item, does nothing. Returns 0 if successful, else -1.
    int rename (const void *oldKey, const void *newKey);

    //  Set a free function for the specified hash table item. When the item is
    //  destroyed, the free function, if any, is called on that item.
    //  Use this when hash items are dynamically allocated, to ensure that
    //  you don't have memory leaks. You can pass 'free' or NULL as a free_fn.
    //  Returns the item, or NULL if there is no such item.
    void * freefn (const void *key, zhashx_free_fn freeFn);

    //  Return the number of keys/items in the hash table
    size_t size ();

    //  Return a zlistx_t containing the keys for the items in the
    //  table. Uses the key_duplicator to duplicate all keys and sets the
    //  key_destructor as destructor for the list.
    QZlistx * keys ();

    //  Return a zlistx_t containing the values for the items in the
    //  table. Uses the duplicator to duplicate all items and sets the
    //  destructor as destructor for the list.
    QZlistx * values ();

    //  Simple iterator; returns first item in hash table, in no given order,
    //  or NULL if the table is empty. This method is simpler to use than the
    //  foreach() method, which is deprecated. To access the key for this item
    //  use zhashx_cursor(). NOTE: do NOT modify the table while iterating.
    void * first ();

    //  Simple iterator; returns next item in hash table, in no given order,
    //  or NULL if the last item was already returned. Use this together with
    //  zhashx_first() to process all items in a hash table. If you need the
    //  items in sorted order, use zhashx_keys() and then zlistx_sort(). To
    //  access the key for this item use zhashx_cursor(). NOTE: do NOT modify
    //  the table while iterating.
    void * next ();

    //  After a successful first/next method, returns the key for the item that
    //  was returned. This is a constant string that you may not modify or
    //  deallocate, and which lasts as long as the item in the hash. After an
    //  unsuccessful first/next, returns NULL.
    const void * cursor ();

    //  Add a comment to hash table before saving to disk. You can add as many
    //  comment lines as you like. These comment lines are discarded when loading
    //  the file. If you use a null format, all comments are deleted.
    void comment (const QString &param);

    //  Save hash table to a text file in name=value format. Hash values must be
    //  printable strings; keys may not contain '=' character. Returns 0 if OK,
    //  else -1 if a file error occurred.
    int save (const QString &filename);

    //  Load hash table from a text file in name=value format; hash table must
    //  already exist. Hash values must printable strings; keys may not contain
    //  '=' character. Returns 0 if OK, else -1 if a file was not readable.
    int load (const QString &filename);

    //  When a hash table was loaded from a file by zhashx_load, this method will
    //  reload the file if it has been modified since, and is "stable", i.e. not
    //  still changing. Returns 0 if OK, -1 if there was an error reloading the
    //  file.
    int refresh ();

    //  Serialize hash table to a binary frame that can be sent in a message.
    //  The packed format is compatible with the 'dictionary' type defined in
    //  http://rfc.zeromq.org/spec:35/FILEMQ, and implemented by zproto:
    //
    //     ; A list of name/value pairs
    //     dictionary      = dict-count *( dict-name dict-value )
    //     dict-count      = number-4
    //     dict-value      = longstr
    //     dict-name       = string
    //
    //     ; Strings are always length + text contents
    //     longstr         = number-4 *VCHAR
    //     string          = number-1 *VCHAR
    //
    //     ; Numbers are unsigned integers in network byte order
    //     number-1        = 1OCTET
    //     number-4        = 4OCTET
    //
    //  Comments are not included in the packed data. Item values MUST be
    //  strings.
    QZframe * pack ();

    //  Same as pack but uses a user-defined serializer function to convert items
    //  into longstr.
    QZframe * packOwn (zhashx_serializer_fn serializer);

    //  Make a copy of the list; items are duplicated if you set a duplicator
    //  for the list, otherwise not. Copying a null reference returns a null
    //  reference. Note that this method's behavior changed slightly for CZMQ
    //  v3.x, as it does not set nor respect autofree. It does however let you
    //  duplicate any hash table safely. The old behavior is in zhashx_dup_v2.
    QZhashx * dup ();

    //  Set a user-defined deallocator for hash items; by default items are not
    //  freed when the hash is destroyed.
    void setDestructor (zhashx_destructor_fn destructor);

    //  Set a user-defined duplicator for hash items; by default items are not
    //  copied when the hash is duplicated.
    void setDuplicator (zhashx_duplicator_fn duplicator);

    //  Set a user-defined deallocator for keys; by default keys are freed
    //  when the hash is destroyed using free().
    void setKeyDestructor (zhashx_destructor_fn destructor);

    //  Set a user-defined duplicator for keys; by default keys are duplicated
    //  using strdup.
    void setKeyDuplicator (zhashx_duplicator_fn duplicator);

    //  Set a user-defined comparator for keys; by default keys are
    //  compared using strcmp.
    //  The callback function should return zero (0) on matching
    //  items.
    void setKeyComparator (zhashx_comparator_fn comparator);

    //  Set a user-defined hash function for keys; by default keys are
    //  hashed by a modified Bernstein hashing function.
    void setKeyHasher (zhashx_hash_fn hasher);

    //  Make copy of hash table; if supplied table is null, returns null.
    //  Does not copy items themselves. Rebuilds new table so may be slow on
    //  very large tables. NOTE: only works with item values that are strings
    //  since there's no other way to know how to duplicate the item value.
    QZhashx * dupV2 ();

    //  Self test of this class.
    static void test (bool verbose);

    zhashx_t *self;
};
#endif //  Q_ZHASHX_H
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
