################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class Zpoller(object):
    """
    event-driven reactor
    """

    def __init__(self, reader, *reader_args):
        """
        Create new poller, specifying zero or more readers. The list of
        readers ends in a NULL. Each reader can be a zsock_t instance, a
        zactor_t instance, a libzmq socket (void *), or a file handle.
        """
        p = utils.lib.zpoller_new(reader._p, *reader_args)
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zpoller_destroy_py)

    def add(self, reader):
        """
        Add a reader to be polled. Returns 0 if OK, -1 on failure. The reader may
        be a libzmq void * socket, a zsock_t instance, a zactor_t instance or a
        file handle.
        """
        return utils.lib.zpoller_add(self._p, reader._p)

    def remove(self, reader):
        """
        Remove a reader from the poller; returns 0 if OK, -1 on failure. The reader
        must have been passed during construction, or in an zpoller_add () call.
        """
        return utils.lib.zpoller_remove(self._p, reader._p)

    def set_nonstop(self, nonstop):
        """
        By default the poller stops if the process receives a SIGINT or SIGTERM
        signal. This makes it impossible to shut-down message based architectures
        like zactors. This method lets you switch off break handling. The default
        nonstop setting is off (false).
        """
        utils.lib.zpoller_set_nonstop(self._p, nonstop)

    def wait(self, timeout):
        """
        Poll the registered readers for I/O, return first reader that has input.
        The reader will be a libzmq void * socket, a zsock_t, a zactor_t
        instance or a file handle as specified in zpoller_new/zpoller_add. The
        timeout should be zero or greater, or -1 to wait indefinitely. Socket
        priority is defined by their order in the poll list. If you need a
        balanced poll, use the low level zmq_poll method directly. If the poll
        call was interrupted (SIGINT), or the ZMQ context was destroyed, or the
        timeout expired, returns NULL. You can test the actual exit condition by
        calling zpoller_expired () and zpoller_terminated (). The timeout is in
        msec.
        """
        return utils.lib.zpoller_wait(self._p, timeout)

    def expired(self):
        """
        Return true if the last zpoller_wait () call ended because the timeout
        expired, without any error.
        """
        return utils.lib.zpoller_expired(self._p)

    def terminated(self):
        """
        Return true if the last zpoller_wait () call ended because the process
        was interrupted, or the parent context was destroyed.
        """
        return utils.lib.zpoller_terminated(self._p)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zpoller_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
