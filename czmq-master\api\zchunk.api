<class name = "zchunk" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    work with memory chunks

    <callback_type name = "destructor_fn" state = "draft">
        Destroy an item
        <argument name = "hint" type = "anything" by_reference = "1" />
    </callback_type>
    
    <constructor>
        Create a new chunk of the specified size. If you specify the data, it
        is copied into the chunk. If you do not specify the data, the chunk is
        allocated and left empty, and you can then add data using zchunk_append.
        <argument name = "data" type = "buffer" c_type = "const void *" />
        <argument name = "size" type = "size" />
    </constructor>

    <constructor name = "frommem" state = "draft">
        Create a new chunk from memory. Take ownership of the memory and calling the destructor
        on destroy.
        <argument name = "data" type = "buffer" c_type = "void *" />
        <argument name = "size" type = "size" />
        <argument name = "destructor" type = "zchunk_destructor_fn" callback = "1" />
        <argument name = "hint" type = "anything" />
    </constructor>


    <destructor>
        Destroy a chunk
    </destructor>

    <method name = "resize">
        Resizes chunk max_size as requested; chunk_cur size is set to zero
        <argument name = "size" type = "size" />
    </method>

    <method name = "size">
        Return chunk cur size
        <return type = "size" />
    </method>

    <method name = "max_size">
        Return chunk max size
        <return type = "size" />
    </method>

    <method name = "data">
        Return chunk data
        <return type = "buffer" mutable = "1" size = ".size" />
    </method>

    <method name = "set">
        Set chunk data from user-supplied data; truncate if too large. Data may
        be null. Returns actual size of chunk
        <argument name = "data" type = "buffer" c_type = "const void *" />
        <argument name = "size" type = "size" />
        <return type = "size" />
    </method>

    <method name = "fill">
        Fill chunk data from user-supplied octet
        <argument name = "filler" type = "byte" />
        <argument name = "size" type = "size" />
        <return type = "size" />
    </method>

    <method name = "append">
        Append user-supplied data to chunk, return resulting chunk size. If the
        data would exceeded the available space, it is truncated. If you want to
        grow the chunk to accommodate new data, use the zchunk_extend method.
        <argument name = "data" type = "buffer" c_type = "const void *" />
        <argument name = "size" type = "size" />
        <return type = "size" />
    </method>

    <method name = "extend">
        Append user-supplied data to chunk, return resulting chunk size. If the
        data would exceeded the available space, the chunk grows in size.
        <argument name = "data" type = "buffer" c_type = "const void *" />
        <argument name = "size" type = "size" />
        <return type = "size" />
    </method>

    <method name = "consume">
        Copy as much data from 'source' into the chunk as possible; returns the
        new size of chunk. If all data from 'source' is used, returns exhausted
        on the source chunk. Source can be consumed as many times as needed until
        it is exhausted. If source was already exhausted, does not change chunk.
        <argument name = "source" type = "zchunk" mutable = "1" />
        <return type = "size" />
    </method>

    <method name = "exhausted">
        Returns true if the chunk was exhausted by consume methods, or if the
        chunk has a size of zero.
        <return type = "boolean" />
    </method>

    <method name = "read" singleton = "1">
        Read chunk from an open file descriptor
        <argument name = "handle" type = "FILE" mutable = "1" />
        <argument name = "bytes" type = "size" />
        <return type = "zchunk" fresh = "1" />
    </method>

    <method name = "write">
        Write chunk to an open file descriptor
        <argument name = "handle" type = "FILE" mutable = "1" />
        <return type = "integer" />
    </method>

    <method name = "slurp" singleton = "1">
        Try to slurp an entire file into a chunk. Will read up to maxsize of
        the file. If maxsize is 0, will attempt to read the entire file and
        fail with an assertion if that cannot fit into memory. Returns a new
        chunk containing the file data, or NULL if the file could not be read.
        <argument name = "filename" type = "string" />
        <argument name = "maxsize" type = "size" />
        <return type = "zchunk" fresh = "1" />
    </method>

    <method name = "dup">
        Create copy of chunk, as new chunk object. Returns a fresh zchunk_t
        object, or null if there was not enough heap memory. If chunk is null,
        returns null.
        <return type = "zchunk" fresh = "1" />
    </method>

    <method name = "strhex">
        Return chunk data encoded as printable hex string. Caller must free
        string when finished with it.
        <return type = "string" fresh = "1" />
    </method>

    <method name = "strdup">
        Return chunk data copied into freshly allocated string
        Caller must free string when finished with it.
        <return type = "string" fresh = "1" />
    </method>

    <method name = "streq">
        Return TRUE if chunk body is equal to string, excluding terminator
        <argument name = "string" type = "string" />
        <return type = "boolean" />
    </method>

    <method name = "pack">
        Transform zchunk into a zframe that can be sent in a message.
        <return type = "zframe" fresh = "1" />
    </method>

    <method name = "packx" state = "draft" singleton = "1">
        Transform zchunk into a zframe that can be sent in a message.
        Take ownership of the chunk.
        <argument name = "self_p" type = "zchunk" by_reference = "1" />
        <return type = "zframe" fresh = "1" />
    </method>

    <method name = "unpack" singleton = "1">
        Transform a zframe into a zchunk.
        <argument name = "frame" type = "zframe" mutable = "1" />
        <return type = "zchunk" fresh = "1" />
    </method>

    <method name = "digest">
        Calculate SHA1 digest for chunk, using zdigest class.
        <return type = "string" />
    </method>

    <method name = "fprint">
        Dump chunk to FILE stream, for debugging and tracing.
        <argument name = "file" type = "FILE" mutable = "1" />
    </method>

    <method name = "print">
        Dump message to stderr, for debugging and tracing.
        See zchunk_fprint for details
    </method>

    <method name = "is" singleton = "1">
        Probe the supplied object, and report if it looks like a zchunk_t.
        <argument name = "self" type = "anything" mutable = "1" />
        <return type = "boolean" />
    </method>
</class>
