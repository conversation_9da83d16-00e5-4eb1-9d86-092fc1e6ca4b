################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
cmake_minimum_required (VERSION 2.8...4.0)

project (czmqjni CXX)
enable_language (C)

# Search for Find*.cmake files in the following locations
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}")
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/../../..")

########################################################################
# JNI dependency
########################################################################
find_package (JNI REQUIRED)
include_directories (${JAVA_INCLUDE_PATH} ${JAVA_INCLUDE_PATH2} src/native/include)

########################################################################
# LIBZMQ dependency
########################################################################
find_package(libzmq REQUIRED)
IF (LIBZMQ_FOUND)
    include_directories(${libzmq_INCLUDE_DIRS})
    list(APPEND MORE_LIBRARIES ${libzmq_LIBRARIES})
ELSE (LIBZMQ_FOUND)
    message( FATAL_ERROR "libzmq not found." )
ENDIF (LIBZMQ_FOUND)

########################################################################
# CZMQ dependency
########################################################################
find_package(czmq REQUIRED)
IF (CZMQ_FOUND)
    include_directories(${czmq_INCLUDE_DIRS})
    list(APPEND MORE_LIBRARIES ${czmq_LIBRARIES})
ELSE (CZMQ_FOUND)
    message( FATAL_ERROR "czmq not found." )
ENDIF (CZMQ_FOUND)

set (czmqjni_sources
    src/main/c/org_zeromq_czmq_Zarmour.c
    src/main/c/org_zeromq_czmq_Zcert.c
    src/main/c/org_zeromq_czmq_Zcertstore.c
    src/main/c/org_zeromq_czmq_Zchunk.c
    src/main/c/org_zeromq_czmq_Zclock.c
    src/main/c/org_zeromq_czmq_Zconfig.c
    src/main/c/org_zeromq_czmq_Zdigest.c
    src/main/c/org_zeromq_czmq_Zdir.c
    src/main/c/org_zeromq_czmq_ZdirPatch.c
    src/main/c/org_zeromq_czmq_Zfile.c
    src/main/c/org_zeromq_czmq_Zframe.c
    src/main/c/org_zeromq_czmq_Zhash.c
    src/main/c/org_zeromq_czmq_Zhashx.c
    src/main/c/org_zeromq_czmq_Ziflist.c
    src/main/c/org_zeromq_czmq_Zlist.c
    src/main/c/org_zeromq_czmq_Zlistx.c
    src/main/c/org_zeromq_czmq_Zloop.c
    src/main/c/org_zeromq_czmq_Zmsg.c
    src/main/c/org_zeromq_czmq_Zpoller.c
    src/main/c/org_zeromq_czmq_Zproc.c
    src/main/c/org_zeromq_czmq_Zsock.c
    src/main/c/org_zeromq_czmq_Zstr.c
    src/main/c/org_zeromq_czmq_Zsys.c
    src/main/c/org_zeromq_czmq_Ztimerset.c
    src/main/c/org_zeromq_czmq_Ztrie.c
    src/main/c/org_zeromq_czmq_Zuuid.c
    src/main/c/org_zeromq_czmq_ZhttpClient.c
    src/main/c/org_zeromq_czmq_ZhttpServer.c
    src/main/c/org_zeromq_czmq_ZhttpServerOptions.c
    src/main/c/org_zeromq_czmq_ZhttpRequest.c
    src/main/c/org_zeromq_czmq_ZhttpResponse.c
    src/main/c/org_zeromq_czmq_Zosc.c
)

add_library (czmqjni SHARED ${czmqjni_sources})
add_definitions (-DCZMQ_BUILD_DRAFT_API)

set (CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -pedantic -O2")
set (CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/build)
set (CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/build)
set (CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/build)

target_link_libraries (czmqjni ${MORE_LIBRARIES})
