/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#ifndef Q_ZHTTP_RESPONSE_H
#define Q_ZHTTP_RESPONSE_H

#include "qczmq.h"

class QT_CZMQ_EXPORT QZhttpResponse : public QObject
{
    Q_OBJECT
public:

    //  Copy-construct to return the proper wrapped c types
    QZhttpResponse (zhttp_response_t *self, QObject *qObjParent = 0);

    //  Create a new zhttp_response.
    explicit QZhttpResponse (QObject *qObjParent = 0);

    //  Destroy the zhttp_response.
    ~QZhttpResponse ();

    //  Send a response to a request.
    //  Returns 0 if successful and -1 otherwise.
    int send (QZsock *sock, void **connection);

    //  Receive a response from zhttp_client.
    //  On success return 0, -1 otherwise.
    //
    //  Recv returns the two user arguments which was provided with the request.
    //  The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an argument.
    int recv (QZhttpClient *client, void **arg, void **arg2);

    //  Get the response content type
    const QString contentType ();

    //  Set the content type of the response.
    void setContentType (const QString &value);

    //  Get the status code of the response.
    quint32 statusCode ();

    //  Set the status code of the response.
    void setStatusCode (quint32 statusCode);

    //  Get the headers of the response.
    QZhash * headers ();

    //  Get the content length of the response
    size_t contentLength ();

    //  Get the content of the response.
    const QString content ();

    //  Get the content of the response.
    QString getContent ();

    //  Set the content of the response.
    //  The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
    void setContentConst (const QString &content);

    //  Set the content to NULL
    void resetContent ();

    //  Self test of this class.
    static void test (bool verbose);

    zhttp_response_t *self;
};
#endif //  Q_ZHTTP_RESPONSE_H
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
