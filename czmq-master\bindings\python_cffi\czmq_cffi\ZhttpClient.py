################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class ZhttpClient(object):
    """
    Http client, allowing multiple requests simultaneously and integrate easily with zpoller.
Use zhttp_request class to create and send the request.
Use zhttp_response class to receive the response.
    """

    def __init__(self, verbose):
        """
        Create a new http client
        """
        p = utils.lib.zhttp_client_new(verbose)
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zhttp_client_destroy_py)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zhttp_client_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
