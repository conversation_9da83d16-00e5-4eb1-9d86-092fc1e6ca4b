################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class Zdigest(object):
    """
    provides hashing functions (SHA-1 at present)
    """

    def __init__(self):
        """
        Constructor - creates new digest object, which you use to build up a
        digest by repeatedly calling zdigest_update() on chunks of data.
        """
        p = utils.lib.zdigest_new()
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zdigest_destroy_py)

    def update(self, buffer, length):
        """
        Add buffer into digest calculation
        """
        utils.lib.zdigest_update(self._p, buffer, length)

    def data(self):
        """
        Return final digest hash data. If built without crypto support,
        returns NULL.
        """
        return utils.lib.zdigest_data(self._p)

    def size(self):
        """
        Return final digest hash size
        """
        return utils.lib.zdigest_size(self._p)

    def string(self):
        """
        Return digest as printable hex string; caller should not modify nor
        free this string. After calling this, you may not use zdigest_update()
        on the same digest. If built without crypto support, returns NULL.
        """
        return utils.lib.zdigest_string(self._p)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zdigest_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
