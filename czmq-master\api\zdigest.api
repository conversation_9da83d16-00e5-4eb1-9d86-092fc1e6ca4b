<class name = "zdigest" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    provides hashing functions (SHA-1 at present)
    
    <constructor>
        Constructor - creates new digest object, which you use to build up a
        digest by repeatedly calling zdigest_update() on chunks of data.
    </constructor>

    <destructor>
        Destroy a digest object
    </destructor>

    <method name = "update">
        Add buffer into digest calculation
        <argument name = "buffer" type = "buffer" />
        <argument name = "length" type = "size" />
    </method>

    <method name = "data">
        Return final digest hash data. If built without crypto support,
        returns NULL.
        <return type = "buffer" size = ".size" />
    </method>

    <method name = "size">
        Return final digest hash size
        <return type = "size" />
    </method>

    <method name = "string">
        Return digest as printable hex string; caller should not modify nor
        free this string. After calling this, you may not use zdigest_update()
        on the same digest. If built without crypto support, returns NULL.
        <return type = "string" mutable = "1" />
    </method>
</class>
