<class name = "zconfig" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    work with config files written in rfc.zeromq.org/spec:4/ZPL.

    <callback_type name = "fct">
        <argument name = "self" type = "zconfig" />
        <argument name = "arg" type = "anything" />
        <argument name = "level" type = "integer" />
        <return type = "integer" />
    </callback_type>

    <constructor>
        Create new config item
        <argument name = "name" type = "string" />
        <argument name = "parent" type = "zconfig" />
    </constructor>

    <destructor>
        Destroy a config item and all its children
    </destructor>

    <method name = "dup" state = "draft">
        Create copy of zconfig, caller MUST free the value
        Create copy of config, as new zconfig object. Returns a fresh zconfig_t
        object. If config is null, or memory was exhausted, returns null.
        <return type = "zconfig" fresh = "1" />
    </method>


    <method name = "name">
        Return name of config item
        <return type = "string" mutable = "1" />
    </method>

    <method name = "value">
        Return value of config item
        <return type = "string" mutable = "1" />
    </method>

    <method name = "put">
        Insert or update configuration key with value
        <argument name = "path" type = "string" />
        <argument name = "value" type = "string" />
    </method>

    <method name = "putf">
        Equivalent to zconfig_put, accepting a format specifier and variable
        argument list, instead of a single string value.
        <argument name = "path" type = "string" />
        <argument name = "format" type = "format" />
    </method>

    <method name = "get">
        Get value for config item into a string value; leading slash is optional
        and ignored.
        <argument name = "path" type = "string" />
        <argument name = "default value" type = "string" />
        <return type = "string" mutable = "1" />
    </method>
    <method name = "set name">
        Set config item name, name may be NULL
        <argument name = "name" type = "string" />
    </method>

    <method name = "set value">
        Set new value for config item. The new value may be a string, a printf
        format, or NULL. Note that if string may possibly contain '%', or if it
        comes from an insecure source, you must use '%s' as the format, followed
        by the string.
        <argument name = "format" type = "format" />
    </method>

    <method name = "child">
        Find our first child, if any
        <return type = "zconfig" />
    </method>

    <method name = "next">
        Find our first sibling, if any
        <return type = "zconfig" />
    </method>

    <method name = "locate">
        Find a config item along a path; leading slash is optional and ignored.
        <argument name = "path" type = "string" />
        <return type = "zconfig" />
    </method>

    <method name = "at depth">
        Locate the last config item at a specified depth
        <argument name = "level" type = "integer" />
        <return type = "zconfig" />
    </method>

    <method name = "execute">
        Execute a callback for each config item in the tree; returns zero if
        successful, else -1.
        <argument name = "handler" type = "zconfig_fct" callback = "1" />
        <argument name = "arg" type = "anything" />
        <return type = "integer" />
    </method>

    <method name = "set comment">
        Add comment to config item before saving to disk. You can add as many
        comment lines as you like. If you use a null format, all comments are
        deleted.
        <argument name = "format" type = "format" />
    </method>

    <method name = "comments">
        Return comments of config item, as zlist.
        <return type = "zlist" />
    </method>

    <constructor name = "load">
        Load a config tree from a specified ZPL text file; returns a zconfig_t
        reference for the root, if the file exists and is readable. Returns NULL
        if the file does not exist.
        <argument name = "filename" type = "string" />
    </constructor>

    <method name = "save">
        Save a config tree to a specified ZPL text file, where a filename
        "-" means dump to standard output.
        <argument name = "filename" type = "string" />
        <return type = "integer" />
    </method>

    <constructor name = "loadf">
        Equivalent to zconfig_load, taking a format string instead of a fixed
        filename.
        <argument name = "format" type = "format" />
    </constructor>

    <method name = "savef">
        Equivalent to zconfig_save, taking a format string instead of a fixed
        filename.
        <argument name = "format" type = "format" />
        <return type = "integer" />
    </method>

    <method name = "filename">
        Report filename used during zconfig_load, or NULL if none
        <return type = "string" />
    </method>

    <method name = "reload" singleton = "1" >
        Reload config tree from same file that it was previously loaded from.
        Returns 0 if OK, -1 if there was an error (and then does not change
        existing data).
        <argument name = "self_p" type = "zconfig" by_reference = "1" />
        <return type = "integer" />
    </method>

    <method name = "chunk load" singleton = "1" >
        Load a config tree from a memory chunk
        <argument name = "chunk" type = "zchunk" />
        <return type = "zconfig" />
    </method>

    <method name = "chunk save">
        Save a config tree to a new memory chunk
        <return type = "zchunk" />
    </method>

    <method name = "str load" singleton = "1" >
        Load a config tree from a null-terminated string
        <argument name = "string" type = "string" />
        <return type = "zconfig" fresh = "1" />
    </method>

    <method name = "str save">
        Save a config tree to a new null terminated string
        <return type = "string" fresh = "1" />
    </method>

    <method name = "has changed">
        Return true if a configuration tree was loaded from a file and that
        file has changed in since the tree was loaded.
        <return type = "boolean" />
    </method>

    <method name = "remove_subtree" state = "draft">
        Destroy subtree (all children)
    </method>

    <method name = "remove" singleton = "1" state = "draft">
        Destroy node and subtree (all children)
        <argument name = "self_p" type = "zconfig" by_reference = "1" />
    </method>

    <method name = "fprint">
        Print the config file to open stream
        <argument name = "file" type = "FILE" />
    </method>

    <method name = "print">
        Print properties of object
    </method>

    <method name = "test" singleton = "1">
        Self test of this class
        <argument name = "verbose" type = "boolean" />
    </method>
</class>
