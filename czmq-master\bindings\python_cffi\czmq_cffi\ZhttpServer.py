################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class ZhttpServer(object):
    """
    Simple http server.
To start handling requests:
1. Create a dealer socket
2. Connect the socket to the server backend address provided in the options.
3. Create a zhttp_request.
4. Call zhttp_request_recv to accept a new request.
5. Call zhttp_response_send to send a response.

You can connect as many dealers as you want.
The Server is using simple dealer socket to route the requests.

NOTE: when using libmicrohttpd << 0.9.34 the application might experience
high CPU usage due to the lack of MHD_suspend_connection and
MHD_resume_connection APIs. It is recommended to use this class only with
libmicrohttpd at least 0.9.34 in production.
    """

    def __init__(self, options):
        """
        Create a new http server
        """
        p = utils.lib.zhttp_server_new(options._p)
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zhttp_server_destroy_py)

    def port(self):
        """
        Return the port the server is listening on.
        """
        return utils.lib.zhttp_server_port(self._p)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zhttp_server_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
