<?xml version="1.0" encoding="utf-8"?>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{0C4A2E28-8C9E-4B27-85D9-BB679AD84AC7}</ProjectGuid>
    <ProjectName>libczmqjni</ProjectName>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="DebugDLL|Win32">
      <Configuration>DebugDLL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseDLL|Win32">
      <Configuration>ReleaseDLL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugDLL|x64">
      <Configuration>DebugDLL</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseDLL|x64">
      <Configuration>ReleaseDLL</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Configuration">
    <ConfigurationType Condition="$(Configuration.IndexOf('DLL')) == -1">StaticLibrary</ConfigurationType>
    <ConfigurationType Condition="$(Configuration.IndexOf('DLL')) != -1">DynamicLibrary</ConfigurationType>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="PropertySheets">
    <Import Project="..\..\properties\$(Configuration).props" />
    <Import Project="$(ProjectName).props" />
  </ImportGroup>
  <ItemGroup>
    <ClInclude Include="..\..\platform.h" />
    <ClInclude Include="..\..\resource.h" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\..\include\czmq.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zarmour.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zcert.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zcertstore.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zchunk.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zclock.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zconfig.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zdigest.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zdir.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_ZdirPatch.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zfile.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zframe.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zhash.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zhashx.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Ziflist.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zlist.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zlistx.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zloop.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zmsg.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zpoller.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zproc.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zsock.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zstr.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zsys.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Ztimerset.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Ztrie.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zuuid.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_ZhttpClient.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_ZhttpServer.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_ZhttpServerOptions.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_ZhttpRequest.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_ZhttpResponse.h" />
    <ClInclude Include="..\..\..\src\native\include\org_zeromq_czmq_Zosc.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zarmour.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zcert.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zcertstore.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zchunk.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zclock.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zconfig.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zdigest.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zdir.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_ZdirPatch.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zfile.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zframe.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zhash.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zhashx.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Ziflist.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zlist.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zlistx.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zloop.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zmsg.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zpoller.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zproc.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zsock.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zstr.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zsys.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Ztimerset.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Ztrie.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zuuid.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_ZhttpClient.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_ZhttpServer.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_ZhttpServerOptions.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_ZhttpRequest.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_ZhttpResponse.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\src\main\c\org_zeromq_czmq_Zosc.c">
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\..\resource.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
  -->
</Project>
