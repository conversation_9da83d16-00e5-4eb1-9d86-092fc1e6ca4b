<class name = "ziflist" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    List of network interfaces available on system

    <constructor>
        Get a list of network interfaces currently defined on the system
    </constructor>

    <destructor>
        Destroy a ziflist instance
    </destructor>

    <method name = "reload">
        Reload network interfaces from system
    </method>

    <method name = "size">
        Return the number of network interfaces on system
        <return type = "size" />
    </method>

    <method name = "first">
        Get first network interface, return NULL if there are none
        <return type = "string" />
    </method>

    <method name = "next">
        Get next network interface, return NULL if we hit the last one
        <return type = "string" />
    </method>

    <method name = "address">
        Return the current interface IP address as a printable string
        <return type = "string" />
    </method>

    <method name = "broadcast">
        Return the current interface broadcast address as a printable string
        <return type = "string" />
    </method>

    <method name = "netmask">
        Return the current interface network mask as a printable string
        <return type = "string" />
    </method>

    <method name = "mac" state = "draft">
        Return the current interface MAC address as a printable string
        <return type = "string" />
    </method>

    <method name = "print">
        Return the list of interfaces.
    </method>

    <method name = "new ipv6" singleton = "1" state = "draft">
        Get a list of network interfaces currently defined on the system
        Includes IPv6 interfaces
        <return type = "ziflist" fresh = "1" />
    </method>

    <method name = "reload ipv6" state = "draft">
        Reload network interfaces from system, including IPv6
    </method>

    <method name = "is ipv6" state = "draft">
        Return true if the current interface uses IPv6
        <return type = "boolean" />
    </method>
</class>
