################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

CC=gcc
# replace the following with wherever you have installed libzmq
PREFIX=/usr/local
INCDIR=-I$(PREFIX)/include -I.
LIBDIR=-L$(PREFIX)/lib
CFLAGS=-Wall -Os -g -DCZMQ_EXPORTS $(INCDIR)

OBJS = zactor.o zargs.o zarmour.o zcert.o zcertstore.o zchunk.o zclock.o zconfig.o zdigest.o zdir.o zdir_patch.o zfile.o zframe.o zhash.o zhashx.o ziflist.o zlist.o zlistx.o zloop.o zmsg.o zpoller.o zproc.o zsock.o zstr.o zsys.o ztimerset.o ztrie.o zuuid.o zhttp_client.o zhttp_server.o zhttp_server_options.o zhttp_request.o zhttp_response.o zosc.o zauth.o zbeacon.o zgossip.o zmonitor.o zproxy.o zrex.o zgossip_msg.o czmq_private_selftest.o

%.o: ../../src/%.c
	$(CC) -c -o $@ $< $(CFLAGS)

all: libczmq.dll czmq_selftest.exe

libczmq.dll: $(OBJS)
	$(CC) -shared -o $@ $(OBJS) -Wl,--out-implib,$@.a $(LIBDIR) -lzmq

# the test functions are not exported into the DLL
czmq_selftest.exe: czmq_selftest.o $(OBJS)
	$(CC) -o $@ $^ $(LIBDIR) -lzmq
clean:
	del *.o *.a *.dll *.exe

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
