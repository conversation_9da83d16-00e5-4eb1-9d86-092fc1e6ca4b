<class name = "zdir" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    work with file-system directories

    <constructor>
        Create a new directory item that loads in the full tree of the specified
        path, optionally located under some parent path. If parent is "-", then
        loads only the top-level directory, and does not use parent as a path.
        <argument name = "path" type = "string" />
        <argument name = "parent" type = "string" />
    </constructor>

    <destructor>
        Destroy a directory tree and all children it contains.
    </destructor>

    <method name = "path">
        Return directory path
        <return type = "string" />
    </method>

    <method name = "modified">
        Return last modification time for directory.
        <return type = "time" />
    </method>

    <method name = "cursize">
        Return total hierarchy size, in bytes of data contained in all files
        in the directory tree.
        <return type = "file_size" />
    </method>

    <method name = "count">
        Return directory count
        <return type = "size" />
    </method>

    <method name = "list">
        Returns a sorted list of zfile objects; Each entry in the list is a pointer
        to a zfile_t item already allocated in the zdir tree. Do not destroy the
        original zdir tree until you are done with this list.
        <return type = "zlist" fresh = "1" />
    </method>
    
    <method name = "list_paths">
        Returns a sorted list of char*; Each entry in the list is a path of a file
        or directory contained in self.
        <return type = "zlist" fresh = "1" />
    </method>

    <method name = "remove">
        Remove directory, optionally including all files that it contains, at
        all levels. If force is false, will only remove the directory if empty.
        If force is true, will remove all files and all subdirectories.
        <argument name = "force" type = "boolean" />
    </method>

    <method name = "diff" singleton = "1">
        Calculate differences between two versions of a directory tree.
        Returns a list of zdir_patch_t patches. Either older or newer may
        be null, indicating the directory is empty/absent. If alias is set,
        generates virtual filename (minus path, plus alias).
        <argument name = "older" type = "zdir" />
        <argument name = "newer" type = "zdir" />
        <argument name = "alias" type = "string" />
        <return type = "zlist" fresh = "1" />
    </method>

    <method name = "resync">
        Return full contents of directory as a zdir_patch list.
        <argument name = "alias" type = "string" />
        <return type = "zlist" fresh = "1" />
    </method>

    <method name = "cache">
        Load directory cache; returns a hash table containing the SHA-1 digests
        of every file in the tree. The cache is saved between runs in .cache.
        <return type = "zhash" fresh = "1" />
    </method>

    <method name = "fprint">
        Print contents of directory to open stream
        <argument name = "file" type = "FILE" />
        <argument name = "indent" type = "integer" />
    </method>

    <method name = "print">
        Print contents of directory to stdout
        <argument name = "indent" type = "integer" />
    </method>

    <method name = "watch" singleton = "1">
        Create a new zdir_watch actor instance:

            zactor_t *watch = zactor_new (zdir_watch, NULL);

        Destroy zdir_watch instance:

            zactor_destroy (&amp;watch);

        Enable verbose logging of commands and activity:

            zstr_send (watch, "VERBOSE");

        Subscribe to changes to a directory path:

            zsock_send (watch, "ss", "SUBSCRIBE", "directory_path");

        Unsubscribe from changes to a directory path:

            zsock_send (watch, "ss", "UNSUBSCRIBE", "directory_path");

        Receive directory changes:
            zsock_recv (watch, "sp", &amp;path, &amp;patches);

            // Delete the received data.
            free (path);
            zlist_destroy (&amp;patches);
        <argument name = "pipe" type = "zsock" />
        <argument name = "unused" type = "anything" />
    </method>
</class>
