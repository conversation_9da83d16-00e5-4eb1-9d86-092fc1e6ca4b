/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

dependencies {
    implementation project(':czmq-jni')
    runtimeOnly "org.zeromq.czmq:czmq-jni-linux-x86_64:${project.version}"
    runtimeOnly "org.zeromq.czmq:czmq-jni-osx-x86_64:${project.version}"
    runtimeOnly "org.zeromq.czmq:czmq-jni-windows-x86_64:${project.version}"
}

//  ------------------------------------------------------------------
//  Install and Publish section

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
            artifactId = 'czmq-jni-all'
            pom {
                name = 'czmq-jni-all'
                description = 'The high-level C binding for 0MQ'
                packaging = 'jar'
                url = 'https://github.com/zeromq/czmq'
                licenses {
                    license {
                        name = 'Mozilla Public License Version 2.0'
                        url = 'https://www.mozilla.org/en-US/MPL/2.0/'
                    }
                }
                scm {
                    connection = 'https://github.com/zeromq/czmq.git'
                    developerConnection = 'https://github.com/zeromq/czmq.git'
                    url = 'https://github.com/zeromq/czmq'
                }
            }
        }
    }
}

artifactoryPublish {
    publications ('mavenJava')
}

bintray {
    user = System.getenv('BINTRAY_USER')
    key = System.getenv('BINTRAY_KEY')
    publications = ['mavenJava']
    publish = true
    override = true
    pkg {
        repo = 'maven'
        name = 'czmq-jni-all'
        desc = 'The high-level C binding for 0MQ'
        userOrg = System.getenv('BINTRAY_USER_ORG')
        licenses = ['MPL-2.0']
        websiteUrl = 'https://github.com/zeromq/czmq'
        issueTrackerUrl = 'https://github.com/zeromq/czmq/issues'
        vcsUrl = 'https://github.com/zeromq/czmq.git'
        githubRepo = System.getenv('BINTRAY_USER_ORG') + '/czmq'
        version {
            name = project.version
            vcsTag= project.version
        }
    }
}
