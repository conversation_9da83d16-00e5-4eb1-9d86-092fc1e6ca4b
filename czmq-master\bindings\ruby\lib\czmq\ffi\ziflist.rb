################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

module CZMQ
  module FFI

    # List of network interfaces available on system
    # @note This class is 100% generated using zproject.
    class Ziflist
      # Raised when one tries to use an instance of {Ziflist} after
      # the internal pointer to the native object has been nullified.
      class DestroyedError < RuntimeError; end

      # Boilerplate for self pointer, initializer, and finalizer
      class << self
        alias :__new :new
      end
      # Attaches the pointer _ptr_ to this instance and defines a finalizer for
      # it if necessary.
      # @param ptr [::FFI::Pointer]
      # @param finalize [Boolean]
      def initialize(ptr, finalize = true)
        @ptr = ptr
        if @ptr.null?
          @ptr = nil # Remove null pointers so we don't have to test for them.
        elsif finalize
          @finalizer = self.class.create_finalizer_for @ptr
          ObjectSpace.define_finalizer self, @finalizer
        end
      end
      # @param ptr [::FFI::Pointer]
      # @return [Proc]
      def self.create_finalizer_for(ptr)
        ptr_ptr = ::FFI::MemoryPointer.new :pointer

        Proc.new do
          ptr_ptr.write_pointer ptr
          ::CZMQ::FFI.ziflist_destroy ptr_ptr
        end
      end
      # @return [Boolean]
      def null?
        !@ptr or @ptr.null?
      end
      # Return internal pointer
      # @return [::FFI::Pointer]
      def __ptr
        raise DestroyedError unless @ptr
        @ptr
      end
      # So external Libraries can just pass the Object to a FFI function which expects a :pointer
      alias_method :to_ptr, :__ptr
      # Nullify internal pointer and return pointer pointer.
      # @note This detaches the current instance from the native object
      #   and thus makes it unusable.
      # @return [::FFI::MemoryPointer] the pointer pointing to a pointer
      #   pointing to the native object
      def __ptr_give_ref
        raise DestroyedError unless @ptr
        ptr_ptr = ::FFI::MemoryPointer.new :pointer
        ptr_ptr.write_pointer @ptr
        __undef_finalizer if @finalizer
        @ptr = nil
        ptr_ptr
      end
      # Undefines the finalizer for this object.
      # @note Only use this if you need to and can guarantee that the native
      #   object will be freed by other means.
      # @return [void]
      def __undef_finalizer
        ObjectSpace.undefine_finalizer self
        @finalizer = nil
      end

      # Get a list of network interfaces currently defined on the system
      # @return [CZMQ::Ziflist]
      def self.new()
        ptr = ::CZMQ::FFI.ziflist_new()
        __new ptr
      end

      # Destroy a ziflist instance
      #
      # @return [void]
      def destroy()
        return unless @ptr
        self_p = __ptr_give_ref
        result = ::CZMQ::FFI.ziflist_destroy(self_p)
        result
      end

      # Reload network interfaces from system
      #
      # @return [void]
      def reload()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ziflist_reload(self_p)
        result
      end

      # Return the number of network interfaces on system
      #
      # @return [Integer]
      def size()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ziflist_size(self_p)
        result
      end

      # Get first network interface, return NULL if there are none
      #
      # @return [String]
      def first()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ziflist_first(self_p)
        result
      end

      # Get next network interface, return NULL if we hit the last one
      #
      # @return [String]
      def next()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ziflist_next(self_p)
        result
      end

      # Return the current interface IP address as a printable string
      #
      # @return [String]
      def address()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ziflist_address(self_p)
        result
      end

      # Return the current interface broadcast address as a printable string
      #
      # @return [String]
      def broadcast()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ziflist_broadcast(self_p)
        result
      end

      # Return the current interface network mask as a printable string
      #
      # @return [String]
      def netmask()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ziflist_netmask(self_p)
        result
      end

      # Return the current interface MAC address as a printable string
      #
      # @return [String]
      def mac()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ziflist_mac(self_p)
        result
      end

      # Return the list of interfaces.
      #
      # @return [void]
      def print()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ziflist_print(self_p)
        result
      end

      # Get a list of network interfaces currently defined on the system
      # Includes IPv6 interfaces
      #
      # @return [Ziflist]
      def self.new_ipv6()
        result = ::CZMQ::FFI.ziflist_new_ipv6()
        result = Ziflist.__new result, true
        result
      end

      # Reload network interfaces from system, including IPv6
      #
      # @return [void]
      def reload_ipv6()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ziflist_reload_ipv6(self_p)
        result
      end

      # Return true if the current interface uses IPv6
      #
      # @return [Boolean]
      def is_ipv6()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.ziflist_is_ipv6(self_p)
        result
      end

      # Self test of this class.
      #
      # @param verbose [Boolean]
      # @return [void]
      def self.test(verbose)
        verbose = !(0==verbose||!verbose) # boolean
        result = ::CZMQ::FFI.ziflist_test(verbose)
        result
      end
    end
  end
end

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
