/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZtimerset.h"


///
//  Add a timer to the set. Returns timer id if OK, -1 on failure.
int QmlZtimerset::add (size_t interval, ztimerset_fn handler, void *arg) {
    return ztimerset_add (self, interval, handler, arg);
};

///
//  Cancel a timer. Returns 0 if OK, -1 on failure.
int QmlZtimerset::cancel (int timerId) {
    return ztimerset_cancel (self, timerId);
};

///
//  Set timer interval. Returns 0 if OK, -1 on failure.
//  This method is slow, canceling the timer and adding a new one yield better performance.
int QmlZtimerset::setInterval (int timerId, size_t interval) {
    return ztimerset_set_interval (self, timerId, interval);
};

///
//  Reset timer to start interval counting from current time. Returns 0 if OK, -1 on failure.
//  This method is slow, canceling the timer and adding a new one yield better performance.
int QmlZtimerset::reset (int timerId) {
    return ztimerset_reset (self, timerId);
};

///
//  Return the time until the next interval.
//  Should be used as timeout parameter for the zpoller wait method.
//  The timeout is in msec.
int QmlZtimerset::timeout () {
    return ztimerset_timeout (self);
};

///
//  Invoke callback function of all timers which their interval has elapsed.
//  Should be call after zpoller wait method.
//  Returns 0 if OK, -1 on failure.
int QmlZtimerset::execute () {
    return ztimerset_execute (self);
};


QObject* QmlZtimerset::qmlAttachedProperties(QObject* object) {
    return new QmlZtimersetAttached(object);
}


///
//  Self test of this class.
void QmlZtimersetAttached::test (bool verbose) {
    ztimerset_test (verbose);
};

///
//  Create new timer set.
QmlZtimerset *QmlZtimersetAttached::construct () {
    QmlZtimerset *qmlSelf = new QmlZtimerset ();
    qmlSelf->self = ztimerset_new ();
    return qmlSelf;
};

///
//  Destroy a timer set
void QmlZtimersetAttached::destruct (QmlZtimerset *qmlSelf) {
    ztimerset_destroy (&qmlSelf->self);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
