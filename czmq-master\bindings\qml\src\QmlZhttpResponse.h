/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#ifndef QML_ZHTTP_RESPONSE_H
#define QML_ZHTTP_RESPONSE_H

#include <QtQml>

#include <czmq.h>
#include "qml_czmq_plugin.h"


class QmlZhttpResponse : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isNULL READ isNULL)

public:
    zhttp_response_t *self;

    QmlZhttpResponse() { self = NULL; }
    bool isNULL() { return self == NULL; }

    static QObject* qmlAttachedProperties(QObject* object); // defined in QmlZhttpResponse.cpp

public slots:
    //  Send a response to a request.
    //  Returns 0 if successful and -1 otherwise.
    int send (QmlZsock *sock, void **connection);

    //  Receive a response from zhttp_client.
    //  On success return 0, -1 otherwise.
    //
    //  Recv returns the two user arguments which was provided with the request.
    //  The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an argument.
    int recv (QmlZhttpClient *client, void **arg, void **arg2);

    //  Get the response content type
    const QString contentType ();

    //  Set the content type of the response.
    void setContentType (const QString &value);

    //  Get the status code of the response.
    uint32_t statusCode ();

    //  Set the status code of the response.
    void setStatusCode (uint32_t statusCode);

    //  Get the headers of the response.
    QmlZhash *headers ();

    //  Get the content length of the response
    size_t contentLength ();

    //  Get the content of the response.
    const QString content ();

    //  Get the content of the response.
    QString getContent ();

    //  Set the content of the response.
    //  Content must by dynamically allocated string.
    //  Takes ownership of the content.
    void setContent (QString content);

    //  Set the content of the response.
    //  The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
    void setContentConst (const QString &content);

    //  Set the content to NULL
    void resetContent ();
};

class QmlZhttpResponseAttached : public QObject
{
    Q_OBJECT
    QObject* m_attached;

public:
    QmlZhttpResponseAttached (QObject* attached) {
        Q_UNUSED (attached);
    };

public slots:
    //  Self test of this class.
    void test (bool verbose);

    //  Create a new zhttp_response.
    QmlZhttpResponse *construct ();

    //  Destroy the zhttp_response.
    void destruct (QmlZhttpResponse *qmlSelf);
};


QML_DECLARE_TYPEINFO(QmlZhttpResponse, QML_HAS_ATTACHED_PROPERTIES)

#endif
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
