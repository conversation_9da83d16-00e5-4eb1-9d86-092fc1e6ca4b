################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

module CZMQ
  module FFI

    # work with config files written in rfc.zeromq.org/spec:4/ZPL.
    # @note This class is 100% generated using zproject.
    class Zconfig
      # Raised when one tries to use an instance of {Zconfig} after
      # the internal pointer to the native object has been nullified.
      class DestroyedError < RuntimeError; end

      # Boilerplate for self pointer, initializer, and finalizer
      class << self
        alias :__new :new
      end
      # Attaches the pointer _ptr_ to this instance and defines a finalizer for
      # it if necessary.
      # @param ptr [::FFI::Pointer]
      # @param finalize [Boolean]
      def initialize(ptr, finalize = true)
        @ptr = ptr
        if @ptr.null?
          @ptr = nil # Remove null pointers so we don't have to test for them.
        elsif finalize
          @finalizer = self.class.create_finalizer_for @ptr
          ObjectSpace.define_finalizer self, @finalizer
        end
      end
      # @param ptr [::FFI::Pointer]
      # @return [Proc]
      def self.create_finalizer_for(ptr)
        ptr_ptr = ::FFI::MemoryPointer.new :pointer

        Proc.new do
          ptr_ptr.write_pointer ptr
          ::CZMQ::FFI.zconfig_destroy ptr_ptr
        end
      end
      # @return [Boolean]
      def null?
        !@ptr or @ptr.null?
      end
      # Return internal pointer
      # @return [::FFI::Pointer]
      def __ptr
        raise DestroyedError unless @ptr
        @ptr
      end
      # So external Libraries can just pass the Object to a FFI function which expects a :pointer
      alias_method :to_ptr, :__ptr
      # Nullify internal pointer and return pointer pointer.
      # @note This detaches the current instance from the native object
      #   and thus makes it unusable.
      # @return [::FFI::MemoryPointer] the pointer pointing to a pointer
      #   pointing to the native object
      def __ptr_give_ref
        raise DestroyedError unless @ptr
        ptr_ptr = ::FFI::MemoryPointer.new :pointer
        ptr_ptr.write_pointer @ptr
        __undef_finalizer if @finalizer
        @ptr = nil
        ptr_ptr
      end
      # Undefines the finalizer for this object.
      # @note Only use this if you need to and can guarantee that the native
      #   object will be freed by other means.
      # @return [void]
      def __undef_finalizer
        ObjectSpace.undefine_finalizer self
        @finalizer = nil
      end

      # Create a new callback of the following type:
      #
      #     typedef int (zconfig_fct) (
      #         zconfig_t *self, void *arg, int level);
      #
      # @note WARNING: If your Ruby code doesn't retain a reference to the
      #   FFI::Function object after passing it to a C function call,
      #   it may be garbage collected while C still holds the pointer,
      #   potentially resulting in a segmentation fault.
      def self.fct
        ::FFI::Function.new :int, [:pointer, :pointer, :int], blocking: true do |self_, arg, level|
          self_ = Zconfig.__new self_, false
          result = yield self_, arg, level
          result = Integer(result)
          result
        end
      end

      # Create new config item
      # @param name [String, #to_s, nil]
      # @param parent [Zconfig, #__ptr]
      # @return [CZMQ::Zconfig]
      def self.new(name, parent)
        parent = parent.__ptr if parent
        ptr = ::CZMQ::FFI.zconfig_new(name, parent)
        __new ptr
      end

      # Load a config tree from a specified ZPL text file; returns a zconfig_t
      # reference for the root, if the file exists and is readable. Returns NULL
      # if the file does not exist.
      # @param filename [String, #to_s, nil]
      # @return [CZMQ::Zconfig]
      def self.load(filename)
        ptr = ::CZMQ::FFI.zconfig_load(filename)
        __new ptr
      end

      # Equivalent to zconfig_load, taking a format string instead of a fixed
      # filename.
      # @param format [String, #to_s, nil]
      # @param args [Array<Object>]
      # @return [CZMQ::Zconfig]
      def self.loadf(format, *args)
        ptr = ::CZMQ::FFI.zconfig_loadf(format, *args)
        __new ptr
      end

      # Destroy a config item and all its children
      #
      # @return [void]
      def destroy()
        return unless @ptr
        self_p = __ptr_give_ref
        result = ::CZMQ::FFI.zconfig_destroy(self_p)
        result
      end

      # Create copy of zconfig, caller MUST free the value
      # Create copy of config, as new zconfig object. Returns a fresh zconfig_t
      # object. If config is null, or memory was exhausted, returns null.
      #
      # @return [Zconfig]
      def dup()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_dup(self_p)
        result = Zconfig.__new result, true
        result
      end

      # Return name of config item
      #
      # @return [::FFI::Pointer]
      def name()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_name(self_p)
        result
      end

      # Return value of config item
      #
      # @return [::FFI::Pointer]
      def value()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_value(self_p)
        result
      end

      # Insert or update configuration key with value
      #
      # @param path [String, #to_s, nil]
      # @param value [String, #to_s, nil]
      # @return [void]
      def put(path, value)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_put(self_p, path, value)
        result
      end

      # Equivalent to zconfig_put, accepting a format specifier and variable
      # argument list, instead of a single string value.
      #
      # @param path [String, #to_s, nil]
      # @param format [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [void]
      def putf(path, format, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_putf(self_p, path, format, *args)
        result
      end

      # Get value for config item into a string value; leading slash is optional
      # and ignored.
      #
      # @param path [String, #to_s, nil]
      # @param default_value [String, #to_s, nil]
      # @return [::FFI::Pointer]
      def get(path, default_value)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_get(self_p, path, default_value)
        result
      end

      # Set config item name, name may be NULL
      #
      # @param name [String, #to_s, nil]
      # @return [void]
      def set_name(name)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_set_name(self_p, name)
        result
      end

      # Set new value for config item. The new value may be a string, a printf
      # format, or NULL. Note that if string may possibly contain '%', or if it
      # comes from an insecure source, you must use '%s' as the format, followed
      # by the string.
      #
      # @param format [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [void]
      def set_value(format, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_set_value(self_p, format, *args)
        result
      end

      # Find our first child, if any
      #
      # @return [Zconfig]
      def child()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_child(self_p)
        result = Zconfig.__new result, false
        result
      end

      # Find our first sibling, if any
      #
      # @return [Zconfig]
      def next()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_next(self_p)
        result = Zconfig.__new result, false
        result
      end

      # Find a config item along a path; leading slash is optional and ignored.
      #
      # @param path [String, #to_s, nil]
      # @return [Zconfig]
      def locate(path)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_locate(self_p, path)
        result = Zconfig.__new result, false
        result
      end

      # Locate the last config item at a specified depth
      #
      # @param level [Integer, #to_int, #to_i]
      # @return [Zconfig]
      def at_depth(level)
        raise DestroyedError unless @ptr
        self_p = @ptr
        level = Integer(level)
        result = ::CZMQ::FFI.zconfig_at_depth(self_p, level)
        result = Zconfig.__new result, false
        result
      end

      # Execute a callback for each config item in the tree; returns zero if
      # successful, else -1.
      #
      # @param handler [::FFI::Pointer, #to_ptr]
      # @param arg [::FFI::Pointer, #to_ptr]
      # @return [Integer]
      def execute(handler, arg)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_execute(self_p, handler, arg)
        result
      end

      # Add comment to config item before saving to disk. You can add as many
      # comment lines as you like. If you use a null format, all comments are
      # deleted.
      #
      # @param format [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [void]
      def set_comment(format, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_set_comment(self_p, format, *args)
        result
      end

      # Return comments of config item, as zlist.
      #
      # @return [Zlist]
      def comments()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_comments(self_p)
        result = Zlist.__new result, false
        result
      end

      # Save a config tree to a specified ZPL text file, where a filename
      # "-" means dump to standard output.
      #
      # @param filename [String, #to_s, nil]
      # @return [Integer]
      def save(filename)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_save(self_p, filename)
        result
      end

      # Equivalent to zconfig_save, taking a format string instead of a fixed
      # filename.
      #
      # @param format [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def savef(format, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_savef(self_p, format, *args)
        result
      end

      # Report filename used during zconfig_load, or NULL if none
      #
      # @return [String]
      def filename()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_filename(self_p)
        result
      end

      # Reload config tree from same file that it was previously loaded from.
      # Returns 0 if OK, -1 if there was an error (and then does not change
      # existing data).
      #
      # @param self_p [#__ptr_give_ref]
      # @return [Integer]
      def self.reload(self_p)
        self_p = self_p.__ptr_give_ref
        result = ::CZMQ::FFI.zconfig_reload(self_p)
        result
      end

      # Load a config tree from a memory chunk
      #
      # @param chunk [Zchunk, #__ptr]
      # @return [Zconfig]
      def self.chunk_load(chunk)
        chunk = chunk.__ptr if chunk
        result = ::CZMQ::FFI.zconfig_chunk_load(chunk)
        result = Zconfig.__new result, false
        result
      end

      # Save a config tree to a new memory chunk
      #
      # @return [Zchunk]
      def chunk_save()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_chunk_save(self_p)
        result = Zchunk.__new result, false
        result
      end

      # Load a config tree from a null-terminated string
      #
      # @param string [String, #to_s, nil]
      # @return [Zconfig]
      def self.str_load(string)
        result = ::CZMQ::FFI.zconfig_str_load(string)
        result = Zconfig.__new result, true
        result
      end

      # Save a config tree to a new null terminated string
      #
      # @return [::FFI::AutoPointer]
      def str_save()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_str_save(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Return true if a configuration tree was loaded from a file and that
      # file has changed in since the tree was loaded.
      #
      # @return [Boolean]
      def has_changed()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_has_changed(self_p)
        result
      end

      # Destroy subtree (all children)
      #
      # @return [void]
      def remove_subtree()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_remove_subtree(self_p)
        result
      end

      # Destroy node and subtree (all children)
      #
      # @param self_p [#__ptr_give_ref]
      # @return [void]
      def self.remove(self_p)
        self_p = self_p.__ptr_give_ref
        result = ::CZMQ::FFI.zconfig_remove(self_p)
        result
      end

      # Print the config file to open stream
      #
      # @param file [::FFI::Pointer, #to_ptr]
      # @return [void]
      def fprint(file)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_fprint(self_p, file)
        result
      end

      # Print properties of object
      #
      # @return [void]
      def print()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zconfig_print(self_p)
        result
      end

      # Self test of this class
      #
      # @param verbose [Boolean]
      # @return [void]
      def self.test(verbose)
        verbose = !(0==verbose||!verbose) # boolean
        result = ::CZMQ::FFI.zconfig_test(verbose)
        result
      end
    end
  end
end

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
