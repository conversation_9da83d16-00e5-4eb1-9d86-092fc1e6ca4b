/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZsock.h"


///
//  Bind a socket to a formatted endpoint. For tcp:// endpoints, supports
//  ephemeral ports, if you specify the port number as "*". By default
//  zsock uses the IANA designated range from C000 (49152) to FFFF (65535).
//  To override this range, follow the "*" with "[first-last]". Either or
//  both first and last may be empty. To bind to a random port within the
//  range, use "!" in place of "*".
//
//  Examples:
//      tcp://127.0.0.1:*           bind to first free port from C000 up
//      tcp://127.0.0.1:!           bind to random port from C000 to FFFF
//      tcp://127.0.0.1:*[60000-]   bind to first free port from 60000 up
//      tcp://127.0.0.1:![-60000]   bind to random port from C000 to 60000
//      tcp://127.0.0.1:![55000-55999]
//                                  bind to random port from 55000 to 55999
//
//  On success, returns the actual port number used, for tcp:// endpoints,
//  and 0 for other transports. On failure, returns -1. Note that when using
//  ephemeral ports, a port may be reused by different services without
//  clients being aware. Protocols that run on ephemeral ports should take
//  this into account.
int QmlZsock::bind (const QString &format) {
    return zsock_bind (self, "%s", format.toUtf8().data());
};

///
//  Returns last bound endpoint, if any.
const QString QmlZsock::endpoint () {
    return QString (zsock_endpoint (self));
};

///
//  Unbind a socket from a formatted endpoint.
//  Returns 0 if OK, -1 if the endpoint was invalid or the function
//  isn't supported.
int QmlZsock::unbind (const QString &format) {
    return zsock_unbind (self, "%s", format.toUtf8().data());
};

///
//  Connect a socket to a formatted endpoint
//  Returns 0 if OK, -1 if the endpoint was invalid.
int QmlZsock::connect (const QString &format) {
    return zsock_connect (self, "%s", format.toUtf8().data());
};

///
//  Disconnect a socket from a formatted endpoint
//  Returns 0 if OK, -1 if the endpoint was invalid or the function
//  isn't supported.
int QmlZsock::disconnect (const QString &format) {
    return zsock_disconnect (self, "%s", format.toUtf8().data());
};

///
//  Attach a socket to zero or more endpoints. If endpoints is not null,
//  parses as list of ZeroMQ endpoints, separated by commas, and prefixed by
//  '@' (to bind the socket) or '>' (to connect the socket). Returns 0 if all
//  endpoints were valid, or -1 if there was a syntax error. If the endpoint
//  does not start with '@' or '>', the serverish argument defines whether
//  it is used to bind (serverish = true) or connect (serverish = false).
int QmlZsock::attach (const QString &endpoints, bool serverish) {
    return zsock_attach (self, endpoints.toUtf8().data(), serverish);
};

///
//  Returns socket type as printable constant string.
const QString QmlZsock::typeStr () {
    return QString (zsock_type_str (self));
};

///
//  Send a 'picture' message to the socket (or actor). The picture is a
//  string that defines the type of each frame. This makes it easy to send
//  a complex multiframe message in one call. The picture can contain any
//  of these characters, each corresponding to one or two arguments:
//
//      i = int (signed)
//      1 = uint8_t
//      2 = uint16_t
//      4 = uint32_t
//      8 = uint64_t
//      s = char *
//      b = byte *, size_t (2 arguments)
//      c = zchunk_t *
//      f = zframe_t *
//      h = zhashx_t *
//      l = zlistx_t * (DRAFT)
//      U = zuuid_t *
//      p = void * (sends the pointer value, only meaningful over inproc)
//      m = zmsg_t * (sends all frames in the zmsg)
//      z = sends zero-sized frame (0 arguments)
//      u = uint (deprecated)
//
//  Note that s, b, c, and f are encoded the same way and the choice is
//  offered as a convenience to the sender, which may or may not already
//  have data in a zchunk or zframe. Does not change or take ownership of
//  any arguments. Returns 0 if successful, -1 if sending failed for any
//  reason.
int QmlZsock::send (const QString &picture) {
    return zsock_send (self, picture.toUtf8().data());
};

///
//  Send a 'picture' message to the socket (or actor). This is a va_list
//  version of zsock_send (), so please consult its documentation for the
//  details.
int QmlZsock::vsend (const QString &picture, va_list argptr) {
    return zsock_vsend (self, picture.toUtf8().data(), argptr);
};

///
//  Receive a 'picture' message to the socket (or actor). See zsock_send for
//  the format and meaning of the picture. Returns the picture elements into
//  a series of pointers as provided by the caller:
//
//      i = int * (stores signed integer)
//      4 = uint32_t * (stores 32-bit unsigned integer)
//      8 = uint64_t * (stores 64-bit unsigned integer)
//      s = char ** (allocates new string)
//      b = byte **, size_t * (2 arguments) (allocates memory)
//      c = zchunk_t ** (creates zchunk)
//      f = zframe_t ** (creates zframe)
//      U = zuuid_t * (creates a zuuid with the data)
//      h = zhashx_t ** (creates zhashx)
//      l = zlistx_t ** (creates zlistx) (DRAFT)
//      p = void ** (stores pointer)
//      m = zmsg_t ** (creates a zmsg with the remaining frames)
//      z = null, asserts empty frame (0 arguments)
//      u = uint * (stores unsigned integer, deprecated)
//
//  Note that zsock_recv creates the returned objects, and the caller must
//  destroy them when finished with them. The supplied pointers do not need
//  to be initialized. Returns 0 if successful, or -1 if it failed to recv
//  a message, in which case the pointers are not modified. When message
//  frames are truncated (a short message), sets return values to zero/null.
//  If an argument pointer is NULL, does not store any value (skips it).
//  An 'n' picture matches an empty frame; if the message does not match,
//  the method will return -1.
int QmlZsock::recv (const QString &picture) {
    return zsock_recv (self, picture.toUtf8().data());
};

///
//  Receive a 'picture' message from the socket (or actor). This is a
//  va_list version of zsock_recv (), so please consult its documentation
//  for the details.
int QmlZsock::vrecv (const QString &picture, va_list argptr) {
    return zsock_vrecv (self, picture.toUtf8().data(), argptr);
};

///
//  Send a binary encoded 'picture' message to the socket (or actor). This
//  method is similar to zsock_send, except the arguments are encoded in a
//  binary format that is compatible with zproto, and is designed to reduce
//  memory allocations. The pattern argument is a string that defines the
//  type of each argument. Supports these argument types:
//
//   pattern    C type                  zproto type:
//      1       uint8_t                 type = "number" size = "1"
//      2       uint16_t                type = "number" size = "2"
//      4       uint32_t                type = "number" size = "3"
//      8       uint64_t                type = "number" size = "4"
//      s       char *, 0-255 chars     type = "string"
//      S       char *, 0-2^32-1 chars  type = "longstr"
//      c       zchunk_t *              type = "chunk"
//      f       zframe_t *              type = "frame"
//      u       zuuid_t *               type = "uuid"
//      m       zmsg_t *                type = "msg"
//      p       void *, sends pointer value, only over inproc
//
//  Does not change or take ownership of any arguments. Returns 0 if
//  successful, -1 if sending failed for any reason.
int QmlZsock::bsend (const QString &picture) {
    return zsock_bsend (self, picture.toUtf8().data());
};

///
//  Receive a binary encoded 'picture' message from the socket (or actor).
//  This method is similar to zsock_recv, except the arguments are encoded
//  in a binary format that is compatible with zproto, and is designed to
//  reduce memory allocations. The pattern argument is a string that defines
//  the type of each argument. See zsock_bsend for the supported argument
//  types. All arguments must be pointers; this call sets them to point to
//  values held on a per-socket basis.
//  For types 1, 2, 4 and 8 the caller must allocate the memory itself before
//  calling zsock_brecv.
//  For types S, the caller must free the value once finished with it, as
//  zsock_brecv will allocate the buffer.
//  For type s, the caller must not free the value as it is stored in a
//  local cache for performance purposes.
//  For types c, f, u and m the caller must call the appropriate destructor
//  depending on the object as zsock_brecv will create new objects.
//  For type p the caller must coordinate with the sender, as it is just a
//  pointer value being passed.
int QmlZsock::brecv (const QString &picture) {
    return zsock_brecv (self, picture.toUtf8().data());
};

///
//  Return socket routing ID if any. This returns 0 if the socket is not
//  of type ZMQ_SERVER or if no request was already received on it.
uint32_t QmlZsock::routingId () {
    return zsock_routing_id (self);
};

///
//  Set routing ID on socket. The socket MUST be of type ZMQ_SERVER.
//  This will be used when sending messages on the socket via the zsock API.
void QmlZsock::setRoutingId (uint32_t routingId) {
    zsock_set_routing_id (self, routingId);
};

///
//  Set socket to use unbounded pipes (HWM=0); use this in cases when you are
//  totally certain the message volume can fit in memory. This method works
//  across all versions of ZeroMQ. Takes a polymorphic socket reference.
void QmlZsock::setUnbounded () {
    zsock_set_unbounded (self);
};

///
//  Send a signal over a socket. A signal is a short message carrying a
//  success/failure code (by convention, 0 means OK). Signals are encoded
//  to be distinguishable from "normal" messages. Accepts a zsock_t or a
//  zactor_t argument, and returns 0 if successful, -1 if the signal could
//  not be sent. Takes a polymorphic socket reference.
int QmlZsock::signal (byte status) {
    return zsock_signal (self, status);
};

///
//  Wait on a signal. Use this to coordinate between threads, over pipe
//  pairs. Blocks until the signal is received. Returns -1 on error, 0 or
//  greater on success. Accepts a zsock_t or a zactor_t as argument.
//  Takes a polymorphic socket reference.
int QmlZsock::wait () {
    return zsock_wait (self);
};

///
//  If there is a partial message still waiting on the socket, remove and
//  discard it. This is useful when reading partial messages, to get specific
//  message types.
void QmlZsock::flush () {
    zsock_flush (self);
};

///
//  Join a group for the RADIO-DISH pattern. Call only on ZMQ_DISH.
//  Returns 0 if OK, -1 if failed.
int QmlZsock::join (const QString &group) {
    return zsock_join (self, group.toUtf8().data());
};

///
//  Leave a group for the RADIO-DISH pattern. Call only on ZMQ_DISH.
//  Returns 0 if OK, -1 if failed.
int QmlZsock::leave (const QString &group) {
    return zsock_leave (self, group.toUtf8().data());
};

///
//  Check whether the socket has available message to read.
bool QmlZsock::hasIn () {
    return zsock_has_in (self);
};

///
//  Get socket option `priority`.
//  Available from libzmq 4.3.0.
int QmlZsock::priority () {
    return zsock_priority (self);
};

///
//  Set socket option `priority`.
//  Available from libzmq 4.3.0.
void QmlZsock::setPriority (int priority) {
    zsock_set_priority (self, priority);
};

///
//  Get socket option `reconnect_stop`.
//  Available from libzmq 4.3.0.
int QmlZsock::reconnectStop () {
    return zsock_reconnect_stop (self);
};

///
//  Set socket option `reconnect_stop`.
//  Available from libzmq 4.3.0.
void QmlZsock::setReconnectStop (int reconnectStop) {
    zsock_set_reconnect_stop (self, reconnectStop);
};

///
//  Set socket option `only_first_subscribe`.
//  Available from libzmq 4.3.0.
void QmlZsock::setOnlyFirstSubscribe (int onlyFirstSubscribe) {
    zsock_set_only_first_subscribe (self, onlyFirstSubscribe);
};

///
//  Set socket option `hello_msg`.
//  Available from libzmq 4.3.0.
void QmlZsock::setHelloMsg (QmlZframe *helloMsg) {
    zsock_set_hello_msg (self, helloMsg->self);
};

///
//  Set socket option `disconnect_msg`.
//  Available from libzmq 4.3.0.
void QmlZsock::setDisconnectMsg (QmlZframe *disconnectMsg) {
    zsock_set_disconnect_msg (self, disconnectMsg->self);
};

///
//  Set socket option `wss_trust_system`.
//  Available from libzmq 4.3.0.
void QmlZsock::setWssTrustSystem (int wssTrustSystem) {
    zsock_set_wss_trust_system (self, wssTrustSystem);
};

///
//  Set socket option `wss_hostname`.
//  Available from libzmq 4.3.0.
void QmlZsock::setWssHostname (const QString &wssHostname) {
    zsock_set_wss_hostname (self, wssHostname.toUtf8().data());
};

///
//  Set socket option `wss_trust_pem`.
//  Available from libzmq 4.3.0.
void QmlZsock::setWssTrustPem (const QString &wssTrustPem) {
    zsock_set_wss_trust_pem (self, wssTrustPem.toUtf8().data());
};

///
//  Set socket option `wss_cert_pem`.
//  Available from libzmq 4.3.0.
void QmlZsock::setWssCertPem (const QString &wssCertPem) {
    zsock_set_wss_cert_pem (self, wssCertPem.toUtf8().data());
};

///
//  Set socket option `wss_key_pem`.
//  Available from libzmq 4.3.0.
void QmlZsock::setWssKeyPem (const QString &wssKeyPem) {
    zsock_set_wss_key_pem (self, wssKeyPem.toUtf8().data());
};

///
//  Get socket option `out_batch_size`.
//  Available from libzmq 4.3.0.
int QmlZsock::outBatchSize () {
    return zsock_out_batch_size (self);
};

///
//  Set socket option `out_batch_size`.
//  Available from libzmq 4.3.0.
void QmlZsock::setOutBatchSize (int outBatchSize) {
    zsock_set_out_batch_size (self, outBatchSize);
};

///
//  Get socket option `in_batch_size`.
//  Available from libzmq 4.3.0.
int QmlZsock::inBatchSize () {
    return zsock_in_batch_size (self);
};

///
//  Set socket option `in_batch_size`.
//  Available from libzmq 4.3.0.
void QmlZsock::setInBatchSize (int inBatchSize) {
    zsock_set_in_batch_size (self, inBatchSize);
};

///
//  Get socket option `socks_password`.
//  Available from libzmq 4.3.0.
QString QmlZsock::socksPassword () {
    char *retStr_ = zsock_socks_password (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `socks_password`.
//  Available from libzmq 4.3.0.
void QmlZsock::setSocksPassword (const QString &socksPassword) {
    zsock_set_socks_password (self, socksPassword.toUtf8().data());
};

///
//  Get socket option `socks_username`.
//  Available from libzmq 4.3.0.
QString QmlZsock::socksUsername () {
    char *retStr_ = zsock_socks_username (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `socks_username`.
//  Available from libzmq 4.3.0.
void QmlZsock::setSocksUsername (const QString &socksUsername) {
    zsock_set_socks_username (self, socksUsername.toUtf8().data());
};

///
//  Set socket option `xpub_manual_last_value`.
//  Available from libzmq 4.3.0.
void QmlZsock::setXpubManualLastValue (int xpubManualLastValue) {
    zsock_set_xpub_manual_last_value (self, xpubManualLastValue);
};

///
//  Get socket option `router_notify`.
//  Available from libzmq 4.3.0.
int QmlZsock::routerNotify () {
    return zsock_router_notify (self);
};

///
//  Set socket option `router_notify`.
//  Available from libzmq 4.3.0.
void QmlZsock::setRouterNotify (int routerNotify) {
    zsock_set_router_notify (self, routerNotify);
};

///
//  Get socket option `multicast_loop`.
//  Available from libzmq 4.3.0.
int QmlZsock::multicastLoop () {
    return zsock_multicast_loop (self);
};

///
//  Set socket option `multicast_loop`.
//  Available from libzmq 4.3.0.
void QmlZsock::setMulticastLoop (int multicastLoop) {
    zsock_set_multicast_loop (self, multicastLoop);
};

///
//  Get socket option `metadata`.
//  Available from libzmq 4.3.0.
QString QmlZsock::metadata () {
    char *retStr_ = zsock_metadata (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `metadata`.
//  Available from libzmq 4.3.0.
void QmlZsock::setMetadata (const QString &metadata) {
    zsock_set_metadata (self, metadata.toUtf8().data());
};

///
//  Get socket option `loopback_fastpath`.
//  Available from libzmq 4.3.0.
int QmlZsock::loopbackFastpath () {
    return zsock_loopback_fastpath (self);
};

///
//  Set socket option `loopback_fastpath`.
//  Available from libzmq 4.3.0.
void QmlZsock::setLoopbackFastpath (int loopbackFastpath) {
    zsock_set_loopback_fastpath (self, loopbackFastpath);
};

///
//  Get socket option `zap_enforce_domain`.
//  Available from libzmq 4.3.0.
int QmlZsock::zapEnforceDomain () {
    return zsock_zap_enforce_domain (self);
};

///
//  Set socket option `zap_enforce_domain`.
//  Available from libzmq 4.3.0.
void QmlZsock::setZapEnforceDomain (int zapEnforceDomain) {
    zsock_set_zap_enforce_domain (self, zapEnforceDomain);
};

///
//  Get socket option `gssapi_principal_nametype`.
//  Available from libzmq 4.3.0.
int QmlZsock::gssapiPrincipalNametype () {
    return zsock_gssapi_principal_nametype (self);
};

///
//  Set socket option `gssapi_principal_nametype`.
//  Available from libzmq 4.3.0.
void QmlZsock::setGssapiPrincipalNametype (int gssapiPrincipalNametype) {
    zsock_set_gssapi_principal_nametype (self, gssapiPrincipalNametype);
};

///
//  Get socket option `gssapi_service_principal_nametype`.
//  Available from libzmq 4.3.0.
int QmlZsock::gssapiServicePrincipalNametype () {
    return zsock_gssapi_service_principal_nametype (self);
};

///
//  Set socket option `gssapi_service_principal_nametype`.
//  Available from libzmq 4.3.0.
void QmlZsock::setGssapiServicePrincipalNametype (int gssapiServicePrincipalNametype) {
    zsock_set_gssapi_service_principal_nametype (self, gssapiServicePrincipalNametype);
};

///
//  Get socket option `bindtodevice`.
//  Available from libzmq 4.3.0.
QString QmlZsock::bindtodevice () {
    char *retStr_ = zsock_bindtodevice (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `bindtodevice`.
//  Available from libzmq 4.3.0.
void QmlZsock::setBindtodevice (const QString &bindtodevice) {
    zsock_set_bindtodevice (self, bindtodevice.toUtf8().data());
};

///
//  Get socket option `heartbeat_ivl`.
//  Available from libzmq 4.2.0.
int QmlZsock::heartbeatIvl () {
    return zsock_heartbeat_ivl (self);
};

///
//  Set socket option `heartbeat_ivl`.
//  Available from libzmq 4.2.0.
void QmlZsock::setHeartbeatIvl (int heartbeatIvl) {
    zsock_set_heartbeat_ivl (self, heartbeatIvl);
};

///
//  Get socket option `heartbeat_ttl`.
//  Available from libzmq 4.2.0.
int QmlZsock::heartbeatTtl () {
    return zsock_heartbeat_ttl (self);
};

///
//  Set socket option `heartbeat_ttl`.
//  Available from libzmq 4.2.0.
void QmlZsock::setHeartbeatTtl (int heartbeatTtl) {
    zsock_set_heartbeat_ttl (self, heartbeatTtl);
};

///
//  Get socket option `heartbeat_timeout`.
//  Available from libzmq 4.2.0.
int QmlZsock::heartbeatTimeout () {
    return zsock_heartbeat_timeout (self);
};

///
//  Set socket option `heartbeat_timeout`.
//  Available from libzmq 4.2.0.
void QmlZsock::setHeartbeatTimeout (int heartbeatTimeout) {
    zsock_set_heartbeat_timeout (self, heartbeatTimeout);
};

///
//  Get socket option `use_fd`.
//  Available from libzmq 4.2.0.
int QmlZsock::useFd () {
    return zsock_use_fd (self);
};

///
//  Set socket option `use_fd`.
//  Available from libzmq 4.2.0.
void QmlZsock::setUseFd (int useFd) {
    zsock_set_use_fd (self, useFd);
};

///
//  Set socket option `xpub_manual`.
//  Available from libzmq 4.2.0.
void QmlZsock::setXpubManual (int xpubManual) {
    zsock_set_xpub_manual (self, xpubManual);
};

///
//  Set socket option `xpub_welcome_msg`.
//  Available from libzmq 4.2.0.
void QmlZsock::setXpubWelcomeMsg (const QString &xpubWelcomeMsg) {
    zsock_set_xpub_welcome_msg (self, xpubWelcomeMsg.toUtf8().data());
};

///
//  Set socket option `stream_notify`.
//  Available from libzmq 4.2.0.
void QmlZsock::setStreamNotify (int streamNotify) {
    zsock_set_stream_notify (self, streamNotify);
};

///
//  Get socket option `invert_matching`.
//  Available from libzmq 4.2.0.
int QmlZsock::invertMatching () {
    return zsock_invert_matching (self);
};

///
//  Set socket option `invert_matching`.
//  Available from libzmq 4.2.0.
void QmlZsock::setInvertMatching (int invertMatching) {
    zsock_set_invert_matching (self, invertMatching);
};

///
//  Set socket option `xpub_verboser`.
//  Available from libzmq 4.2.0.
void QmlZsock::setXpubVerboser (int xpubVerboser) {
    zsock_set_xpub_verboser (self, xpubVerboser);
};

///
//  Get socket option `connect_timeout`.
//  Available from libzmq 4.2.0.
int QmlZsock::connectTimeout () {
    return zsock_connect_timeout (self);
};

///
//  Set socket option `connect_timeout`.
//  Available from libzmq 4.2.0.
void QmlZsock::setConnectTimeout (int connectTimeout) {
    zsock_set_connect_timeout (self, connectTimeout);
};

///
//  Get socket option `tcp_maxrt`.
//  Available from libzmq 4.2.0.
int QmlZsock::tcpMaxrt () {
    return zsock_tcp_maxrt (self);
};

///
//  Set socket option `tcp_maxrt`.
//  Available from libzmq 4.2.0.
void QmlZsock::setTcpMaxrt (int tcpMaxrt) {
    zsock_set_tcp_maxrt (self, tcpMaxrt);
};

///
//  Get socket option `thread_safe`.
//  Available from libzmq 4.2.0.
int QmlZsock::threadSafe () {
    return zsock_thread_safe (self);
};

///
//  Get socket option `multicast_maxtpdu`.
//  Available from libzmq 4.2.0.
int QmlZsock::multicastMaxtpdu () {
    return zsock_multicast_maxtpdu (self);
};

///
//  Set socket option `multicast_maxtpdu`.
//  Available from libzmq 4.2.0.
void QmlZsock::setMulticastMaxtpdu (int multicastMaxtpdu) {
    zsock_set_multicast_maxtpdu (self, multicastMaxtpdu);
};

///
//  Get socket option `vmci_buffer_size`.
//  Available from libzmq 4.2.0.
int QmlZsock::vmciBufferSize () {
    return zsock_vmci_buffer_size (self);
};

///
//  Set socket option `vmci_buffer_size`.
//  Available from libzmq 4.2.0.
void QmlZsock::setVmciBufferSize (int vmciBufferSize) {
    zsock_set_vmci_buffer_size (self, vmciBufferSize);
};

///
//  Get socket option `vmci_buffer_min_size`.
//  Available from libzmq 4.2.0.
int QmlZsock::vmciBufferMinSize () {
    return zsock_vmci_buffer_min_size (self);
};

///
//  Set socket option `vmci_buffer_min_size`.
//  Available from libzmq 4.2.0.
void QmlZsock::setVmciBufferMinSize (int vmciBufferMinSize) {
    zsock_set_vmci_buffer_min_size (self, vmciBufferMinSize);
};

///
//  Get socket option `vmci_buffer_max_size`.
//  Available from libzmq 4.2.0.
int QmlZsock::vmciBufferMaxSize () {
    return zsock_vmci_buffer_max_size (self);
};

///
//  Set socket option `vmci_buffer_max_size`.
//  Available from libzmq 4.2.0.
void QmlZsock::setVmciBufferMaxSize (int vmciBufferMaxSize) {
    zsock_set_vmci_buffer_max_size (self, vmciBufferMaxSize);
};

///
//  Get socket option `vmci_connect_timeout`.
//  Available from libzmq 4.2.0.
int QmlZsock::vmciConnectTimeout () {
    return zsock_vmci_connect_timeout (self);
};

///
//  Set socket option `vmci_connect_timeout`.
//  Available from libzmq 4.2.0.
void QmlZsock::setVmciConnectTimeout (int vmciConnectTimeout) {
    zsock_set_vmci_connect_timeout (self, vmciConnectTimeout);
};

///
//  Get socket option `tos`.
//  Available from libzmq 4.1.0.
int QmlZsock::tos () {
    return zsock_tos (self);
};

///
//  Set socket option `tos`.
//  Available from libzmq 4.1.0.
void QmlZsock::setTos (int tos) {
    zsock_set_tos (self, tos);
};

///
//  Set socket option `router_handover`.
//  Available from libzmq 4.1.0.
void QmlZsock::setRouterHandover (int routerHandover) {
    zsock_set_router_handover (self, routerHandover);
};

///
//  Set socket option `connect_rid`.
//  Available from libzmq 4.1.0.
void QmlZsock::setConnectRid (const QString &connectRid) {
    zsock_set_connect_rid (self, connectRid.toUtf8().data());
};

///
//  Set socket option `connect_rid` from 32-octet binary
//  Available from libzmq 4.1.0.
void QmlZsock::setConnectRidBin (const byte *connectRid) {
    zsock_set_connect_rid_bin (self, connectRid);
};

///
//  Get socket option `handshake_ivl`.
//  Available from libzmq 4.1.0.
int QmlZsock::handshakeIvl () {
    return zsock_handshake_ivl (self);
};

///
//  Set socket option `handshake_ivl`.
//  Available from libzmq 4.1.0.
void QmlZsock::setHandshakeIvl (int handshakeIvl) {
    zsock_set_handshake_ivl (self, handshakeIvl);
};

///
//  Get socket option `socks_proxy`.
//  Available from libzmq 4.1.0.
QString QmlZsock::socksProxy () {
    char *retStr_ = zsock_socks_proxy (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `socks_proxy`.
//  Available from libzmq 4.1.0.
void QmlZsock::setSocksProxy (const QString &socksProxy) {
    zsock_set_socks_proxy (self, socksProxy.toUtf8().data());
};

///
//  Set socket option `xpub_nodrop`.
//  Available from libzmq 4.1.0.
void QmlZsock::setXpubNodrop (int xpubNodrop) {
    zsock_set_xpub_nodrop (self, xpubNodrop);
};

///
//  Set socket option `router_mandatory`.
//  Available from libzmq 4.0.0.
void QmlZsock::setRouterMandatory (int routerMandatory) {
    zsock_set_router_mandatory (self, routerMandatory);
};

///
//  Set socket option `probe_router`.
//  Available from libzmq 4.0.0.
void QmlZsock::setProbeRouter (int probeRouter) {
    zsock_set_probe_router (self, probeRouter);
};

///
//  Set socket option `req_relaxed`.
//  Available from libzmq 4.0.0.
void QmlZsock::setReqRelaxed (int reqRelaxed) {
    zsock_set_req_relaxed (self, reqRelaxed);
};

///
//  Set socket option `req_correlate`.
//  Available from libzmq 4.0.0.
void QmlZsock::setReqCorrelate (int reqCorrelate) {
    zsock_set_req_correlate (self, reqCorrelate);
};

///
//  Set socket option `conflate`.
//  Available from libzmq 4.0.0.
void QmlZsock::setConflate (int conflate) {
    zsock_set_conflate (self, conflate);
};

///
//  Get socket option `zap_domain`.
//  Available from libzmq 4.0.0.
QString QmlZsock::zapDomain () {
    char *retStr_ = zsock_zap_domain (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `zap_domain`.
//  Available from libzmq 4.0.0.
void QmlZsock::setZapDomain (const QString &zapDomain) {
    zsock_set_zap_domain (self, zapDomain.toUtf8().data());
};

///
//  Get socket option `mechanism`.
//  Available from libzmq 4.0.0.
int QmlZsock::mechanism () {
    return zsock_mechanism (self);
};

///
//  Get socket option `plain_server`.
//  Available from libzmq 4.0.0.
int QmlZsock::plainServer () {
    return zsock_plain_server (self);
};

///
//  Set socket option `plain_server`.
//  Available from libzmq 4.0.0.
void QmlZsock::setPlainServer (int plainServer) {
    zsock_set_plain_server (self, plainServer);
};

///
//  Get socket option `plain_username`.
//  Available from libzmq 4.0.0.
QString QmlZsock::plainUsername () {
    char *retStr_ = zsock_plain_username (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `plain_username`.
//  Available from libzmq 4.0.0.
void QmlZsock::setPlainUsername (const QString &plainUsername) {
    zsock_set_plain_username (self, plainUsername.toUtf8().data());
};

///
//  Get socket option `plain_password`.
//  Available from libzmq 4.0.0.
QString QmlZsock::plainPassword () {
    char *retStr_ = zsock_plain_password (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `plain_password`.
//  Available from libzmq 4.0.0.
void QmlZsock::setPlainPassword (const QString &plainPassword) {
    zsock_set_plain_password (self, plainPassword.toUtf8().data());
};

///
//  Get socket option `curve_server`.
//  Available from libzmq 4.0.0.
int QmlZsock::curveServer () {
    return zsock_curve_server (self);
};

///
//  Set socket option `curve_server`.
//  Available from libzmq 4.0.0.
void QmlZsock::setCurveServer (int curveServer) {
    zsock_set_curve_server (self, curveServer);
};

///
//  Get socket option `curve_publickey`.
//  Available from libzmq 4.0.0.
QString QmlZsock::curvePublickey () {
    char *retStr_ = zsock_curve_publickey (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `curve_publickey`.
//  Available from libzmq 4.0.0.
void QmlZsock::setCurvePublickey (const QString &curvePublickey) {
    zsock_set_curve_publickey (self, curvePublickey.toUtf8().data());
};

///
//  Set socket option `curve_publickey` from 32-octet binary
//  Available from libzmq 4.0.0.
void QmlZsock::setCurvePublickeyBin (const byte *curvePublickey) {
    zsock_set_curve_publickey_bin (self, curvePublickey);
};

///
//  Get socket option `curve_secretkey`.
//  Available from libzmq 4.0.0.
QString QmlZsock::curveSecretkey () {
    char *retStr_ = zsock_curve_secretkey (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `curve_secretkey`.
//  Available from libzmq 4.0.0.
void QmlZsock::setCurveSecretkey (const QString &curveSecretkey) {
    zsock_set_curve_secretkey (self, curveSecretkey.toUtf8().data());
};

///
//  Set socket option `curve_secretkey` from 32-octet binary
//  Available from libzmq 4.0.0.
void QmlZsock::setCurveSecretkeyBin (const byte *curveSecretkey) {
    zsock_set_curve_secretkey_bin (self, curveSecretkey);
};

///
//  Get socket option `curve_serverkey`.
//  Available from libzmq 4.0.0.
QString QmlZsock::curveServerkey () {
    char *retStr_ = zsock_curve_serverkey (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `curve_serverkey`.
//  Available from libzmq 4.0.0.
void QmlZsock::setCurveServerkey (const QString &curveServerkey) {
    zsock_set_curve_serverkey (self, curveServerkey.toUtf8().data());
};

///
//  Set socket option `curve_serverkey` from 32-octet binary
//  Available from libzmq 4.0.0.
void QmlZsock::setCurveServerkeyBin (const byte *curveServerkey) {
    zsock_set_curve_serverkey_bin (self, curveServerkey);
};

///
//  Get socket option `gssapi_server`.
//  Available from libzmq 4.0.0.
int QmlZsock::gssapiServer () {
    return zsock_gssapi_server (self);
};

///
//  Set socket option `gssapi_server`.
//  Available from libzmq 4.0.0.
void QmlZsock::setGssapiServer (int gssapiServer) {
    zsock_set_gssapi_server (self, gssapiServer);
};

///
//  Get socket option `gssapi_plaintext`.
//  Available from libzmq 4.0.0.
int QmlZsock::gssapiPlaintext () {
    return zsock_gssapi_plaintext (self);
};

///
//  Set socket option `gssapi_plaintext`.
//  Available from libzmq 4.0.0.
void QmlZsock::setGssapiPlaintext (int gssapiPlaintext) {
    zsock_set_gssapi_plaintext (self, gssapiPlaintext);
};

///
//  Get socket option `gssapi_principal`.
//  Available from libzmq 4.0.0.
QString QmlZsock::gssapiPrincipal () {
    char *retStr_ = zsock_gssapi_principal (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `gssapi_principal`.
//  Available from libzmq 4.0.0.
void QmlZsock::setGssapiPrincipal (const QString &gssapiPrincipal) {
    zsock_set_gssapi_principal (self, gssapiPrincipal.toUtf8().data());
};

///
//  Get socket option `gssapi_service_principal`.
//  Available from libzmq 4.0.0.
QString QmlZsock::gssapiServicePrincipal () {
    char *retStr_ = zsock_gssapi_service_principal (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `gssapi_service_principal`.
//  Available from libzmq 4.0.0.
void QmlZsock::setGssapiServicePrincipal (const QString &gssapiServicePrincipal) {
    zsock_set_gssapi_service_principal (self, gssapiServicePrincipal.toUtf8().data());
};

///
//  Get socket option `ipv6`.
//  Available from libzmq 4.0.0.
int QmlZsock::ipv6 () {
    return zsock_ipv6 (self);
};

///
//  Set socket option `ipv6`.
//  Available from libzmq 4.0.0.
void QmlZsock::setIpv6 (int ipv6) {
    zsock_set_ipv6 (self, ipv6);
};

///
//  Get socket option `immediate`.
//  Available from libzmq 4.0.0.
int QmlZsock::immediate () {
    return zsock_immediate (self);
};

///
//  Set socket option `immediate`.
//  Available from libzmq 4.0.0.
void QmlZsock::setImmediate (int immediate) {
    zsock_set_immediate (self, immediate);
};

///
//  Get socket option `sndhwm`.
//  Available from libzmq 3.0.0.
int QmlZsock::sndhwm () {
    return zsock_sndhwm (self);
};

///
//  Set socket option `sndhwm`.
//  Available from libzmq 3.0.0.
void QmlZsock::setSndhwm (int sndhwm) {
    zsock_set_sndhwm (self, sndhwm);
};

///
//  Get socket option `rcvhwm`.
//  Available from libzmq 3.0.0.
int QmlZsock::rcvhwm () {
    return zsock_rcvhwm (self);
};

///
//  Set socket option `rcvhwm`.
//  Available from libzmq 3.0.0.
void QmlZsock::setRcvhwm (int rcvhwm) {
    zsock_set_rcvhwm (self, rcvhwm);
};

///
//  Get socket option `maxmsgsize`.
//  Available from libzmq 3.0.0.
int QmlZsock::maxmsgsize () {
    return zsock_maxmsgsize (self);
};

///
//  Set socket option `maxmsgsize`.
//  Available from libzmq 3.0.0.
void QmlZsock::setMaxmsgsize (int maxmsgsize) {
    zsock_set_maxmsgsize (self, maxmsgsize);
};

///
//  Get socket option `multicast_hops`.
//  Available from libzmq 3.0.0.
int QmlZsock::multicastHops () {
    return zsock_multicast_hops (self);
};

///
//  Set socket option `multicast_hops`.
//  Available from libzmq 3.0.0.
void QmlZsock::setMulticastHops (int multicastHops) {
    zsock_set_multicast_hops (self, multicastHops);
};

///
//  Set socket option `xpub_verbose`.
//  Available from libzmq 3.0.0.
void QmlZsock::setXpubVerbose (int xpubVerbose) {
    zsock_set_xpub_verbose (self, xpubVerbose);
};

///
//  Get socket option `tcp_keepalive`.
//  Available from libzmq 3.0.0.
int QmlZsock::tcpKeepalive () {
    return zsock_tcp_keepalive (self);
};

///
//  Set socket option `tcp_keepalive`.
//  Available from libzmq 3.0.0.
void QmlZsock::setTcpKeepalive (int tcpKeepalive) {
    zsock_set_tcp_keepalive (self, tcpKeepalive);
};

///
//  Get socket option `tcp_keepalive_idle`.
//  Available from libzmq 3.0.0.
int QmlZsock::tcpKeepaliveIdle () {
    return zsock_tcp_keepalive_idle (self);
};

///
//  Set socket option `tcp_keepalive_idle`.
//  Available from libzmq 3.0.0.
void QmlZsock::setTcpKeepaliveIdle (int tcpKeepaliveIdle) {
    zsock_set_tcp_keepalive_idle (self, tcpKeepaliveIdle);
};

///
//  Get socket option `tcp_keepalive_cnt`.
//  Available from libzmq 3.0.0.
int QmlZsock::tcpKeepaliveCnt () {
    return zsock_tcp_keepalive_cnt (self);
};

///
//  Set socket option `tcp_keepalive_cnt`.
//  Available from libzmq 3.0.0.
void QmlZsock::setTcpKeepaliveCnt (int tcpKeepaliveCnt) {
    zsock_set_tcp_keepalive_cnt (self, tcpKeepaliveCnt);
};

///
//  Get socket option `tcp_keepalive_intvl`.
//  Available from libzmq 3.0.0.
int QmlZsock::tcpKeepaliveIntvl () {
    return zsock_tcp_keepalive_intvl (self);
};

///
//  Set socket option `tcp_keepalive_intvl`.
//  Available from libzmq 3.0.0.
void QmlZsock::setTcpKeepaliveIntvl (int tcpKeepaliveIntvl) {
    zsock_set_tcp_keepalive_intvl (self, tcpKeepaliveIntvl);
};

///
//  Get socket option `tcp_accept_filter`.
//  Available from libzmq 3.0.0.
QString QmlZsock::tcpAcceptFilter () {
    char *retStr_ = zsock_tcp_accept_filter (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `tcp_accept_filter`.
//  Available from libzmq 3.0.0.
void QmlZsock::setTcpAcceptFilter (const QString &tcpAcceptFilter) {
    zsock_set_tcp_accept_filter (self, tcpAcceptFilter.toUtf8().data());
};

///
//  Get socket option `last_endpoint`.
//  Available from libzmq 3.0.0.
QString QmlZsock::lastEndpoint () {
    char *retStr_ = zsock_last_endpoint (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `router_raw`.
//  Available from libzmq 3.0.0.
void QmlZsock::setRouterRaw (int routerRaw) {
    zsock_set_router_raw (self, routerRaw);
};

///
//  Get socket option `ipv4only`.
//  Available from libzmq 3.0.0.
int QmlZsock::ipv4only () {
    return zsock_ipv4only (self);
};

///
//  Set socket option `ipv4only`.
//  Available from libzmq 3.0.0.
void QmlZsock::setIpv4only (int ipv4only) {
    zsock_set_ipv4only (self, ipv4only);
};

///
//  Set socket option `delay_attach_on_connect`.
//  Available from libzmq 3.0.0.
void QmlZsock::setDelayAttachOnConnect (int delayAttachOnConnect) {
    zsock_set_delay_attach_on_connect (self, delayAttachOnConnect);
};

///
//  Get socket option `hwm`.
//  Available from libzmq 2.0.0 to 3.0.0.
int QmlZsock::hwm () {
    return zsock_hwm (self);
};

///
//  Set socket option `hwm`.
//  Available from libzmq 2.0.0 to 3.0.0.
void QmlZsock::setHwm (int hwm) {
    zsock_set_hwm (self, hwm);
};

///
//  Get socket option `swap`.
//  Available from libzmq 2.0.0 to 3.0.0.
int QmlZsock::swap () {
    return zsock_swap (self);
};

///
//  Set socket option `swap`.
//  Available from libzmq 2.0.0 to 3.0.0.
void QmlZsock::setSwap (int swap) {
    zsock_set_swap (self, swap);
};

///
//  Get socket option `affinity`.
//  Available from libzmq 2.0.0.
int QmlZsock::affinity () {
    return zsock_affinity (self);
};

///
//  Set socket option `affinity`.
//  Available from libzmq 2.0.0.
void QmlZsock::setAffinity (int affinity) {
    zsock_set_affinity (self, affinity);
};

///
//  Get socket option `identity`.
//  Available from libzmq 2.0.0.
QString QmlZsock::identity () {
    char *retStr_ = zsock_identity (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set socket option `identity`.
//  Available from libzmq 2.0.0.
void QmlZsock::setIdentity (const QString &identity) {
    zsock_set_identity (self, identity.toUtf8().data());
};

///
//  Get socket option `rate`.
//  Available from libzmq 2.0.0.
int QmlZsock::rate () {
    return zsock_rate (self);
};

///
//  Set socket option `rate`.
//  Available from libzmq 2.0.0.
void QmlZsock::setRate (int rate) {
    zsock_set_rate (self, rate);
};

///
//  Get socket option `recovery_ivl`.
//  Available from libzmq 2.0.0.
int QmlZsock::recoveryIvl () {
    return zsock_recovery_ivl (self);
};

///
//  Set socket option `recovery_ivl`.
//  Available from libzmq 2.0.0.
void QmlZsock::setRecoveryIvl (int recoveryIvl) {
    zsock_set_recovery_ivl (self, recoveryIvl);
};

///
//  Get socket option `recovery_ivl_msec`.
//  Available from libzmq 2.0.0 to 3.0.0.
int QmlZsock::recoveryIvlMsec () {
    return zsock_recovery_ivl_msec (self);
};

///
//  Set socket option `recovery_ivl_msec`.
//  Available from libzmq 2.0.0 to 3.0.0.
void QmlZsock::setRecoveryIvlMsec (int recoveryIvlMsec) {
    zsock_set_recovery_ivl_msec (self, recoveryIvlMsec);
};

///
//  Get socket option `mcast_loop`.
//  Available from libzmq 2.0.0 to 3.0.0.
int QmlZsock::mcastLoop () {
    return zsock_mcast_loop (self);
};

///
//  Set socket option `mcast_loop`.
//  Available from libzmq 2.0.0 to 3.0.0.
void QmlZsock::setMcastLoop (int mcastLoop) {
    zsock_set_mcast_loop (self, mcastLoop);
};

///
//  Get socket option `rcvtimeo`.
//  Available from libzmq 2.2.0.
int QmlZsock::rcvtimeo () {
    return zsock_rcvtimeo (self);
};

///
//  Set socket option `rcvtimeo`.
//  Available from libzmq 2.2.0.
void QmlZsock::setRcvtimeo (int rcvtimeo) {
    zsock_set_rcvtimeo (self, rcvtimeo);
};

///
//  Get socket option `sndtimeo`.
//  Available from libzmq 2.2.0.
int QmlZsock::sndtimeo () {
    return zsock_sndtimeo (self);
};

///
//  Set socket option `sndtimeo`.
//  Available from libzmq 2.2.0.
void QmlZsock::setSndtimeo (int sndtimeo) {
    zsock_set_sndtimeo (self, sndtimeo);
};

///
//  Get socket option `sndbuf`.
//  Available from libzmq 2.0.0.
int QmlZsock::sndbuf () {
    return zsock_sndbuf (self);
};

///
//  Set socket option `sndbuf`.
//  Available from libzmq 2.0.0.
void QmlZsock::setSndbuf (int sndbuf) {
    zsock_set_sndbuf (self, sndbuf);
};

///
//  Get socket option `rcvbuf`.
//  Available from libzmq 2.0.0.
int QmlZsock::rcvbuf () {
    return zsock_rcvbuf (self);
};

///
//  Set socket option `rcvbuf`.
//  Available from libzmq 2.0.0.
void QmlZsock::setRcvbuf (int rcvbuf) {
    zsock_set_rcvbuf (self, rcvbuf);
};

///
//  Get socket option `linger`.
//  Available from libzmq 2.0.0.
int QmlZsock::linger () {
    return zsock_linger (self);
};

///
//  Set socket option `linger`.
//  Available from libzmq 2.0.0.
void QmlZsock::setLinger (int linger) {
    zsock_set_linger (self, linger);
};

///
//  Get socket option `reconnect_ivl`.
//  Available from libzmq 2.0.0.
int QmlZsock::reconnectIvl () {
    return zsock_reconnect_ivl (self);
};

///
//  Set socket option `reconnect_ivl`.
//  Available from libzmq 2.0.0.
void QmlZsock::setReconnectIvl (int reconnectIvl) {
    zsock_set_reconnect_ivl (self, reconnectIvl);
};

///
//  Get socket option `reconnect_ivl_max`.
//  Available from libzmq 2.0.0.
int QmlZsock::reconnectIvlMax () {
    return zsock_reconnect_ivl_max (self);
};

///
//  Set socket option `reconnect_ivl_max`.
//  Available from libzmq 2.0.0.
void QmlZsock::setReconnectIvlMax (int reconnectIvlMax) {
    zsock_set_reconnect_ivl_max (self, reconnectIvlMax);
};

///
//  Get socket option `backlog`.
//  Available from libzmq 2.0.0.
int QmlZsock::backlog () {
    return zsock_backlog (self);
};

///
//  Set socket option `backlog`.
//  Available from libzmq 2.0.0.
void QmlZsock::setBacklog (int backlog) {
    zsock_set_backlog (self, backlog);
};

///
//  Set socket option `subscribe`.
//  Available from libzmq 2.0.0.
void QmlZsock::setSubscribe (const QString &subscribe) {
    zsock_set_subscribe (self, subscribe.toUtf8().data());
};

///
//  Set socket option `unsubscribe`.
//  Available from libzmq 2.0.0.
void QmlZsock::setUnsubscribe (const QString &unsubscribe) {
    zsock_set_unsubscribe (self, unsubscribe.toUtf8().data());
};

///
//  Get socket option `type`.
//  Available from libzmq 2.0.0.
int QmlZsock::type () {
    return zsock_type (self);
};

///
//  Get socket option `rcvmore`.
//  Available from libzmq 2.0.0.
int QmlZsock::rcvmore () {
    return zsock_rcvmore (self);
};

///
//  Get socket option `fd`.
//  Available from libzmq 2.0.0.
SOCKET QmlZsock::fd () {
    return zsock_fd (self);
};

///
//  Get socket option `events`.
//  Available from libzmq 2.0.0.
int QmlZsock::events () {
    return zsock_events (self);
};


QObject* QmlZsock::qmlAttachedProperties(QObject* object) {
    return new QmlZsockAttached(object);
}


///
//  Probe the supplied object, and report if it looks like a zsock_t.
//  Takes a polymorphic socket reference.
bool QmlZsockAttached::is (void *self) {
    return zsock_is (self);
};

///
//  Probe the supplied reference. If it looks like a zsock_t instance, return
//  the underlying libzmq socket handle; else if it looks like a file
//  descriptor, return NULL; else if it looks like a libzmq socket handle,
//  return the supplied value. Takes a polymorphic socket reference.
void *QmlZsockAttached::resolve (void *self) {
    return zsock_resolve (self);
};

///
//  Self test of this class.
void QmlZsockAttached::test (bool verbose) {
    zsock_test (verbose);
};

///
//  Create a new socket. Returns the new socket, or NULL if the new socket
//  could not be created. Note that the symbol zsock_new (and other
//  constructors/destructors for zsock) are redirected to the *_checked
//  variant, enabling intelligent socket leak detection. This can have
//  performance implications if you use a LOT of sockets. To turn off this
//  redirection behaviour, define ZSOCK_NOCHECK.
QmlZsock *QmlZsockAttached::construct (int type) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new (type);
    return qmlSelf;
};

///
//  Create a PUB socket. Default action is bind.
QmlZsock *QmlZsockAttached::constructPub (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_pub (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a SUB socket, and optionally subscribe to some prefix string. Default
//  action is connect.
QmlZsock *QmlZsockAttached::constructSub (const QString &endpoint, const QString &subscribe) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_sub (endpoint.toUtf8().data(), subscribe.toUtf8().data());
    return qmlSelf;
};

///
//  Create a REQ socket. Default action is connect.
QmlZsock *QmlZsockAttached::constructReq (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_req (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a REP socket. Default action is bind.
QmlZsock *QmlZsockAttached::constructRep (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_rep (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a DEALER socket. Default action is connect.
QmlZsock *QmlZsockAttached::constructDealer (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_dealer (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a ROUTER socket. Default action is bind.
QmlZsock *QmlZsockAttached::constructRouter (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_router (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a PUSH socket. Default action is connect.
QmlZsock *QmlZsockAttached::constructPush (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_push (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a PULL socket. Default action is bind.
QmlZsock *QmlZsockAttached::constructPull (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_pull (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create an XPUB socket. Default action is bind.
QmlZsock *QmlZsockAttached::constructXpub (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_xpub (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create an XSUB socket. Default action is connect.
QmlZsock *QmlZsockAttached::constructXsub (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_xsub (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a PAIR socket. Default action is connect.
QmlZsock *QmlZsockAttached::constructPair (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_pair (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a STREAM socket. Default action is connect.
QmlZsock *QmlZsockAttached::constructStream (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_stream (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a SERVER socket. Default action is bind.
QmlZsock *QmlZsockAttached::constructServer (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_server (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a CLIENT socket. Default action is connect.
QmlZsock *QmlZsockAttached::constructClient (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_client (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a RADIO socket. Default action is bind.
QmlZsock *QmlZsockAttached::constructRadio (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_radio (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a DISH socket. Default action is connect.
QmlZsock *QmlZsockAttached::constructDish (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_dish (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a GATHER socket. Default action is bind.
QmlZsock *QmlZsockAttached::constructGather (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_gather (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a SCATTER socket. Default action is connect.
QmlZsock *QmlZsockAttached::constructScatter (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_scatter (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Create a DGRAM (UDP) socket. Default action is bind.
//  The endpoint is a string consisting of a
//  'transport'`://` followed by an 'address'. As this is
//  a UDP socket the 'transport' has to be 'udp'. The
//  'address' specifies the ip address and port to
//  bind to. For example:  udp://127.0.0.1:1234
//  Note: To send to an endpoint over UDP you have to
//  send a message with the destination endpoint address
//  as a first message!
QmlZsock *QmlZsockAttached::constructDgram (const QString &endpoint) {
    QmlZsock *qmlSelf = new QmlZsock ();
    qmlSelf->self = zsock_new_dgram (endpoint.toUtf8().data());
    return qmlSelf;
};

///
//  Destroy the socket. You must use this for any socket created via the
//  zsock_new method.
void QmlZsockAttached::destruct (QmlZsock *qmlSelf) {
    zsock_destroy (&qmlSelf->self);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
