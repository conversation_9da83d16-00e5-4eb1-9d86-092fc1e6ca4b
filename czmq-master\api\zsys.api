<class name = "zsys" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->

    <callback_type name = "handler_fn">
        Callback for interrupt signal handler
        <argument name = "signal_value" type = "integer" />
    </callback_type>

    <method name = "init" singleton = "1">
        Initialize CZMQ zsys layer; this happens automatically when you create
        a socket or an actor; however this call lets you force initialization
        earlier, so e.g. logging is properly set-up before you start working.
        Not threadsafe, so call only from main thread. Safe to call multiple
        times. Returns global CZMQ context.
        <return type = "anything" />
    </method>

    <method name = "shutdown" singleton = "1">
        Optionally shut down the CZMQ zsys layer; this normally happens automatically
        when the process exits; however this call lets you force a shutdown
        earlier, avoiding any potential problems with atexit() ordering, especially
        with Windows dlls.
    </method>

    <method name = "socket" singleton = "1">
        Get a new ZMQ socket, automagically creating a ZMQ context if this is
        the first time. Caller is responsible for destroying the ZMQ socket
        before process exits, to avoid a ZMQ deadlock. Note: you should not use
        this method in CZMQ apps, use zsock_new() instead.
        *** This is for CZMQ internal use only and may change arbitrarily ***
        <argument name = "type" type = "integer" />
        <argument name = "filename" type = "string" />
        <argument name = "line_nbr" type = "size" />
        <return type = "anything" />
    </method>

    <method name = "close" singleton = "1">
        Destroy/close a ZMQ socket. You should call this for every socket you
        create using zsys_socket().
        *** This is for CZMQ internal use only and may change arbitrarily ***
        <argument name = "handle" type = "anything" />
        <argument name = "filename" type = "string" />
        <argument name = "line_nbr" type = "size" />
        <return type = "integer" />
    </method>

    <method name = "sockname" singleton = "1">
        Return ZMQ socket name for socket type
        *** This is for CZMQ internal use only and may change arbitrarily ***
        <argument name = "socktype" type = "integer" />
        <return type = "string" mutable = "1" />
    </method>

    <method name = "create pipe" singleton = "1">
        Create a pipe, which consists of two PAIR sockets connected over inproc.
        The pipe is configured to use the zsys_pipehwm setting. Returns the
        frontend socket successful, NULL if failed.
        <argument name = "backend_p" type = "zsock" by_reference = "1" />
        <return type = "zsock" />
    </method>

    <method name = "handler set" singleton = "1">
        Set interrupt handler; this saves the default handlers so that a
        zsys_handler_reset () can restore them. If you call this multiple times
        then the last handler will take affect. If handler_fn is NULL, disables
        default SIGINT/SIGTERM handling in CZMQ.
        <argument name = "handler_fn" type = "zsys_handler_fn" callback = "1" by_reference = "1" />
    </method>

    <method name = "handler reset" singleton = "1">
        Reset interrupt handler, call this at exit if needed
    </method>

    <method name = "catch interrupts" singleton = "1">
        Set default interrupt handler, so Ctrl-C or SIGTERM will set
        zsys_interrupted. Idempotent; safe to call multiple times.
        Can be suppressed by ZSYS_SIGHANDLER=false
        *** This is for CZMQ internal use only and may change arbitrarily ***
    </method>

    <method name = "is interrupted" singleton = "1" state = "draft">
        Check if default interrupt handler of Ctrl-C or SIGTERM was called.
        Does not work if ZSYS_SIGHANDLER is false and code does not call
        set interrupted on signal.
        <return type = "boolean" />
    </method>

    <method name = "set interrupted" singleton = "1" state = "draft">
        Set interrupted flag. This is done by default signal handler, however
        this can be handy for language bindings or cases without default
        signal handler.
    </method>

    <method name = "file exists" singleton = "1">
        Return 1 if file exists, else zero
        <argument name = "filename" type = "string" />
        <return type = "boolean" />
    </method>

    <method name = "file modified" singleton = "1">
        Return file modification time. Returns 0 if the file does not exist.
        <argument name = "filename" type = "string" />
        <return type = "time" />
    </method>

    <method name = "file mode" singleton = "1">
        Return file mode; provides at least support for the POSIX S_ISREG(m)
        and S_ISDIR(m) macros and the S_IRUSR and S_IWUSR bits, on all boxes.
        Returns a mode_t cast to int, or -1 in case of error.
        <argument name = "filename" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "file delete" singleton = "1">
        Delete file. Does not complain if the file is absent
        <argument name = "filename" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "file stable" singleton = "1">
        Check if file is 'stable'
        <argument name = "filename" type = "string" />
        <return type = "boolean" />
    </method>

    <method name = "dir create" singleton = "1" polymorphic = "1">
        Create a file path if it doesn't exist. The file path is treated as
        printf format.
        <argument name = "pathname" type = "string" variadic = "1" />
        <return type = "integer" />
    </method>

    <method name = "dir delete" singleton = "1" polymorphic = "1">
        Remove a file path if empty; the pathname is treated as printf format.
        <argument name = "pathname" type = "string" variadic = "1" />
        <return type = "integer" />
    </method>

    <method name = "dir change" singleton = "1">
        Move to a specified working directory. Returns 0 if OK, -1 if this failed.
        <argument name = "pathname" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "file mode private" singleton = "1">
        Set private file creation mode; all files created from here will be
        readable/writable by the owner only.
    </method>

    <method name = "file mode default" singleton = "1">
        Reset default file creation mode; all files created from here will use
        process file mode defaults.
    </method>

    <method name = "version" singleton = "1">
        Return the CZMQ version for run-time API detection; returns version
        number into provided fields, providing reference isn't null in each case.
        <argument name = "major" type = "integer" by_reference = "1" />
        <argument name = "minor" type = "integer" by_reference = "1" />
        <argument name = "patch" type = "integer" by_reference = "1" />
    </method>

    <method name = "sprintf hint" singleton = "1" polymorphic = "1" state = "draft">
        Format a string using printf formatting, returning a freshly allocated
        buffer. If there was insufficient memory, returns NULL. Free the returned
        string using zstr_free(). The hinted version allows one to optimize by using
        a larger starting buffer size (known to/assumed by the developer) and so
        avoid reallocations.

        <argument name = "hint" type = "integer" />
        <argument name = "format" type = "string" variadic = "1" />
        <return type = "string" mutable = "1" />
    </method>

    <method name = "sprintf" singleton = "1" polymorphic = "1">
        Format a string using printf formatting, returning a freshly allocated
        buffer. If there was insufficient memory, returns NULL. Free the returned
        string using zstr_free().
        <argument name = "format" type = "string" variadic = "1" />
        <return type = "string" mutable = "1" />
    </method>

    <method name = "vprintf" singleton = "1" polymorphic = "1">
        Format a string with a va_list argument, returning a freshly allocated
        buffer. If there was insufficient memory, returns NULL. Free the returned
        string using zstr_free().
        <argument name = "format" type = "string"/>
        <argument name = "argptr" type = "va_list" />
        <return type = "string" mutable = "1" />
    </method>

    <method name = "udp new" singleton = "1">
        Create UDP beacon socket; if the routable option is true, uses
        multicast (not yet implemented), else uses broadcast. This method
        and related ones might _eventually_ be moved to a zudp class.
        *** This is for CZMQ internal use only and may change arbitrarily ***
        <argument name = "routable" type = "boolean" />
        <return type = "socket" />
    </method>

    <method name = "udp close" singleton = "1">
        Close a UDP socket
        *** This is for CZMQ internal use only and may change arbitrarily ***
        <argument name = "handle" type = "socket" />
        <return type = "integer" />
    </method>

    <method name = "udp send" singleton = "1">
        Send zframe to UDP socket, return -1 if sending failed due to
        interface having disappeared (happens easily with WiFi)
        *** This is for CZMQ internal use only and may change arbitrarily ***
        <argument name = "udpsock" type = "socket" />
        <argument name = "frame" type = "zframe" />
        <argument name = "address" type = "anything" c_type = "inaddr_t *" />
        <argument name = "addrlen" type = "integer" />
        <return type = "integer" />
    </method>

    <method name = "udp recv" singleton = "1">
        Receive zframe from UDP socket, and set address of peer that sent it
        The peername must be a char [INET_ADDRSTRLEN] array if IPv6 is disabled or
        NI_MAXHOST if it's enabled. Returns NULL when failing to get peer address.
        *** This is for CZMQ internal use only and may change arbitrarily ***
        <argument name = "udpsock" type = "socket" />
        <argument name = "peername" type = "string" mutable = "1" />
        <argument name = "peerlen" type = "integer" />
        <return type = "zframe" />
    </method>

    <method name = "socket error" singleton = "1">
        Handle an I/O error on some socket operation; will report and die on
        fatal errors, and continue silently on "try again" errors.
        *** This is for CZMQ internal use only and may change arbitrarily ***
        <argument name = "reason" type = "string" />
    </method>

    <method name = "hostname" singleton = "1">
        Return current host name, for use in public tcp:// endpoints. Caller gets
        a freshly allocated string, should free it using zstr_free(). If the host
        name is not resolvable, returns NULL.
        <return type = "string" mutable = "1" />
    </method>

    <method name = "daemonize" singleton = "1">
        Move the current process into the background. The precise effect depends
        on the operating system. On POSIX boxes, moves to a specified working
        directory (if specified), closes all file handles, reopens stdin, stdout,
        and stderr to the null device, and sets the process to ignore SIGHUP. On
        Windows, does nothing. Returns 0 if OK, -1 if there was an error.
        <argument name = "workdir" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "run as" singleton = "1">
        Drop the process ID into the lockfile, with exclusive lock, and switch
        the process to the specified group and/or user. Any of the arguments
        may be null, indicating a no-op. Returns 0 on success, -1 on failure.
        Note if you combine this with zsys_daemonize, run after, not before
        that method, or the lockfile will hold the wrong process ID.
        <argument name = "lockfile" type = "string" />
        <argument name = "group" type = "string" />
        <argument name = "user" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "has curve" singleton = "1">
        Returns true if the underlying libzmq supports CURVE security.
        Uses a heuristic probe according to the version of libzmq being used.
        <return type = "boolean" />
    </method>

    <method name = "set io threads" singleton = "1">
        Configure the number of I/O threads that ZeroMQ will use. A good
        rule of thumb is one thread per gigabit of traffic in or out. The
        default is 1, sufficient for most applications. If the environment
        variable ZSYS_IO_THREADS is defined, that provides the default.
        Note that this method is valid only before any socket is created.
        <argument name = "io_threads" type = "size" />
    </method>

    <method name = "set thread sched policy" singleton = "1">
        Configure the scheduling policy of the ZMQ context thread pool.
        Not available on Windows. See the sched_setscheduler man page or sched.h
        for more information. If the environment variable ZSYS_THREAD_SCHED_POLICY
        is defined, that provides the default.
        Note that this method is valid only before any socket is created.
        <argument name = "policy" type = "integer" />
    </method>

    <method name = "set thread priority" singleton = "1">
        Configure the scheduling priority of the ZMQ context thread pool.
        Not available on Windows. See the sched_setscheduler man page or sched.h
        for more information. If the environment variable ZSYS_THREAD_PRIORITY is
        defined, that provides the default.
        Note that this method is valid only before any socket is created.
        <argument name = "priority" type = "integer" />
    </method>

    <method name = "set thread name prefix" singleton = "1">
        Configure the numeric prefix to each thread created for the internal
        context's thread pool. This option is only supported on Linux.
        If the environment variable ZSYS_THREAD_NAME_PREFIX is defined, that
        provides the default.
        Note that this method is valid only before any socket is created.
        <argument name = "prefix" type = "integer" />
    </method>

    <method name = "thread name prefix" singleton = "1">
        Return thread name prefix.
        <return type = "integer" />
    </method>

    <method name = "set thread name prefix str" singleton = "1" state = "draft">
        Configure the numeric prefix to each thread created for the internal
        context's thread pool. This option is only supported on Linux.
        If the environment variable ZSYS_THREAD_NAME_PREFIX_STR is defined, that
        provides the default.
        Note that this method is valid only before any socket is created.
        <argument name = "prefix" type = "string" />
    </method>

    <method name = "thread name prefix str" singleton = "1" state = "draft">
        Return thread name prefix.
        <return type = "string" />
    </method>

    <method name = "thread affinity cpu add" singleton = "1">
        Adds a specific CPU to the affinity list of the ZMQ context thread pool.
        This option is only supported on Linux.
        Note that this method is valid only before any socket is created.
        <argument name = "cpu" type = "integer" />
    </method>

    <method name = "thread affinity cpu remove" singleton = "1">
        Removes a specific CPU to the affinity list of the ZMQ context thread pool.
        This option is only supported on Linux.
        Note that this method is valid only before any socket is created.
        <argument name = "cpu" type = "integer" />
    </method>

    <method name = "set max sockets" singleton = "1">
        Configure the number of sockets that ZeroMQ will allow. The default
        is 1024. The actual limit depends on the system, and you can query it
        by using zsys_socket_limit (). A value of zero means "maximum".
        Note that this method is valid only before any socket is created.
        <argument name = "max sockets" type = "size" />
    </method>

    <method name = "socket limit" singleton = "1">
        Return maximum number of ZeroMQ sockets that the system will support.
        <return type = "size" />
    </method>

    <method name = "set max msgsz" singleton = "1">
        Configure the maximum allowed size of a message sent.
        The default is INT_MAX.
        <argument name = "max msgsz" type = "integer" />
    </method>

    <method name = "max msgsz" singleton = "1">
        Return maximum message size.
        <return type = "integer" />
    </method>

    <method name = "set zero copy recv" singleton = "1" state = "draft" >
        Configure whether to use zero copy strategy in libzmq. If the environment
        variable ZSYS_ZERO_COPY_RECV is defined, that provides the default.
        Otherwise the default is 1.
        <argument name = "zero copy" type = "integer" />
    </method>

    <method name = "zero copy recv" singleton = "1" state = "draft" >
        Return ZMQ_ZERO_COPY_RECV option.
        <return type = "integer" />
    </method>

    <method name = "set file stable age msec" singleton = "1" state = "draft">
        Configure the threshold value of filesystem object age per st_mtime
        that should elapse until we consider that object "stable" at the
        current zclock_time() moment.
        The default is S_DEFAULT_ZSYS_FILE_STABLE_AGE_MSEC defined in zsys.c
        which generally depends on host OS, with fallback value of 5000.
        <argument name = "file stable age msec" type = "msecs" />
    </method>

    <method name = "file stable age msec" singleton = "1" state = "draft">
        Return current threshold value of file stable age in msec.
        This can be used in code that chooses to wait for this timeout
        before testing if a filesystem object is "stable" or not.
        <return type = "msecs" />
    </method>

    <method name = "set linger" singleton = "1">
        Configure the default linger timeout in msecs for new zsock instances.
        You can also set this separately on each zsock_t instance. The default
        linger time is zero, i.e. any pending messages will be dropped. If the
        environment variable ZSYS_LINGER is defined, that provides the default.
        Note that process exit will typically be delayed by the linger time.
        <argument name = "linger" type = "size" />
    </method>

    <method name = "set sndhwm" singleton = "1">
        Configure the default outgoing pipe limit (HWM) for new zsock instances.
        You can also set this separately on each zsock_t instance. The default
        HWM is 1,000, on all versions of ZeroMQ. If the environment variable
        ZSYS_SNDHWM is defined, that provides the default. Note that a value of
        zero means no limit, i.e. infinite memory consumption.
        <argument name = "sndhwm" type = "size" />
    </method>

    <method name = "set rcvhwm" singleton = "1">
        Configure the default incoming pipe limit (HWM) for new zsock instances.
        You can also set this separately on each zsock_t instance. The default
        HWM is 1,000, on all versions of ZeroMQ. If the environment variable
        ZSYS_RCVHWM is defined, that provides the default. Note that a value of
        zero means no limit, i.e. infinite memory consumption.
        <argument name = "rcvhwm" type = "size" />
    </method>

    <method name = "set pipehwm" singleton = "1">
        Configure the default HWM for zactor internal pipes; this is set on both
        ends of the pipe, for outgoing messages only (sndhwm). The default HWM is
        1,000, on all versions of ZeroMQ. If the environment var ZSYS_ACTORHWM is
        defined, that provides the default. Note that a value of zero means no
        limit, i.e. infinite memory consumption.
        <argument name = "pipehwm" type = "size" />
    </method>

    <method name = "pipehwm" singleton = "1">
        Return the HWM for zactor internal pipes.
        <return type = "size" />
    </method>

    <method name = "set ipv6" singleton = "1">
        Configure use of IPv6 for new zsock instances. By default sockets accept
        and make only IPv4 connections. When you enable IPv6, sockets will accept
        and connect to both IPv4 and IPv6 peers. You can override the setting on
        each zsock_t instance. The default is IPv4 only (ipv6 set to 0). If the
        environment variable ZSYS_IPV6 is defined (as 1 or 0), this provides the
        default. Note: has no effect on ZMQ v2.
        <argument name = "ipv6" type = "integer" />
    </method>

    <method name = "ipv6" singleton = "1">
        Return use of IPv6 for zsock instances.
        <return type = "integer" />
    </method>

    <method name = "ipv6 available" singleton = "1" state = "draft">
        Test if ipv6 is available on the system. Return true if available.
        The only way to reliably check is to actually open a socket and 
        try to bind it. (ported from libzmq)
        <return type = "boolean" />
    </method>

    <method name = "set interface" singleton = "1">
        Set network interface name to use for broadcasts, particularly zbeacon.
        This lets the interface be configured for test environments where required.
        For example, on Mac OS X, zbeacon cannot bind to *************** which is
        the default when there is no specified interface. If the environment
        variable ZSYS_INTERFACE is set, use that as the default interface name.
        Setting the interface to "*" means "use all available interfaces".
        <argument name = "value" type = "string" />
    </method>

    <method name = "interface" singleton = "1">
        Return network interface to use for broadcasts, or "" if none was set.
        <return type = "string" />
    </method>

    <method name = "set ipv6 address" singleton = "1">
        Set IPv6 address to use zbeacon socket, particularly for receiving zbeacon.
        This needs to be set IPv6 is enabled as IPv6 can have multiple addresses
        on a given interface. If the environment variable ZSYS_IPV6_ADDRESS is set,
        use that as the default IPv6 address.
        <argument name = "value" type = "string" />
    </method>

    <method name = "ipv6 address" singleton = "1">
        Return IPv6 address to use for zbeacon reception, or "" if none was set.
        <return type = "string" />
    </method>

    <method name = "set ipv6 mcast address" singleton = "1">
        Set IPv6 milticast address to use for sending zbeacon messages. This needs
        to be set if IPv6 is enabled. If the environment variable
        ZSYS_IPV6_MCAST_ADDRESS is set, use that as the default IPv6 multicast
        address.
        <argument name = "value" type = "string" />
    </method>

    <method name = "ipv6 mcast address" singleton = "1">
        Return IPv6 multicast address to use for sending zbeacon, or "" if none was
        set.
        <return type = "string" />
    </method>

    <method name = "set ipv4 mcast address" singleton = "1"  state = "draft">
        Set IPv4 multicast address to use for sending zbeacon messages. By default
        IPv4 multicast is NOT used. If the environment variable
        ZSYS_IPV4_MCAST_ADDRESS is set, use that as the default IPv4 multicast
        address. Calling this function or setting ZSYS_IPV4_MCAST_ADDRESS
        will enable IPv4 zbeacon messages.
        <argument name = "value" type = "string" />
    </method>

    <method name = "ipv4 mcast address" singleton = "1" state = "draft">
        Return IPv4 multicast address to use for sending zbeacon, or NULL if none was
        set.
        <return type = "string" />
    </method>

	<method name = "set mcast ttl" singleton = "1"  state = "draft">
        Set multicast TTL default is 1
        <argument name = "value" type = "byte" />
    </method>

	<method name = "mcast ttl" singleton = "1"  state = "draft">
        Get multicast TTL
        <return type = "byte" />
    </method>

    <method name = "set auto use fd" singleton = "1">
        Configure the automatic use of pre-allocated FDs when creating new sockets.
        If 0 (default), nothing will happen. Else, when a new socket is bound, the
        system API will be used to check if an existing pre-allocated FD with a
        matching port (if TCP) or path (if IPC) exists, and if it does it will be
        set via the ZMQ_USE_FD socket option so that the library will use it
        instead of creating a new socket.
        <argument name = "auto use fd" type = "integer" />
    </method>

    <method name = "auto use fd" singleton = "1">
        Return use of automatic pre-allocated FDs for zsock instances.
        <return type = "integer" />
    </method>

    <method name = "zprintf" singleton = "1" state = "draft">
        Print formatted string. Format is specified by variable names
        in Python-like format style

        "%(KEY)s=%(VALUE)s", KEY=key, VALUE=value
        become
        "key=value"

        Returns freshly allocated string or NULL in a case of error.
        Not enough memory, invalid format specifier, name not in args
        <argument name = "format" type = "string" />
        <argument name = "args" type = "zhash" />
        <return type = "string" fresh = "1" />
    </method>
    <method name = "zprintf_error" singleton = "1" state = "draft">
        Return error string for given format/args combination.

        <argument name = "format" type = "string" />
        <argument name = "args" type = "zhash" />
        <return type = "string" fresh = "1" />
    </method>

    <method name = "zplprintf" singleton = "1" state = "draft">
        Print formatted string. Format is specified by variable names
        in Python-like format style

        "%(KEY)s=%(VALUE)s", KEY=key, VALUE=value
        become
        "key=value"

        Returns freshly allocated string or NULL in a case of error.
        Not enough memory, invalid format specifier, name not in args
        <argument name = "format" type = "string" />
        <argument name = "args" type = "zconfig" />
        <return type = "string" fresh = "1" />
    </method>

    <method name = "zplprintf_error" singleton = "1" state = "draft">
        Return error string for given format/args combination.

        <argument name = "format" type = "string" />
        <argument name = "args" type = "zconfig" />
        <return type = "string" fresh = "1" />
    </method>

    <method name = "set logident" singleton = "1">
        Set log identity, which is a string that prefixes all log messages sent
        by this process. The log identity defaults to the environment variable
        ZSYS_LOGIDENT, if that is set.
        <argument name = "value" type = "string" />
    </method>

    <method name = "set logstream" singleton = "1">
        Set stream to receive log traffic. By default, log traffic is sent to
        stdout. If you set the stream to NULL, no stream will receive the log
        traffic (it may still be sent to the system facility).
        <argument name = "stream" type = "FILE" />
    </method>

    <method name = "set logsender" singleton = "1">
        Sends log output to a PUB socket bound to the specified endpoint. To
        collect such log output, create a SUB socket, subscribe to the traffic
        you care about, and connect to the endpoint. Log traffic is sent as a
        single string frame, in the same format as when sent to stdout. The
        log system supports a single sender; multiple calls to this method will
        bind the same sender to multiple endpoints. To disable the sender, call
        this method with a null argument.
        <argument name = "endpoint" type = "string" />
    </method>

    <method name = "set logsystem" singleton = "1">
        Enable or disable logging to the system facility (syslog on POSIX boxes,
        event log on Windows). By default this is disabled.
        <argument name = "logsystem" type = "boolean" />
    </method>

    <method name = "error" singleton = "1" polymorphic = "1">
        Log error condition - highest priority
        <argument name = "format" type = "string" variadic = "1" />
    </method>

    <method name = "warning" singleton = "1" polymorphic = "1">
        Log warning condition - high priority
        <argument name = "format" type = "string" variadic = "1" />
    </method>

    <method name = "notice" singleton = "1" polymorphic = "1">
        Log normal, but significant, condition - normal priority
        <argument name = "format" type = "string" variadic = "1" />
    </method>

    <method name = "info" singleton = "1" polymorphic = "1">
        Log informational message - low priority
        <argument name = "format" type = "string" variadic = "1" />
    </method>

    <method name = "debug" singleton = "1" polymorphic = "1">
        Log debug-level message - lowest priority
        <argument name = "format" type = "string" variadic = "1" />
    </method>
</class>
