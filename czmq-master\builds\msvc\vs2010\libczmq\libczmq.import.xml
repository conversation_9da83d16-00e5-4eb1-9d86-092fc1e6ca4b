<?xml version="1.0" encoding="utf-8"?>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
<ProjectSchemaDefinitions xmlns="clr-namespace:Microsoft.Build.Framework.XamlTypes;assembly=Microsoft.Build.Framework">
  <Rule Name="czmq-linkage-uiextension" PageTemplate="tool" DisplayName="Local Dependencies" SwitchPrefix="/" Order="1">
    <Rule.Categories>
      <Category Name="libczmq" DisplayName="CZMQ" />
    </Rule.Categories>
    <Rule.DataSource>
      <DataSource Persistence="ProjectFile" ItemType="" />
    </Rule.DataSource>
    <EnumProperty Name="Linkage-czmq" DisplayName="Linkage" Description="How CZMQ will be linked into the output of this project" Category="czmq">
      <EnumValue Name="" DisplayName="Not linked" />
      <EnumValue Name="dynamic" DisplayName="Dynamic (DLL)" />
      <EnumValue Name="static" DisplayName="Static (LIB)" />
      <EnumValue Name="ltcg" DisplayName="Static using link time compile generation (LTCG)" />
    </EnumProperty>
  </Rule>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
</ProjectSchemaDefinitions>
