<class name = "zhttp_client" state = "draft">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    Http client, allowing multiple requests simultaneously and integrate easily with zpoller.
    Use zhttp_request class to create and send the request.
    Use zhttp_response class to receive the response.

    <constructor>
        Create a new http client
	<argument name = "verbose" type = "boolean" />
    </constructor>

    <destructor>
        Destroy an http client
    </destructor>
</class>
