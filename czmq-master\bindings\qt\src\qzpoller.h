/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#ifndef Q_ZPOLLER_H
#define Q_ZPOLLER_H

#include "qczmq.h"

class QT_CZMQ_EXPORT QZpoller : public QObject
{
    Q_OBJECT
public:

    //  Copy-construct to return the proper wrapped c types
    QZpoller (zpoller_t *self, QObject *qObjParent = 0);

    //  Destroy a poller
    ~QZpoller ();

    //  Add a reader to be polled. Returns 0 if OK, -1 on failure. The reader may
    //  be a libzmq void * socket, a zsock_t instance, a zactor_t instance or a
    //  file handle.
    int add (void *reader);

    //  Remove a reader from the poller; returns 0 if OK, -1 on failure. The reader
    //  must have been passed during construction, or in an zpoller_add () call.
    int remove (void *reader);

    //  By default the poller stops if the process receives a SIGINT or SIGTERM
    //  signal. This makes it impossible to shut-down message based architectures
    //  like zactors. This method lets you switch off break handling. The default
    //  nonstop setting is off (false).
    void setNonstop (bool nonstop);

    //  Poll the registered readers for I/O, return first reader that has input.
    //  The reader will be a libzmq void * socket, a zsock_t, a zactor_t
    //  instance or a file handle as specified in zpoller_new/zpoller_add. The
    //  timeout should be zero or greater, or -1 to wait indefinitely. Socket
    //  priority is defined by their order in the poll list. If you need a
    //  balanced poll, use the low level zmq_poll method directly. If the poll
    //  call was interrupted (SIGINT), or the ZMQ context was destroyed, or the
    //  timeout expired, returns NULL. You can test the actual exit condition by
    //  calling zpoller_expired () and zpoller_terminated (). The timeout is in
    //  msec.
    void * wait (int timeout);

    //  Return true if the last zpoller_wait () call ended because the timeout
    //  expired, without any error.
    bool expired ();

    //  Return true if the last zpoller_wait () call ended because the process
    //  was interrupted, or the parent context was destroyed.
    bool terminated ();

    //  Self test of this class.
    static void test (bool verbose);

    zpoller_t *self;
};
#endif //  Q_ZPOLLER_H
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
