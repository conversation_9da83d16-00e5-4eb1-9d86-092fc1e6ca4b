/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#include <stdio.h>
#include <stdlib.h>
#include <jni.h>
#include "czmq.h"
#include "org_zeromq_czmq_Ztimerset.h"

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Ztimerset__1_1new (JNIEnv *env, jclass c)
{
    //  Disable CZMQ signal handling; allow Java to deal with it
    zsys_handler_set (NULL);
    jlong new_ = (jlong) (intptr_t) ztimerset_new ();
    return new_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Ztimerset__1_1destroy (JNIEnv *env, jclass c, jlong self)
{
    ztimerset_destroy ((ztimerset_t **) &self);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Ztimerset__1_1cancel (JNIEnv *env, jclass c, jlong self, jint timer_id)
{
    jint cancel_ = (jint) ztimerset_cancel ((ztimerset_t *) (intptr_t) self, (int) timer_id);
    return cancel_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Ztimerset__1_1setInterval (JNIEnv *env, jclass c, jlong self, jint timer_id, jlong interval)
{
    jint set_interval_ = (jint) ztimerset_set_interval ((ztimerset_t *) (intptr_t) self, (int) timer_id, (size_t) interval);
    return set_interval_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Ztimerset__1_1reset (JNIEnv *env, jclass c, jlong self, jint timer_id)
{
    jint reset_ = (jint) ztimerset_reset ((ztimerset_t *) (intptr_t) self, (int) timer_id);
    return reset_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Ztimerset__1_1timeout (JNIEnv *env, jclass c, jlong self)
{
    jint timeout_ = (jint) ztimerset_timeout ((ztimerset_t *) (intptr_t) self);
    return timeout_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Ztimerset__1_1execute (JNIEnv *env, jclass c, jlong self)
{
    jint execute_ = (jint) ztimerset_execute ((ztimerset_t *) (intptr_t) self);
    return execute_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Ztimerset__1_1test (JNIEnv *env, jclass c, jboolean verbose)
{
    ztimerset_test ((bool) verbose);
}

