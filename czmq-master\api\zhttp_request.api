<class name = "zhttp_request" state = "draft">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    Http request that can be received from zhttp_server or sent to zhttp_client.
    Class can be reused between send & recv calls.
    Headers and Content is being destroyed after every send call.

    <constructor>
        Create a new http request.
    </constructor>

    <destructor>
        Destroy an http request.
    </destructor>

    <method name = "recv">
        Receive a new request from zhttp_server.
        Return the underlying connection if successful, to be used when calling zhttp_response_send.

        <argument name = "sock" type = "zsock" />
        <return type = "anything" />
    </method>

    <method name = "send">
        Send a request to zhttp_client.
        Url and the request path will be concatenated.
        This behavior is useful for url rewrite and reverse proxy.

        Send also allow two user provided arguments which will be returned with the response.
        The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an arg.
        <argument name = "client" type = "zhttp_client" />
        <argument name = "timeout" type = "integer" />
        <argument name = "arg" type = "anything" />
        <argument name = "arg2" type = "anything" />

        <return type = "integer" />
    </method>

    <method name = "method">
        Get the request method
        <return type = "string" />
    </method>

    <method name = "set method">
        Set the request method
        <argument name = "method" type = "string" />
    </method>

    <method name = "url">
        Get the request url.
        When receiving a request from http server this is only the path part of the url.
        <return type = "string" />
    </method>

    <method name = "set url">
        Set the request url
        When sending a request to http client this should be full url.
        <argument name = "url" type = "string" />
    </method>

    <method name = "content type">
        Get the request content type
        <return type = "string" />
    </method>

    <method name = "set content type">
        Set the request content type
        <argument name = "content type" type = "string" />
    </method>

    <method name = "content length">
        Get the content length of the request
        <return type = "size" />
    </method>

    <method name = "headers">
        Get the headers of the request
        <return type = "zhash" />
    </method>

    <method name = "content">
        Get the content of the request.
        <return type = "string" />
    </method>

    <method name = "get content">
        Get the content of the request.
        <return type = "string" fresh = "1" />
    </method>

    <method name = "set content">
        Set the content of the request.
        Content must by dynamically allocated string.
        Takes ownership of the content.
        <argument name = "content" type = "string" by_reference = "1" />
    </method>

    <method name = "set content const">
        Set the content of the request..
        The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
        <argument name = "content" type = "string" />
    </method>

    <method name = "reset content">
        Set the content to NULL
    </method>

    <method name = "set username">
        Set the request username
        <argument name = "username" type = "string" />
    </method>

    <method name = "set password">
        Set the request password
        <argument name = "password" type = "string" />
    </method>

    <method name = "match">
        Match the path of the request.
        Support wildcards with '%s' symbol inside the match string.
        Matching wildcards until the next '/', '?' or '\0'.
        On successful match the variadic arguments will be filled with the matching strings.
        On successful match the method is modifying the url field and break it into substrings.
        If you need to use the url, do it before matching or take a copy.

        User must not free the variadic arguments as they are part of the url.

        To use the percent symbol, just double it, e.g "%%something".

        Example:
        if (zhttp_request_match (request, "POST", "/send/%s/%s", &name, &id))

        <argument name = "method" type = "string" />
        <argument name = "path" type = "string" variadic = "1" />

        <return type = "boolean" />
    </method>
</class>
