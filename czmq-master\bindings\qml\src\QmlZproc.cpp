/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZproc.h"


///
//  Return command line arguments (the first item is the executable) or
//  NULL if not set.
QmlZlist *QmlZproc::args () {
    QmlZlist *retQ_ = new QmlZlist ();
    retQ_->self = zproc_args (self);
    return retQ_;
};

///
//  Setup the command line arguments, the first item must be an (absolute) filename
//  to run.
void QmlZproc::setArgs (QmlZlist *arguments) {
    zproc_set_args (self, &arguments->self);
};

///
//  Setup the command line arguments, the first item must be an (absolute) filename
//  to run. Variadic function, must be NULL terminated.
void QmlZproc::setArgsx (const QString &arguments) {
    zproc_set_argsx (self, arguments.toUtf8().data());
};

///
//  Setup the environment variables for the process.
void QmlZproc::setEnv (QmlZhash *arguments) {
    zproc_set_env (self, &arguments->self);
};

///
//  Connects process stdin with a readable ('>', connect) zeromq socket. If
//  socket argument is NULL, zproc creates own managed pair of inproc
//  sockets.  The writable one is then accessible via zproc_stdin method.
void QmlZproc::setStdin (void *socket) {
    zproc_set_stdin (self, socket);
};

///
//  Connects process stdout with a writable ('@', bind) zeromq socket. If
//  socket argument is NULL, zproc creates own managed pair of inproc
//  sockets.  The readable one is then accessible via zproc_stdout method.
void QmlZproc::setStdout (void *socket) {
    zproc_set_stdout (self, socket);
};

///
//  Connects process stderr with a writable ('@', bind) zeromq socket. If
//  socket argument is NULL, zproc creates own managed pair of inproc
//  sockets.  The readable one is then accessible via zproc_stderr method.
void QmlZproc::setStderr (void *socket) {
    zproc_set_stderr (self, socket);
};

///
//  Return subprocess stdin writable socket. NULL for
//  not initialized or external sockets.
void *QmlZproc::stdin () {
    return zproc_stdin (self);
};

///
//  Return subprocess stdout readable socket. NULL for
//  not initialized or external sockets.
void *QmlZproc::stdout () {
    return zproc_stdout (self);
};

///
//  Return subprocess stderr readable socket. NULL for
//  not initialized or external sockets.
void *QmlZproc::stderr () {
    return zproc_stderr (self);
};

///
//  Starts the process, return just before execve/CreateProcess.
int QmlZproc::run () {
    return zproc_run (self);
};

///
//  process exit code
int QmlZproc::returncode () {
    return zproc_returncode (self);
};

///
//  PID of the process
int QmlZproc::pid () {
    return zproc_pid (self);
};

///
//  return true if process is running, false if not yet started or finished
bool QmlZproc::running () {
    return zproc_running (self);
};

///
//  The timeout should be zero or greater, or -1 to wait indefinitely.
//  wait or poll process status, return return code
int QmlZproc::wait (int timeout) {
    return zproc_wait (self, timeout);
};

///
//  send SIGTERM signal to the subprocess, wait for grace period and
//  eventually send SIGKILL
void QmlZproc::shutdown (int timeout) {
    zproc_shutdown (self, timeout);
};

///
//  return internal actor, useful for the polling if process died
void *QmlZproc::actor () {
    return zproc_actor (self);
};

///
//  send a signal to the subprocess
void QmlZproc::kill (int signal) {
    zproc_kill (self, signal);
};

///
//  set verbose mode
void QmlZproc::setVerbose (bool verbose) {
    zproc_set_verbose (self, verbose);
};


QObject* QmlZproc::qmlAttachedProperties(QObject* object) {
    return new QmlZprocAttached(object);
}


///
//  Self test of this class.
void QmlZprocAttached::test (bool verbose) {
    zproc_test (verbose);
};

///
//  Create a new zproc.
//  NOTE: On Windows and with libzmq3 and libzmq2 this function
//  returns NULL. Code needs to be ported there.
QmlZproc *QmlZprocAttached::construct () {
    QmlZproc *qmlSelf = new QmlZproc ();
    qmlSelf->self = zproc_new ();
    return qmlSelf;
};

///
//  Destroy zproc, wait until process ends.
void QmlZprocAttached::destruct (QmlZproc *qmlSelf) {
    zproc_destroy (&qmlSelf->self);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
