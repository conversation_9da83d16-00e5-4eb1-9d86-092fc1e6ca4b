<class name = "zargs" state = "draft">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    Platform independent command line argument parsing helpers

    There are two kind of elements provided by this class
    Named parameters, accessed by param_get and param_has methods
      * --named-parameter
      * --parameter with_value
      * -a val
    Positional arguments, accessed by zargs_first, zargs_next

    It DOES:
    * provide easy to use CLASS compatible API for accessing argv
    * is platform independent
    * provide getopt_long style -- argument, which delimits parameters from arguments
    * makes parameters position independent

    It does NOT
    * change argv
    * provide a "declarative" way to define command line interface

    In future it SHALL
    * hide several formats of command line to one (-Idir, --include=dir,
      --include dir are the same from API pov)
    <constructor>
        Create a new zargs from command line arguments.

        <argument name = "argc" type = "integer"/>
        <argument name = "argv" type = "string" by_reference = "1"/>
    </constructor>

    <destructor>
        Destroy zargs instance.
    </destructor>

    <method name = "progname" >
	Return program name (argv[0])
	<return type = "string" />
    </method>

    <method name = "arguments" >
	Return number of positional arguments
        <return type = "size" />
    </method>

    <method name = "first" >
	Return first positional argument or NULL
	<return type = "string" />
    </method>

    <method name = "next" >
	Return next positional argument or NULL
	<return type = "string" />
    </method>

    <method name = "param first" >
	Return first named parameter value, or NULL if there are no named
        parameters, or value for which zargs_param_empty (arg) returns true.
        <return type = "string" />
    </method>

    <method name = "param next" >
	Return next named parameter value, or NULL if there are no named
        parameters, or value for which zargs_param_empty (arg) returns true.
        <return type = "string" />
    </method>

    <method name = "param name" >
	Return current parameter name, or NULL if there are no named parameters.
        <return type = "string" />
    </method>

    <method name = "get">
	Return value of named parameter or NULL is it has no value (or was not specified)
        <argument name = "name" type = "string" />
        <return type="string" />
    </method>

    <method name = "getx">
	Return value of one of parameter(s) or NULL is it has no value (or was not specified)
        <argument name = "name" type = "string" variadic = "1" />
        <return type="string" />
    </method>

    <method name = "has">
        Returns true if named parameter was specified on command line
	<argument name ="name" type = "string" />
	<return type="boolean" />
    </method>

    <method name = "hasx">
        Returns true if named parameter(s) was specified on command line
	<argument name ="name" type = "string" variadic = "1" />
	<return type="boolean" />
    </method>

    <method name = "print" >
        Print an instance of zargs.
    </method>
</class>
