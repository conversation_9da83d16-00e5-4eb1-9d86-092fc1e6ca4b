# Github Actions CI script
# This is a skeleton created by zproject.
# You can add hand-written code here.

name: CI
on:
  push:
  pull_request:


jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-20.04
            BUILD_TYPE: default
            DRAFT: enabled
            CLANG_FORMAT: clang-format-11
            PACKAGES: automake autoconf clang-format-11 git uuid-dev libsystemd-dev liblz4-dev libnss3-dev libzmq3-dev libcurl4-nss-dev libmicrohttpd-dev asciidoc xmlto
          - os: ubuntu-20.04
            BUILD_TYPE: default
            DRAFT: disabled
            CLANG_FORMAT: clang-format-11
            PACKAGES: automake autoconf clang-format-11 git uuid-dev libsystemd-dev liblz4-dev libnss3-dev libzmq3-dev libcurl4-nss-dev libmicrohttpd-dev
          - os: ubuntu-20.04
            BUILD_TYPE: valgrind
            DRAFT: enabled
            PACKAGES: automake autoconf valgrind git uuid-dev libsystemd-dev liblz4-dev libnss3-dev libzmq3-dev libcurl4-nss-dev libmicrohttpd-dev
          - os: ubuntu-20.04
            BUILD_TYPE: cmake
            DRAFT: enabled
            PACKAGES: cmake git uuid-dev libsystemd-dev liblz4-dev libnss3-dev libzmq3-dev libcurl4-nss-dev libmicrohttpd-dev
          - os: macos-latest
            BUILD_TYPE: default
            PACKAGES: automake autoconf libtool  ossp-uuid zeromq curl libmicrohttpd
            DRAFT: enabled
          - os: macos-latest
            BUILD_TYPE: default
            DRAFT: disabled
            PACKAGES: automake autoconf libtool  ossp-uuid zeromq curl libmicrohttpd
# For non-cmake users, there is an autotools solution with a bit more overhead
# to have dependencies ready and pass configure script before making this check).
# Note that the autotools variant will also require dependencies preinstalled to
# pass its configure script:
          - os: ubuntu-20.04
            BUILD_TYPE: cmake
            DO_CLANG_FORMAT_CHECK: 1
            CLANG_FORMAT: clang-format-11
            PACKAGES: cmake clang-format-11 git uuid-dev libsystemd-dev liblz4-dev libnss3-dev libzmq3-dev libcurl4-nss-dev libmicrohttpd-dev
# Note: the ABI compliance checker script currently assumes that:
# 1) Your project sources have a "latest_release" branch or tag
#    to check out and compare the current commit's ABI to;
# 2) Prerequisites are available as packages - no custom rebuilds.
          - os: ubuntu-20.04
            BUILD_TYPE: abi-compliance-checker
            PACKAGES: universal-ctags abi-dumper abi-compliance-checker git uuid-dev libsystemd-dev liblz4-dev libnss3-dev libzmq3-dev libcurl4-nss-dev libmicrohttpd-dev
          - os: ubuntu-20.04
            BUILD_TYPE: check_zproject
            PACKAGES: git uuid-dev libsystemd-dev liblz4-dev libnss3-dev libzmq3-dev libcurl4-nss-dev libmicrohttpd-dev generator-scripting-language zproject
    env:
# Set CI_TIME: true to enable build-step profiling
# Set CI_TRACE: true to enable shell script tracing
# Set CI_CONFIG_QUIET: true to enable "configure --quiet" (only report stderr)
# Set CI_REQUIRE_GOOD_GITIGNORE: false to NOT fail if "git status -s" is not clean
# Set CI_REQUIRE_GOOD_CLANG_FORMAT: true to fail if "clang-format" check is not clean
      CI_TIME: false
      CI_TRACE: false
      CI_CONFIG_QUIET: true
      CI_REQUIRE_GOOD_GITIGNORE: false
      CI_REQUIRE_GOOD_CLANG_FORMAT: false
      CI_TEST_DISTCHECK: true
      platform: ${{ matrix.platform }}
      configuration: ${{ matrix.configuration }}
      ENABLE_DRAFTS: ${{ matrix.ENABLE_DRAFTS }}
      BUILD_TYPE: ${{ matrix.BUILD_TYPE }}
      DRAFT: ${{ matrix.DRAFT }}
      DO_CLANG_FORMAT_CHECK: ${{ matrix.DO_CLANG_FORMAT_CHECK }}
      CLANG_FORMAT: ${{ matrix.CLANG_FORMAT }}
      CI_SELFTEST: ${{ matrix.CI_SELFTEST }}
    steps:
    - name: Add debian packages
      if: matrix.os == 'ubuntu-20.04'
      uses: myci-actions/add-deb-repo@10
      with:
        repo-name: obs
        repo: deb http://download.opensuse.org/repositories/network:/messaging:/zeromq:/git-stable/xUbuntu_20.04/ ./
        keys-asc: https://download.opensuse.org/repositories/network:/messaging:/zeromq:/git-stable/xUbuntu_20.04/Release.key
        install: ${{ matrix.PACKAGES }}
    - name: Add brew packages
      if: matrix.os == 'macos-latest'
      shell: bash
      run: brew install ${{ matrix.PACKAGES }}
    - uses: actions/checkout@v2
      with:
        path: czmq
    - name: build
      if: matrix.os == 'ubuntu-20.04' || matrix.os == 'macos-latest'
      shell: bash
      working-directory: czmq
      run: ./ci_build.sh
