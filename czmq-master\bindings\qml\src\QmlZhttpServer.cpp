/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZhttpServer.h"


///
//  Return the port the server is listening on.
int QmlZhttpServer::port () {
    return zhttp_server_port (self);
};


QObject* QmlZhttpServer::qmlAttachedProperties(QObject* object) {
    return new QmlZhttpServerAttached(object);
}


///
//  Self test of this class.
void QmlZhttpServerAttached::test (bool verbose) {
    zhttp_server_test (verbose);
};

///
//  Create a new http server
QmlZhttpServer *QmlZhttpServerAttached::construct (QmlZhttpServerOptions *options) {
    QmlZhttpServer *qmlSelf = new QmlZhttpServer ();
    qmlSelf->self = zhttp_server_new (options->self);
    return qmlSelf;
};

///
//  Destroy an http server
void QmlZhttpServerAttached::destruct (QmlZhttpServer *qmlSelf) {
    zhttp_server_destroy (&qmlSelf->self);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
