/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#include <stdio.h>
#include <stdlib.h>
#include <jni.h>
#include "czmq.h"
#include "org_zeromq_czmq_ZhttpServerOptions.h"

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_ZhttpServerOptions__1_1new (JNIEnv *env, jclass c)
{
    //  Disable CZMQ signal handling; allow Java to deal with it
    zsys_handler_set (NULL);
    jlong new_ = (jlong) (intptr_t) zhttp_server_options_new ();
    return new_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_ZhttpServerOptions__1_1fromConfig (JNIEnv *env, jclass c, jlong config)
{
    jlong from_config_ = (jlong) (intptr_t) zhttp_server_options_from_config ((zconfig_t *) (intptr_t) config);
    return from_config_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpServerOptions__1_1destroy (JNIEnv *env, jclass c, jlong self)
{
    zhttp_server_options_destroy ((zhttp_server_options_t **) &self);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_ZhttpServerOptions__1_1port (JNIEnv *env, jclass c, jlong self)
{
    jint port_ = (jint) zhttp_server_options_port ((zhttp_server_options_t *) (intptr_t) self);
    return port_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpServerOptions__1_1setPort (JNIEnv *env, jclass c, jlong self, jint port)
{
    zhttp_server_options_set_port ((zhttp_server_options_t *) (intptr_t) self, (int) port);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_ZhttpServerOptions__1_1backendAddress (JNIEnv *env, jclass c, jlong self)
{
    char *backend_address_ = (char *) zhttp_server_options_backend_address ((zhttp_server_options_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, backend_address_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpServerOptions__1_1setBackendAddress (JNIEnv *env, jclass c, jlong self, jstring address)
{
    char *address_ = (char *) (*env)->GetStringUTFChars (env, address, NULL);
    zhttp_server_options_set_backend_address ((zhttp_server_options_t *) (intptr_t) self, address_);
    (*env)->ReleaseStringUTFChars (env, address, address_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpServerOptions__1_1test (JNIEnv *env, jclass c, jboolean verbose)
{
    zhttp_server_options_test ((bool) verbose);
}

