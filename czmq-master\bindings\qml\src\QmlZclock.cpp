/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZclock.h"



QObject* QmlZclock::qmlAttachedProperties(QObject* object) {
    return new QmlZclockAttached(object);
}


///
//  Sleep for a number of milliseconds
void QmlZclockAttached::sleep (int msecs) {
    zclock_sleep (msecs);
};

///
//  Return current system clock as milliseconds. Note that this clock can
//  jump backwards (if the system clock is changed) so is unsafe to use for
//  timers and time offsets. Use zclock_mono for that instead.
int64_t QmlZclockAttached::time () {
    return zclock_time ();
};

///
//  Return current monotonic clock in milliseconds. Use this when you compute
//  time offsets. The monotonic clock is not affected by system changes and
//  so will never be reset backwards, unlike a system clock.
int64_t QmlZclockAttached::mono () {
    return zclock_mono ();
};

///
//  Return current monotonic clock in microseconds. Use this when you compute
//  time offsets. The monotonic clock is not affected by system changes and
//  so will never be reset backwards, unlike a system clock.
int64_t QmlZclockAttached::usecs () {
    return zclock_usecs ();
};

///
//  Return formatted date/time as fresh string. Free using zstr_free().
QString QmlZclockAttached::timestr () {
    char *retStr_ = zclock_timestr ();
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Self test of this class.
void QmlZclockAttached::test (bool verbose) {
    zclock_test (verbose);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
