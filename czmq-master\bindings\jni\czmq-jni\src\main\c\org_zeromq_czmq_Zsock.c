/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#include <stdio.h>
#include <stdlib.h>
#include <jni.h>
#include "czmq.h"
#include "org_zeromq_czmq_Zsock.h"

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1new (JNIEnv *env, jclass c, jint type)
{
    //  Disable CZMQ signal handling; allow Java to deal with it
    zsys_handler_set (NULL);
    jlong new_ = (jlong) (intptr_t) zsock_new ((int) type);
    return new_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newPub (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_pub_ = (jlong) (intptr_t) zsock_new_pub (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_pub_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newSub (JNIEnv *env, jclass c, jstring endpoint, jstring subscribe)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    char *subscribe_ = (char *) (*env)->GetStringUTFChars (env, subscribe, NULL);
    jlong new_sub_ = (jlong) (intptr_t) zsock_new_sub (endpoint_, subscribe_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    (*env)->ReleaseStringUTFChars (env, subscribe, subscribe_);
    return new_sub_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newReq (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_req_ = (jlong) (intptr_t) zsock_new_req (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_req_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newRep (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_rep_ = (jlong) (intptr_t) zsock_new_rep (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_rep_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newDealer (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_dealer_ = (jlong) (intptr_t) zsock_new_dealer (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_dealer_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newRouter (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_router_ = (jlong) (intptr_t) zsock_new_router (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_router_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newPush (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_push_ = (jlong) (intptr_t) zsock_new_push (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_push_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newPull (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_pull_ = (jlong) (intptr_t) zsock_new_pull (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_pull_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newXpub (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_xpub_ = (jlong) (intptr_t) zsock_new_xpub (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_xpub_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newXsub (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_xsub_ = (jlong) (intptr_t) zsock_new_xsub (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_xsub_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newPair (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_pair_ = (jlong) (intptr_t) zsock_new_pair (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_pair_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newStream (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_stream_ = (jlong) (intptr_t) zsock_new_stream (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_stream_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newServer (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_server_ = (jlong) (intptr_t) zsock_new_server (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_server_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newClient (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_client_ = (jlong) (intptr_t) zsock_new_client (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_client_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newRadio (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_radio_ = (jlong) (intptr_t) zsock_new_radio (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_radio_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newDish (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_dish_ = (jlong) (intptr_t) zsock_new_dish (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_dish_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newGather (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_gather_ = (jlong) (intptr_t) zsock_new_gather (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_gather_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newScatter (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_scatter_ = (jlong) (intptr_t) zsock_new_scatter (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_scatter_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1newDgram (JNIEnv *env, jclass c, jstring endpoint)
{
    char *endpoint_ = (char *) (*env)->GetStringUTFChars (env, endpoint, NULL);
    jlong new_dgram_ = (jlong) (intptr_t) zsock_new_dgram (endpoint_);
    (*env)->ReleaseStringUTFChars (env, endpoint, endpoint_);
    return new_dgram_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1destroy (JNIEnv *env, jclass c, jlong self)
{
    zsock_destroy ((zsock_t **) &self);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1bind (JNIEnv *env, jclass c, jlong self, jstring format)
{
    char *format_ = (char *) (*env)->GetStringUTFChars (env, format, NULL);
    jint bind_ = (jint) zsock_bind ((zsock_t *) (intptr_t) self, "%s", format_);
    (*env)->ReleaseStringUTFChars (env, format, format_);
    return bind_;
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1endpoint (JNIEnv *env, jclass c, jlong self)
{
    char *endpoint_ = (char *) zsock_endpoint ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, endpoint_);
    return return_string_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1unbind (JNIEnv *env, jclass c, jlong self, jstring format)
{
    char *format_ = (char *) (*env)->GetStringUTFChars (env, format, NULL);
    jint unbind_ = (jint) zsock_unbind ((zsock_t *) (intptr_t) self, "%s", format_);
    (*env)->ReleaseStringUTFChars (env, format, format_);
    return unbind_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1connect (JNIEnv *env, jclass c, jlong self, jstring format)
{
    char *format_ = (char *) (*env)->GetStringUTFChars (env, format, NULL);
    jint connect_ = (jint) zsock_connect ((zsock_t *) (intptr_t) self, "%s", format_);
    (*env)->ReleaseStringUTFChars (env, format, format_);
    return connect_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1disconnect (JNIEnv *env, jclass c, jlong self, jstring format)
{
    char *format_ = (char *) (*env)->GetStringUTFChars (env, format, NULL);
    jint disconnect_ = (jint) zsock_disconnect ((zsock_t *) (intptr_t) self, "%s", format_);
    (*env)->ReleaseStringUTFChars (env, format, format_);
    return disconnect_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1attach (JNIEnv *env, jclass c, jlong self, jstring endpoints, jboolean serverish)
{
    char *endpoints_ = (char *) (*env)->GetStringUTFChars (env, endpoints, NULL);
    jint attach_ = (jint) zsock_attach ((zsock_t *) (intptr_t) self, endpoints_, (bool) serverish);
    (*env)->ReleaseStringUTFChars (env, endpoints, endpoints_);
    return attach_;
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1typeStr (JNIEnv *env, jclass c, jlong self)
{
    char *type_str_ = (char *) zsock_type_str ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, type_str_);
    return return_string_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1send (JNIEnv *env, jclass c, jlong self, jstring picture)
{
    char *picture_ = (char *) (*env)->GetStringUTFChars (env, picture, NULL);
    jint send_ = (jint) zsock_send ((zsock_t *) (intptr_t) self, picture_, NULL);
    (*env)->ReleaseStringUTFChars (env, picture, picture_);
    return send_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1recv (JNIEnv *env, jclass c, jlong self, jstring picture)
{
    char *picture_ = (char *) (*env)->GetStringUTFChars (env, picture, NULL);
    jint recv_ = (jint) zsock_recv ((zsock_t *) (intptr_t) self, picture_, NULL);
    (*env)->ReleaseStringUTFChars (env, picture, picture_);
    return recv_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1bsend (JNIEnv *env, jclass c, jlong self, jstring picture)
{
    char *picture_ = (char *) (*env)->GetStringUTFChars (env, picture, NULL);
    jint bsend_ = (jint) zsock_bsend ((zsock_t *) (intptr_t) self, picture_, NULL);
    (*env)->ReleaseStringUTFChars (env, picture, picture_);
    return bsend_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1brecv (JNIEnv *env, jclass c, jlong self, jstring picture)
{
    char *picture_ = (char *) (*env)->GetStringUTFChars (env, picture, NULL);
    jint brecv_ = (jint) zsock_brecv ((zsock_t *) (intptr_t) self, picture_, NULL);
    (*env)->ReleaseStringUTFChars (env, picture, picture_);
    return brecv_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1routingId (JNIEnv *env, jclass c, jlong self)
{
    jint routing_id_ = (jint) zsock_routing_id ((zsock_t *) (intptr_t) self);
    return routing_id_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setRoutingId (JNIEnv *env, jclass c, jlong self, jint routing_id)
{
    zsock_set_routing_id ((zsock_t *) (intptr_t) self, (uint32_t) routing_id);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setUnbounded (JNIEnv *env, jclass c, jlong self)
{
    zsock_set_unbounded ((zsock_t *) (intptr_t) self);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1signal (JNIEnv *env, jclass c, jlong self, jbyte status)
{
    jint signal_ = (jint) zsock_signal ((zsock_t *) (intptr_t) self, (byte) status);
    return signal_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1wait (JNIEnv *env, jclass c, jlong self)
{
    jint wait_ = (jint) zsock_wait ((zsock_t *) (intptr_t) self);
    return wait_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1flush (JNIEnv *env, jclass c, jlong self)
{
    zsock_flush ((zsock_t *) (intptr_t) self);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1join (JNIEnv *env, jclass c, jlong self, jstring group)
{
    char *group_ = (char *) (*env)->GetStringUTFChars (env, group, NULL);
    jint join_ = (jint) zsock_join ((zsock_t *) (intptr_t) self, group_);
    (*env)->ReleaseStringUTFChars (env, group, group_);
    return join_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1leave (JNIEnv *env, jclass c, jlong self, jstring group)
{
    char *group_ = (char *) (*env)->GetStringUTFChars (env, group, NULL);
    jint leave_ = (jint) zsock_leave ((zsock_t *) (intptr_t) self, group_);
    (*env)->ReleaseStringUTFChars (env, group, group_);
    return leave_;
}

JNIEXPORT jboolean JNICALL
Java_org_zeromq_czmq_Zsock__1_1is (JNIEnv *env, jclass c, jlong self)
{
    jboolean is_ = (jboolean) zsock_is ((void *) (intptr_t) self);
    return is_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zsock__1_1resolve (JNIEnv *env, jclass c, jlong self)
{
    jlong resolve_ = (jlong) (intptr_t) zsock_resolve ((void *) (intptr_t) self);
    return resolve_;
}

JNIEXPORT jboolean JNICALL
Java_org_zeromq_czmq_Zsock__1_1hasIn (JNIEnv *env, jclass c, jlong self)
{
    jboolean has_in_ = (jboolean) zsock_has_in ((zsock_t *) (intptr_t) self);
    return has_in_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1priority (JNIEnv *env, jclass c, jlong self)
{
    jint priority_ = (jint) zsock_priority ((zsock_t *) (intptr_t) self);
    return priority_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setPriority (JNIEnv *env, jclass c, jlong self, jint priority)
{
    zsock_set_priority ((zsock_t *) (intptr_t) self, (int) priority);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1reconnectStop (JNIEnv *env, jclass c, jlong self)
{
    jint reconnect_stop_ = (jint) zsock_reconnect_stop ((zsock_t *) (intptr_t) self);
    return reconnect_stop_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setReconnectStop (JNIEnv *env, jclass c, jlong self, jint reconnect_stop)
{
    zsock_set_reconnect_stop ((zsock_t *) (intptr_t) self, (int) reconnect_stop);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setOnlyFirstSubscribe (JNIEnv *env, jclass c, jlong self, jint only_first_subscribe)
{
    zsock_set_only_first_subscribe ((zsock_t *) (intptr_t) self, (int) only_first_subscribe);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setHelloMsg (JNIEnv *env, jclass c, jlong self, jlong hello_msg)
{
    zsock_set_hello_msg ((zsock_t *) (intptr_t) self, (zframe_t *) (intptr_t) hello_msg);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setDisconnectMsg (JNIEnv *env, jclass c, jlong self, jlong disconnect_msg)
{
    zsock_set_disconnect_msg ((zsock_t *) (intptr_t) self, (zframe_t *) (intptr_t) disconnect_msg);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setWssTrustSystem (JNIEnv *env, jclass c, jlong self, jint wss_trust_system)
{
    zsock_set_wss_trust_system ((zsock_t *) (intptr_t) self, (int) wss_trust_system);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setWssHostname (JNIEnv *env, jclass c, jlong self, jstring wss_hostname)
{
    char *wss_hostname_ = (char *) (*env)->GetStringUTFChars (env, wss_hostname, NULL);
    zsock_set_wss_hostname ((zsock_t *) (intptr_t) self, wss_hostname_);
    (*env)->ReleaseStringUTFChars (env, wss_hostname, wss_hostname_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setWssTrustPem (JNIEnv *env, jclass c, jlong self, jstring wss_trust_pem)
{
    char *wss_trust_pem_ = (char *) (*env)->GetStringUTFChars (env, wss_trust_pem, NULL);
    zsock_set_wss_trust_pem ((zsock_t *) (intptr_t) self, wss_trust_pem_);
    (*env)->ReleaseStringUTFChars (env, wss_trust_pem, wss_trust_pem_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setWssCertPem (JNIEnv *env, jclass c, jlong self, jstring wss_cert_pem)
{
    char *wss_cert_pem_ = (char *) (*env)->GetStringUTFChars (env, wss_cert_pem, NULL);
    zsock_set_wss_cert_pem ((zsock_t *) (intptr_t) self, wss_cert_pem_);
    (*env)->ReleaseStringUTFChars (env, wss_cert_pem, wss_cert_pem_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setWssKeyPem (JNIEnv *env, jclass c, jlong self, jstring wss_key_pem)
{
    char *wss_key_pem_ = (char *) (*env)->GetStringUTFChars (env, wss_key_pem, NULL);
    zsock_set_wss_key_pem ((zsock_t *) (intptr_t) self, wss_key_pem_);
    (*env)->ReleaseStringUTFChars (env, wss_key_pem, wss_key_pem_);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1outBatchSize (JNIEnv *env, jclass c, jlong self)
{
    jint out_batch_size_ = (jint) zsock_out_batch_size ((zsock_t *) (intptr_t) self);
    return out_batch_size_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setOutBatchSize (JNIEnv *env, jclass c, jlong self, jint out_batch_size)
{
    zsock_set_out_batch_size ((zsock_t *) (intptr_t) self, (int) out_batch_size);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1inBatchSize (JNIEnv *env, jclass c, jlong self)
{
    jint in_batch_size_ = (jint) zsock_in_batch_size ((zsock_t *) (intptr_t) self);
    return in_batch_size_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setInBatchSize (JNIEnv *env, jclass c, jlong self, jint in_batch_size)
{
    zsock_set_in_batch_size ((zsock_t *) (intptr_t) self, (int) in_batch_size);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1socksPassword (JNIEnv *env, jclass c, jlong self)
{
    char *socks_password_ = (char *) zsock_socks_password ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, socks_password_);
    zstr_free (&socks_password_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setSocksPassword (JNIEnv *env, jclass c, jlong self, jstring socks_password)
{
    char *socks_password_ = (char *) (*env)->GetStringUTFChars (env, socks_password, NULL);
    zsock_set_socks_password ((zsock_t *) (intptr_t) self, socks_password_);
    (*env)->ReleaseStringUTFChars (env, socks_password, socks_password_);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1socksUsername (JNIEnv *env, jclass c, jlong self)
{
    char *socks_username_ = (char *) zsock_socks_username ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, socks_username_);
    zstr_free (&socks_username_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setSocksUsername (JNIEnv *env, jclass c, jlong self, jstring socks_username)
{
    char *socks_username_ = (char *) (*env)->GetStringUTFChars (env, socks_username, NULL);
    zsock_set_socks_username ((zsock_t *) (intptr_t) self, socks_username_);
    (*env)->ReleaseStringUTFChars (env, socks_username, socks_username_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setXpubManualLastValue (JNIEnv *env, jclass c, jlong self, jint xpub_manual_last_value)
{
    zsock_set_xpub_manual_last_value ((zsock_t *) (intptr_t) self, (int) xpub_manual_last_value);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1routerNotify (JNIEnv *env, jclass c, jlong self)
{
    jint router_notify_ = (jint) zsock_router_notify ((zsock_t *) (intptr_t) self);
    return router_notify_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setRouterNotify (JNIEnv *env, jclass c, jlong self, jint router_notify)
{
    zsock_set_router_notify ((zsock_t *) (intptr_t) self, (int) router_notify);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1multicastLoop (JNIEnv *env, jclass c, jlong self)
{
    jint multicast_loop_ = (jint) zsock_multicast_loop ((zsock_t *) (intptr_t) self);
    return multicast_loop_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setMulticastLoop (JNIEnv *env, jclass c, jlong self, jint multicast_loop)
{
    zsock_set_multicast_loop ((zsock_t *) (intptr_t) self, (int) multicast_loop);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1metadata (JNIEnv *env, jclass c, jlong self)
{
    char *metadata_ = (char *) zsock_metadata ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, metadata_);
    zstr_free (&metadata_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setMetadata (JNIEnv *env, jclass c, jlong self, jstring metadata)
{
    char *metadata_ = (char *) (*env)->GetStringUTFChars (env, metadata, NULL);
    zsock_set_metadata ((zsock_t *) (intptr_t) self, metadata_);
    (*env)->ReleaseStringUTFChars (env, metadata, metadata_);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1loopbackFastpath (JNIEnv *env, jclass c, jlong self)
{
    jint loopback_fastpath_ = (jint) zsock_loopback_fastpath ((zsock_t *) (intptr_t) self);
    return loopback_fastpath_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setLoopbackFastpath (JNIEnv *env, jclass c, jlong self, jint loopback_fastpath)
{
    zsock_set_loopback_fastpath ((zsock_t *) (intptr_t) self, (int) loopback_fastpath);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1zapEnforceDomain (JNIEnv *env, jclass c, jlong self)
{
    jint zap_enforce_domain_ = (jint) zsock_zap_enforce_domain ((zsock_t *) (intptr_t) self);
    return zap_enforce_domain_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setZapEnforceDomain (JNIEnv *env, jclass c, jlong self, jint zap_enforce_domain)
{
    zsock_set_zap_enforce_domain ((zsock_t *) (intptr_t) self, (int) zap_enforce_domain);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1gssapiPrincipalNametype (JNIEnv *env, jclass c, jlong self)
{
    jint gssapi_principal_nametype_ = (jint) zsock_gssapi_principal_nametype ((zsock_t *) (intptr_t) self);
    return gssapi_principal_nametype_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setGssapiPrincipalNametype (JNIEnv *env, jclass c, jlong self, jint gssapi_principal_nametype)
{
    zsock_set_gssapi_principal_nametype ((zsock_t *) (intptr_t) self, (int) gssapi_principal_nametype);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1gssapiServicePrincipalNametype (JNIEnv *env, jclass c, jlong self)
{
    jint gssapi_service_principal_nametype_ = (jint) zsock_gssapi_service_principal_nametype ((zsock_t *) (intptr_t) self);
    return gssapi_service_principal_nametype_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setGssapiServicePrincipalNametype (JNIEnv *env, jclass c, jlong self, jint gssapi_service_principal_nametype)
{
    zsock_set_gssapi_service_principal_nametype ((zsock_t *) (intptr_t) self, (int) gssapi_service_principal_nametype);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1bindtodevice (JNIEnv *env, jclass c, jlong self)
{
    char *bindtodevice_ = (char *) zsock_bindtodevice ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, bindtodevice_);
    zstr_free (&bindtodevice_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setBindtodevice (JNIEnv *env, jclass c, jlong self, jstring bindtodevice)
{
    char *bindtodevice_ = (char *) (*env)->GetStringUTFChars (env, bindtodevice, NULL);
    zsock_set_bindtodevice ((zsock_t *) (intptr_t) self, bindtodevice_);
    (*env)->ReleaseStringUTFChars (env, bindtodevice, bindtodevice_);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1heartbeatIvl (JNIEnv *env, jclass c, jlong self)
{
    jint heartbeat_ivl_ = (jint) zsock_heartbeat_ivl ((zsock_t *) (intptr_t) self);
    return heartbeat_ivl_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setHeartbeatIvl (JNIEnv *env, jclass c, jlong self, jint heartbeat_ivl)
{
    zsock_set_heartbeat_ivl ((zsock_t *) (intptr_t) self, (int) heartbeat_ivl);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1heartbeatTtl (JNIEnv *env, jclass c, jlong self)
{
    jint heartbeat_ttl_ = (jint) zsock_heartbeat_ttl ((zsock_t *) (intptr_t) self);
    return heartbeat_ttl_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setHeartbeatTtl (JNIEnv *env, jclass c, jlong self, jint heartbeat_ttl)
{
    zsock_set_heartbeat_ttl ((zsock_t *) (intptr_t) self, (int) heartbeat_ttl);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1heartbeatTimeout (JNIEnv *env, jclass c, jlong self)
{
    jint heartbeat_timeout_ = (jint) zsock_heartbeat_timeout ((zsock_t *) (intptr_t) self);
    return heartbeat_timeout_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setHeartbeatTimeout (JNIEnv *env, jclass c, jlong self, jint heartbeat_timeout)
{
    zsock_set_heartbeat_timeout ((zsock_t *) (intptr_t) self, (int) heartbeat_timeout);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1useFd (JNIEnv *env, jclass c, jlong self)
{
    jint use_fd_ = (jint) zsock_use_fd ((zsock_t *) (intptr_t) self);
    return use_fd_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setUseFd (JNIEnv *env, jclass c, jlong self, jint use_fd)
{
    zsock_set_use_fd ((zsock_t *) (intptr_t) self, (int) use_fd);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setXpubManual (JNIEnv *env, jclass c, jlong self, jint xpub_manual)
{
    zsock_set_xpub_manual ((zsock_t *) (intptr_t) self, (int) xpub_manual);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setXpubWelcomeMsg (JNIEnv *env, jclass c, jlong self, jstring xpub_welcome_msg)
{
    char *xpub_welcome_msg_ = (char *) (*env)->GetStringUTFChars (env, xpub_welcome_msg, NULL);
    zsock_set_xpub_welcome_msg ((zsock_t *) (intptr_t) self, xpub_welcome_msg_);
    (*env)->ReleaseStringUTFChars (env, xpub_welcome_msg, xpub_welcome_msg_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setStreamNotify (JNIEnv *env, jclass c, jlong self, jint stream_notify)
{
    zsock_set_stream_notify ((zsock_t *) (intptr_t) self, (int) stream_notify);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1invertMatching (JNIEnv *env, jclass c, jlong self)
{
    jint invert_matching_ = (jint) zsock_invert_matching ((zsock_t *) (intptr_t) self);
    return invert_matching_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setInvertMatching (JNIEnv *env, jclass c, jlong self, jint invert_matching)
{
    zsock_set_invert_matching ((zsock_t *) (intptr_t) self, (int) invert_matching);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setXpubVerboser (JNIEnv *env, jclass c, jlong self, jint xpub_verboser)
{
    zsock_set_xpub_verboser ((zsock_t *) (intptr_t) self, (int) xpub_verboser);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1connectTimeout (JNIEnv *env, jclass c, jlong self)
{
    jint connect_timeout_ = (jint) zsock_connect_timeout ((zsock_t *) (intptr_t) self);
    return connect_timeout_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setConnectTimeout (JNIEnv *env, jclass c, jlong self, jint connect_timeout)
{
    zsock_set_connect_timeout ((zsock_t *) (intptr_t) self, (int) connect_timeout);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1tcpMaxrt (JNIEnv *env, jclass c, jlong self)
{
    jint tcp_maxrt_ = (jint) zsock_tcp_maxrt ((zsock_t *) (intptr_t) self);
    return tcp_maxrt_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setTcpMaxrt (JNIEnv *env, jclass c, jlong self, jint tcp_maxrt)
{
    zsock_set_tcp_maxrt ((zsock_t *) (intptr_t) self, (int) tcp_maxrt);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1threadSafe (JNIEnv *env, jclass c, jlong self)
{
    jint thread_safe_ = (jint) zsock_thread_safe ((zsock_t *) (intptr_t) self);
    return thread_safe_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1multicastMaxtpdu (JNIEnv *env, jclass c, jlong self)
{
    jint multicast_maxtpdu_ = (jint) zsock_multicast_maxtpdu ((zsock_t *) (intptr_t) self);
    return multicast_maxtpdu_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setMulticastMaxtpdu (JNIEnv *env, jclass c, jlong self, jint multicast_maxtpdu)
{
    zsock_set_multicast_maxtpdu ((zsock_t *) (intptr_t) self, (int) multicast_maxtpdu);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1vmciBufferSize (JNIEnv *env, jclass c, jlong self)
{
    jint vmci_buffer_size_ = (jint) zsock_vmci_buffer_size ((zsock_t *) (intptr_t) self);
    return vmci_buffer_size_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setVmciBufferSize (JNIEnv *env, jclass c, jlong self, jint vmci_buffer_size)
{
    zsock_set_vmci_buffer_size ((zsock_t *) (intptr_t) self, (int) vmci_buffer_size);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1vmciBufferMinSize (JNIEnv *env, jclass c, jlong self)
{
    jint vmci_buffer_min_size_ = (jint) zsock_vmci_buffer_min_size ((zsock_t *) (intptr_t) self);
    return vmci_buffer_min_size_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setVmciBufferMinSize (JNIEnv *env, jclass c, jlong self, jint vmci_buffer_min_size)
{
    zsock_set_vmci_buffer_min_size ((zsock_t *) (intptr_t) self, (int) vmci_buffer_min_size);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1vmciBufferMaxSize (JNIEnv *env, jclass c, jlong self)
{
    jint vmci_buffer_max_size_ = (jint) zsock_vmci_buffer_max_size ((zsock_t *) (intptr_t) self);
    return vmci_buffer_max_size_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setVmciBufferMaxSize (JNIEnv *env, jclass c, jlong self, jint vmci_buffer_max_size)
{
    zsock_set_vmci_buffer_max_size ((zsock_t *) (intptr_t) self, (int) vmci_buffer_max_size);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1vmciConnectTimeout (JNIEnv *env, jclass c, jlong self)
{
    jint vmci_connect_timeout_ = (jint) zsock_vmci_connect_timeout ((zsock_t *) (intptr_t) self);
    return vmci_connect_timeout_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setVmciConnectTimeout (JNIEnv *env, jclass c, jlong self, jint vmci_connect_timeout)
{
    zsock_set_vmci_connect_timeout ((zsock_t *) (intptr_t) self, (int) vmci_connect_timeout);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1tos (JNIEnv *env, jclass c, jlong self)
{
    jint tos_ = (jint) zsock_tos ((zsock_t *) (intptr_t) self);
    return tos_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setTos (JNIEnv *env, jclass c, jlong self, jint tos)
{
    zsock_set_tos ((zsock_t *) (intptr_t) self, (int) tos);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setRouterHandover (JNIEnv *env, jclass c, jlong self, jint router_handover)
{
    zsock_set_router_handover ((zsock_t *) (intptr_t) self, (int) router_handover);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setConnectRid (JNIEnv *env, jclass c, jlong self, jstring connect_rid)
{
    char *connect_rid_ = (char *) (*env)->GetStringUTFChars (env, connect_rid, NULL);
    zsock_set_connect_rid ((zsock_t *) (intptr_t) self, connect_rid_);
    (*env)->ReleaseStringUTFChars (env, connect_rid, connect_rid_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setConnectRidBin (JNIEnv *env, jclass c, jlong self, jbyteArray connect_rid)
{
    jbyte *connect_rid_ = (byte *) (*env)->GetByteArrayElements (env, connect_rid, 0);
    zsock_set_connect_rid_bin ((zsock_t *) (intptr_t) self, connect_rid_);
    (*env)->ReleaseByteArrayElements (env, connect_rid, (jbyte *) connect_rid_, 0);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1handshakeIvl (JNIEnv *env, jclass c, jlong self)
{
    jint handshake_ivl_ = (jint) zsock_handshake_ivl ((zsock_t *) (intptr_t) self);
    return handshake_ivl_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setHandshakeIvl (JNIEnv *env, jclass c, jlong self, jint handshake_ivl)
{
    zsock_set_handshake_ivl ((zsock_t *) (intptr_t) self, (int) handshake_ivl);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1socksProxy (JNIEnv *env, jclass c, jlong self)
{
    char *socks_proxy_ = (char *) zsock_socks_proxy ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, socks_proxy_);
    zstr_free (&socks_proxy_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setSocksProxy (JNIEnv *env, jclass c, jlong self, jstring socks_proxy)
{
    char *socks_proxy_ = (char *) (*env)->GetStringUTFChars (env, socks_proxy, NULL);
    zsock_set_socks_proxy ((zsock_t *) (intptr_t) self, socks_proxy_);
    (*env)->ReleaseStringUTFChars (env, socks_proxy, socks_proxy_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setXpubNodrop (JNIEnv *env, jclass c, jlong self, jint xpub_nodrop)
{
    zsock_set_xpub_nodrop ((zsock_t *) (intptr_t) self, (int) xpub_nodrop);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setRouterMandatory (JNIEnv *env, jclass c, jlong self, jint router_mandatory)
{
    zsock_set_router_mandatory ((zsock_t *) (intptr_t) self, (int) router_mandatory);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setProbeRouter (JNIEnv *env, jclass c, jlong self, jint probe_router)
{
    zsock_set_probe_router ((zsock_t *) (intptr_t) self, (int) probe_router);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setReqRelaxed (JNIEnv *env, jclass c, jlong self, jint req_relaxed)
{
    zsock_set_req_relaxed ((zsock_t *) (intptr_t) self, (int) req_relaxed);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setReqCorrelate (JNIEnv *env, jclass c, jlong self, jint req_correlate)
{
    zsock_set_req_correlate ((zsock_t *) (intptr_t) self, (int) req_correlate);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setConflate (JNIEnv *env, jclass c, jlong self, jint conflate)
{
    zsock_set_conflate ((zsock_t *) (intptr_t) self, (int) conflate);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1zapDomain (JNIEnv *env, jclass c, jlong self)
{
    char *zap_domain_ = (char *) zsock_zap_domain ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, zap_domain_);
    zstr_free (&zap_domain_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setZapDomain (JNIEnv *env, jclass c, jlong self, jstring zap_domain)
{
    char *zap_domain_ = (char *) (*env)->GetStringUTFChars (env, zap_domain, NULL);
    zsock_set_zap_domain ((zsock_t *) (intptr_t) self, zap_domain_);
    (*env)->ReleaseStringUTFChars (env, zap_domain, zap_domain_);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1mechanism (JNIEnv *env, jclass c, jlong self)
{
    jint mechanism_ = (jint) zsock_mechanism ((zsock_t *) (intptr_t) self);
    return mechanism_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1plainServer (JNIEnv *env, jclass c, jlong self)
{
    jint plain_server_ = (jint) zsock_plain_server ((zsock_t *) (intptr_t) self);
    return plain_server_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setPlainServer (JNIEnv *env, jclass c, jlong self, jint plain_server)
{
    zsock_set_plain_server ((zsock_t *) (intptr_t) self, (int) plain_server);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1plainUsername (JNIEnv *env, jclass c, jlong self)
{
    char *plain_username_ = (char *) zsock_plain_username ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, plain_username_);
    zstr_free (&plain_username_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setPlainUsername (JNIEnv *env, jclass c, jlong self, jstring plain_username)
{
    char *plain_username_ = (char *) (*env)->GetStringUTFChars (env, plain_username, NULL);
    zsock_set_plain_username ((zsock_t *) (intptr_t) self, plain_username_);
    (*env)->ReleaseStringUTFChars (env, plain_username, plain_username_);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1plainPassword (JNIEnv *env, jclass c, jlong self)
{
    char *plain_password_ = (char *) zsock_plain_password ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, plain_password_);
    zstr_free (&plain_password_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setPlainPassword (JNIEnv *env, jclass c, jlong self, jstring plain_password)
{
    char *plain_password_ = (char *) (*env)->GetStringUTFChars (env, plain_password, NULL);
    zsock_set_plain_password ((zsock_t *) (intptr_t) self, plain_password_);
    (*env)->ReleaseStringUTFChars (env, plain_password, plain_password_);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1curveServer (JNIEnv *env, jclass c, jlong self)
{
    jint curve_server_ = (jint) zsock_curve_server ((zsock_t *) (intptr_t) self);
    return curve_server_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setCurveServer (JNIEnv *env, jclass c, jlong self, jint curve_server)
{
    zsock_set_curve_server ((zsock_t *) (intptr_t) self, (int) curve_server);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1curvePublickey (JNIEnv *env, jclass c, jlong self)
{
    char *curve_publickey_ = (char *) zsock_curve_publickey ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, curve_publickey_);
    zstr_free (&curve_publickey_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setCurvePublickey (JNIEnv *env, jclass c, jlong self, jstring curve_publickey)
{
    char *curve_publickey_ = (char *) (*env)->GetStringUTFChars (env, curve_publickey, NULL);
    zsock_set_curve_publickey ((zsock_t *) (intptr_t) self, curve_publickey_);
    (*env)->ReleaseStringUTFChars (env, curve_publickey, curve_publickey_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setCurvePublickeyBin (JNIEnv *env, jclass c, jlong self, jbyteArray curve_publickey)
{
    jbyte *curve_publickey_ = (byte *) (*env)->GetByteArrayElements (env, curve_publickey, 0);
    zsock_set_curve_publickey_bin ((zsock_t *) (intptr_t) self, curve_publickey_);
    (*env)->ReleaseByteArrayElements (env, curve_publickey, (jbyte *) curve_publickey_, 0);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1curveSecretkey (JNIEnv *env, jclass c, jlong self)
{
    char *curve_secretkey_ = (char *) zsock_curve_secretkey ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, curve_secretkey_);
    zstr_free (&curve_secretkey_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setCurveSecretkey (JNIEnv *env, jclass c, jlong self, jstring curve_secretkey)
{
    char *curve_secretkey_ = (char *) (*env)->GetStringUTFChars (env, curve_secretkey, NULL);
    zsock_set_curve_secretkey ((zsock_t *) (intptr_t) self, curve_secretkey_);
    (*env)->ReleaseStringUTFChars (env, curve_secretkey, curve_secretkey_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setCurveSecretkeyBin (JNIEnv *env, jclass c, jlong self, jbyteArray curve_secretkey)
{
    jbyte *curve_secretkey_ = (byte *) (*env)->GetByteArrayElements (env, curve_secretkey, 0);
    zsock_set_curve_secretkey_bin ((zsock_t *) (intptr_t) self, curve_secretkey_);
    (*env)->ReleaseByteArrayElements (env, curve_secretkey, (jbyte *) curve_secretkey_, 0);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1curveServerkey (JNIEnv *env, jclass c, jlong self)
{
    char *curve_serverkey_ = (char *) zsock_curve_serverkey ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, curve_serverkey_);
    zstr_free (&curve_serverkey_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setCurveServerkey (JNIEnv *env, jclass c, jlong self, jstring curve_serverkey)
{
    char *curve_serverkey_ = (char *) (*env)->GetStringUTFChars (env, curve_serverkey, NULL);
    zsock_set_curve_serverkey ((zsock_t *) (intptr_t) self, curve_serverkey_);
    (*env)->ReleaseStringUTFChars (env, curve_serverkey, curve_serverkey_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setCurveServerkeyBin (JNIEnv *env, jclass c, jlong self, jbyteArray curve_serverkey)
{
    jbyte *curve_serverkey_ = (byte *) (*env)->GetByteArrayElements (env, curve_serverkey, 0);
    zsock_set_curve_serverkey_bin ((zsock_t *) (intptr_t) self, curve_serverkey_);
    (*env)->ReleaseByteArrayElements (env, curve_serverkey, (jbyte *) curve_serverkey_, 0);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1gssapiServer (JNIEnv *env, jclass c, jlong self)
{
    jint gssapi_server_ = (jint) zsock_gssapi_server ((zsock_t *) (intptr_t) self);
    return gssapi_server_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setGssapiServer (JNIEnv *env, jclass c, jlong self, jint gssapi_server)
{
    zsock_set_gssapi_server ((zsock_t *) (intptr_t) self, (int) gssapi_server);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1gssapiPlaintext (JNIEnv *env, jclass c, jlong self)
{
    jint gssapi_plaintext_ = (jint) zsock_gssapi_plaintext ((zsock_t *) (intptr_t) self);
    return gssapi_plaintext_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setGssapiPlaintext (JNIEnv *env, jclass c, jlong self, jint gssapi_plaintext)
{
    zsock_set_gssapi_plaintext ((zsock_t *) (intptr_t) self, (int) gssapi_plaintext);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1gssapiPrincipal (JNIEnv *env, jclass c, jlong self)
{
    char *gssapi_principal_ = (char *) zsock_gssapi_principal ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, gssapi_principal_);
    zstr_free (&gssapi_principal_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setGssapiPrincipal (JNIEnv *env, jclass c, jlong self, jstring gssapi_principal)
{
    char *gssapi_principal_ = (char *) (*env)->GetStringUTFChars (env, gssapi_principal, NULL);
    zsock_set_gssapi_principal ((zsock_t *) (intptr_t) self, gssapi_principal_);
    (*env)->ReleaseStringUTFChars (env, gssapi_principal, gssapi_principal_);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1gssapiServicePrincipal (JNIEnv *env, jclass c, jlong self)
{
    char *gssapi_service_principal_ = (char *) zsock_gssapi_service_principal ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, gssapi_service_principal_);
    zstr_free (&gssapi_service_principal_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setGssapiServicePrincipal (JNIEnv *env, jclass c, jlong self, jstring gssapi_service_principal)
{
    char *gssapi_service_principal_ = (char *) (*env)->GetStringUTFChars (env, gssapi_service_principal, NULL);
    zsock_set_gssapi_service_principal ((zsock_t *) (intptr_t) self, gssapi_service_principal_);
    (*env)->ReleaseStringUTFChars (env, gssapi_service_principal, gssapi_service_principal_);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1ipv6 (JNIEnv *env, jclass c, jlong self)
{
    jint ipv6_ = (jint) zsock_ipv6 ((zsock_t *) (intptr_t) self);
    return ipv6_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setIpv6 (JNIEnv *env, jclass c, jlong self, jint ipv6)
{
    zsock_set_ipv6 ((zsock_t *) (intptr_t) self, (int) ipv6);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1immediate (JNIEnv *env, jclass c, jlong self)
{
    jint immediate_ = (jint) zsock_immediate ((zsock_t *) (intptr_t) self);
    return immediate_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setImmediate (JNIEnv *env, jclass c, jlong self, jint immediate)
{
    zsock_set_immediate ((zsock_t *) (intptr_t) self, (int) immediate);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1sndhwm (JNIEnv *env, jclass c, jlong self)
{
    jint sndhwm_ = (jint) zsock_sndhwm ((zsock_t *) (intptr_t) self);
    return sndhwm_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setSndhwm (JNIEnv *env, jclass c, jlong self, jint sndhwm)
{
    zsock_set_sndhwm ((zsock_t *) (intptr_t) self, (int) sndhwm);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1rcvhwm (JNIEnv *env, jclass c, jlong self)
{
    jint rcvhwm_ = (jint) zsock_rcvhwm ((zsock_t *) (intptr_t) self);
    return rcvhwm_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setRcvhwm (JNIEnv *env, jclass c, jlong self, jint rcvhwm)
{
    zsock_set_rcvhwm ((zsock_t *) (intptr_t) self, (int) rcvhwm);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1maxmsgsize (JNIEnv *env, jclass c, jlong self)
{
    jint maxmsgsize_ = (jint) zsock_maxmsgsize ((zsock_t *) (intptr_t) self);
    return maxmsgsize_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setMaxmsgsize (JNIEnv *env, jclass c, jlong self, jint maxmsgsize)
{
    zsock_set_maxmsgsize ((zsock_t *) (intptr_t) self, (int) maxmsgsize);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1multicastHops (JNIEnv *env, jclass c, jlong self)
{
    jint multicast_hops_ = (jint) zsock_multicast_hops ((zsock_t *) (intptr_t) self);
    return multicast_hops_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setMulticastHops (JNIEnv *env, jclass c, jlong self, jint multicast_hops)
{
    zsock_set_multicast_hops ((zsock_t *) (intptr_t) self, (int) multicast_hops);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setXpubVerbose (JNIEnv *env, jclass c, jlong self, jint xpub_verbose)
{
    zsock_set_xpub_verbose ((zsock_t *) (intptr_t) self, (int) xpub_verbose);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1tcpKeepalive (JNIEnv *env, jclass c, jlong self)
{
    jint tcp_keepalive_ = (jint) zsock_tcp_keepalive ((zsock_t *) (intptr_t) self);
    return tcp_keepalive_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setTcpKeepalive (JNIEnv *env, jclass c, jlong self, jint tcp_keepalive)
{
    zsock_set_tcp_keepalive ((zsock_t *) (intptr_t) self, (int) tcp_keepalive);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1tcpKeepaliveIdle (JNIEnv *env, jclass c, jlong self)
{
    jint tcp_keepalive_idle_ = (jint) zsock_tcp_keepalive_idle ((zsock_t *) (intptr_t) self);
    return tcp_keepalive_idle_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setTcpKeepaliveIdle (JNIEnv *env, jclass c, jlong self, jint tcp_keepalive_idle)
{
    zsock_set_tcp_keepalive_idle ((zsock_t *) (intptr_t) self, (int) tcp_keepalive_idle);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1tcpKeepaliveCnt (JNIEnv *env, jclass c, jlong self)
{
    jint tcp_keepalive_cnt_ = (jint) zsock_tcp_keepalive_cnt ((zsock_t *) (intptr_t) self);
    return tcp_keepalive_cnt_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setTcpKeepaliveCnt (JNIEnv *env, jclass c, jlong self, jint tcp_keepalive_cnt)
{
    zsock_set_tcp_keepalive_cnt ((zsock_t *) (intptr_t) self, (int) tcp_keepalive_cnt);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1tcpKeepaliveIntvl (JNIEnv *env, jclass c, jlong self)
{
    jint tcp_keepalive_intvl_ = (jint) zsock_tcp_keepalive_intvl ((zsock_t *) (intptr_t) self);
    return tcp_keepalive_intvl_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setTcpKeepaliveIntvl (JNIEnv *env, jclass c, jlong self, jint tcp_keepalive_intvl)
{
    zsock_set_tcp_keepalive_intvl ((zsock_t *) (intptr_t) self, (int) tcp_keepalive_intvl);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1tcpAcceptFilter (JNIEnv *env, jclass c, jlong self)
{
    char *tcp_accept_filter_ = (char *) zsock_tcp_accept_filter ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, tcp_accept_filter_);
    zstr_free (&tcp_accept_filter_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setTcpAcceptFilter (JNIEnv *env, jclass c, jlong self, jstring tcp_accept_filter)
{
    char *tcp_accept_filter_ = (char *) (*env)->GetStringUTFChars (env, tcp_accept_filter, NULL);
    zsock_set_tcp_accept_filter ((zsock_t *) (intptr_t) self, tcp_accept_filter_);
    (*env)->ReleaseStringUTFChars (env, tcp_accept_filter, tcp_accept_filter_);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1lastEndpoint (JNIEnv *env, jclass c, jlong self)
{
    char *last_endpoint_ = (char *) zsock_last_endpoint ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, last_endpoint_);
    zstr_free (&last_endpoint_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setRouterRaw (JNIEnv *env, jclass c, jlong self, jint router_raw)
{
    zsock_set_router_raw ((zsock_t *) (intptr_t) self, (int) router_raw);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1ipv4only (JNIEnv *env, jclass c, jlong self)
{
    jint ipv4only_ = (jint) zsock_ipv4only ((zsock_t *) (intptr_t) self);
    return ipv4only_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setIpv4only (JNIEnv *env, jclass c, jlong self, jint ipv4only)
{
    zsock_set_ipv4only ((zsock_t *) (intptr_t) self, (int) ipv4only);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setDelayAttachOnConnect (JNIEnv *env, jclass c, jlong self, jint delay_attach_on_connect)
{
    zsock_set_delay_attach_on_connect ((zsock_t *) (intptr_t) self, (int) delay_attach_on_connect);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1hwm (JNIEnv *env, jclass c, jlong self)
{
    jint hwm_ = (jint) zsock_hwm ((zsock_t *) (intptr_t) self);
    return hwm_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setHwm (JNIEnv *env, jclass c, jlong self, jint hwm)
{
    zsock_set_hwm ((zsock_t *) (intptr_t) self, (int) hwm);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1swap (JNIEnv *env, jclass c, jlong self)
{
    jint swap_ = (jint) zsock_swap ((zsock_t *) (intptr_t) self);
    return swap_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setSwap (JNIEnv *env, jclass c, jlong self, jint swap)
{
    zsock_set_swap ((zsock_t *) (intptr_t) self, (int) swap);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1affinity (JNIEnv *env, jclass c, jlong self)
{
    jint affinity_ = (jint) zsock_affinity ((zsock_t *) (intptr_t) self);
    return affinity_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setAffinity (JNIEnv *env, jclass c, jlong self, jint affinity)
{
    zsock_set_affinity ((zsock_t *) (intptr_t) self, (int) affinity);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zsock__1_1identity (JNIEnv *env, jclass c, jlong self)
{
    char *identity_ = (char *) zsock_identity ((zsock_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, identity_);
    zstr_free (&identity_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setIdentity (JNIEnv *env, jclass c, jlong self, jstring identity)
{
    char *identity_ = (char *) (*env)->GetStringUTFChars (env, identity, NULL);
    zsock_set_identity ((zsock_t *) (intptr_t) self, identity_);
    (*env)->ReleaseStringUTFChars (env, identity, identity_);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1rate (JNIEnv *env, jclass c, jlong self)
{
    jint rate_ = (jint) zsock_rate ((zsock_t *) (intptr_t) self);
    return rate_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setRate (JNIEnv *env, jclass c, jlong self, jint rate)
{
    zsock_set_rate ((zsock_t *) (intptr_t) self, (int) rate);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1recoveryIvl (JNIEnv *env, jclass c, jlong self)
{
    jint recovery_ivl_ = (jint) zsock_recovery_ivl ((zsock_t *) (intptr_t) self);
    return recovery_ivl_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setRecoveryIvl (JNIEnv *env, jclass c, jlong self, jint recovery_ivl)
{
    zsock_set_recovery_ivl ((zsock_t *) (intptr_t) self, (int) recovery_ivl);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1recoveryIvlMsec (JNIEnv *env, jclass c, jlong self)
{
    jint recovery_ivl_msec_ = (jint) zsock_recovery_ivl_msec ((zsock_t *) (intptr_t) self);
    return recovery_ivl_msec_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setRecoveryIvlMsec (JNIEnv *env, jclass c, jlong self, jint recovery_ivl_msec)
{
    zsock_set_recovery_ivl_msec ((zsock_t *) (intptr_t) self, (int) recovery_ivl_msec);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1mcastLoop (JNIEnv *env, jclass c, jlong self)
{
    jint mcast_loop_ = (jint) zsock_mcast_loop ((zsock_t *) (intptr_t) self);
    return mcast_loop_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setMcastLoop (JNIEnv *env, jclass c, jlong self, jint mcast_loop)
{
    zsock_set_mcast_loop ((zsock_t *) (intptr_t) self, (int) mcast_loop);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1rcvtimeo (JNIEnv *env, jclass c, jlong self)
{
    jint rcvtimeo_ = (jint) zsock_rcvtimeo ((zsock_t *) (intptr_t) self);
    return rcvtimeo_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setRcvtimeo (JNIEnv *env, jclass c, jlong self, jint rcvtimeo)
{
    zsock_set_rcvtimeo ((zsock_t *) (intptr_t) self, (int) rcvtimeo);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1sndtimeo (JNIEnv *env, jclass c, jlong self)
{
    jint sndtimeo_ = (jint) zsock_sndtimeo ((zsock_t *) (intptr_t) self);
    return sndtimeo_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setSndtimeo (JNIEnv *env, jclass c, jlong self, jint sndtimeo)
{
    zsock_set_sndtimeo ((zsock_t *) (intptr_t) self, (int) sndtimeo);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1sndbuf (JNIEnv *env, jclass c, jlong self)
{
    jint sndbuf_ = (jint) zsock_sndbuf ((zsock_t *) (intptr_t) self);
    return sndbuf_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setSndbuf (JNIEnv *env, jclass c, jlong self, jint sndbuf)
{
    zsock_set_sndbuf ((zsock_t *) (intptr_t) self, (int) sndbuf);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1rcvbuf (JNIEnv *env, jclass c, jlong self)
{
    jint rcvbuf_ = (jint) zsock_rcvbuf ((zsock_t *) (intptr_t) self);
    return rcvbuf_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setRcvbuf (JNIEnv *env, jclass c, jlong self, jint rcvbuf)
{
    zsock_set_rcvbuf ((zsock_t *) (intptr_t) self, (int) rcvbuf);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1linger (JNIEnv *env, jclass c, jlong self)
{
    jint linger_ = (jint) zsock_linger ((zsock_t *) (intptr_t) self);
    return linger_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setLinger (JNIEnv *env, jclass c, jlong self, jint linger)
{
    zsock_set_linger ((zsock_t *) (intptr_t) self, (int) linger);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1reconnectIvl (JNIEnv *env, jclass c, jlong self)
{
    jint reconnect_ivl_ = (jint) zsock_reconnect_ivl ((zsock_t *) (intptr_t) self);
    return reconnect_ivl_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setReconnectIvl (JNIEnv *env, jclass c, jlong self, jint reconnect_ivl)
{
    zsock_set_reconnect_ivl ((zsock_t *) (intptr_t) self, (int) reconnect_ivl);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1reconnectIvlMax (JNIEnv *env, jclass c, jlong self)
{
    jint reconnect_ivl_max_ = (jint) zsock_reconnect_ivl_max ((zsock_t *) (intptr_t) self);
    return reconnect_ivl_max_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setReconnectIvlMax (JNIEnv *env, jclass c, jlong self, jint reconnect_ivl_max)
{
    zsock_set_reconnect_ivl_max ((zsock_t *) (intptr_t) self, (int) reconnect_ivl_max);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1backlog (JNIEnv *env, jclass c, jlong self)
{
    jint backlog_ = (jint) zsock_backlog ((zsock_t *) (intptr_t) self);
    return backlog_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setBacklog (JNIEnv *env, jclass c, jlong self, jint backlog)
{
    zsock_set_backlog ((zsock_t *) (intptr_t) self, (int) backlog);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setSubscribe (JNIEnv *env, jclass c, jlong self, jstring subscribe)
{
    char *subscribe_ = (char *) (*env)->GetStringUTFChars (env, subscribe, NULL);
    zsock_set_subscribe ((zsock_t *) (intptr_t) self, subscribe_);
    (*env)->ReleaseStringUTFChars (env, subscribe, subscribe_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1setUnsubscribe (JNIEnv *env, jclass c, jlong self, jstring unsubscribe)
{
    char *unsubscribe_ = (char *) (*env)->GetStringUTFChars (env, unsubscribe, NULL);
    zsock_set_unsubscribe ((zsock_t *) (intptr_t) self, unsubscribe_);
    (*env)->ReleaseStringUTFChars (env, unsubscribe, unsubscribe_);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1type (JNIEnv *env, jclass c, jlong self)
{
    jint type_ = (jint) zsock_type ((zsock_t *) (intptr_t) self);
    return type_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1rcvmore (JNIEnv *env, jclass c, jlong self)
{
    jint rcvmore_ = (jint) zsock_rcvmore ((zsock_t *) (intptr_t) self);
    return rcvmore_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zsock__1_1events (JNIEnv *env, jclass c, jlong self)
{
    jint events_ = (jint) zsock_events ((zsock_t *) (intptr_t) self);
    return events_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zsock__1_1test (JNIEnv *env, jclass c, jboolean verbose)
{
    zsock_test ((bool) verbose);
}

