/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#ifndef QML_ZFILE_H
#define QML_ZFILE_H

#include <QtQml>

#include <czmq.h>
#include "qml_czmq_plugin.h"


class QmlZfile : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isNULL READ isNULL)

public:
    zfile_t *self;

    QmlZfile() { self = NULL; }
    bool isNULL() { return self == NULL; }

    static QObject* qmlAttachedProperties(QObject* object); // defined in QmlZfile.cpp

public slots:
    //  Duplicate a file item, returns a newly constructed item. If the file
    //  is null, or memory was exhausted, returns null.
    QmlZfile *dup ();

    //  Return file name, remove path if provided
    const QString filename (const QString &path);

    //  Refresh file properties from disk; this is not done automatically
    //  on access methods, otherwise it is not possible to compare directory
    //  snapshots.
    void restat ();

    //  Return when the file was last modified. If you want this to reflect the
    //  current situation, call zfile_restat before checking this property.
    time_t modified ();

    //  Return the last-known size of the file. If you want this to reflect the
    //  current situation, call zfile_restat before checking this property.
    off_t cursize ();

    //  Return true if the file is a directory. If you want this to reflect
    //  any external changes, call zfile_restat before checking this property.
    bool isDirectory ();

    //  Return true if the file is a regular file. If you want this to reflect
    //  any external changes, call zfile_restat before checking this property.
    bool isRegular ();

    //  Return true if the file is readable by this process. If you want this to
    //  reflect any external changes, call zfile_restat before checking this
    //  property.
    bool isReadable ();

    //  Return true if the file is writeable by this process. If you want this
    //  to reflect any external changes, call zfile_restat before checking this
    //  property.
    bool isWriteable ();

    //  Check if file has stopped changing and can be safely processed.
    //  Updates the file statistics from disk at every call.
    bool isStable ();

    //  Return true if the file was changed on disk since the zfile_t object
    //  was created, or the last zfile_restat() call made on it.
    bool hasChanged ();

    //  Remove the file from disk
    void remove ();

    //  Open file for reading
    //  Returns 0 if OK, -1 if not found or not accessible
    int input ();

    //  Open file for writing, creating directory if needed
    //  File is created if necessary; chunks can be written to file at any
    //  location. Returns 0 if OK, -1 if error.
    int output ();

    //  Read chunk from file at specified position. If this was the last chunk,
    //  sets the eof property. Returns a null chunk in case of error.
    QmlZchunk *read (size_t bytes, off_t offset);

    //  Returns true if zfile_read() just read the last chunk in the file.
    bool eof ();

    //  Write chunk to file at specified position
    //  Return 0 if OK, else -1
    int write (QmlZchunk *chunk, off_t offset);

    //  Read next line of text from file. Returns a pointer to the text line,
    //  or NULL if there was nothing more to read from the file.
    const QString readln ();

    //  Close file, if open
    void close ();

    //  Return file handle, if opened
    FILE *handle ();

    //  Calculate SHA1 digest for file, using zdigest class.
    const QString digest ();
};

class QmlZfileAttached : public QObject
{
    Q_OBJECT
    QObject* m_attached;

public:
    QmlZfileAttached (QObject* attached) {
        Q_UNUSED (attached);
    };

public slots:
    //  Self test of this class.
    void test (bool verbose);

    //  If file exists, populates properties. CZMQ supports portable symbolic
    //  links, which are files with the extension ".ln". A symbolic link is a
    //  text file containing one line, the filename of a target file. Reading
    //  data from the symbolic link actually reads from the target file. Path
    //  may be NULL, in which case it is not used.
    QmlZfile *construct (const QString &path, const QString &name);

    //  Create new temporary file for writing via tmpfile. File is automatically
    //  deleted on destroy
    QmlZfile *tmp ();

    //  Destroy a file item
    void destruct (QmlZfile *qmlSelf);
};


QML_DECLARE_TYPEINFO(QmlZfile, QML_HAS_ATTACHED_PROPERTIES)

#endif
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
