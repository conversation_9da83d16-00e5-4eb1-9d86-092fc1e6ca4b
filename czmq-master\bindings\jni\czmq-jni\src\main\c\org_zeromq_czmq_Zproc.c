/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#include <stdio.h>
#include <stdlib.h>
#include <jni.h>
#include "czmq.h"
#include "org_zeromq_czmq_Zproc.h"

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zproc__1_1new (JNIEnv *env, jclass c)
{
    //  Disable CZMQ signal handling; allow Java to deal with it
    zsys_handler_set (NULL);
    jlong new_ = (jlong) (intptr_t) zproc_new ();
    return new_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zproc__1_1destroy (JNIEnv *env, jclass c, jlong self)
{
    zproc_destroy ((zproc_t **) &self);
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zproc__1_1args (JNIEnv *env, jclass c, jlong self)
{
    jlong args_ = (jlong) (intptr_t) zproc_args ((zproc_t *) (intptr_t) self);
    return args_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zproc__1_1setArgs (JNIEnv *env, jclass c, jlong self, jlong arguments)
{
    zproc_set_args ((zproc_t *) (intptr_t) self, (zlist_t **) (intptr_t) &arguments);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zproc__1_1setArgsx (JNIEnv *env, jclass c, jlong self, jstring arguments)
{
    char *arguments_ = (char *) (*env)->GetStringUTFChars (env, arguments, NULL);
    zproc_set_argsx ((zproc_t *) (intptr_t) self, arguments_, NULL);
    (*env)->ReleaseStringUTFChars (env, arguments, arguments_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zproc__1_1setEnv (JNIEnv *env, jclass c, jlong self, jlong arguments)
{
    zproc_set_env ((zproc_t *) (intptr_t) self, (zhash_t **) (intptr_t) &arguments);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zproc__1_1setStdin (JNIEnv *env, jclass c, jlong self, jlong socket)
{
    zproc_set_stdin ((zproc_t *) (intptr_t) self, (void *) (intptr_t) socket);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zproc__1_1setStdout (JNIEnv *env, jclass c, jlong self, jlong socket)
{
    zproc_set_stdout ((zproc_t *) (intptr_t) self, (void *) (intptr_t) socket);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zproc__1_1setStderr (JNIEnv *env, jclass c, jlong self, jlong socket)
{
    zproc_set_stderr ((zproc_t *) (intptr_t) self, (void *) (intptr_t) socket);
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zproc__1_1stdin (JNIEnv *env, jclass c, jlong self)
{
    jlong stdin_ = (jlong) (intptr_t) zproc_stdin ((zproc_t *) (intptr_t) self);
    return stdin_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zproc__1_1stdout (JNIEnv *env, jclass c, jlong self)
{
    jlong stdout_ = (jlong) (intptr_t) zproc_stdout ((zproc_t *) (intptr_t) self);
    return stdout_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zproc__1_1stderr (JNIEnv *env, jclass c, jlong self)
{
    jlong stderr_ = (jlong) (intptr_t) zproc_stderr ((zproc_t *) (intptr_t) self);
    return stderr_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zproc__1_1run (JNIEnv *env, jclass c, jlong self)
{
    jint run_ = (jint) zproc_run ((zproc_t *) (intptr_t) self);
    return run_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zproc__1_1returncode (JNIEnv *env, jclass c, jlong self)
{
    jint returncode_ = (jint) zproc_returncode ((zproc_t *) (intptr_t) self);
    return returncode_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zproc__1_1pid (JNIEnv *env, jclass c, jlong self)
{
    jint pid_ = (jint) zproc_pid ((zproc_t *) (intptr_t) self);
    return pid_;
}

JNIEXPORT jboolean JNICALL
Java_org_zeromq_czmq_Zproc__1_1running (JNIEnv *env, jclass c, jlong self)
{
    jboolean running_ = (jboolean) zproc_running ((zproc_t *) (intptr_t) self);
    return running_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zproc__1_1wait (JNIEnv *env, jclass c, jlong self, jint timeout)
{
    jint wait_ = (jint) zproc_wait ((zproc_t *) (intptr_t) self, (int) timeout);
    return wait_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zproc__1_1shutdown (JNIEnv *env, jclass c, jlong self, jint timeout)
{
    zproc_shutdown ((zproc_t *) (intptr_t) self, (int) timeout);
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zproc__1_1actor (JNIEnv *env, jclass c, jlong self)
{
    jlong actor_ = (jlong) (intptr_t) zproc_actor ((zproc_t *) (intptr_t) self);
    return actor_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zproc__1_1kill (JNIEnv *env, jclass c, jlong self, jint signal)
{
    zproc_kill ((zproc_t *) (intptr_t) self, (int) signal);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zproc__1_1setVerbose (JNIEnv *env, jclass c, jlong self, jboolean verbose)
{
    zproc_set_verbose ((zproc_t *) (intptr_t) self, (bool) verbose);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zproc__1_1test (JNIEnv *env, jclass c, jboolean verbose)
{
    zproc_test ((bool) verbose);
}

