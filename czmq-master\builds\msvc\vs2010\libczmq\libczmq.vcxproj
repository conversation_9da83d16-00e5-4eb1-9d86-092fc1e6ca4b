<?xml version="1.0" encoding="utf-8"?>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{0C4A2E28-8C9E-4B27-85D9-BB679AD84AC7}</ProjectGuid>
    <ProjectName>libczmq</ProjectName>
    <PlatformToolset>v100</PlatformToolset>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="DebugDLL|Win32">
      <Configuration>DebugDLL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseDLL|Win32">
      <Configuration>ReleaseDLL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugDLL|x64">
      <Configuration>DebugDLL</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseDLL|x64">
      <Configuration>ReleaseDLL</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugLTCG|Win32">
      <Configuration>DebugLTCG</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseLTCG|Win32">
      <Configuration>ReleaseLTCG</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugLTCG|x64">
      <Configuration>DebugLTCG</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseLTCG|x64">
      <Configuration>ReleaseLTCG</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugLIB|Win32">
      <Configuration>DebugLIB</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseLIB|Win32">
      <Configuration>ReleaseLIB</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugLIB|x64">
      <Configuration>DebugLIB</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseLIB|x64">
      <Configuration>ReleaseLIB</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Configuration">
    <ConfigurationType Condition="$(Configuration.IndexOf('DLL')) == -1">StaticLibrary</ConfigurationType>
    <ConfigurationType Condition="$(Configuration.IndexOf('DLL')) != -1">DynamicLibrary</ConfigurationType>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="PropertySheets">
    <Import Project="$(ProjectDir)..\..\properties\$(Configuration).props" />
    <Import Project="$(ProjectDir)..\..\properties\Output.props" />
    <Import Project="$(ProjectDir)$(ProjectName).props" />
  </ImportGroup>
  <ItemGroup>
    <ClInclude Include="..\..\platform.h" />
    <ClInclude Include="..\..\resource.h" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\include\czmq_library.h" />
    <ClInclude Include="..\..\..\..\include\czmq_prelude.h" />
    <ClInclude Include="..\..\..\..\include\czmq.h" />
    <ClInclude Include="..\..\..\..\src\zsock_option.inc" />
    <ClInclude Include="..\..\..\..\src\zgossip_engine.inc" />
    <ClInclude Include="..\..\..\..\src\zhash_primes.inc" />
    <ClInclude Include="..\..\..\..\src\foreign/sha1/sha1.inc_c" />
    <ClInclude Include="..\..\..\..\src\foreign/sha1/sha1.h" />
    <ClInclude Include="..\..\..\..\src\foreign/slre/slre.inc_c" />
    <ClInclude Include="..\..\..\..\src\foreign/slre/slre.h" />
    <ClInclude Include="..\..\..\..\src\foreign/slre/readme.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\src\zactor.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zargs.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zarmour.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zcert.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zcertstore.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zchunk.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zclock.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zconfig.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zdigest.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zdir.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zdir_patch.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zfile.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zframe.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhash.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhashx.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ziflist.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zlist.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zlistx.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zloop.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zmsg.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zpoller.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zproc.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zsock.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zstr.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zsys.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ztimerset.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ztrie.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zuuid.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhttp_client.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhttp_server.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhttp_server_options.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhttp_request.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhttp_response.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zosc.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zauth.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zbeacon.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zgossip.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zmonitor.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zproxy.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zrex.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zgossip_msg.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\czmq_private_selftest.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\..\resource.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\nuget\package.bat" />
    <None Include="..\..\nuget\package.nuspec" />
    <None Include="..\..\nuget\package.targets" />
    <None Include="..\..\nuget\package.xml" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
  -->
</Project>
