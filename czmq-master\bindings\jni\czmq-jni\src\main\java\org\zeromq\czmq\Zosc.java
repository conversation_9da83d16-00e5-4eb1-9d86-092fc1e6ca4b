/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
package org.zeromq.czmq;

import org.zeromq.tools.ZmqNativeLoader;

import java.util.LinkedHashMap;
import java.util.Map;

public class Zosc implements AutoCloseable {
    static {
        Map<String, Boolean> libraries = new LinkedHashMap<>();
        libraries.put("zmq", false);
        libraries.put("uuid", true);
        libraries.put("systemd", true);
        libraries.put("lz4", true);
        libraries.put("curl", true);
        libraries.put("nss", true);
        libraries.put("microhttpd", true);
        libraries.put("czmq", false);
        libraries.put("czmqjni", false);
        ZmqNativeLoader.loadLibraries(libraries);
    }
    public long self;
    /*
    Create a new empty OSC message with the specified address string.
    */
    native static long __new (String address);
    public Zosc (String address) {
        /*  TODO: if __new fails, self is null...            */
        self = __new (address);
    }
    public Zosc (long pointer) {
        self = pointer;
    }
    /*
    Create a new OSC message from the specified zframe. Takes ownership of
    the zframe.
    */
    native static long __fromframe (long frame);
    public static Zosc fromframe (Zframe frame) {
        return new Zosc (__fromframe (frame.self));
    }
    /*
    Create a new zosc message from memory. Take ownership of the memory
    and calling free on the data after construction.
    */
    native static long __frommem (byte [] data, long size);
    public static Zosc frommem (byte [] data, long size) {
        return new Zosc (__frommem (data, size));
    }
    /*
    Create a new zosc message from a string. This the same syntax as
    zosc_create but written as a single line string.
    */
    native static long __fromstring (String oscstring);
    public static Zosc fromstring (String oscstring) {
        return new Zosc (__fromstring (oscstring));
    }
    /*
    Create a new zosc message from the given format and arguments.
    The format type tags are as follows:
      i - 32bit integer
      h - 64bit integer
      f - 32bit floating point number (IEEE)
      d - 64bit (double) floating point number
      s - string (NULL terminated)
      t = timetag: an OSC timetag in NTP format (uint64_t)
      S - symbol
      c - char
      m - 4 byte midi packet (8 digits hexadecimal)
      T - TRUE (no value required)
      F - FALSE (no value required)
      N - NIL (no value required)
      I - Impulse (for triggers) or INFINITUM (no value required)
      b - binary blob
    */
    native static long __create (String address, String format);
    public static Zosc create (String address, String format) {
        return new Zosc (__create (address, format));
    }
    /*
    Destroy an OSC message
    */
    native static void __destroy (long self);
    @Override
    public void close () {
        __destroy (self);
        self = 0;
    }
    /*
    Return chunk data size
    */
    native static long __size (long self);
    public long size () {
        return __size (self);
    }
    /*
    Return OSC chunk data. Caller does not own the data!
    */
    native static byte [] __data (long self);
    public byte [] data () {
        return __data (self);
    }
    /*
    Return the OSC address string
    */
    native static String __address (long self);
    public String address () {
        return __address (self);
    }
    /*
    Return the OSC format of the message.
      i - 32bit integer
      h - 64bit integer
      f - 32bit floating point number (IEEE)
      d - 64bit (double) floating point number
      s - string (NULL terminated)
      t = timetag: an OSC timetag in NTP format (uint64_t)
      S - symbol
      c - char
      m - 4 byte midi packet (8 digits hexadecimal)
      T - TRUE (no value required)
      F - FALSE (no value required)
      N - NIL (no value required)
      I - Impulse (for triggers) or INFINITUM (no value required)
      b - binary blob
    */
    native static String __format (long self);
    public String format () {
        return __format (self);
    }
    /*
    Append data to the osc message. The format describes the data that
    needs to be appended in the message. This essentially relocates all
    data!
    The format type tags are as follows:
      i - 32bit integer
      h - 64bit integer
      f - 32bit floating point number (IEEE)
      d - 64bit (double) floating point number
      s - string (NULL terminated)
      t = timetag: an OSC timetag in NTP format (uint64_t)
      S - symbol
      c - char
      m - 4 byte midi packet (8 digits hexadecimal)
      T - TRUE (no value required)
      F - FALSE (no value required)
      N - NIL (no value required)
      I - Impulse (for triggers) or INFINITUM (no value required)
      b - binary blob
    */
    native static int __append (long self, String format);
    public int append (String format) {
        return __append (self, format);
    }
    /*
    Retrieve the values provided by the given format. Note that zosc_retr
    creates the objects and the caller must destroy them when finished.
    The supplied pointers do not need to be initialized. Returns 0 if
    successful, or -1 if it failed to retrieve a value in which case the
    pointers are not modified. If an argument pointer is NULL is skips the
    value. See the format method for a detailed list op type tags for the
    format string.
    */
    native static int __retr (long self, String format);
    public int retr (String format) {
        return __retr (self, format);
    }
    /*
    Create copy of the message, as new chunk object. Returns a fresh zosc_t
    object, or null if there was not enough heap memory. If chunk is null,
    returns null.
    */
    native static long __dup (long self);
    public Zosc dup () {
        return new Zosc (__dup (self));
    }
    /*
    Transform zosc into a zframe that can be sent in a message.
    */
    native static long __pack (long self);
    public Zframe pack () {
        return new Zframe (__pack (self));
    }
    /*
    Transform zosc into a zframe that can be sent in a message.
    Take ownership of the chunk.
    */
    native static long __packx (long self);
    public void packx () {
        self = __packx (self);
    }
    /*
    Transform a zframe into a zosc.
    */
    native static long __unpack (long frame);
    public static Zosc unpack (Zframe frame) {
        return new Zosc (__unpack (frame.self));
    }
    /*
    Return a string describing the the OSC message. The returned string must be freed by the caller.
    */
    native static String __dump (long self);
    public String dump () {
        return __dump (self);
    }
    /*
    Dump OSC message to stdout, for debugging and tracing.
    */
    native static void __print (long self);
    public void print () {
        __print (self);
    }
    /*
    Probe the supplied object, and report if it looks like a zosc_t.
    */
    native static boolean __is (long self);
    public static boolean is (long self) {
        return __is (self);
    }
    /*
    Return a pointer to the item at the head of the OSC data.
    Sets the given char argument to the type tag of the data.
    If the message is empty, returns NULL and the sets the
    given char to NULL.
    */
    native static long __first (long self, char type);
    public long first (char type) {
        return __first (self, type);
    }
    /*
    Return the next item of the OSC message. If the list is empty, returns
    NULL. To move to the start of the OSC message call zosc_first ().
    */
    native static long __next (long self, char type);
    public long next (char type) {
        return __next (self, type);
    }
    /*
    Return a pointer to the item at the tail of the OSC message.
    Sets the given char argument to the type tag of the data. If
    the message is empty, returns NULL.
    */
    native static long __last (long self, char type);
    public long last (char type) {
        return __last (self, type);
    }
    /*
    Set the provided 32 bit integer from value at the current cursor position in the message.
    If the type tag at the current position does not correspond it will fail and
    return -1. Returns 0 on success.
    */
    native static int __popInt32 (long self, int val);
    public int popInt32 (int val) {
        return __popInt32 (self, val);
    }
    /*
    Set the provided 64 bit integer from the value at the current cursor position in the message.
    If the type tag at the current position does not correspond it will fail and
    return -1. Returns 0 on success.
    */
    native static int __popInt64 (long self, long val);
    public int popInt64 (long val) {
        return __popInt64 (self, val);
    }
    /*
    Set the provided float from the value at the current cursor position in the message.
    If the type tag at the current position does not correspond it will fail and
    return -1. Returns 0 on success.
    */
    native static int __popFloat (long self, float val);
    public int popFloat (float val) {
        return __popFloat (self, val);
    }
    /*
    Set the provided double from the value at the current cursor position in the message.
    If the type tag at the current position does not correspond it will fail and
    return -1. Returns 0 on success.
    */
    native static int __popDouble (long self, double val);
    public int popDouble (double val) {
        return __popDouble (self, val);
    }
    /*
    Set the provided char from the value at the current cursor position in the message.
    If the type tag at the current position does not correspond it will fail and
    return -1. Returns 0 on success.
    */
    native static int __popChar (long self, char val);
    public int popChar (char val) {
        return __popChar (self, val);
    }
    /*
    Set the provided boolean from the type tag in the message. Booleans are not represented
    in the data in the message, only in the type tag. If the type tag at the current
    position does not correspond it will fail and return -1. Returns 0 on success.
    */
    native static int __popBool (long self, boolean val);
    public int popBool (boolean val) {
        return __popBool (self, val);
    }
    /*
    Set the provided 4 bytes (unsigned 32bit int) from the value at the current
    cursor position in the message. If the type tag at the current position does
    not correspond it will fail and return -1. Returns 0 on success.
    */
    native static int __popMidi (long self, int val);
    public int popMidi (int val) {
        return __popMidi (self, val);
    }
    /*
    Self test of this class.
    */
    native static void __test (boolean verbose);
    public static void test (boolean verbose) {
        __test (verbose);
    }
}
