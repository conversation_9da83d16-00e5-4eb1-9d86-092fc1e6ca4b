<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <ImportGroup Label="PropertySheets">
    <Import Project="Common.props" />
  </ImportGroup>

  <PropertyGroup>
    <_PropertySheetDisplayName>Release Settings</_PropertySheetDisplayName>
    <DebugOrRelease>Release</DebugOrRelease>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>

  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalOptions>/Oy- %(AdditionalOptions)</AdditionalOptions>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild>false</MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Processor)' == 'x86'">
    <ClCompile>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
    </ClCompile>
  </ItemDefinitionGroup>

</Project>
