/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#ifndef QML_ZHTTP_SERVER_OPTIONS_H
#define QML_ZHTTP_SERVER_OPTIONS_H

#include <QtQml>

#include <czmq.h>
#include "qml_czmq_plugin.h"


class QmlZhttpServerOptions : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isNULL READ isNULL)

public:
    zhttp_server_options_t *self;

    QmlZhttpServerOptions() { self = NULL; }
    bool isNULL() { return self == NULL; }

    static QObject* qmlAttachedProperties(QObject* object); // defined in QmlZhttpServerOptions.cpp

public slots:
    //  Get the server listening port.
    int port ();

    //  Set the server listening port
    void setPort (int port);

    //  Get the address sockets should connect to in order to receive requests.
    const QString backendAddress ();

    //  Set the address sockets should connect to in order to receive requests.
    void setBackendAddress (const QString &address);
};

class QmlZhttpServerOptionsAttached : public QObject
{
    Q_OBJECT
    QObject* m_attached;

public:
    QmlZhttpServerOptionsAttached (QObject* attached) {
        Q_UNUSED (attached);
    };

public slots:
    //  Self test of this class.
    void test (bool verbose);

    //  Create a new zhttp_server_options.
    QmlZhttpServerOptions *construct ();

    //  Create options from config tree.
    QmlZhttpServerOptions *fromConfig (QmlZconfig *config);

    //  Destroy the zhttp_server_options.
    void destruct (QmlZhttpServerOptions *qmlSelf);
};


QML_DECLARE_TYPEINFO(QmlZhttpServerOptions, QML_HAS_ATTACHED_PROPERTIES)

#endif
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
