/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZcertstore::QZcertstore (zcertstore_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Create a new certificate store from a disk directory, loading and
//  indexing all certificates in that location. The directory itself may be
//  absent, and created later, or modified at any time. The certificate store
//  is automatically refreshed on any zcertstore_lookup() call. If the
//  location is specified as NULL, creates a pure-memory store, which you
//  can work with by inserting certificates at runtime.
QZcertstore::QZcertstore (const QString &location, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zcertstore_new (location.toUtf8().data());
}

///
//  Destroy a certificate store object in memory. Does not affect anything
//  stored on disk.
QZcertstore::~QZcertstore ()
{
    zcertstore_destroy (&self);
}

///
//  Override the default disk loader with a custom loader fn.
void QZcertstore::setLoader (zcertstore_loader loader, zcertstore_destructor destructor, void *state)
{
    zcertstore_set_loader (self, loader, destructor, state);

}

///
//  Look up certificate by public key, returns zcert_t object if found,
//  else returns NULL. The public key is provided in Z85 text format.
QZcert * QZcertstore::lookup (const QString &publicKey)
{
    QZcert *rv = new QZcert (zcertstore_lookup (self, publicKey.toUtf8().data()));
    return rv;
}

///
//  Insert certificate into certificate store in memory. Note that this
//  does not save the certificate to disk. To do that, use zcert_save()
//  directly on the certificate. Takes ownership of zcert_t object.
void QZcertstore::insert (QZcert *certP)
{
    zcertstore_insert (self, &certP->self);

}

///
//  Empty certificate hashtable. This wrapper exists to be friendly to bindings,
//  which don't usually have access to struct internals.
void QZcertstore::empty ()
{
    zcertstore_empty (self);

}

///
//  Print list of certificates in store to logging facility
void QZcertstore::print ()
{
    zcertstore_print (self);

}

///
//  Return a list of all the certificates in the store.
//  The caller takes ownership of the zlistx_t object and is responsible
//  for destroying it.  The caller does not take ownership of the zcert_t
//  objects.
QZlistx * QZcertstore::certs ()
{
    QZlistx *rv = new QZlistx (zcertstore_certs (self));
    return rv;
}

///
//  Return the state stored in certstore
void * QZcertstore::state ()
{
    void * rv = zcertstore_state (self);
    return rv;
}

///
//  Self test of this class
void QZcertstore::test (bool verbose)
{
    zcertstore_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
