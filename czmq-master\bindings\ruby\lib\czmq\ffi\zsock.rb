################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

module CZMQ
  module FFI

    # high-level socket API that hides libzmq contexts and sockets
    # @note This class is 100% generated using zproject.
    class Zsock
      # Raised when one tries to use an instance of {Zsock} after
      # the internal pointer to the native object has been nullified.
      class DestroyedError < RuntimeError; end

      # Boilerplate for self pointer, initializer, and finalizer
      class << self
        alias :__new :new
      end
      # Attaches the pointer _ptr_ to this instance and defines a finalizer for
      # it if necessary.
      # @param ptr [::FFI::Pointer]
      # @param finalize [Boolean]
      def initialize(ptr, finalize = true)
        @ptr = ptr
        if @ptr.null?
          @ptr = nil # Remove null pointers so we don't have to test for them.
        elsif finalize
          @finalizer = self.class.create_finalizer_for @ptr
          ObjectSpace.define_finalizer self, @finalizer
        end
      end
      # @param ptr [::FFI::Pointer]
      # @return [Proc]
      def self.create_finalizer_for(ptr)
        ptr_ptr = ::FFI::MemoryPointer.new :pointer

        Proc.new do
          ptr_ptr.write_pointer ptr
          ::CZMQ::FFI.zsock_destroy ptr_ptr
        end
      end
      # @return [Boolean]
      def null?
        !@ptr or @ptr.null?
      end
      # Return internal pointer
      # @return [::FFI::Pointer]
      def __ptr
        raise DestroyedError unless @ptr
        @ptr
      end
      # So external Libraries can just pass the Object to a FFI function which expects a :pointer
      alias_method :to_ptr, :__ptr
      # Nullify internal pointer and return pointer pointer.
      # @note This detaches the current instance from the native object
      #   and thus makes it unusable.
      # @return [::FFI::MemoryPointer] the pointer pointing to a pointer
      #   pointing to the native object
      def __ptr_give_ref
        raise DestroyedError unless @ptr
        ptr_ptr = ::FFI::MemoryPointer.new :pointer
        ptr_ptr.write_pointer @ptr
        __undef_finalizer if @finalizer
        @ptr = nil
        ptr_ptr
      end
      # Undefines the finalizer for this object.
      # @note Only use this if you need to and can guarantee that the native
      #   object will be freed by other means.
      # @return [void]
      def __undef_finalizer
        ObjectSpace.undefine_finalizer self
        @finalizer = nil
      end

      # Create a new socket. Returns the new socket, or NULL if the new socket
      # could not be created. Note that the symbol zsock_new (and other
      # constructors/destructors for zsock) are redirected to the *_checked
      # variant, enabling intelligent socket leak detection. This can have
      # performance implications if you use a LOT of sockets. To turn off this
      # redirection behaviour, define ZSOCK_NOCHECK.
      # @param type [Integer, #to_int, #to_i]
      # @return [CZMQ::Zsock]
      def self.new(type)
        type = Integer(type)
        ptr = ::CZMQ::FFI.zsock_new(type)
        __new ptr
      end

      # Create a PUB socket. Default action is bind.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_pub(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_pub(endpoint)
        __new ptr
      end

      # Create a SUB socket, and optionally subscribe to some prefix string. Default
      # action is connect.
      # @param endpoint [String, #to_s, nil]
      # @param subscribe [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_sub(endpoint, subscribe)
        ptr = ::CZMQ::FFI.zsock_new_sub(endpoint, subscribe)
        __new ptr
      end

      # Create a REQ socket. Default action is connect.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_req(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_req(endpoint)
        __new ptr
      end

      # Create a REP socket. Default action is bind.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_rep(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_rep(endpoint)
        __new ptr
      end

      # Create a DEALER socket. Default action is connect.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_dealer(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_dealer(endpoint)
        __new ptr
      end

      # Create a ROUTER socket. Default action is bind.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_router(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_router(endpoint)
        __new ptr
      end

      # Create a PUSH socket. Default action is connect.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_push(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_push(endpoint)
        __new ptr
      end

      # Create a PULL socket. Default action is bind.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_pull(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_pull(endpoint)
        __new ptr
      end

      # Create an XPUB socket. Default action is bind.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_xpub(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_xpub(endpoint)
        __new ptr
      end

      # Create an XSUB socket. Default action is connect.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_xsub(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_xsub(endpoint)
        __new ptr
      end

      # Create a PAIR socket. Default action is connect.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_pair(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_pair(endpoint)
        __new ptr
      end

      # Create a STREAM socket. Default action is connect.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_stream(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_stream(endpoint)
        __new ptr
      end

      # Create a SERVER socket. Default action is bind.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_server(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_server(endpoint)
        __new ptr
      end

      # Create a CLIENT socket. Default action is connect.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_client(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_client(endpoint)
        __new ptr
      end

      # Create a RADIO socket. Default action is bind.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_radio(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_radio(endpoint)
        __new ptr
      end

      # Create a DISH socket. Default action is connect.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_dish(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_dish(endpoint)
        __new ptr
      end

      # Create a GATHER socket. Default action is bind.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_gather(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_gather(endpoint)
        __new ptr
      end

      # Create a SCATTER socket. Default action is connect.
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_scatter(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_scatter(endpoint)
        __new ptr
      end

      # Create a DGRAM (UDP) socket. Default action is bind.
      # The endpoint is a string consisting of a
      # 'transport'`://` followed by an 'address'. As this is
      # a UDP socket the 'transport' has to be 'udp'. The
      # 'address' specifies the ip address and port to
      # bind to. For example:  udp://127.0.0.1:1234
      # Note: To send to an endpoint over UDP you have to
      # send a message with the destination endpoint address
      # as a first message!
      # @param endpoint [String, #to_s, nil]
      # @return [CZMQ::Zsock]
      def self.new_dgram(endpoint)
        ptr = ::CZMQ::FFI.zsock_new_dgram(endpoint)
        __new ptr
      end

      # Destroy the socket. You must use this for any socket created via the
      # zsock_new method.
      #
      # @return [void]
      def destroy()
        return unless @ptr
        self_p = __ptr_give_ref
        result = ::CZMQ::FFI.zsock_destroy(self_p)
        result
      end

      # Bind a socket to a formatted endpoint. For tcp:// endpoints, supports
      # ephemeral ports, if you specify the port number as "*". By default
      # zsock uses the IANA designated range from C000 (49152) to FFFF (65535).
      # To override this range, follow the "*" with "[first-last]". Either or
      # both first and last may be empty. To bind to a random port within the
      # range, use "!" in place of "*".
      #
      # Examples:
      #     tcp://127.0.0.1:*           bind to first free port from C000 up
      #     tcp://127.0.0.1:!           bind to random port from C000 to FFFF
      #     tcp://127.0.0.1:*[60000-]   bind to first free port from 60000 up
      #     tcp://127.0.0.1:![-60000]   bind to random port from C000 to 60000
      #     tcp://127.0.0.1:![55000-55999]
      #                                 bind to random port from 55000 to 55999
      #
      # On success, returns the actual port number used, for tcp:// endpoints,
      # and 0 for other transports. On failure, returns -1. Note that when using
      # ephemeral ports, a port may be reused by different services without
      # clients being aware. Protocols that run on ephemeral ports should take
      # this into account.
      #
      # @param format [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def bind(format, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_bind(self_p, format, *args)
        result
      end

      # Returns last bound endpoint, if any.
      #
      # @return [String]
      def endpoint()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_endpoint(self_p)
        result
      end

      # Unbind a socket from a formatted endpoint.
      # Returns 0 if OK, -1 if the endpoint was invalid or the function
      # isn't supported.
      #
      # @param format [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def unbind(format, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_unbind(self_p, format, *args)
        result
      end

      # Connect a socket to a formatted endpoint
      # Returns 0 if OK, -1 if the endpoint was invalid.
      #
      # @param format [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def connect(format, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_connect(self_p, format, *args)
        result
      end

      # Disconnect a socket from a formatted endpoint
      # Returns 0 if OK, -1 if the endpoint was invalid or the function
      # isn't supported.
      #
      # @param format [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def disconnect(format, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_disconnect(self_p, format, *args)
        result
      end

      # Attach a socket to zero or more endpoints. If endpoints is not null,
      # parses as list of ZeroMQ endpoints, separated by commas, and prefixed by
      # '@' (to bind the socket) or '>' (to connect the socket). Returns 0 if all
      # endpoints were valid, or -1 if there was a syntax error. If the endpoint
      # does not start with '@' or '>', the serverish argument defines whether
      # it is used to bind (serverish = true) or connect (serverish = false).
      #
      # @param endpoints [String, #to_s, nil]
      # @param serverish [Boolean]
      # @return [Integer]
      def attach(endpoints, serverish)
        raise DestroyedError unless @ptr
        self_p = @ptr
        serverish = !(0==serverish||!serverish) # boolean
        result = ::CZMQ::FFI.zsock_attach(self_p, endpoints, serverish)
        result
      end

      # Returns socket type as printable constant string.
      #
      # @return [String]
      def type_str()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_type_str(self_p)
        result
      end

      # Send a 'picture' message to the socket (or actor). The picture is a
      # string that defines the type of each frame. This makes it easy to send
      # a complex multiframe message in one call. The picture can contain any
      # of these characters, each corresponding to one or two arguments:
      #
      #     i = int (signed)
      #     1 = uint8_t
      #     2 = uint16_t
      #     4 = uint32_t
      #     8 = uint64_t
      #     s = char *
      #     b = byte *, size_t (2 arguments)
      #     c = zchunk_t *
      #     f = zframe_t *
      #     h = zhashx_t *
      #     l = zlistx_t * (DRAFT)
      #     U = zuuid_t *
      #     p = void * (sends the pointer value, only meaningful over inproc)
      #     m = zmsg_t * (sends all frames in the zmsg)
      #     z = sends zero-sized frame (0 arguments)
      #     u = uint (deprecated)
      #
      # Note that s, b, c, and f are encoded the same way and the choice is
      # offered as a convenience to the sender, which may or may not already
      # have data in a zchunk or zframe. Does not change or take ownership of
      # any arguments. Returns 0 if successful, -1 if sending failed for any
      # reason.
      #
      # @param picture [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def send(picture, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_send(self_p, picture, *args)
        result
      end

      # Send a 'picture' message to the socket (or actor). The picture is a
      # string that defines the type of each frame. This makes it easy to send
      # a complex multiframe message in one call. The picture can contain any
      # of these characters, each corresponding to one or two arguments:
      #
      #     i = int (signed)
      #     1 = uint8_t
      #     2 = uint16_t
      #     4 = uint32_t
      #     8 = uint64_t
      #     s = char *
      #     b = byte *, size_t (2 arguments)
      #     c = zchunk_t *
      #     f = zframe_t *
      #     h = zhashx_t *
      #     l = zlistx_t * (DRAFT)
      #     U = zuuid_t *
      #     p = void * (sends the pointer value, only meaningful over inproc)
      #     m = zmsg_t * (sends all frames in the zmsg)
      #     z = sends zero-sized frame (0 arguments)
      #     u = uint (deprecated)
      #
      # Note that s, b, c, and f are encoded the same way and the choice is
      # offered as a convenience to the sender, which may or may not already
      # have data in a zchunk or zframe. Does not change or take ownership of
      # any arguments. Returns 0 if successful, -1 if sending failed for any
      # reason.
      #
      # This is the polymorphic version of #send.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param picture [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def self.send(self_p, picture, *args)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_send(self_p, picture, *args)
        result
      end

      # Send a 'picture' message to the socket (or actor). This is a va_list
      # version of zsock_send (), so please consult its documentation for the
      # details.
      #
      # @param picture [String, #to_s, nil]
      # @param argptr [::FFI::Pointer, #to_ptr]
      # @return [Integer]
      def vsend(picture, argptr)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_vsend(self_p, picture, argptr)
        result
      end

      # Send a 'picture' message to the socket (or actor). This is a va_list
      # version of zsock_send (), so please consult its documentation for the
      # details.
      #
      # This is the polymorphic version of #vsend.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param picture [String, #to_s, nil]
      # @param argptr [::FFI::Pointer, #to_ptr]
      # @return [Integer]
      def self.vsend(self_p, picture, argptr)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_vsend(self_p, picture, argptr)
        result
      end

      # Receive a 'picture' message to the socket (or actor). See zsock_send for
      # the format and meaning of the picture. Returns the picture elements into
      # a series of pointers as provided by the caller:
      #
      #     i = int * (stores signed integer)
      #     4 = uint32_t * (stores 32-bit unsigned integer)
      #     8 = uint64_t * (stores 64-bit unsigned integer)
      #     s = char ** (allocates new string)
      #     b = byte **, size_t * (2 arguments) (allocates memory)
      #     c = zchunk_t ** (creates zchunk)
      #     f = zframe_t ** (creates zframe)
      #     U = zuuid_t * (creates a zuuid with the data)
      #     h = zhashx_t ** (creates zhashx)
      #     l = zlistx_t ** (creates zlistx) (DRAFT)
      #     p = void ** (stores pointer)
      #     m = zmsg_t ** (creates a zmsg with the remaining frames)
      #     z = null, asserts empty frame (0 arguments)
      #     u = uint * (stores unsigned integer, deprecated)
      #
      # Note that zsock_recv creates the returned objects, and the caller must
      # destroy them when finished with them. The supplied pointers do not need
      # to be initialized. Returns 0 if successful, or -1 if it failed to recv
      # a message, in which case the pointers are not modified. When message
      # frames are truncated (a short message), sets return values to zero/null.
      # If an argument pointer is NULL, does not store any value (skips it).
      # An 'n' picture matches an empty frame; if the message does not match,
      # the method will return -1.
      #
      # @param picture [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def recv(picture, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_recv(self_p, picture, *args)
        result
      end

      # Receive a 'picture' message to the socket (or actor). See zsock_send for
      # the format and meaning of the picture. Returns the picture elements into
      # a series of pointers as provided by the caller:
      #
      #     i = int * (stores signed integer)
      #     4 = uint32_t * (stores 32-bit unsigned integer)
      #     8 = uint64_t * (stores 64-bit unsigned integer)
      #     s = char ** (allocates new string)
      #     b = byte **, size_t * (2 arguments) (allocates memory)
      #     c = zchunk_t ** (creates zchunk)
      #     f = zframe_t ** (creates zframe)
      #     U = zuuid_t * (creates a zuuid with the data)
      #     h = zhashx_t ** (creates zhashx)
      #     l = zlistx_t ** (creates zlistx) (DRAFT)
      #     p = void ** (stores pointer)
      #     m = zmsg_t ** (creates a zmsg with the remaining frames)
      #     z = null, asserts empty frame (0 arguments)
      #     u = uint * (stores unsigned integer, deprecated)
      #
      # Note that zsock_recv creates the returned objects, and the caller must
      # destroy them when finished with them. The supplied pointers do not need
      # to be initialized. Returns 0 if successful, or -1 if it failed to recv
      # a message, in which case the pointers are not modified. When message
      # frames are truncated (a short message), sets return values to zero/null.
      # If an argument pointer is NULL, does not store any value (skips it).
      # An 'n' picture matches an empty frame; if the message does not match,
      # the method will return -1.
      #
      # This is the polymorphic version of #recv.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param picture [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def self.recv(self_p, picture, *args)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_recv(self_p, picture, *args)
        result
      end

      # Receive a 'picture' message from the socket (or actor). This is a
      # va_list version of zsock_recv (), so please consult its documentation
      # for the details.
      #
      # @param picture [String, #to_s, nil]
      # @param argptr [::FFI::Pointer, #to_ptr]
      # @return [Integer]
      def vrecv(picture, argptr)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_vrecv(self_p, picture, argptr)
        result
      end

      # Receive a 'picture' message from the socket (or actor). This is a
      # va_list version of zsock_recv (), so please consult its documentation
      # for the details.
      #
      # This is the polymorphic version of #vrecv.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param picture [String, #to_s, nil]
      # @param argptr [::FFI::Pointer, #to_ptr]
      # @return [Integer]
      def self.vrecv(self_p, picture, argptr)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_vrecv(self_p, picture, argptr)
        result
      end

      # Send a binary encoded 'picture' message to the socket (or actor). This
      # method is similar to zsock_send, except the arguments are encoded in a
      # binary format that is compatible with zproto, and is designed to reduce
      # memory allocations. The pattern argument is a string that defines the
      # type of each argument. Supports these argument types:
      #
      #  pattern    C type                  zproto type:
      #     1       uint8_t                 type = "number" size = "1"
      #     2       uint16_t                type = "number" size = "2"
      #     4       uint32_t                type = "number" size = "3"
      #     8       uint64_t                type = "number" size = "4"
      #     s       char *, 0-255 chars     type = "string"
      #     S       char *, 0-2^32-1 chars  type = "longstr"
      #     c       zchunk_t *              type = "chunk"
      #     f       zframe_t *              type = "frame"
      #     u       zuuid_t *               type = "uuid"
      #     m       zmsg_t *                type = "msg"
      #     p       void *, sends pointer value, only over inproc
      #
      # Does not change or take ownership of any arguments. Returns 0 if
      # successful, -1 if sending failed for any reason.
      #
      # @param picture [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def bsend(picture, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_bsend(self_p, picture, *args)
        result
      end

      # Send a binary encoded 'picture' message to the socket (or actor). This
      # method is similar to zsock_send, except the arguments are encoded in a
      # binary format that is compatible with zproto, and is designed to reduce
      # memory allocations. The pattern argument is a string that defines the
      # type of each argument. Supports these argument types:
      #
      #  pattern    C type                  zproto type:
      #     1       uint8_t                 type = "number" size = "1"
      #     2       uint16_t                type = "number" size = "2"
      #     4       uint32_t                type = "number" size = "3"
      #     8       uint64_t                type = "number" size = "4"
      #     s       char *, 0-255 chars     type = "string"
      #     S       char *, 0-2^32-1 chars  type = "longstr"
      #     c       zchunk_t *              type = "chunk"
      #     f       zframe_t *              type = "frame"
      #     u       zuuid_t *               type = "uuid"
      #     m       zmsg_t *                type = "msg"
      #     p       void *, sends pointer value, only over inproc
      #
      # Does not change or take ownership of any arguments. Returns 0 if
      # successful, -1 if sending failed for any reason.
      #
      # This is the polymorphic version of #bsend.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param picture [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def self.bsend(self_p, picture, *args)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_bsend(self_p, picture, *args)
        result
      end

      # Receive a binary encoded 'picture' message from the socket (or actor).
      # This method is similar to zsock_recv, except the arguments are encoded
      # in a binary format that is compatible with zproto, and is designed to
      # reduce memory allocations. The pattern argument is a string that defines
      # the type of each argument. See zsock_bsend for the supported argument
      # types. All arguments must be pointers; this call sets them to point to
      # values held on a per-socket basis.
      # For types 1, 2, 4 and 8 the caller must allocate the memory itself before
      # calling zsock_brecv.
      # For types S, the caller must free the value once finished with it, as
      # zsock_brecv will allocate the buffer.
      # For type s, the caller must not free the value as it is stored in a
      # local cache for performance purposes.
      # For types c, f, u and m the caller must call the appropriate destructor
      # depending on the object as zsock_brecv will create new objects.
      # For type p the caller must coordinate with the sender, as it is just a
      # pointer value being passed.
      #
      # @param picture [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def brecv(picture, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_brecv(self_p, picture, *args)
        result
      end

      # Receive a binary encoded 'picture' message from the socket (or actor).
      # This method is similar to zsock_recv, except the arguments are encoded
      # in a binary format that is compatible with zproto, and is designed to
      # reduce memory allocations. The pattern argument is a string that defines
      # the type of each argument. See zsock_bsend for the supported argument
      # types. All arguments must be pointers; this call sets them to point to
      # values held on a per-socket basis.
      # For types 1, 2, 4 and 8 the caller must allocate the memory itself before
      # calling zsock_brecv.
      # For types S, the caller must free the value once finished with it, as
      # zsock_brecv will allocate the buffer.
      # For type s, the caller must not free the value as it is stored in a
      # local cache for performance purposes.
      # For types c, f, u and m the caller must call the appropriate destructor
      # depending on the object as zsock_brecv will create new objects.
      # For type p the caller must coordinate with the sender, as it is just a
      # pointer value being passed.
      #
      # This is the polymorphic version of #brecv.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param picture [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Integer]
      def self.brecv(self_p, picture, *args)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_brecv(self_p, picture, *args)
        result
      end

      # Return socket routing ID if any. This returns 0 if the socket is not
      # of type ZMQ_SERVER or if no request was already received on it.
      #
      # @return [Integer]
      def routing_id()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_routing_id(self_p)
        result
      end

      # Set routing ID on socket. The socket MUST be of type ZMQ_SERVER.
      # This will be used when sending messages on the socket via the zsock API.
      #
      # @param routing_id [Integer, #to_int, #to_i]
      # @return [void]
      def set_routing_id(routing_id)
        raise DestroyedError unless @ptr
        self_p = @ptr
        routing_id = Integer(routing_id)
        result = ::CZMQ::FFI.zsock_set_routing_id(self_p, routing_id)
        result
      end

      # Set socket to use unbounded pipes (HWM=0); use this in cases when you are
      # totally certain the message volume can fit in memory. This method works
      # across all versions of ZeroMQ. Takes a polymorphic socket reference.
      #
      # @return [void]
      def set_unbounded()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_unbounded(self_p)
        result
      end

      # Set socket to use unbounded pipes (HWM=0); use this in cases when you are
      # totally certain the message volume can fit in memory. This method works
      # across all versions of ZeroMQ. Takes a polymorphic socket reference.
      #
      # This is the polymorphic version of #set_unbounded.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [void]
      def self.set_unbounded(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_unbounded(self_p)
        result
      end

      # Send a signal over a socket. A signal is a short message carrying a
      # success/failure code (by convention, 0 means OK). Signals are encoded
      # to be distinguishable from "normal" messages. Accepts a zsock_t or a
      # zactor_t argument, and returns 0 if successful, -1 if the signal could
      # not be sent. Takes a polymorphic socket reference.
      #
      # @param status [Integer, #to_int, #to_i]
      # @return [Integer]
      def signal(status)
        raise DestroyedError unless @ptr
        self_p = @ptr
        status = Integer(status)
        result = ::CZMQ::FFI.zsock_signal(self_p, status)
        result
      end

      # Send a signal over a socket. A signal is a short message carrying a
      # success/failure code (by convention, 0 means OK). Signals are encoded
      # to be distinguishable from "normal" messages. Accepts a zsock_t or a
      # zactor_t argument, and returns 0 if successful, -1 if the signal could
      # not be sent. Takes a polymorphic socket reference.
      #
      # This is the polymorphic version of #signal.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param status [Integer, #to_int, #to_i]
      # @return [Integer]
      def self.signal(self_p, status)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        status = Integer(status)
        result = ::CZMQ::FFI.zsock_signal(self_p, status)
        result
      end

      # Wait on a signal. Use this to coordinate between threads, over pipe
      # pairs. Blocks until the signal is received. Returns -1 on error, 0 or
      # greater on success. Accepts a zsock_t or a zactor_t as argument.
      # Takes a polymorphic socket reference.
      #
      # @return [Integer]
      def wait()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_wait(self_p)
        result
      end

      # Wait on a signal. Use this to coordinate between threads, over pipe
      # pairs. Blocks until the signal is received. Returns -1 on error, 0 or
      # greater on success. Accepts a zsock_t or a zactor_t as argument.
      # Takes a polymorphic socket reference.
      #
      # This is the polymorphic version of #wait.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.wait(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_wait(self_p)
        result
      end

      # If there is a partial message still waiting on the socket, remove and
      # discard it. This is useful when reading partial messages, to get specific
      # message types.
      #
      # @return [void]
      def flush()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_flush(self_p)
        result
      end

      # If there is a partial message still waiting on the socket, remove and
      # discard it. This is useful when reading partial messages, to get specific
      # message types.
      #
      # This is the polymorphic version of #flush.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [void]
      def self.flush(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_flush(self_p)
        result
      end

      # Join a group for the RADIO-DISH pattern. Call only on ZMQ_DISH.
      # Returns 0 if OK, -1 if failed.
      #
      # @param group [String, #to_s, nil]
      # @return [Integer]
      def join(group)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_join(self_p, group)
        result
      end

      # Join a group for the RADIO-DISH pattern. Call only on ZMQ_DISH.
      # Returns 0 if OK, -1 if failed.
      #
      # This is the polymorphic version of #join.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param group [String, #to_s, nil]
      # @return [Integer]
      def self.join(self_p, group)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_join(self_p, group)
        result
      end

      # Leave a group for the RADIO-DISH pattern. Call only on ZMQ_DISH.
      # Returns 0 if OK, -1 if failed.
      #
      # @param group [String, #to_s, nil]
      # @return [Integer]
      def leave(group)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_leave(self_p, group)
        result
      end

      # Leave a group for the RADIO-DISH pattern. Call only on ZMQ_DISH.
      # Returns 0 if OK, -1 if failed.
      #
      # This is the polymorphic version of #leave.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param group [String, #to_s, nil]
      # @return [Integer]
      def self.leave(self_p, group)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_leave(self_p, group)
        result
      end

      # Probe the supplied object, and report if it looks like a zsock_t.
      # Takes a polymorphic socket reference.
      #
      # @param self_ [::FFI::Pointer, #to_ptr]
      # @return [Boolean]
      def self.is(self_)
        result = ::CZMQ::FFI.zsock_is(self_)
        result
      end

      # Probe the supplied reference. If it looks like a zsock_t instance, return
      # the underlying libzmq socket handle; else if it looks like a file
      # descriptor, return NULL; else if it looks like a libzmq socket handle,
      # return the supplied value. Takes a polymorphic socket reference.
      #
      # @param self_ [::FFI::Pointer, #to_ptr]
      # @return [::FFI::Pointer]
      def self.resolve(self_)
        result = ::CZMQ::FFI.zsock_resolve(self_)
        result
      end

      # Check whether the socket has available message to read.
      #
      # @return [Boolean]
      def has_in()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_has_in(self_p)
        result
      end

      # Check whether the socket has available message to read.
      #
      # This is the polymorphic version of #has_in.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Boolean]
      def self.has_in(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_has_in(self_p)
        result
      end

      # Get socket option `priority`.
      # Available from libzmq 4.3.0.
      #
      # @return [Integer]
      def priority()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_priority(self_p)
        result
      end

      # Get socket option `priority`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #priority.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.priority(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_priority(self_p)
        result
      end

      # Set socket option `priority`.
      # Available from libzmq 4.3.0.
      #
      # @param priority [Integer, #to_int, #to_i]
      # @return [void]
      def set_priority(priority)
        raise DestroyedError unless @ptr
        self_p = @ptr
        priority = Integer(priority)
        result = ::CZMQ::FFI.zsock_set_priority(self_p, priority)
        result
      end

      # Set socket option `priority`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_priority.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param priority [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_priority(self_p, priority)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        priority = Integer(priority)
        result = ::CZMQ::FFI.zsock_set_priority(self_p, priority)
        result
      end

      # Get socket option `reconnect_stop`.
      # Available from libzmq 4.3.0.
      #
      # @return [Integer]
      def reconnect_stop()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_reconnect_stop(self_p)
        result
      end

      # Get socket option `reconnect_stop`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #reconnect_stop.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.reconnect_stop(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_reconnect_stop(self_p)
        result
      end

      # Set socket option `reconnect_stop`.
      # Available from libzmq 4.3.0.
      #
      # @param reconnect_stop [Integer, #to_int, #to_i]
      # @return [void]
      def set_reconnect_stop(reconnect_stop)
        raise DestroyedError unless @ptr
        self_p = @ptr
        reconnect_stop = Integer(reconnect_stop)
        result = ::CZMQ::FFI.zsock_set_reconnect_stop(self_p, reconnect_stop)
        result
      end

      # Set socket option `reconnect_stop`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_reconnect_stop.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param reconnect_stop [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_reconnect_stop(self_p, reconnect_stop)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        reconnect_stop = Integer(reconnect_stop)
        result = ::CZMQ::FFI.zsock_set_reconnect_stop(self_p, reconnect_stop)
        result
      end

      # Set socket option `only_first_subscribe`.
      # Available from libzmq 4.3.0.
      #
      # @param only_first_subscribe [Integer, #to_int, #to_i]
      # @return [void]
      def set_only_first_subscribe(only_first_subscribe)
        raise DestroyedError unless @ptr
        self_p = @ptr
        only_first_subscribe = Integer(only_first_subscribe)
        result = ::CZMQ::FFI.zsock_set_only_first_subscribe(self_p, only_first_subscribe)
        result
      end

      # Set socket option `only_first_subscribe`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_only_first_subscribe.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param only_first_subscribe [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_only_first_subscribe(self_p, only_first_subscribe)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        only_first_subscribe = Integer(only_first_subscribe)
        result = ::CZMQ::FFI.zsock_set_only_first_subscribe(self_p, only_first_subscribe)
        result
      end

      # Set socket option `hello_msg`.
      # Available from libzmq 4.3.0.
      #
      # @param hello_msg [Zframe, #__ptr]
      # @return [void]
      def set_hello_msg(hello_msg)
        raise DestroyedError unless @ptr
        self_p = @ptr
        hello_msg = hello_msg.__ptr if hello_msg
        result = ::CZMQ::FFI.zsock_set_hello_msg(self_p, hello_msg)
        result
      end

      # Set socket option `hello_msg`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_hello_msg.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param hello_msg [Zframe, #__ptr]
      # @return [void]
      def self.set_hello_msg(self_p, hello_msg)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        hello_msg = hello_msg.__ptr if hello_msg
        result = ::CZMQ::FFI.zsock_set_hello_msg(self_p, hello_msg)
        result
      end

      # Set socket option `disconnect_msg`.
      # Available from libzmq 4.3.0.
      #
      # @param disconnect_msg [Zframe, #__ptr]
      # @return [void]
      def set_disconnect_msg(disconnect_msg)
        raise DestroyedError unless @ptr
        self_p = @ptr
        disconnect_msg = disconnect_msg.__ptr if disconnect_msg
        result = ::CZMQ::FFI.zsock_set_disconnect_msg(self_p, disconnect_msg)
        result
      end

      # Set socket option `disconnect_msg`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_disconnect_msg.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param disconnect_msg [Zframe, #__ptr]
      # @return [void]
      def self.set_disconnect_msg(self_p, disconnect_msg)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        disconnect_msg = disconnect_msg.__ptr if disconnect_msg
        result = ::CZMQ::FFI.zsock_set_disconnect_msg(self_p, disconnect_msg)
        result
      end

      # Set socket option `wss_trust_system`.
      # Available from libzmq 4.3.0.
      #
      # @param wss_trust_system [Integer, #to_int, #to_i]
      # @return [void]
      def set_wss_trust_system(wss_trust_system)
        raise DestroyedError unless @ptr
        self_p = @ptr
        wss_trust_system = Integer(wss_trust_system)
        result = ::CZMQ::FFI.zsock_set_wss_trust_system(self_p, wss_trust_system)
        result
      end

      # Set socket option `wss_trust_system`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_wss_trust_system.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param wss_trust_system [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_wss_trust_system(self_p, wss_trust_system)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        wss_trust_system = Integer(wss_trust_system)
        result = ::CZMQ::FFI.zsock_set_wss_trust_system(self_p, wss_trust_system)
        result
      end

      # Set socket option `wss_hostname`.
      # Available from libzmq 4.3.0.
      #
      # @param wss_hostname [String, #to_s, nil]
      # @return [void]
      def set_wss_hostname(wss_hostname)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_wss_hostname(self_p, wss_hostname)
        result
      end

      # Set socket option `wss_hostname`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_wss_hostname.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param wss_hostname [String, #to_s, nil]
      # @return [void]
      def self.set_wss_hostname(self_p, wss_hostname)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_wss_hostname(self_p, wss_hostname)
        result
      end

      # Set socket option `wss_trust_pem`.
      # Available from libzmq 4.3.0.
      #
      # @param wss_trust_pem [String, #to_s, nil]
      # @return [void]
      def set_wss_trust_pem(wss_trust_pem)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_wss_trust_pem(self_p, wss_trust_pem)
        result
      end

      # Set socket option `wss_trust_pem`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_wss_trust_pem.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param wss_trust_pem [String, #to_s, nil]
      # @return [void]
      def self.set_wss_trust_pem(self_p, wss_trust_pem)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_wss_trust_pem(self_p, wss_trust_pem)
        result
      end

      # Set socket option `wss_cert_pem`.
      # Available from libzmq 4.3.0.
      #
      # @param wss_cert_pem [String, #to_s, nil]
      # @return [void]
      def set_wss_cert_pem(wss_cert_pem)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_wss_cert_pem(self_p, wss_cert_pem)
        result
      end

      # Set socket option `wss_cert_pem`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_wss_cert_pem.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param wss_cert_pem [String, #to_s, nil]
      # @return [void]
      def self.set_wss_cert_pem(self_p, wss_cert_pem)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_wss_cert_pem(self_p, wss_cert_pem)
        result
      end

      # Set socket option `wss_key_pem`.
      # Available from libzmq 4.3.0.
      #
      # @param wss_key_pem [String, #to_s, nil]
      # @return [void]
      def set_wss_key_pem(wss_key_pem)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_wss_key_pem(self_p, wss_key_pem)
        result
      end

      # Set socket option `wss_key_pem`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_wss_key_pem.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param wss_key_pem [String, #to_s, nil]
      # @return [void]
      def self.set_wss_key_pem(self_p, wss_key_pem)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_wss_key_pem(self_p, wss_key_pem)
        result
      end

      # Get socket option `out_batch_size`.
      # Available from libzmq 4.3.0.
      #
      # @return [Integer]
      def out_batch_size()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_out_batch_size(self_p)
        result
      end

      # Get socket option `out_batch_size`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #out_batch_size.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.out_batch_size(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_out_batch_size(self_p)
        result
      end

      # Set socket option `out_batch_size`.
      # Available from libzmq 4.3.0.
      #
      # @param out_batch_size [Integer, #to_int, #to_i]
      # @return [void]
      def set_out_batch_size(out_batch_size)
        raise DestroyedError unless @ptr
        self_p = @ptr
        out_batch_size = Integer(out_batch_size)
        result = ::CZMQ::FFI.zsock_set_out_batch_size(self_p, out_batch_size)
        result
      end

      # Set socket option `out_batch_size`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_out_batch_size.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param out_batch_size [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_out_batch_size(self_p, out_batch_size)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        out_batch_size = Integer(out_batch_size)
        result = ::CZMQ::FFI.zsock_set_out_batch_size(self_p, out_batch_size)
        result
      end

      # Get socket option `in_batch_size`.
      # Available from libzmq 4.3.0.
      #
      # @return [Integer]
      def in_batch_size()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_in_batch_size(self_p)
        result
      end

      # Get socket option `in_batch_size`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #in_batch_size.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.in_batch_size(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_in_batch_size(self_p)
        result
      end

      # Set socket option `in_batch_size`.
      # Available from libzmq 4.3.0.
      #
      # @param in_batch_size [Integer, #to_int, #to_i]
      # @return [void]
      def set_in_batch_size(in_batch_size)
        raise DestroyedError unless @ptr
        self_p = @ptr
        in_batch_size = Integer(in_batch_size)
        result = ::CZMQ::FFI.zsock_set_in_batch_size(self_p, in_batch_size)
        result
      end

      # Set socket option `in_batch_size`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_in_batch_size.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param in_batch_size [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_in_batch_size(self_p, in_batch_size)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        in_batch_size = Integer(in_batch_size)
        result = ::CZMQ::FFI.zsock_set_in_batch_size(self_p, in_batch_size)
        result
      end

      # Get socket option `socks_password`.
      # Available from libzmq 4.3.0.
      #
      # @return [::FFI::AutoPointer]
      def socks_password()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_socks_password(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `socks_password`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #socks_password.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.socks_password(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_socks_password(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `socks_password`.
      # Available from libzmq 4.3.0.
      #
      # @param socks_password [String, #to_s, nil]
      # @return [void]
      def set_socks_password(socks_password)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_socks_password(self_p, socks_password)
        result
      end

      # Set socket option `socks_password`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_socks_password.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param socks_password [String, #to_s, nil]
      # @return [void]
      def self.set_socks_password(self_p, socks_password)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_socks_password(self_p, socks_password)
        result
      end

      # Get socket option `socks_username`.
      # Available from libzmq 4.3.0.
      #
      # @return [::FFI::AutoPointer]
      def socks_username()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_socks_username(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `socks_username`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #socks_username.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.socks_username(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_socks_username(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `socks_username`.
      # Available from libzmq 4.3.0.
      #
      # @param socks_username [String, #to_s, nil]
      # @return [void]
      def set_socks_username(socks_username)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_socks_username(self_p, socks_username)
        result
      end

      # Set socket option `socks_username`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_socks_username.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param socks_username [String, #to_s, nil]
      # @return [void]
      def self.set_socks_username(self_p, socks_username)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_socks_username(self_p, socks_username)
        result
      end

      # Set socket option `xpub_manual_last_value`.
      # Available from libzmq 4.3.0.
      #
      # @param xpub_manual_last_value [Integer, #to_int, #to_i]
      # @return [void]
      def set_xpub_manual_last_value(xpub_manual_last_value)
        raise DestroyedError unless @ptr
        self_p = @ptr
        xpub_manual_last_value = Integer(xpub_manual_last_value)
        result = ::CZMQ::FFI.zsock_set_xpub_manual_last_value(self_p, xpub_manual_last_value)
        result
      end

      # Set socket option `xpub_manual_last_value`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_xpub_manual_last_value.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param xpub_manual_last_value [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_xpub_manual_last_value(self_p, xpub_manual_last_value)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        xpub_manual_last_value = Integer(xpub_manual_last_value)
        result = ::CZMQ::FFI.zsock_set_xpub_manual_last_value(self_p, xpub_manual_last_value)
        result
      end

      # Get socket option `router_notify`.
      # Available from libzmq 4.3.0.
      #
      # @return [Integer]
      def router_notify()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_router_notify(self_p)
        result
      end

      # Get socket option `router_notify`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #router_notify.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.router_notify(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_router_notify(self_p)
        result
      end

      # Set socket option `router_notify`.
      # Available from libzmq 4.3.0.
      #
      # @param router_notify [Integer, #to_int, #to_i]
      # @return [void]
      def set_router_notify(router_notify)
        raise DestroyedError unless @ptr
        self_p = @ptr
        router_notify = Integer(router_notify)
        result = ::CZMQ::FFI.zsock_set_router_notify(self_p, router_notify)
        result
      end

      # Set socket option `router_notify`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_router_notify.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param router_notify [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_router_notify(self_p, router_notify)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        router_notify = Integer(router_notify)
        result = ::CZMQ::FFI.zsock_set_router_notify(self_p, router_notify)
        result
      end

      # Get socket option `multicast_loop`.
      # Available from libzmq 4.3.0.
      #
      # @return [Integer]
      def multicast_loop()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_multicast_loop(self_p)
        result
      end

      # Get socket option `multicast_loop`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #multicast_loop.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.multicast_loop(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_multicast_loop(self_p)
        result
      end

      # Set socket option `multicast_loop`.
      # Available from libzmq 4.3.0.
      #
      # @param multicast_loop [Integer, #to_int, #to_i]
      # @return [void]
      def set_multicast_loop(multicast_loop)
        raise DestroyedError unless @ptr
        self_p = @ptr
        multicast_loop = Integer(multicast_loop)
        result = ::CZMQ::FFI.zsock_set_multicast_loop(self_p, multicast_loop)
        result
      end

      # Set socket option `multicast_loop`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_multicast_loop.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param multicast_loop [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_multicast_loop(self_p, multicast_loop)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        multicast_loop = Integer(multicast_loop)
        result = ::CZMQ::FFI.zsock_set_multicast_loop(self_p, multicast_loop)
        result
      end

      # Get socket option `metadata`.
      # Available from libzmq 4.3.0.
      #
      # @return [::FFI::AutoPointer]
      def metadata()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_metadata(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `metadata`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #metadata.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.metadata(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_metadata(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `metadata`.
      # Available from libzmq 4.3.0.
      #
      # @param metadata [String, #to_s, nil]
      # @return [void]
      def set_metadata(metadata)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_metadata(self_p, metadata)
        result
      end

      # Set socket option `metadata`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_metadata.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param metadata [String, #to_s, nil]
      # @return [void]
      def self.set_metadata(self_p, metadata)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_metadata(self_p, metadata)
        result
      end

      # Get socket option `loopback_fastpath`.
      # Available from libzmq 4.3.0.
      #
      # @return [Integer]
      def loopback_fastpath()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_loopback_fastpath(self_p)
        result
      end

      # Get socket option `loopback_fastpath`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #loopback_fastpath.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.loopback_fastpath(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_loopback_fastpath(self_p)
        result
      end

      # Set socket option `loopback_fastpath`.
      # Available from libzmq 4.3.0.
      #
      # @param loopback_fastpath [Integer, #to_int, #to_i]
      # @return [void]
      def set_loopback_fastpath(loopback_fastpath)
        raise DestroyedError unless @ptr
        self_p = @ptr
        loopback_fastpath = Integer(loopback_fastpath)
        result = ::CZMQ::FFI.zsock_set_loopback_fastpath(self_p, loopback_fastpath)
        result
      end

      # Set socket option `loopback_fastpath`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_loopback_fastpath.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param loopback_fastpath [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_loopback_fastpath(self_p, loopback_fastpath)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        loopback_fastpath = Integer(loopback_fastpath)
        result = ::CZMQ::FFI.zsock_set_loopback_fastpath(self_p, loopback_fastpath)
        result
      end

      # Get socket option `zap_enforce_domain`.
      # Available from libzmq 4.3.0.
      #
      # @return [Integer]
      def zap_enforce_domain()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_zap_enforce_domain(self_p)
        result
      end

      # Get socket option `zap_enforce_domain`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #zap_enforce_domain.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.zap_enforce_domain(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_zap_enforce_domain(self_p)
        result
      end

      # Set socket option `zap_enforce_domain`.
      # Available from libzmq 4.3.0.
      #
      # @param zap_enforce_domain [Integer, #to_int, #to_i]
      # @return [void]
      def set_zap_enforce_domain(zap_enforce_domain)
        raise DestroyedError unless @ptr
        self_p = @ptr
        zap_enforce_domain = Integer(zap_enforce_domain)
        result = ::CZMQ::FFI.zsock_set_zap_enforce_domain(self_p, zap_enforce_domain)
        result
      end

      # Set socket option `zap_enforce_domain`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_zap_enforce_domain.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param zap_enforce_domain [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_zap_enforce_domain(self_p, zap_enforce_domain)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        zap_enforce_domain = Integer(zap_enforce_domain)
        result = ::CZMQ::FFI.zsock_set_zap_enforce_domain(self_p, zap_enforce_domain)
        result
      end

      # Get socket option `gssapi_principal_nametype`.
      # Available from libzmq 4.3.0.
      #
      # @return [Integer]
      def gssapi_principal_nametype()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_gssapi_principal_nametype(self_p)
        result
      end

      # Get socket option `gssapi_principal_nametype`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #gssapi_principal_nametype.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.gssapi_principal_nametype(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_gssapi_principal_nametype(self_p)
        result
      end

      # Set socket option `gssapi_principal_nametype`.
      # Available from libzmq 4.3.0.
      #
      # @param gssapi_principal_nametype [Integer, #to_int, #to_i]
      # @return [void]
      def set_gssapi_principal_nametype(gssapi_principal_nametype)
        raise DestroyedError unless @ptr
        self_p = @ptr
        gssapi_principal_nametype = Integer(gssapi_principal_nametype)
        result = ::CZMQ::FFI.zsock_set_gssapi_principal_nametype(self_p, gssapi_principal_nametype)
        result
      end

      # Set socket option `gssapi_principal_nametype`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_gssapi_principal_nametype.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param gssapi_principal_nametype [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_gssapi_principal_nametype(self_p, gssapi_principal_nametype)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        gssapi_principal_nametype = Integer(gssapi_principal_nametype)
        result = ::CZMQ::FFI.zsock_set_gssapi_principal_nametype(self_p, gssapi_principal_nametype)
        result
      end

      # Get socket option `gssapi_service_principal_nametype`.
      # Available from libzmq 4.3.0.
      #
      # @return [Integer]
      def gssapi_service_principal_nametype()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_gssapi_service_principal_nametype(self_p)
        result
      end

      # Get socket option `gssapi_service_principal_nametype`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #gssapi_service_principal_nametype.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.gssapi_service_principal_nametype(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_gssapi_service_principal_nametype(self_p)
        result
      end

      # Set socket option `gssapi_service_principal_nametype`.
      # Available from libzmq 4.3.0.
      #
      # @param gssapi_service_principal_nametype [Integer, #to_int, #to_i]
      # @return [void]
      def set_gssapi_service_principal_nametype(gssapi_service_principal_nametype)
        raise DestroyedError unless @ptr
        self_p = @ptr
        gssapi_service_principal_nametype = Integer(gssapi_service_principal_nametype)
        result = ::CZMQ::FFI.zsock_set_gssapi_service_principal_nametype(self_p, gssapi_service_principal_nametype)
        result
      end

      # Set socket option `gssapi_service_principal_nametype`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_gssapi_service_principal_nametype.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param gssapi_service_principal_nametype [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_gssapi_service_principal_nametype(self_p, gssapi_service_principal_nametype)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        gssapi_service_principal_nametype = Integer(gssapi_service_principal_nametype)
        result = ::CZMQ::FFI.zsock_set_gssapi_service_principal_nametype(self_p, gssapi_service_principal_nametype)
        result
      end

      # Get socket option `bindtodevice`.
      # Available from libzmq 4.3.0.
      #
      # @return [::FFI::AutoPointer]
      def bindtodevice()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_bindtodevice(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `bindtodevice`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #bindtodevice.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.bindtodevice(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_bindtodevice(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `bindtodevice`.
      # Available from libzmq 4.3.0.
      #
      # @param bindtodevice [String, #to_s, nil]
      # @return [void]
      def set_bindtodevice(bindtodevice)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_bindtodevice(self_p, bindtodevice)
        result
      end

      # Set socket option `bindtodevice`.
      # Available from libzmq 4.3.0.
      #
      # This is the polymorphic version of #set_bindtodevice.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param bindtodevice [String, #to_s, nil]
      # @return [void]
      def self.set_bindtodevice(self_p, bindtodevice)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_bindtodevice(self_p, bindtodevice)
        result
      end

      # Get socket option `heartbeat_ivl`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def heartbeat_ivl()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_heartbeat_ivl(self_p)
        result
      end

      # Get socket option `heartbeat_ivl`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #heartbeat_ivl.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.heartbeat_ivl(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_heartbeat_ivl(self_p)
        result
      end

      # Set socket option `heartbeat_ivl`.
      # Available from libzmq 4.2.0.
      #
      # @param heartbeat_ivl [Integer, #to_int, #to_i]
      # @return [void]
      def set_heartbeat_ivl(heartbeat_ivl)
        raise DestroyedError unless @ptr
        self_p = @ptr
        heartbeat_ivl = Integer(heartbeat_ivl)
        result = ::CZMQ::FFI.zsock_set_heartbeat_ivl(self_p, heartbeat_ivl)
        result
      end

      # Set socket option `heartbeat_ivl`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_heartbeat_ivl.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param heartbeat_ivl [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_heartbeat_ivl(self_p, heartbeat_ivl)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        heartbeat_ivl = Integer(heartbeat_ivl)
        result = ::CZMQ::FFI.zsock_set_heartbeat_ivl(self_p, heartbeat_ivl)
        result
      end

      # Get socket option `heartbeat_ttl`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def heartbeat_ttl()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_heartbeat_ttl(self_p)
        result
      end

      # Get socket option `heartbeat_ttl`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #heartbeat_ttl.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.heartbeat_ttl(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_heartbeat_ttl(self_p)
        result
      end

      # Set socket option `heartbeat_ttl`.
      # Available from libzmq 4.2.0.
      #
      # @param heartbeat_ttl [Integer, #to_int, #to_i]
      # @return [void]
      def set_heartbeat_ttl(heartbeat_ttl)
        raise DestroyedError unless @ptr
        self_p = @ptr
        heartbeat_ttl = Integer(heartbeat_ttl)
        result = ::CZMQ::FFI.zsock_set_heartbeat_ttl(self_p, heartbeat_ttl)
        result
      end

      # Set socket option `heartbeat_ttl`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_heartbeat_ttl.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param heartbeat_ttl [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_heartbeat_ttl(self_p, heartbeat_ttl)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        heartbeat_ttl = Integer(heartbeat_ttl)
        result = ::CZMQ::FFI.zsock_set_heartbeat_ttl(self_p, heartbeat_ttl)
        result
      end

      # Get socket option `heartbeat_timeout`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def heartbeat_timeout()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_heartbeat_timeout(self_p)
        result
      end

      # Get socket option `heartbeat_timeout`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #heartbeat_timeout.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.heartbeat_timeout(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_heartbeat_timeout(self_p)
        result
      end

      # Set socket option `heartbeat_timeout`.
      # Available from libzmq 4.2.0.
      #
      # @param heartbeat_timeout [Integer, #to_int, #to_i]
      # @return [void]
      def set_heartbeat_timeout(heartbeat_timeout)
        raise DestroyedError unless @ptr
        self_p = @ptr
        heartbeat_timeout = Integer(heartbeat_timeout)
        result = ::CZMQ::FFI.zsock_set_heartbeat_timeout(self_p, heartbeat_timeout)
        result
      end

      # Set socket option `heartbeat_timeout`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_heartbeat_timeout.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param heartbeat_timeout [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_heartbeat_timeout(self_p, heartbeat_timeout)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        heartbeat_timeout = Integer(heartbeat_timeout)
        result = ::CZMQ::FFI.zsock_set_heartbeat_timeout(self_p, heartbeat_timeout)
        result
      end

      # Get socket option `use_fd`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def use_fd()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_use_fd(self_p)
        result
      end

      # Get socket option `use_fd`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #use_fd.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.use_fd(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_use_fd(self_p)
        result
      end

      # Set socket option `use_fd`.
      # Available from libzmq 4.2.0.
      #
      # @param use_fd [Integer, #to_int, #to_i]
      # @return [void]
      def set_use_fd(use_fd)
        raise DestroyedError unless @ptr
        self_p = @ptr
        use_fd = Integer(use_fd)
        result = ::CZMQ::FFI.zsock_set_use_fd(self_p, use_fd)
        result
      end

      # Set socket option `use_fd`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_use_fd.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param use_fd [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_use_fd(self_p, use_fd)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        use_fd = Integer(use_fd)
        result = ::CZMQ::FFI.zsock_set_use_fd(self_p, use_fd)
        result
      end

      # Set socket option `xpub_manual`.
      # Available from libzmq 4.2.0.
      #
      # @param xpub_manual [Integer, #to_int, #to_i]
      # @return [void]
      def set_xpub_manual(xpub_manual)
        raise DestroyedError unless @ptr
        self_p = @ptr
        xpub_manual = Integer(xpub_manual)
        result = ::CZMQ::FFI.zsock_set_xpub_manual(self_p, xpub_manual)
        result
      end

      # Set socket option `xpub_manual`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_xpub_manual.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param xpub_manual [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_xpub_manual(self_p, xpub_manual)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        xpub_manual = Integer(xpub_manual)
        result = ::CZMQ::FFI.zsock_set_xpub_manual(self_p, xpub_manual)
        result
      end

      # Set socket option `xpub_welcome_msg`.
      # Available from libzmq 4.2.0.
      #
      # @param xpub_welcome_msg [String, #to_s, nil]
      # @return [void]
      def set_xpub_welcome_msg(xpub_welcome_msg)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_xpub_welcome_msg(self_p, xpub_welcome_msg)
        result
      end

      # Set socket option `xpub_welcome_msg`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_xpub_welcome_msg.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param xpub_welcome_msg [String, #to_s, nil]
      # @return [void]
      def self.set_xpub_welcome_msg(self_p, xpub_welcome_msg)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_xpub_welcome_msg(self_p, xpub_welcome_msg)
        result
      end

      # Set socket option `stream_notify`.
      # Available from libzmq 4.2.0.
      #
      # @param stream_notify [Integer, #to_int, #to_i]
      # @return [void]
      def set_stream_notify(stream_notify)
        raise DestroyedError unless @ptr
        self_p = @ptr
        stream_notify = Integer(stream_notify)
        result = ::CZMQ::FFI.zsock_set_stream_notify(self_p, stream_notify)
        result
      end

      # Set socket option `stream_notify`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_stream_notify.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param stream_notify [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_stream_notify(self_p, stream_notify)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        stream_notify = Integer(stream_notify)
        result = ::CZMQ::FFI.zsock_set_stream_notify(self_p, stream_notify)
        result
      end

      # Get socket option `invert_matching`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def invert_matching()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_invert_matching(self_p)
        result
      end

      # Get socket option `invert_matching`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #invert_matching.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.invert_matching(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_invert_matching(self_p)
        result
      end

      # Set socket option `invert_matching`.
      # Available from libzmq 4.2.0.
      #
      # @param invert_matching [Integer, #to_int, #to_i]
      # @return [void]
      def set_invert_matching(invert_matching)
        raise DestroyedError unless @ptr
        self_p = @ptr
        invert_matching = Integer(invert_matching)
        result = ::CZMQ::FFI.zsock_set_invert_matching(self_p, invert_matching)
        result
      end

      # Set socket option `invert_matching`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_invert_matching.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param invert_matching [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_invert_matching(self_p, invert_matching)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        invert_matching = Integer(invert_matching)
        result = ::CZMQ::FFI.zsock_set_invert_matching(self_p, invert_matching)
        result
      end

      # Set socket option `xpub_verboser`.
      # Available from libzmq 4.2.0.
      #
      # @param xpub_verboser [Integer, #to_int, #to_i]
      # @return [void]
      def set_xpub_verboser(xpub_verboser)
        raise DestroyedError unless @ptr
        self_p = @ptr
        xpub_verboser = Integer(xpub_verboser)
        result = ::CZMQ::FFI.zsock_set_xpub_verboser(self_p, xpub_verboser)
        result
      end

      # Set socket option `xpub_verboser`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_xpub_verboser.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param xpub_verboser [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_xpub_verboser(self_p, xpub_verboser)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        xpub_verboser = Integer(xpub_verboser)
        result = ::CZMQ::FFI.zsock_set_xpub_verboser(self_p, xpub_verboser)
        result
      end

      # Get socket option `connect_timeout`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def connect_timeout()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_connect_timeout(self_p)
        result
      end

      # Get socket option `connect_timeout`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #connect_timeout.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.connect_timeout(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_connect_timeout(self_p)
        result
      end

      # Set socket option `connect_timeout`.
      # Available from libzmq 4.2.0.
      #
      # @param connect_timeout [Integer, #to_int, #to_i]
      # @return [void]
      def set_connect_timeout(connect_timeout)
        raise DestroyedError unless @ptr
        self_p = @ptr
        connect_timeout = Integer(connect_timeout)
        result = ::CZMQ::FFI.zsock_set_connect_timeout(self_p, connect_timeout)
        result
      end

      # Set socket option `connect_timeout`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_connect_timeout.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param connect_timeout [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_connect_timeout(self_p, connect_timeout)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        connect_timeout = Integer(connect_timeout)
        result = ::CZMQ::FFI.zsock_set_connect_timeout(self_p, connect_timeout)
        result
      end

      # Get socket option `tcp_maxrt`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def tcp_maxrt()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_tcp_maxrt(self_p)
        result
      end

      # Get socket option `tcp_maxrt`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #tcp_maxrt.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.tcp_maxrt(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_tcp_maxrt(self_p)
        result
      end

      # Set socket option `tcp_maxrt`.
      # Available from libzmq 4.2.0.
      #
      # @param tcp_maxrt [Integer, #to_int, #to_i]
      # @return [void]
      def set_tcp_maxrt(tcp_maxrt)
        raise DestroyedError unless @ptr
        self_p = @ptr
        tcp_maxrt = Integer(tcp_maxrt)
        result = ::CZMQ::FFI.zsock_set_tcp_maxrt(self_p, tcp_maxrt)
        result
      end

      # Set socket option `tcp_maxrt`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_tcp_maxrt.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param tcp_maxrt [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_tcp_maxrt(self_p, tcp_maxrt)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        tcp_maxrt = Integer(tcp_maxrt)
        result = ::CZMQ::FFI.zsock_set_tcp_maxrt(self_p, tcp_maxrt)
        result
      end

      # Get socket option `thread_safe`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def thread_safe()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_thread_safe(self_p)
        result
      end

      # Get socket option `thread_safe`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #thread_safe.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.thread_safe(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_thread_safe(self_p)
        result
      end

      # Get socket option `multicast_maxtpdu`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def multicast_maxtpdu()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_multicast_maxtpdu(self_p)
        result
      end

      # Get socket option `multicast_maxtpdu`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #multicast_maxtpdu.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.multicast_maxtpdu(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_multicast_maxtpdu(self_p)
        result
      end

      # Set socket option `multicast_maxtpdu`.
      # Available from libzmq 4.2.0.
      #
      # @param multicast_maxtpdu [Integer, #to_int, #to_i]
      # @return [void]
      def set_multicast_maxtpdu(multicast_maxtpdu)
        raise DestroyedError unless @ptr
        self_p = @ptr
        multicast_maxtpdu = Integer(multicast_maxtpdu)
        result = ::CZMQ::FFI.zsock_set_multicast_maxtpdu(self_p, multicast_maxtpdu)
        result
      end

      # Set socket option `multicast_maxtpdu`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_multicast_maxtpdu.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param multicast_maxtpdu [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_multicast_maxtpdu(self_p, multicast_maxtpdu)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        multicast_maxtpdu = Integer(multicast_maxtpdu)
        result = ::CZMQ::FFI.zsock_set_multicast_maxtpdu(self_p, multicast_maxtpdu)
        result
      end

      # Get socket option `vmci_buffer_size`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def vmci_buffer_size()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_vmci_buffer_size(self_p)
        result
      end

      # Get socket option `vmci_buffer_size`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #vmci_buffer_size.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.vmci_buffer_size(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_vmci_buffer_size(self_p)
        result
      end

      # Set socket option `vmci_buffer_size`.
      # Available from libzmq 4.2.0.
      #
      # @param vmci_buffer_size [Integer, #to_int, #to_i]
      # @return [void]
      def set_vmci_buffer_size(vmci_buffer_size)
        raise DestroyedError unless @ptr
        self_p = @ptr
        vmci_buffer_size = Integer(vmci_buffer_size)
        result = ::CZMQ::FFI.zsock_set_vmci_buffer_size(self_p, vmci_buffer_size)
        result
      end

      # Set socket option `vmci_buffer_size`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_vmci_buffer_size.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param vmci_buffer_size [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_vmci_buffer_size(self_p, vmci_buffer_size)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        vmci_buffer_size = Integer(vmci_buffer_size)
        result = ::CZMQ::FFI.zsock_set_vmci_buffer_size(self_p, vmci_buffer_size)
        result
      end

      # Get socket option `vmci_buffer_min_size`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def vmci_buffer_min_size()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_vmci_buffer_min_size(self_p)
        result
      end

      # Get socket option `vmci_buffer_min_size`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #vmci_buffer_min_size.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.vmci_buffer_min_size(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_vmci_buffer_min_size(self_p)
        result
      end

      # Set socket option `vmci_buffer_min_size`.
      # Available from libzmq 4.2.0.
      #
      # @param vmci_buffer_min_size [Integer, #to_int, #to_i]
      # @return [void]
      def set_vmci_buffer_min_size(vmci_buffer_min_size)
        raise DestroyedError unless @ptr
        self_p = @ptr
        vmci_buffer_min_size = Integer(vmci_buffer_min_size)
        result = ::CZMQ::FFI.zsock_set_vmci_buffer_min_size(self_p, vmci_buffer_min_size)
        result
      end

      # Set socket option `vmci_buffer_min_size`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_vmci_buffer_min_size.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param vmci_buffer_min_size [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_vmci_buffer_min_size(self_p, vmci_buffer_min_size)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        vmci_buffer_min_size = Integer(vmci_buffer_min_size)
        result = ::CZMQ::FFI.zsock_set_vmci_buffer_min_size(self_p, vmci_buffer_min_size)
        result
      end

      # Get socket option `vmci_buffer_max_size`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def vmci_buffer_max_size()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_vmci_buffer_max_size(self_p)
        result
      end

      # Get socket option `vmci_buffer_max_size`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #vmci_buffer_max_size.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.vmci_buffer_max_size(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_vmci_buffer_max_size(self_p)
        result
      end

      # Set socket option `vmci_buffer_max_size`.
      # Available from libzmq 4.2.0.
      #
      # @param vmci_buffer_max_size [Integer, #to_int, #to_i]
      # @return [void]
      def set_vmci_buffer_max_size(vmci_buffer_max_size)
        raise DestroyedError unless @ptr
        self_p = @ptr
        vmci_buffer_max_size = Integer(vmci_buffer_max_size)
        result = ::CZMQ::FFI.zsock_set_vmci_buffer_max_size(self_p, vmci_buffer_max_size)
        result
      end

      # Set socket option `vmci_buffer_max_size`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_vmci_buffer_max_size.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param vmci_buffer_max_size [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_vmci_buffer_max_size(self_p, vmci_buffer_max_size)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        vmci_buffer_max_size = Integer(vmci_buffer_max_size)
        result = ::CZMQ::FFI.zsock_set_vmci_buffer_max_size(self_p, vmci_buffer_max_size)
        result
      end

      # Get socket option `vmci_connect_timeout`.
      # Available from libzmq 4.2.0.
      #
      # @return [Integer]
      def vmci_connect_timeout()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_vmci_connect_timeout(self_p)
        result
      end

      # Get socket option `vmci_connect_timeout`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #vmci_connect_timeout.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.vmci_connect_timeout(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_vmci_connect_timeout(self_p)
        result
      end

      # Set socket option `vmci_connect_timeout`.
      # Available from libzmq 4.2.0.
      #
      # @param vmci_connect_timeout [Integer, #to_int, #to_i]
      # @return [void]
      def set_vmci_connect_timeout(vmci_connect_timeout)
        raise DestroyedError unless @ptr
        self_p = @ptr
        vmci_connect_timeout = Integer(vmci_connect_timeout)
        result = ::CZMQ::FFI.zsock_set_vmci_connect_timeout(self_p, vmci_connect_timeout)
        result
      end

      # Set socket option `vmci_connect_timeout`.
      # Available from libzmq 4.2.0.
      #
      # This is the polymorphic version of #set_vmci_connect_timeout.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param vmci_connect_timeout [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_vmci_connect_timeout(self_p, vmci_connect_timeout)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        vmci_connect_timeout = Integer(vmci_connect_timeout)
        result = ::CZMQ::FFI.zsock_set_vmci_connect_timeout(self_p, vmci_connect_timeout)
        result
      end

      # Get socket option `tos`.
      # Available from libzmq 4.1.0.
      #
      # @return [Integer]
      def tos()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_tos(self_p)
        result
      end

      # Get socket option `tos`.
      # Available from libzmq 4.1.0.
      #
      # This is the polymorphic version of #tos.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.tos(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_tos(self_p)
        result
      end

      # Set socket option `tos`.
      # Available from libzmq 4.1.0.
      #
      # @param tos [Integer, #to_int, #to_i]
      # @return [void]
      def set_tos(tos)
        raise DestroyedError unless @ptr
        self_p = @ptr
        tos = Integer(tos)
        result = ::CZMQ::FFI.zsock_set_tos(self_p, tos)
        result
      end

      # Set socket option `tos`.
      # Available from libzmq 4.1.0.
      #
      # This is the polymorphic version of #set_tos.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param tos [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_tos(self_p, tos)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        tos = Integer(tos)
        result = ::CZMQ::FFI.zsock_set_tos(self_p, tos)
        result
      end

      # Set socket option `router_handover`.
      # Available from libzmq 4.1.0.
      #
      # @param router_handover [Integer, #to_int, #to_i]
      # @return [void]
      def set_router_handover(router_handover)
        raise DestroyedError unless @ptr
        self_p = @ptr
        router_handover = Integer(router_handover)
        result = ::CZMQ::FFI.zsock_set_router_handover(self_p, router_handover)
        result
      end

      # Set socket option `router_handover`.
      # Available from libzmq 4.1.0.
      #
      # This is the polymorphic version of #set_router_handover.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param router_handover [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_router_handover(self_p, router_handover)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        router_handover = Integer(router_handover)
        result = ::CZMQ::FFI.zsock_set_router_handover(self_p, router_handover)
        result
      end

      # Set socket option `connect_rid`.
      # Available from libzmq 4.1.0.
      #
      # @param connect_rid [String, #to_s, nil]
      # @return [void]
      def set_connect_rid(connect_rid)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_connect_rid(self_p, connect_rid)
        result
      end

      # Set socket option `connect_rid`.
      # Available from libzmq 4.1.0.
      #
      # This is the polymorphic version of #set_connect_rid.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param connect_rid [String, #to_s, nil]
      # @return [void]
      def self.set_connect_rid(self_p, connect_rid)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_connect_rid(self_p, connect_rid)
        result
      end

      # Set socket option `connect_rid` from 32-octet binary
      # Available from libzmq 4.1.0.
      #
      # @param connect_rid [::FFI::Pointer, #to_ptr]
      # @return [void]
      def set_connect_rid_bin(connect_rid)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_connect_rid_bin(self_p, connect_rid)
        result
      end

      # Set socket option `connect_rid` from 32-octet binary
      # Available from libzmq 4.1.0.
      #
      # This is the polymorphic version of #set_connect_rid_bin.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param connect_rid [::FFI::Pointer, #to_ptr]
      # @return [void]
      def self.set_connect_rid_bin(self_p, connect_rid)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_connect_rid_bin(self_p, connect_rid)
        result
      end

      # Get socket option `handshake_ivl`.
      # Available from libzmq 4.1.0.
      #
      # @return [Integer]
      def handshake_ivl()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_handshake_ivl(self_p)
        result
      end

      # Get socket option `handshake_ivl`.
      # Available from libzmq 4.1.0.
      #
      # This is the polymorphic version of #handshake_ivl.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.handshake_ivl(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_handshake_ivl(self_p)
        result
      end

      # Set socket option `handshake_ivl`.
      # Available from libzmq 4.1.0.
      #
      # @param handshake_ivl [Integer, #to_int, #to_i]
      # @return [void]
      def set_handshake_ivl(handshake_ivl)
        raise DestroyedError unless @ptr
        self_p = @ptr
        handshake_ivl = Integer(handshake_ivl)
        result = ::CZMQ::FFI.zsock_set_handshake_ivl(self_p, handshake_ivl)
        result
      end

      # Set socket option `handshake_ivl`.
      # Available from libzmq 4.1.0.
      #
      # This is the polymorphic version of #set_handshake_ivl.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param handshake_ivl [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_handshake_ivl(self_p, handshake_ivl)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        handshake_ivl = Integer(handshake_ivl)
        result = ::CZMQ::FFI.zsock_set_handshake_ivl(self_p, handshake_ivl)
        result
      end

      # Get socket option `socks_proxy`.
      # Available from libzmq 4.1.0.
      #
      # @return [::FFI::AutoPointer]
      def socks_proxy()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_socks_proxy(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `socks_proxy`.
      # Available from libzmq 4.1.0.
      #
      # This is the polymorphic version of #socks_proxy.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.socks_proxy(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_socks_proxy(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `socks_proxy`.
      # Available from libzmq 4.1.0.
      #
      # @param socks_proxy [String, #to_s, nil]
      # @return [void]
      def set_socks_proxy(socks_proxy)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_socks_proxy(self_p, socks_proxy)
        result
      end

      # Set socket option `socks_proxy`.
      # Available from libzmq 4.1.0.
      #
      # This is the polymorphic version of #set_socks_proxy.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param socks_proxy [String, #to_s, nil]
      # @return [void]
      def self.set_socks_proxy(self_p, socks_proxy)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_socks_proxy(self_p, socks_proxy)
        result
      end

      # Set socket option `xpub_nodrop`.
      # Available from libzmq 4.1.0.
      #
      # @param xpub_nodrop [Integer, #to_int, #to_i]
      # @return [void]
      def set_xpub_nodrop(xpub_nodrop)
        raise DestroyedError unless @ptr
        self_p = @ptr
        xpub_nodrop = Integer(xpub_nodrop)
        result = ::CZMQ::FFI.zsock_set_xpub_nodrop(self_p, xpub_nodrop)
        result
      end

      # Set socket option `xpub_nodrop`.
      # Available from libzmq 4.1.0.
      #
      # This is the polymorphic version of #set_xpub_nodrop.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param xpub_nodrop [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_xpub_nodrop(self_p, xpub_nodrop)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        xpub_nodrop = Integer(xpub_nodrop)
        result = ::CZMQ::FFI.zsock_set_xpub_nodrop(self_p, xpub_nodrop)
        result
      end

      # Set socket option `router_mandatory`.
      # Available from libzmq 4.0.0.
      #
      # @param router_mandatory [Integer, #to_int, #to_i]
      # @return [void]
      def set_router_mandatory(router_mandatory)
        raise DestroyedError unless @ptr
        self_p = @ptr
        router_mandatory = Integer(router_mandatory)
        result = ::CZMQ::FFI.zsock_set_router_mandatory(self_p, router_mandatory)
        result
      end

      # Set socket option `router_mandatory`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_router_mandatory.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param router_mandatory [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_router_mandatory(self_p, router_mandatory)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        router_mandatory = Integer(router_mandatory)
        result = ::CZMQ::FFI.zsock_set_router_mandatory(self_p, router_mandatory)
        result
      end

      # Set socket option `probe_router`.
      # Available from libzmq 4.0.0.
      #
      # @param probe_router [Integer, #to_int, #to_i]
      # @return [void]
      def set_probe_router(probe_router)
        raise DestroyedError unless @ptr
        self_p = @ptr
        probe_router = Integer(probe_router)
        result = ::CZMQ::FFI.zsock_set_probe_router(self_p, probe_router)
        result
      end

      # Set socket option `probe_router`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_probe_router.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param probe_router [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_probe_router(self_p, probe_router)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        probe_router = Integer(probe_router)
        result = ::CZMQ::FFI.zsock_set_probe_router(self_p, probe_router)
        result
      end

      # Set socket option `req_relaxed`.
      # Available from libzmq 4.0.0.
      #
      # @param req_relaxed [Integer, #to_int, #to_i]
      # @return [void]
      def set_req_relaxed(req_relaxed)
        raise DestroyedError unless @ptr
        self_p = @ptr
        req_relaxed = Integer(req_relaxed)
        result = ::CZMQ::FFI.zsock_set_req_relaxed(self_p, req_relaxed)
        result
      end

      # Set socket option `req_relaxed`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_req_relaxed.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param req_relaxed [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_req_relaxed(self_p, req_relaxed)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        req_relaxed = Integer(req_relaxed)
        result = ::CZMQ::FFI.zsock_set_req_relaxed(self_p, req_relaxed)
        result
      end

      # Set socket option `req_correlate`.
      # Available from libzmq 4.0.0.
      #
      # @param req_correlate [Integer, #to_int, #to_i]
      # @return [void]
      def set_req_correlate(req_correlate)
        raise DestroyedError unless @ptr
        self_p = @ptr
        req_correlate = Integer(req_correlate)
        result = ::CZMQ::FFI.zsock_set_req_correlate(self_p, req_correlate)
        result
      end

      # Set socket option `req_correlate`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_req_correlate.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param req_correlate [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_req_correlate(self_p, req_correlate)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        req_correlate = Integer(req_correlate)
        result = ::CZMQ::FFI.zsock_set_req_correlate(self_p, req_correlate)
        result
      end

      # Set socket option `conflate`.
      # Available from libzmq 4.0.0.
      #
      # @param conflate [Integer, #to_int, #to_i]
      # @return [void]
      def set_conflate(conflate)
        raise DestroyedError unless @ptr
        self_p = @ptr
        conflate = Integer(conflate)
        result = ::CZMQ::FFI.zsock_set_conflate(self_p, conflate)
        result
      end

      # Set socket option `conflate`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_conflate.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param conflate [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_conflate(self_p, conflate)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        conflate = Integer(conflate)
        result = ::CZMQ::FFI.zsock_set_conflate(self_p, conflate)
        result
      end

      # Get socket option `zap_domain`.
      # Available from libzmq 4.0.0.
      #
      # @return [::FFI::AutoPointer]
      def zap_domain()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_zap_domain(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `zap_domain`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #zap_domain.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.zap_domain(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_zap_domain(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `zap_domain`.
      # Available from libzmq 4.0.0.
      #
      # @param zap_domain [String, #to_s, nil]
      # @return [void]
      def set_zap_domain(zap_domain)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_zap_domain(self_p, zap_domain)
        result
      end

      # Set socket option `zap_domain`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_zap_domain.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param zap_domain [String, #to_s, nil]
      # @return [void]
      def self.set_zap_domain(self_p, zap_domain)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_zap_domain(self_p, zap_domain)
        result
      end

      # Get socket option `mechanism`.
      # Available from libzmq 4.0.0.
      #
      # @return [Integer]
      def mechanism()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_mechanism(self_p)
        result
      end

      # Get socket option `mechanism`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #mechanism.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.mechanism(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_mechanism(self_p)
        result
      end

      # Get socket option `plain_server`.
      # Available from libzmq 4.0.0.
      #
      # @return [Integer]
      def plain_server()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_plain_server(self_p)
        result
      end

      # Get socket option `plain_server`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #plain_server.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.plain_server(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_plain_server(self_p)
        result
      end

      # Set socket option `plain_server`.
      # Available from libzmq 4.0.0.
      #
      # @param plain_server [Integer, #to_int, #to_i]
      # @return [void]
      def set_plain_server(plain_server)
        raise DestroyedError unless @ptr
        self_p = @ptr
        plain_server = Integer(plain_server)
        result = ::CZMQ::FFI.zsock_set_plain_server(self_p, plain_server)
        result
      end

      # Set socket option `plain_server`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_plain_server.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param plain_server [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_plain_server(self_p, plain_server)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        plain_server = Integer(plain_server)
        result = ::CZMQ::FFI.zsock_set_plain_server(self_p, plain_server)
        result
      end

      # Get socket option `plain_username`.
      # Available from libzmq 4.0.0.
      #
      # @return [::FFI::AutoPointer]
      def plain_username()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_plain_username(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `plain_username`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #plain_username.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.plain_username(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_plain_username(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `plain_username`.
      # Available from libzmq 4.0.0.
      #
      # @param plain_username [String, #to_s, nil]
      # @return [void]
      def set_plain_username(plain_username)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_plain_username(self_p, plain_username)
        result
      end

      # Set socket option `plain_username`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_plain_username.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param plain_username [String, #to_s, nil]
      # @return [void]
      def self.set_plain_username(self_p, plain_username)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_plain_username(self_p, plain_username)
        result
      end

      # Get socket option `plain_password`.
      # Available from libzmq 4.0.0.
      #
      # @return [::FFI::AutoPointer]
      def plain_password()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_plain_password(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `plain_password`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #plain_password.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.plain_password(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_plain_password(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `plain_password`.
      # Available from libzmq 4.0.0.
      #
      # @param plain_password [String, #to_s, nil]
      # @return [void]
      def set_plain_password(plain_password)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_plain_password(self_p, plain_password)
        result
      end

      # Set socket option `plain_password`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_plain_password.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param plain_password [String, #to_s, nil]
      # @return [void]
      def self.set_plain_password(self_p, plain_password)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_plain_password(self_p, plain_password)
        result
      end

      # Get socket option `curve_server`.
      # Available from libzmq 4.0.0.
      #
      # @return [Integer]
      def curve_server()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_curve_server(self_p)
        result
      end

      # Get socket option `curve_server`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #curve_server.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.curve_server(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_curve_server(self_p)
        result
      end

      # Set socket option `curve_server`.
      # Available from libzmq 4.0.0.
      #
      # @param curve_server [Integer, #to_int, #to_i]
      # @return [void]
      def set_curve_server(curve_server)
        raise DestroyedError unless @ptr
        self_p = @ptr
        curve_server = Integer(curve_server)
        result = ::CZMQ::FFI.zsock_set_curve_server(self_p, curve_server)
        result
      end

      # Set socket option `curve_server`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_curve_server.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param curve_server [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_curve_server(self_p, curve_server)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        curve_server = Integer(curve_server)
        result = ::CZMQ::FFI.zsock_set_curve_server(self_p, curve_server)
        result
      end

      # Get socket option `curve_publickey`.
      # Available from libzmq 4.0.0.
      #
      # @return [::FFI::AutoPointer]
      def curve_publickey()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_curve_publickey(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `curve_publickey`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #curve_publickey.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.curve_publickey(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_curve_publickey(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `curve_publickey`.
      # Available from libzmq 4.0.0.
      #
      # @param curve_publickey [String, #to_s, nil]
      # @return [void]
      def set_curve_publickey(curve_publickey)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_curve_publickey(self_p, curve_publickey)
        result
      end

      # Set socket option `curve_publickey`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_curve_publickey.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param curve_publickey [String, #to_s, nil]
      # @return [void]
      def self.set_curve_publickey(self_p, curve_publickey)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_curve_publickey(self_p, curve_publickey)
        result
      end

      # Set socket option `curve_publickey` from 32-octet binary
      # Available from libzmq 4.0.0.
      #
      # @param curve_publickey [::FFI::Pointer, #to_ptr]
      # @return [void]
      def set_curve_publickey_bin(curve_publickey)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_curve_publickey_bin(self_p, curve_publickey)
        result
      end

      # Set socket option `curve_publickey` from 32-octet binary
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_curve_publickey_bin.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param curve_publickey [::FFI::Pointer, #to_ptr]
      # @return [void]
      def self.set_curve_publickey_bin(self_p, curve_publickey)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_curve_publickey_bin(self_p, curve_publickey)
        result
      end

      # Get socket option `curve_secretkey`.
      # Available from libzmq 4.0.0.
      #
      # @return [::FFI::AutoPointer]
      def curve_secretkey()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_curve_secretkey(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `curve_secretkey`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #curve_secretkey.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.curve_secretkey(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_curve_secretkey(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `curve_secretkey`.
      # Available from libzmq 4.0.0.
      #
      # @param curve_secretkey [String, #to_s, nil]
      # @return [void]
      def set_curve_secretkey(curve_secretkey)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_curve_secretkey(self_p, curve_secretkey)
        result
      end

      # Set socket option `curve_secretkey`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_curve_secretkey.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param curve_secretkey [String, #to_s, nil]
      # @return [void]
      def self.set_curve_secretkey(self_p, curve_secretkey)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_curve_secretkey(self_p, curve_secretkey)
        result
      end

      # Set socket option `curve_secretkey` from 32-octet binary
      # Available from libzmq 4.0.0.
      #
      # @param curve_secretkey [::FFI::Pointer, #to_ptr]
      # @return [void]
      def set_curve_secretkey_bin(curve_secretkey)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_curve_secretkey_bin(self_p, curve_secretkey)
        result
      end

      # Set socket option `curve_secretkey` from 32-octet binary
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_curve_secretkey_bin.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param curve_secretkey [::FFI::Pointer, #to_ptr]
      # @return [void]
      def self.set_curve_secretkey_bin(self_p, curve_secretkey)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_curve_secretkey_bin(self_p, curve_secretkey)
        result
      end

      # Get socket option `curve_serverkey`.
      # Available from libzmq 4.0.0.
      #
      # @return [::FFI::AutoPointer]
      def curve_serverkey()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_curve_serverkey(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `curve_serverkey`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #curve_serverkey.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.curve_serverkey(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_curve_serverkey(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `curve_serverkey`.
      # Available from libzmq 4.0.0.
      #
      # @param curve_serverkey [String, #to_s, nil]
      # @return [void]
      def set_curve_serverkey(curve_serverkey)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_curve_serverkey(self_p, curve_serverkey)
        result
      end

      # Set socket option `curve_serverkey`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_curve_serverkey.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param curve_serverkey [String, #to_s, nil]
      # @return [void]
      def self.set_curve_serverkey(self_p, curve_serverkey)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_curve_serverkey(self_p, curve_serverkey)
        result
      end

      # Set socket option `curve_serverkey` from 32-octet binary
      # Available from libzmq 4.0.0.
      #
      # @param curve_serverkey [::FFI::Pointer, #to_ptr]
      # @return [void]
      def set_curve_serverkey_bin(curve_serverkey)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_curve_serverkey_bin(self_p, curve_serverkey)
        result
      end

      # Set socket option `curve_serverkey` from 32-octet binary
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_curve_serverkey_bin.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param curve_serverkey [::FFI::Pointer, #to_ptr]
      # @return [void]
      def self.set_curve_serverkey_bin(self_p, curve_serverkey)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_curve_serverkey_bin(self_p, curve_serverkey)
        result
      end

      # Get socket option `gssapi_server`.
      # Available from libzmq 4.0.0.
      #
      # @return [Integer]
      def gssapi_server()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_gssapi_server(self_p)
        result
      end

      # Get socket option `gssapi_server`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #gssapi_server.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.gssapi_server(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_gssapi_server(self_p)
        result
      end

      # Set socket option `gssapi_server`.
      # Available from libzmq 4.0.0.
      #
      # @param gssapi_server [Integer, #to_int, #to_i]
      # @return [void]
      def set_gssapi_server(gssapi_server)
        raise DestroyedError unless @ptr
        self_p = @ptr
        gssapi_server = Integer(gssapi_server)
        result = ::CZMQ::FFI.zsock_set_gssapi_server(self_p, gssapi_server)
        result
      end

      # Set socket option `gssapi_server`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_gssapi_server.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param gssapi_server [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_gssapi_server(self_p, gssapi_server)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        gssapi_server = Integer(gssapi_server)
        result = ::CZMQ::FFI.zsock_set_gssapi_server(self_p, gssapi_server)
        result
      end

      # Get socket option `gssapi_plaintext`.
      # Available from libzmq 4.0.0.
      #
      # @return [Integer]
      def gssapi_plaintext()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_gssapi_plaintext(self_p)
        result
      end

      # Get socket option `gssapi_plaintext`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #gssapi_plaintext.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.gssapi_plaintext(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_gssapi_plaintext(self_p)
        result
      end

      # Set socket option `gssapi_plaintext`.
      # Available from libzmq 4.0.0.
      #
      # @param gssapi_plaintext [Integer, #to_int, #to_i]
      # @return [void]
      def set_gssapi_plaintext(gssapi_plaintext)
        raise DestroyedError unless @ptr
        self_p = @ptr
        gssapi_plaintext = Integer(gssapi_plaintext)
        result = ::CZMQ::FFI.zsock_set_gssapi_plaintext(self_p, gssapi_plaintext)
        result
      end

      # Set socket option `gssapi_plaintext`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_gssapi_plaintext.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param gssapi_plaintext [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_gssapi_plaintext(self_p, gssapi_plaintext)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        gssapi_plaintext = Integer(gssapi_plaintext)
        result = ::CZMQ::FFI.zsock_set_gssapi_plaintext(self_p, gssapi_plaintext)
        result
      end

      # Get socket option `gssapi_principal`.
      # Available from libzmq 4.0.0.
      #
      # @return [::FFI::AutoPointer]
      def gssapi_principal()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_gssapi_principal(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `gssapi_principal`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #gssapi_principal.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.gssapi_principal(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_gssapi_principal(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `gssapi_principal`.
      # Available from libzmq 4.0.0.
      #
      # @param gssapi_principal [String, #to_s, nil]
      # @return [void]
      def set_gssapi_principal(gssapi_principal)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_gssapi_principal(self_p, gssapi_principal)
        result
      end

      # Set socket option `gssapi_principal`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_gssapi_principal.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param gssapi_principal [String, #to_s, nil]
      # @return [void]
      def self.set_gssapi_principal(self_p, gssapi_principal)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_gssapi_principal(self_p, gssapi_principal)
        result
      end

      # Get socket option `gssapi_service_principal`.
      # Available from libzmq 4.0.0.
      #
      # @return [::FFI::AutoPointer]
      def gssapi_service_principal()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_gssapi_service_principal(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `gssapi_service_principal`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #gssapi_service_principal.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.gssapi_service_principal(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_gssapi_service_principal(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `gssapi_service_principal`.
      # Available from libzmq 4.0.0.
      #
      # @param gssapi_service_principal [String, #to_s, nil]
      # @return [void]
      def set_gssapi_service_principal(gssapi_service_principal)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_gssapi_service_principal(self_p, gssapi_service_principal)
        result
      end

      # Set socket option `gssapi_service_principal`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_gssapi_service_principal.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param gssapi_service_principal [String, #to_s, nil]
      # @return [void]
      def self.set_gssapi_service_principal(self_p, gssapi_service_principal)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_gssapi_service_principal(self_p, gssapi_service_principal)
        result
      end

      # Get socket option `ipv6`.
      # Available from libzmq 4.0.0.
      #
      # @return [Integer]
      def ipv6()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_ipv6(self_p)
        result
      end

      # Get socket option `ipv6`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #ipv6.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.ipv6(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_ipv6(self_p)
        result
      end

      # Set socket option `ipv6`.
      # Available from libzmq 4.0.0.
      #
      # @param ipv6 [Integer, #to_int, #to_i]
      # @return [void]
      def set_ipv6(ipv6)
        raise DestroyedError unless @ptr
        self_p = @ptr
        ipv6 = Integer(ipv6)
        result = ::CZMQ::FFI.zsock_set_ipv6(self_p, ipv6)
        result
      end

      # Set socket option `ipv6`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_ipv6.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param ipv6 [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_ipv6(self_p, ipv6)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        ipv6 = Integer(ipv6)
        result = ::CZMQ::FFI.zsock_set_ipv6(self_p, ipv6)
        result
      end

      # Get socket option `immediate`.
      # Available from libzmq 4.0.0.
      #
      # @return [Integer]
      def immediate()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_immediate(self_p)
        result
      end

      # Get socket option `immediate`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #immediate.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.immediate(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_immediate(self_p)
        result
      end

      # Set socket option `immediate`.
      # Available from libzmq 4.0.0.
      #
      # @param immediate [Integer, #to_int, #to_i]
      # @return [void]
      def set_immediate(immediate)
        raise DestroyedError unless @ptr
        self_p = @ptr
        immediate = Integer(immediate)
        result = ::CZMQ::FFI.zsock_set_immediate(self_p, immediate)
        result
      end

      # Set socket option `immediate`.
      # Available from libzmq 4.0.0.
      #
      # This is the polymorphic version of #set_immediate.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param immediate [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_immediate(self_p, immediate)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        immediate = Integer(immediate)
        result = ::CZMQ::FFI.zsock_set_immediate(self_p, immediate)
        result
      end

      # Get socket option `sndhwm`.
      # Available from libzmq 3.0.0.
      #
      # @return [Integer]
      def sndhwm()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_sndhwm(self_p)
        result
      end

      # Get socket option `sndhwm`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #sndhwm.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.sndhwm(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_sndhwm(self_p)
        result
      end

      # Set socket option `sndhwm`.
      # Available from libzmq 3.0.0.
      #
      # @param sndhwm [Integer, #to_int, #to_i]
      # @return [void]
      def set_sndhwm(sndhwm)
        raise DestroyedError unless @ptr
        self_p = @ptr
        sndhwm = Integer(sndhwm)
        result = ::CZMQ::FFI.zsock_set_sndhwm(self_p, sndhwm)
        result
      end

      # Set socket option `sndhwm`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_sndhwm.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param sndhwm [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_sndhwm(self_p, sndhwm)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        sndhwm = Integer(sndhwm)
        result = ::CZMQ::FFI.zsock_set_sndhwm(self_p, sndhwm)
        result
      end

      # Get socket option `rcvhwm`.
      # Available from libzmq 3.0.0.
      #
      # @return [Integer]
      def rcvhwm()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_rcvhwm(self_p)
        result
      end

      # Get socket option `rcvhwm`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #rcvhwm.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.rcvhwm(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_rcvhwm(self_p)
        result
      end

      # Set socket option `rcvhwm`.
      # Available from libzmq 3.0.0.
      #
      # @param rcvhwm [Integer, #to_int, #to_i]
      # @return [void]
      def set_rcvhwm(rcvhwm)
        raise DestroyedError unless @ptr
        self_p = @ptr
        rcvhwm = Integer(rcvhwm)
        result = ::CZMQ::FFI.zsock_set_rcvhwm(self_p, rcvhwm)
        result
      end

      # Set socket option `rcvhwm`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_rcvhwm.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param rcvhwm [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_rcvhwm(self_p, rcvhwm)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        rcvhwm = Integer(rcvhwm)
        result = ::CZMQ::FFI.zsock_set_rcvhwm(self_p, rcvhwm)
        result
      end

      # Get socket option `maxmsgsize`.
      # Available from libzmq 3.0.0.
      #
      # @return [Integer]
      def maxmsgsize()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_maxmsgsize(self_p)
        result
      end

      # Get socket option `maxmsgsize`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #maxmsgsize.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.maxmsgsize(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_maxmsgsize(self_p)
        result
      end

      # Set socket option `maxmsgsize`.
      # Available from libzmq 3.0.0.
      #
      # @param maxmsgsize [Integer, #to_int, #to_i]
      # @return [void]
      def set_maxmsgsize(maxmsgsize)
        raise DestroyedError unless @ptr
        self_p = @ptr
        maxmsgsize = Integer(maxmsgsize)
        result = ::CZMQ::FFI.zsock_set_maxmsgsize(self_p, maxmsgsize)
        result
      end

      # Set socket option `maxmsgsize`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_maxmsgsize.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param maxmsgsize [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_maxmsgsize(self_p, maxmsgsize)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        maxmsgsize = Integer(maxmsgsize)
        result = ::CZMQ::FFI.zsock_set_maxmsgsize(self_p, maxmsgsize)
        result
      end

      # Get socket option `multicast_hops`.
      # Available from libzmq 3.0.0.
      #
      # @return [Integer]
      def multicast_hops()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_multicast_hops(self_p)
        result
      end

      # Get socket option `multicast_hops`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #multicast_hops.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.multicast_hops(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_multicast_hops(self_p)
        result
      end

      # Set socket option `multicast_hops`.
      # Available from libzmq 3.0.0.
      #
      # @param multicast_hops [Integer, #to_int, #to_i]
      # @return [void]
      def set_multicast_hops(multicast_hops)
        raise DestroyedError unless @ptr
        self_p = @ptr
        multicast_hops = Integer(multicast_hops)
        result = ::CZMQ::FFI.zsock_set_multicast_hops(self_p, multicast_hops)
        result
      end

      # Set socket option `multicast_hops`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_multicast_hops.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param multicast_hops [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_multicast_hops(self_p, multicast_hops)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        multicast_hops = Integer(multicast_hops)
        result = ::CZMQ::FFI.zsock_set_multicast_hops(self_p, multicast_hops)
        result
      end

      # Set socket option `xpub_verbose`.
      # Available from libzmq 3.0.0.
      #
      # @param xpub_verbose [Integer, #to_int, #to_i]
      # @return [void]
      def set_xpub_verbose(xpub_verbose)
        raise DestroyedError unless @ptr
        self_p = @ptr
        xpub_verbose = Integer(xpub_verbose)
        result = ::CZMQ::FFI.zsock_set_xpub_verbose(self_p, xpub_verbose)
        result
      end

      # Set socket option `xpub_verbose`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_xpub_verbose.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param xpub_verbose [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_xpub_verbose(self_p, xpub_verbose)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        xpub_verbose = Integer(xpub_verbose)
        result = ::CZMQ::FFI.zsock_set_xpub_verbose(self_p, xpub_verbose)
        result
      end

      # Get socket option `tcp_keepalive`.
      # Available from libzmq 3.0.0.
      #
      # @return [Integer]
      def tcp_keepalive()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_tcp_keepalive(self_p)
        result
      end

      # Get socket option `tcp_keepalive`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #tcp_keepalive.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.tcp_keepalive(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_tcp_keepalive(self_p)
        result
      end

      # Set socket option `tcp_keepalive`.
      # Available from libzmq 3.0.0.
      #
      # @param tcp_keepalive [Integer, #to_int, #to_i]
      # @return [void]
      def set_tcp_keepalive(tcp_keepalive)
        raise DestroyedError unless @ptr
        self_p = @ptr
        tcp_keepalive = Integer(tcp_keepalive)
        result = ::CZMQ::FFI.zsock_set_tcp_keepalive(self_p, tcp_keepalive)
        result
      end

      # Set socket option `tcp_keepalive`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_tcp_keepalive.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param tcp_keepalive [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_tcp_keepalive(self_p, tcp_keepalive)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        tcp_keepalive = Integer(tcp_keepalive)
        result = ::CZMQ::FFI.zsock_set_tcp_keepalive(self_p, tcp_keepalive)
        result
      end

      # Get socket option `tcp_keepalive_idle`.
      # Available from libzmq 3.0.0.
      #
      # @return [Integer]
      def tcp_keepalive_idle()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_tcp_keepalive_idle(self_p)
        result
      end

      # Get socket option `tcp_keepalive_idle`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #tcp_keepalive_idle.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.tcp_keepalive_idle(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_tcp_keepalive_idle(self_p)
        result
      end

      # Set socket option `tcp_keepalive_idle`.
      # Available from libzmq 3.0.0.
      #
      # @param tcp_keepalive_idle [Integer, #to_int, #to_i]
      # @return [void]
      def set_tcp_keepalive_idle(tcp_keepalive_idle)
        raise DestroyedError unless @ptr
        self_p = @ptr
        tcp_keepalive_idle = Integer(tcp_keepalive_idle)
        result = ::CZMQ::FFI.zsock_set_tcp_keepalive_idle(self_p, tcp_keepalive_idle)
        result
      end

      # Set socket option `tcp_keepalive_idle`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_tcp_keepalive_idle.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param tcp_keepalive_idle [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_tcp_keepalive_idle(self_p, tcp_keepalive_idle)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        tcp_keepalive_idle = Integer(tcp_keepalive_idle)
        result = ::CZMQ::FFI.zsock_set_tcp_keepalive_idle(self_p, tcp_keepalive_idle)
        result
      end

      # Get socket option `tcp_keepalive_cnt`.
      # Available from libzmq 3.0.0.
      #
      # @return [Integer]
      def tcp_keepalive_cnt()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_tcp_keepalive_cnt(self_p)
        result
      end

      # Get socket option `tcp_keepalive_cnt`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #tcp_keepalive_cnt.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.tcp_keepalive_cnt(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_tcp_keepalive_cnt(self_p)
        result
      end

      # Set socket option `tcp_keepalive_cnt`.
      # Available from libzmq 3.0.0.
      #
      # @param tcp_keepalive_cnt [Integer, #to_int, #to_i]
      # @return [void]
      def set_tcp_keepalive_cnt(tcp_keepalive_cnt)
        raise DestroyedError unless @ptr
        self_p = @ptr
        tcp_keepalive_cnt = Integer(tcp_keepalive_cnt)
        result = ::CZMQ::FFI.zsock_set_tcp_keepalive_cnt(self_p, tcp_keepalive_cnt)
        result
      end

      # Set socket option `tcp_keepalive_cnt`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_tcp_keepalive_cnt.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param tcp_keepalive_cnt [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_tcp_keepalive_cnt(self_p, tcp_keepalive_cnt)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        tcp_keepalive_cnt = Integer(tcp_keepalive_cnt)
        result = ::CZMQ::FFI.zsock_set_tcp_keepalive_cnt(self_p, tcp_keepalive_cnt)
        result
      end

      # Get socket option `tcp_keepalive_intvl`.
      # Available from libzmq 3.0.0.
      #
      # @return [Integer]
      def tcp_keepalive_intvl()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_tcp_keepalive_intvl(self_p)
        result
      end

      # Get socket option `tcp_keepalive_intvl`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #tcp_keepalive_intvl.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.tcp_keepalive_intvl(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_tcp_keepalive_intvl(self_p)
        result
      end

      # Set socket option `tcp_keepalive_intvl`.
      # Available from libzmq 3.0.0.
      #
      # @param tcp_keepalive_intvl [Integer, #to_int, #to_i]
      # @return [void]
      def set_tcp_keepalive_intvl(tcp_keepalive_intvl)
        raise DestroyedError unless @ptr
        self_p = @ptr
        tcp_keepalive_intvl = Integer(tcp_keepalive_intvl)
        result = ::CZMQ::FFI.zsock_set_tcp_keepalive_intvl(self_p, tcp_keepalive_intvl)
        result
      end

      # Set socket option `tcp_keepalive_intvl`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_tcp_keepalive_intvl.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param tcp_keepalive_intvl [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_tcp_keepalive_intvl(self_p, tcp_keepalive_intvl)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        tcp_keepalive_intvl = Integer(tcp_keepalive_intvl)
        result = ::CZMQ::FFI.zsock_set_tcp_keepalive_intvl(self_p, tcp_keepalive_intvl)
        result
      end

      # Get socket option `tcp_accept_filter`.
      # Available from libzmq 3.0.0.
      #
      # @return [::FFI::AutoPointer]
      def tcp_accept_filter()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_tcp_accept_filter(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `tcp_accept_filter`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #tcp_accept_filter.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.tcp_accept_filter(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_tcp_accept_filter(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `tcp_accept_filter`.
      # Available from libzmq 3.0.0.
      #
      # @param tcp_accept_filter [String, #to_s, nil]
      # @return [void]
      def set_tcp_accept_filter(tcp_accept_filter)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_tcp_accept_filter(self_p, tcp_accept_filter)
        result
      end

      # Set socket option `tcp_accept_filter`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_tcp_accept_filter.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param tcp_accept_filter [String, #to_s, nil]
      # @return [void]
      def self.set_tcp_accept_filter(self_p, tcp_accept_filter)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_tcp_accept_filter(self_p, tcp_accept_filter)
        result
      end

      # Get socket option `last_endpoint`.
      # Available from libzmq 3.0.0.
      #
      # @return [::FFI::AutoPointer]
      def last_endpoint()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_last_endpoint(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `last_endpoint`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #last_endpoint.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.last_endpoint(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_last_endpoint(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `router_raw`.
      # Available from libzmq 3.0.0.
      #
      # @param router_raw [Integer, #to_int, #to_i]
      # @return [void]
      def set_router_raw(router_raw)
        raise DestroyedError unless @ptr
        self_p = @ptr
        router_raw = Integer(router_raw)
        result = ::CZMQ::FFI.zsock_set_router_raw(self_p, router_raw)
        result
      end

      # Set socket option `router_raw`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_router_raw.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param router_raw [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_router_raw(self_p, router_raw)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        router_raw = Integer(router_raw)
        result = ::CZMQ::FFI.zsock_set_router_raw(self_p, router_raw)
        result
      end

      # Get socket option `ipv4only`.
      # Available from libzmq 3.0.0.
      #
      # @return [Integer]
      def ipv4only()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_ipv4only(self_p)
        result
      end

      # Get socket option `ipv4only`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #ipv4only.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.ipv4only(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_ipv4only(self_p)
        result
      end

      # Set socket option `ipv4only`.
      # Available from libzmq 3.0.0.
      #
      # @param ipv4only [Integer, #to_int, #to_i]
      # @return [void]
      def set_ipv4only(ipv4only)
        raise DestroyedError unless @ptr
        self_p = @ptr
        ipv4only = Integer(ipv4only)
        result = ::CZMQ::FFI.zsock_set_ipv4only(self_p, ipv4only)
        result
      end

      # Set socket option `ipv4only`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_ipv4only.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param ipv4only [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_ipv4only(self_p, ipv4only)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        ipv4only = Integer(ipv4only)
        result = ::CZMQ::FFI.zsock_set_ipv4only(self_p, ipv4only)
        result
      end

      # Set socket option `delay_attach_on_connect`.
      # Available from libzmq 3.0.0.
      #
      # @param delay_attach_on_connect [Integer, #to_int, #to_i]
      # @return [void]
      def set_delay_attach_on_connect(delay_attach_on_connect)
        raise DestroyedError unless @ptr
        self_p = @ptr
        delay_attach_on_connect = Integer(delay_attach_on_connect)
        result = ::CZMQ::FFI.zsock_set_delay_attach_on_connect(self_p, delay_attach_on_connect)
        result
      end

      # Set socket option `delay_attach_on_connect`.
      # Available from libzmq 3.0.0.
      #
      # This is the polymorphic version of #set_delay_attach_on_connect.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param delay_attach_on_connect [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_delay_attach_on_connect(self_p, delay_attach_on_connect)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        delay_attach_on_connect = Integer(delay_attach_on_connect)
        result = ::CZMQ::FFI.zsock_set_delay_attach_on_connect(self_p, delay_attach_on_connect)
        result
      end

      # Get socket option `hwm`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # @return [Integer]
      def hwm()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_hwm(self_p)
        result
      end

      # Get socket option `hwm`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # This is the polymorphic version of #hwm.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.hwm(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_hwm(self_p)
        result
      end

      # Set socket option `hwm`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # @param hwm [Integer, #to_int, #to_i]
      # @return [void]
      def set_hwm(hwm)
        raise DestroyedError unless @ptr
        self_p = @ptr
        hwm = Integer(hwm)
        result = ::CZMQ::FFI.zsock_set_hwm(self_p, hwm)
        result
      end

      # Set socket option `hwm`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # This is the polymorphic version of #set_hwm.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param hwm [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_hwm(self_p, hwm)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        hwm = Integer(hwm)
        result = ::CZMQ::FFI.zsock_set_hwm(self_p, hwm)
        result
      end

      # Get socket option `swap`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # @return [Integer]
      def swap()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_swap(self_p)
        result
      end

      # Get socket option `swap`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # This is the polymorphic version of #swap.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.swap(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_swap(self_p)
        result
      end

      # Set socket option `swap`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # @param swap [Integer, #to_int, #to_i]
      # @return [void]
      def set_swap(swap)
        raise DestroyedError unless @ptr
        self_p = @ptr
        swap = Integer(swap)
        result = ::CZMQ::FFI.zsock_set_swap(self_p, swap)
        result
      end

      # Set socket option `swap`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # This is the polymorphic version of #set_swap.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param swap [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_swap(self_p, swap)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        swap = Integer(swap)
        result = ::CZMQ::FFI.zsock_set_swap(self_p, swap)
        result
      end

      # Get socket option `affinity`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer]
      def affinity()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_affinity(self_p)
        result
      end

      # Get socket option `affinity`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #affinity.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.affinity(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_affinity(self_p)
        result
      end

      # Set socket option `affinity`.
      # Available from libzmq 2.0.0.
      #
      # @param affinity [Integer, #to_int, #to_i]
      # @return [void]
      def set_affinity(affinity)
        raise DestroyedError unless @ptr
        self_p = @ptr
        affinity = Integer(affinity)
        result = ::CZMQ::FFI.zsock_set_affinity(self_p, affinity)
        result
      end

      # Set socket option `affinity`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #set_affinity.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param affinity [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_affinity(self_p, affinity)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        affinity = Integer(affinity)
        result = ::CZMQ::FFI.zsock_set_affinity(self_p, affinity)
        result
      end

      # Get socket option `identity`.
      # Available from libzmq 2.0.0.
      #
      # @return [::FFI::AutoPointer]
      def identity()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_identity(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Get socket option `identity`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #identity.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [::FFI::AutoPointer]
      def self.identity(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_identity(self_p)
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Set socket option `identity`.
      # Available from libzmq 2.0.0.
      #
      # @param identity [String, #to_s, nil]
      # @return [void]
      def set_identity(identity)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_identity(self_p, identity)
        result
      end

      # Set socket option `identity`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #set_identity.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param identity [String, #to_s, nil]
      # @return [void]
      def self.set_identity(self_p, identity)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_identity(self_p, identity)
        result
      end

      # Get socket option `rate`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer]
      def rate()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_rate(self_p)
        result
      end

      # Get socket option `rate`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #rate.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.rate(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_rate(self_p)
        result
      end

      # Set socket option `rate`.
      # Available from libzmq 2.0.0.
      #
      # @param rate [Integer, #to_int, #to_i]
      # @return [void]
      def set_rate(rate)
        raise DestroyedError unless @ptr
        self_p = @ptr
        rate = Integer(rate)
        result = ::CZMQ::FFI.zsock_set_rate(self_p, rate)
        result
      end

      # Set socket option `rate`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #set_rate.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param rate [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_rate(self_p, rate)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        rate = Integer(rate)
        result = ::CZMQ::FFI.zsock_set_rate(self_p, rate)
        result
      end

      # Get socket option `recovery_ivl`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer]
      def recovery_ivl()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_recovery_ivl(self_p)
        result
      end

      # Get socket option `recovery_ivl`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #recovery_ivl.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.recovery_ivl(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_recovery_ivl(self_p)
        result
      end

      # Set socket option `recovery_ivl`.
      # Available from libzmq 2.0.0.
      #
      # @param recovery_ivl [Integer, #to_int, #to_i]
      # @return [void]
      def set_recovery_ivl(recovery_ivl)
        raise DestroyedError unless @ptr
        self_p = @ptr
        recovery_ivl = Integer(recovery_ivl)
        result = ::CZMQ::FFI.zsock_set_recovery_ivl(self_p, recovery_ivl)
        result
      end

      # Set socket option `recovery_ivl`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #set_recovery_ivl.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param recovery_ivl [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_recovery_ivl(self_p, recovery_ivl)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        recovery_ivl = Integer(recovery_ivl)
        result = ::CZMQ::FFI.zsock_set_recovery_ivl(self_p, recovery_ivl)
        result
      end

      # Get socket option `recovery_ivl_msec`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # @return [Integer]
      def recovery_ivl_msec()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_recovery_ivl_msec(self_p)
        result
      end

      # Get socket option `recovery_ivl_msec`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # This is the polymorphic version of #recovery_ivl_msec.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.recovery_ivl_msec(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_recovery_ivl_msec(self_p)
        result
      end

      # Set socket option `recovery_ivl_msec`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # @param recovery_ivl_msec [Integer, #to_int, #to_i]
      # @return [void]
      def set_recovery_ivl_msec(recovery_ivl_msec)
        raise DestroyedError unless @ptr
        self_p = @ptr
        recovery_ivl_msec = Integer(recovery_ivl_msec)
        result = ::CZMQ::FFI.zsock_set_recovery_ivl_msec(self_p, recovery_ivl_msec)
        result
      end

      # Set socket option `recovery_ivl_msec`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # This is the polymorphic version of #set_recovery_ivl_msec.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param recovery_ivl_msec [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_recovery_ivl_msec(self_p, recovery_ivl_msec)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        recovery_ivl_msec = Integer(recovery_ivl_msec)
        result = ::CZMQ::FFI.zsock_set_recovery_ivl_msec(self_p, recovery_ivl_msec)
        result
      end

      # Get socket option `mcast_loop`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # @return [Integer]
      def mcast_loop()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_mcast_loop(self_p)
        result
      end

      # Get socket option `mcast_loop`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # This is the polymorphic version of #mcast_loop.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.mcast_loop(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_mcast_loop(self_p)
        result
      end

      # Set socket option `mcast_loop`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # @param mcast_loop [Integer, #to_int, #to_i]
      # @return [void]
      def set_mcast_loop(mcast_loop)
        raise DestroyedError unless @ptr
        self_p = @ptr
        mcast_loop = Integer(mcast_loop)
        result = ::CZMQ::FFI.zsock_set_mcast_loop(self_p, mcast_loop)
        result
      end

      # Set socket option `mcast_loop`.
      # Available from libzmq 2.0.0 to 3.0.0.
      #
      # This is the polymorphic version of #set_mcast_loop.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param mcast_loop [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_mcast_loop(self_p, mcast_loop)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        mcast_loop = Integer(mcast_loop)
        result = ::CZMQ::FFI.zsock_set_mcast_loop(self_p, mcast_loop)
        result
      end

      # Get socket option `rcvtimeo`.
      # Available from libzmq 2.2.0.
      #
      # @return [Integer]
      def rcvtimeo()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_rcvtimeo(self_p)
        result
      end

      # Get socket option `rcvtimeo`.
      # Available from libzmq 2.2.0.
      #
      # This is the polymorphic version of #rcvtimeo.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.rcvtimeo(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_rcvtimeo(self_p)
        result
      end

      # Set socket option `rcvtimeo`.
      # Available from libzmq 2.2.0.
      #
      # @param rcvtimeo [Integer, #to_int, #to_i]
      # @return [void]
      def set_rcvtimeo(rcvtimeo)
        raise DestroyedError unless @ptr
        self_p = @ptr
        rcvtimeo = Integer(rcvtimeo)
        result = ::CZMQ::FFI.zsock_set_rcvtimeo(self_p, rcvtimeo)
        result
      end

      # Set socket option `rcvtimeo`.
      # Available from libzmq 2.2.0.
      #
      # This is the polymorphic version of #set_rcvtimeo.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param rcvtimeo [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_rcvtimeo(self_p, rcvtimeo)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        rcvtimeo = Integer(rcvtimeo)
        result = ::CZMQ::FFI.zsock_set_rcvtimeo(self_p, rcvtimeo)
        result
      end

      # Get socket option `sndtimeo`.
      # Available from libzmq 2.2.0.
      #
      # @return [Integer]
      def sndtimeo()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_sndtimeo(self_p)
        result
      end

      # Get socket option `sndtimeo`.
      # Available from libzmq 2.2.0.
      #
      # This is the polymorphic version of #sndtimeo.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.sndtimeo(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_sndtimeo(self_p)
        result
      end

      # Set socket option `sndtimeo`.
      # Available from libzmq 2.2.0.
      #
      # @param sndtimeo [Integer, #to_int, #to_i]
      # @return [void]
      def set_sndtimeo(sndtimeo)
        raise DestroyedError unless @ptr
        self_p = @ptr
        sndtimeo = Integer(sndtimeo)
        result = ::CZMQ::FFI.zsock_set_sndtimeo(self_p, sndtimeo)
        result
      end

      # Set socket option `sndtimeo`.
      # Available from libzmq 2.2.0.
      #
      # This is the polymorphic version of #set_sndtimeo.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param sndtimeo [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_sndtimeo(self_p, sndtimeo)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        sndtimeo = Integer(sndtimeo)
        result = ::CZMQ::FFI.zsock_set_sndtimeo(self_p, sndtimeo)
        result
      end

      # Get socket option `sndbuf`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer]
      def sndbuf()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_sndbuf(self_p)
        result
      end

      # Get socket option `sndbuf`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #sndbuf.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.sndbuf(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_sndbuf(self_p)
        result
      end

      # Set socket option `sndbuf`.
      # Available from libzmq 2.0.0.
      #
      # @param sndbuf [Integer, #to_int, #to_i]
      # @return [void]
      def set_sndbuf(sndbuf)
        raise DestroyedError unless @ptr
        self_p = @ptr
        sndbuf = Integer(sndbuf)
        result = ::CZMQ::FFI.zsock_set_sndbuf(self_p, sndbuf)
        result
      end

      # Set socket option `sndbuf`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #set_sndbuf.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param sndbuf [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_sndbuf(self_p, sndbuf)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        sndbuf = Integer(sndbuf)
        result = ::CZMQ::FFI.zsock_set_sndbuf(self_p, sndbuf)
        result
      end

      # Get socket option `rcvbuf`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer]
      def rcvbuf()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_rcvbuf(self_p)
        result
      end

      # Get socket option `rcvbuf`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #rcvbuf.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.rcvbuf(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_rcvbuf(self_p)
        result
      end

      # Set socket option `rcvbuf`.
      # Available from libzmq 2.0.0.
      #
      # @param rcvbuf [Integer, #to_int, #to_i]
      # @return [void]
      def set_rcvbuf(rcvbuf)
        raise DestroyedError unless @ptr
        self_p = @ptr
        rcvbuf = Integer(rcvbuf)
        result = ::CZMQ::FFI.zsock_set_rcvbuf(self_p, rcvbuf)
        result
      end

      # Set socket option `rcvbuf`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #set_rcvbuf.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param rcvbuf [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_rcvbuf(self_p, rcvbuf)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        rcvbuf = Integer(rcvbuf)
        result = ::CZMQ::FFI.zsock_set_rcvbuf(self_p, rcvbuf)
        result
      end

      # Get socket option `linger`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer]
      def linger()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_linger(self_p)
        result
      end

      # Get socket option `linger`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #linger.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.linger(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_linger(self_p)
        result
      end

      # Set socket option `linger`.
      # Available from libzmq 2.0.0.
      #
      # @param linger [Integer, #to_int, #to_i]
      # @return [void]
      def set_linger(linger)
        raise DestroyedError unless @ptr
        self_p = @ptr
        linger = Integer(linger)
        result = ::CZMQ::FFI.zsock_set_linger(self_p, linger)
        result
      end

      # Set socket option `linger`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #set_linger.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param linger [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_linger(self_p, linger)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        linger = Integer(linger)
        result = ::CZMQ::FFI.zsock_set_linger(self_p, linger)
        result
      end

      # Get socket option `reconnect_ivl`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer]
      def reconnect_ivl()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_reconnect_ivl(self_p)
        result
      end

      # Get socket option `reconnect_ivl`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #reconnect_ivl.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.reconnect_ivl(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_reconnect_ivl(self_p)
        result
      end

      # Set socket option `reconnect_ivl`.
      # Available from libzmq 2.0.0.
      #
      # @param reconnect_ivl [Integer, #to_int, #to_i]
      # @return [void]
      def set_reconnect_ivl(reconnect_ivl)
        raise DestroyedError unless @ptr
        self_p = @ptr
        reconnect_ivl = Integer(reconnect_ivl)
        result = ::CZMQ::FFI.zsock_set_reconnect_ivl(self_p, reconnect_ivl)
        result
      end

      # Set socket option `reconnect_ivl`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #set_reconnect_ivl.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param reconnect_ivl [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_reconnect_ivl(self_p, reconnect_ivl)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        reconnect_ivl = Integer(reconnect_ivl)
        result = ::CZMQ::FFI.zsock_set_reconnect_ivl(self_p, reconnect_ivl)
        result
      end

      # Get socket option `reconnect_ivl_max`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer]
      def reconnect_ivl_max()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_reconnect_ivl_max(self_p)
        result
      end

      # Get socket option `reconnect_ivl_max`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #reconnect_ivl_max.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.reconnect_ivl_max(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_reconnect_ivl_max(self_p)
        result
      end

      # Set socket option `reconnect_ivl_max`.
      # Available from libzmq 2.0.0.
      #
      # @param reconnect_ivl_max [Integer, #to_int, #to_i]
      # @return [void]
      def set_reconnect_ivl_max(reconnect_ivl_max)
        raise DestroyedError unless @ptr
        self_p = @ptr
        reconnect_ivl_max = Integer(reconnect_ivl_max)
        result = ::CZMQ::FFI.zsock_set_reconnect_ivl_max(self_p, reconnect_ivl_max)
        result
      end

      # Set socket option `reconnect_ivl_max`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #set_reconnect_ivl_max.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param reconnect_ivl_max [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_reconnect_ivl_max(self_p, reconnect_ivl_max)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        reconnect_ivl_max = Integer(reconnect_ivl_max)
        result = ::CZMQ::FFI.zsock_set_reconnect_ivl_max(self_p, reconnect_ivl_max)
        result
      end

      # Get socket option `backlog`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer]
      def backlog()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_backlog(self_p)
        result
      end

      # Get socket option `backlog`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #backlog.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.backlog(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_backlog(self_p)
        result
      end

      # Set socket option `backlog`.
      # Available from libzmq 2.0.0.
      #
      # @param backlog [Integer, #to_int, #to_i]
      # @return [void]
      def set_backlog(backlog)
        raise DestroyedError unless @ptr
        self_p = @ptr
        backlog = Integer(backlog)
        result = ::CZMQ::FFI.zsock_set_backlog(self_p, backlog)
        result
      end

      # Set socket option `backlog`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #set_backlog.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param backlog [Integer, #to_int, #to_i]
      # @return [void]
      def self.set_backlog(self_p, backlog)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        backlog = Integer(backlog)
        result = ::CZMQ::FFI.zsock_set_backlog(self_p, backlog)
        result
      end

      # Set socket option `subscribe`.
      # Available from libzmq 2.0.0.
      #
      # @param subscribe [String, #to_s, nil]
      # @return [void]
      def set_subscribe(subscribe)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_subscribe(self_p, subscribe)
        result
      end

      # Set socket option `subscribe`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #set_subscribe.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param subscribe [String, #to_s, nil]
      # @return [void]
      def self.set_subscribe(self_p, subscribe)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_subscribe(self_p, subscribe)
        result
      end

      # Set socket option `unsubscribe`.
      # Available from libzmq 2.0.0.
      #
      # @param unsubscribe [String, #to_s, nil]
      # @return [void]
      def set_unsubscribe(unsubscribe)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_set_unsubscribe(self_p, unsubscribe)
        result
      end

      # Set socket option `unsubscribe`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #set_unsubscribe.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @param unsubscribe [String, #to_s, nil]
      # @return [void]
      def self.set_unsubscribe(self_p, unsubscribe)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_set_unsubscribe(self_p, unsubscribe)
        result
      end

      # Get socket option `type`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer]
      def type()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_type(self_p)
        result
      end

      # Get socket option `type`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #type.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.type(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_type(self_p)
        result
      end

      # Get socket option `rcvmore`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer]
      def rcvmore()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_rcvmore(self_p)
        result
      end

      # Get socket option `rcvmore`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #rcvmore.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.rcvmore(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_rcvmore(self_p)
        result
      end

      # Get socket option `fd`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer or FFI::Pointer]
      def fd()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_fd(self_p)
        result
      end

      # Get socket option `fd`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #fd.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer or FFI::Pointer]
      def self.fd(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_fd(self_p)
        result
      end

      # Get socket option `events`.
      # Available from libzmq 2.0.0.
      #
      # @return [Integer]
      def events()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zsock_events(self_p)
        result
      end

      # Get socket option `events`.
      # Available from libzmq 2.0.0.
      #
      # This is the polymorphic version of #events.
      #
      # @param self_p [CZMQ::Zsock, #__ptr, ::FFI::Pointer, nil]
      #   object reference to use this method on
      # @return [Integer]
      def self.events(self_p)
        self_p = self_p.__ptr if self_p.respond_to?(:__ptr)
        result = ::CZMQ::FFI.zsock_events(self_p)
        result
      end

      # Self test of this class.
      #
      # @param verbose [Boolean]
      # @return [void]
      def self.test(verbose)
        verbose = !(0==verbose||!verbose) # boolean
        result = ::CZMQ::FFI.zsock_test(verbose)
        result
      end
    end
  end
end

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
