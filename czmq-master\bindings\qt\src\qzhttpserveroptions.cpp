/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZhttpServerOptions::QZhttpServerOptions (zhttp_server_options_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Create a new zhttp_server_options.
QZhttpServerOptions::QZhttpServerOptions (QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zhttp_server_options_new ();
}

///
//  Create options from config tree.
QZhttpServerOptions* QZhttpServerOptions::fromConfig (QZconfig *config, QObject *qObjParent)
{
    return new QZhttpServerOptions (zhttp_server_options_from_config (config->self), qObjParent);
}

///
//  Destroy the zhttp_server_options.
QZhttpServerOptions::~QZhttpServerOptions ()
{
    zhttp_server_options_destroy (&self);
}

///
//  Get the server listening port.
int QZhttpServerOptions::port ()
{
    int rv = zhttp_server_options_port (self);
    return rv;
}

///
//  Set the server listening port
void QZhttpServerOptions::setPort (int port)
{
    zhttp_server_options_set_port (self, port);

}

///
//  Get the address sockets should connect to in order to receive requests.
const QString QZhttpServerOptions::backendAddress ()
{
    const QString rv = QString (zhttp_server_options_backend_address (self));
    return rv;
}

///
//  Set the address sockets should connect to in order to receive requests.
void QZhttpServerOptions::setBackendAddress (const QString &address)
{
    zhttp_server_options_set_backend_address (self, address.toUtf8().data());

}

///
//  Self test of this class.
void QZhttpServerOptions::test (bool verbose)
{
    zhttp_server_options_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
