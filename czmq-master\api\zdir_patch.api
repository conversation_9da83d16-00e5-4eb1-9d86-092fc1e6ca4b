<class name = "zdir_patch" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    work with directory patches

    <constant name = "create" value = "1">Creates a new file</constant>
    <constant name = "delete" value = "2">Delete a file</constant>

    <constructor>
        Create new patch
        <argument name = "path" type = "string" />
        <argument name = "file" type = "zfile" />
        <argument name = "op" type = "integer" />
        <argument name = "alias" type = "string" />
    </constructor>

    <destructor>
        Destroy a patch
    </destructor>

    <method name = "dup">
        Create copy of a patch. If the patch is null, or memory was exhausted,
        returns null.
        <return type = "zdir_patch" fresh = "1" />
    </method>

    <method name = "path">
        Return patch file directory path
        <return type = "string" />
    </method>

    <method name = "file">
        Return patch file item
        <return type = "zfile" />
    </method>

    <method name = "op">
        Return operation
        <return type = "integer" />
    </method>

    <method name = "vpath">
        Return patch virtual file path
        <return type = "string" />
    </method>

    <method name = "digest set">
        Calculate hash digest for file (create only)
    </method>

    <method name = "digest">
        Return hash digest for patch file
        <return type = "string" />
    </method>
</class>
