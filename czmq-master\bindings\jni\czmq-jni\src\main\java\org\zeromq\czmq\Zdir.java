/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
package org.zeromq.czmq;

import org.zeromq.tools.ZmqNativeLoader;

import java.util.LinkedHashMap;
import java.util.Map;

public class Zdir implements AutoCloseable {
    static {
        Map<String, Boolean> libraries = new LinkedHashMap<>();
        libraries.put("zmq", false);
        libraries.put("uuid", true);
        libraries.put("systemd", true);
        libraries.put("lz4", true);
        libraries.put("curl", true);
        libraries.put("nss", true);
        libraries.put("microhttpd", true);
        libraries.put("czmq", false);
        libraries.put("czmqjni", false);
        ZmqNativeLoader.loadLibraries(libraries);
    }
    public long self;
    /*
    Create a new directory item that loads in the full tree of the specified
    path, optionally located under some parent path. If parent is "-", then
    loads only the top-level directory, and does not use parent as a path.
    */
    native static long __new (String path, String parent);
    public Zdir (String path, String parent) {
        /*  TODO: if __new fails, self is null...            */
        self = __new (path, parent);
    }
    public Zdir (long pointer) {
        self = pointer;
    }
    /*
    Destroy a directory tree and all children it contains.
    */
    native static void __destroy (long self);
    @Override
    public void close () {
        __destroy (self);
        self = 0;
    }
    /*
    Return directory path
    */
    native static String __path (long self);
    public String path () {
        return __path (self);
    }
    /*
    Return last modification time for directory.
    */
    native static long __modified (long self);
    public long modified () {
        return __modified (self);
    }
    /*
    Return total hierarchy size, in bytes of data contained in all files
    in the directory tree.
    */
    native static long __cursize (long self);
    public long cursize () {
        return __cursize (self);
    }
    /*
    Return directory count
    */
    native static long __count (long self);
    public long count () {
        return __count (self);
    }
    /*
    Returns a sorted list of zfile objects; Each entry in the list is a pointer
    to a zfile_t item already allocated in the zdir tree. Do not destroy the
    original zdir tree until you are done with this list.
    */
    native static long __list (long self);
    public Zlist list () {
        return new Zlist (__list (self));
    }
    /*
    Returns a sorted list of char*; Each entry in the list is a path of a file
    or directory contained in self.
    */
    native static long __listPaths (long self);
    public Zlist listPaths () {
        return new Zlist (__listPaths (self));
    }
    /*
    Remove directory, optionally including all files that it contains, at
    all levels. If force is false, will only remove the directory if empty.
    If force is true, will remove all files and all subdirectories.
    */
    native static void __remove (long self, boolean force);
    public void remove (boolean force) {
        __remove (self, force);
    }
    /*
    Calculate differences between two versions of a directory tree.
    Returns a list of zdir_patch_t patches. Either older or newer may
    be null, indicating the directory is empty/absent. If alias is set,
    generates virtual filename (minus path, plus alias).
    */
    native static long __diff (long older, long newer, String alias);
    public static Zlist diff (Zdir older, Zdir newer, String alias) {
        return new Zlist (__diff (older.self, newer.self, alias));
    }
    /*
    Return full contents of directory as a zdir_patch list.
    */
    native static long __resync (long self, String alias);
    public Zlist resync (String alias) {
        return new Zlist (__resync (self, alias));
    }
    /*
    Load directory cache; returns a hash table containing the SHA-1 digests
    of every file in the tree. The cache is saved between runs in .cache.
    */
    native static long __cache (long self);
    public Zhash cache () {
        return new Zhash (__cache (self));
    }
    /*
    Print contents of directory to stdout
    */
    native static void __print (long self, int indent);
    public void print (int indent) {
        __print (self, indent);
    }
    /*
    Create a new zdir_watch actor instance:

        zactor_t *watch = zactor_new (zdir_watch, NULL);

    Destroy zdir_watch instance:

        zactor_destroy (&watch);

    Enable verbose logging of commands and activity:

        zstr_send (watch, "VERBOSE");

    Subscribe to changes to a directory path:

        zsock_send (watch, "ss", "SUBSCRIBE", "directory_path");

    Unsubscribe from changes to a directory path:

        zsock_send (watch, "ss", "UNSUBSCRIBE", "directory_path");

    Receive directory changes:
        zsock_recv (watch, "sp", &path, &patches);

        // Delete the received data.
        free (path);
        zlist_destroy (&patches);
    */
    native static void __watch (long pipe, long unused);
    public static void watch (Zsock pipe, long unused) {
        __watch (pipe.self, unused);
    }
    /*
    Self test of this class.
    */
    native static void __test (boolean verbose);
    public static void test (boolean verbose) {
        __test (verbose);
    }
}
