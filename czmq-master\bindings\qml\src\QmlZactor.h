/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#ifndef QML_ZACTOR_H
#define QML_ZACTOR_H

#include <QtQml>

#include <czmq.h>
#include "qml_czmq_plugin.h"


class QmlZactor : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isNULL READ isNULL)

public:
    zactor_t *self;

    QmlZactor() { self = NULL; }
    bool isNULL() { return self == NULL; }

    static QObject* qmlAttachedProperties(QObject* object); // defined in QmlZactor.cpp

public slots:
    //  Send a zmsg message to the actor, take ownership of the message
    //  and destroy when it has been sent.
    int send (QmlZmsg *msgP);

    //  Receive a zmsg message from the actor. Returns NULL if the actor
    //  was interrupted before the message could be received, or if there
    //  was a timeout on the actor.
    QmlZmsg *recv ();

    //  Return the actor's zsock handle. Use this when you absolutely need
    //  to work with the zsock instance rather than the actor.
    QmlZsock *sock ();

    //  Change default destructor by custom function. Actor MUST be able to handle new message instead of default $TERM.
    void setDestructor (zactor_destructor_fn destructor);
};

class QmlZactorAttached : public QObject
{
    Q_OBJECT
    QObject* m_attached;

public:
    QmlZactorAttached (QObject* attached) {
        Q_UNUSED (attached);
    };

public slots:
    //  Probe the supplied object, and report if it looks like a zactor_t.
    bool is (void *self);

    //  Probe the supplied reference. If it looks like a zactor_t instance,
    //  return the underlying libzmq actor handle; else if it looks like
    //  a libzmq actor handle, return the supplied value.
    void *resolve (void *self);

    //  Self test of this class.
    void test (bool verbose);

    //  Create a new actor passing arbitrary arguments reference.
    QmlZactor *construct (zactor_fn task, void *args);

    //  Destroy an actor.
    void destruct (QmlZactor *qmlSelf);
};


QML_DECLARE_TYPEINFO(QmlZactor, QML_HAS_ATTACHED_PROPERTIES)

#endif
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
