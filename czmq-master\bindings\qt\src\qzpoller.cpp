/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZpoller::QZpoller (zpoller_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Destroy a poller
QZpoller::~QZpoller ()
{
    zpoller_destroy (&self);
}

///
//  Add a reader to be polled. Returns 0 if OK, -1 on failure. The reader may
//  be a libzmq void * socket, a zsock_t instance, a zactor_t instance or a
//  file handle.
int QZpoller::add (void *reader)
{
    int rv = zpoller_add (self, reader);
    return rv;
}

///
//  Remove a reader from the poller; returns 0 if OK, -1 on failure. The reader
//  must have been passed during construction, or in an zpoller_add () call.
int QZpoller::remove (void *reader)
{
    int rv = zpoller_remove (self, reader);
    return rv;
}

///
//  By default the poller stops if the process receives a SIGINT or SIGTERM
//  signal. This makes it impossible to shut-down message based architectures
//  like zactors. This method lets you switch off break handling. The default
//  nonstop setting is off (false).
void QZpoller::setNonstop (bool nonstop)
{
    zpoller_set_nonstop (self, nonstop);

}

///
//  Poll the registered readers for I/O, return first reader that has input.
//  The reader will be a libzmq void * socket, a zsock_t, a zactor_t
//  instance or a file handle as specified in zpoller_new/zpoller_add. The
//  timeout should be zero or greater, or -1 to wait indefinitely. Socket
//  priority is defined by their order in the poll list. If you need a
//  balanced poll, use the low level zmq_poll method directly. If the poll
//  call was interrupted (SIGINT), or the ZMQ context was destroyed, or the
//  timeout expired, returns NULL. You can test the actual exit condition by
//  calling zpoller_expired () and zpoller_terminated (). The timeout is in
//  msec.
void * QZpoller::wait (int timeout)
{
    void * rv = zpoller_wait (self, timeout);
    return rv;
}

///
//  Return true if the last zpoller_wait () call ended because the timeout
//  expired, without any error.
bool QZpoller::expired ()
{
    bool rv = zpoller_expired (self);
    return rv;
}

///
//  Return true if the last zpoller_wait () call ended because the process
//  was interrupted, or the parent context was destroyed.
bool QZpoller::terminated ()
{
    bool rv = zpoller_terminated (self);
    return rv;
}

///
//  Self test of this class.
void QZpoller::test (bool verbose)
{
    zpoller_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
