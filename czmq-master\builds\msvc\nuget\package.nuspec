<?xml version="1.0" encoding="utf-8"?>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
<package xmlns="http://schemas.microsoft.com/packaging/2013/01/nuspec.xsd">
    <metadata minClientVersion="2.5">
        <id>czmq_vc120</id>
        <version>*******</version>
        <title>czmq_vc120</title>
        <authors>See AUTHORS</authors>
        <owners>Eric <PERSON></owners>
        <requireLicenseAcceptance>false</requireLicenseAcceptance>
        <developmentDependency>false</developmentDependency>
        <description>The high-level C binding for 0MQ</description>
        <summary></summary>
        <copyright>Copyright the AUTHORS</copyright>
        <tags>native</tags>
        <dependencies>
            <dependency id="libzmq_vc120" version="*******" />
        </dependencies>
    </metadata>
    <files>
        <!-- include -->
        <file src="..\..\..\include\*.h" target="build\native\include" />

        <!-- targets -->
        <file src="package.targets" target="build\native\czmq_vc120.targets" />
        <file src="package.xml" target="build\native\package.xml" />

        <!-- docs -->
        <!-- Documents (.*) -->
        <!--<file src="..\..\..\docs\*" target="build\native\docs" />-->

        <!-- libraries -->
        <!-- x86 Dynamic libraries (.dll) -->
        <file src="..\..\..\bin\Win32\Release\v120\dynamic\czmq.dll" target="build\native\bin\czmq-x86-v120-mt-4_2_2_0.dll" />
        <file src="..\..\..\bin\Win32\Debug\v120\dynamic\czmq.dll" target="build\native\bin\czmq-x86-v120-mt-gd-4_2_2_0.dll" />

        <!-- x86 Debugging symbols (.pdb) -->
        <!--<file src="..\..\..\bin\Win32\Release\v120\dynamic\czmq.pdb" target="build\native\bin\czmq-x86-v120-mt-4_2_2_0.pdb" />-->
        <file src="..\..\..\bin\Win32\Debug\v120\dynamic\czmq.pdb" target="build\native\bin\czmq-x86-v120-mt-gd-4_2_2_0.pdb" />

        <!-- x86 Import libraries (.imp.lib) -->
        <file src="..\..\..\bin\Win32\Release\v120\dynamic\czmq.lib" target="build\native\bin\czmq-x86-v120-mt-4_2_2_0.imp.lib" />
        <file src="..\..\..\bin\Win32\Debug\v120\dynamic\czmq.lib" target="build\native\bin\czmq-x86-v120-mt-gd-4_2_2_0.imp.lib" />

        <!-- x86 Export libraries (.exp) -->
        <file src="..\..\..\bin\Win32\Release\v120\dynamic\czmq.exp" target="build\native\bin\czmq-x86-v120-mt-4_2_2_0.exp" />
        <file src="..\..\..\bin\Win32\Debug\v120\dynamic\czmq.exp" target="build\native\bin\czmq-x86-v120-mt-gd-4_2_2_0.exp" />

        <!-- x86 Static libraries (.lib) -->
        <file src="..\..\..\bin\Win32\Release\v120\static\czmq.lib" target="build\native\bin\czmq-x86-v120-mt-s-4_2_2_0.lib" />
        <file src="..\..\..\bin\Win32\Debug\v120\static\czmq.lib" target="build\native\bin\czmq-x86-v120-mt-sgd-4_2_2_0.lib" />

        <!-- x86 Static link time code generation libraries (.ltcg.lib) -->
        <file src="..\..\..\bin\Win32\Release\v120\ltcg\czmq.lib" target="build\native\bin\czmq-x86-v120-mt-s-4_2_2_0.ltcg.lib" />
        <file src="..\..\..\bin\Win32\Debug\v120\ltcg\czmq.lib" target="build\native\bin\czmq-x86-v120-mt-sgd-4_2_2_0.ltcg.lib" />

        <!-- x64 Dynamic libraries (.dll) -->
        <file src="..\..\..\bin\x64\Release\v120\dynamic\czmq.dll" target="build\native\bin\czmq-x64-v120-mt-4_2_2_0.dll" />
        <file src="..\..\..\bin\x64\Debug\v120\dynamic\czmq.dll" target="build\native\bin\czmq-x64-v120-mt-gd-4_2_2_0.dll" />

        <!-- x64 Debugging symbols (.pdb) -->
        <!--<file src="..\..\..\bin\x64\Release\v120\dynamic\czmq.pdb" target="build\native\bin\czmq-x64-v120-mt-4_2_2_0.pdb" />-->
        <file src="..\..\..\bin\x64\Debug\v120\dynamic\czmq.pdb" target="build\native\bin\czmq-x64-v120-mt-gd-4_2_2_0.pdb" />

        <!-- x64 Import libraries (.imp.lib) -->
        <file src="..\..\..\bin\x64\Release\v120\dynamic\czmq.lib" target="build\native\bin\czmq-x64-v120-mt-4_2_2_0.imp.lib" />
        <file src="..\..\..\bin\x64\Debug\v120\dynamic\czmq.lib" target="build\native\bin\czmq-x64-v120-mt-gd-4_2_2_0.imp.lib" />

        <!-- x64 Export libraries (.exp) -->
        <file src="..\..\..\bin\x64\Release\v120\dynamic\czmq.exp" target="build\native\bin\czmq-x64-v120-mt-4_2_2_0.exp" />
        <file src="..\..\..\bin\x64\Debug\v120\dynamic\czmq.exp" target="build\native\bin\czmq-x64-v120-mt-gd-4_2_2_0.exp" />

        <!-- x64 Static libraries (.lib) -->
        <file src="..\..\..\bin\x64\Release\v120\static\czmq.lib" target="build\native\bin\czmq-x64-v120-mt-s-4_2_2_0.lib" />
        <file src="..\..\..\bin\x64\Debug\v120\static\czmq.lib" target="build\native\bin\czmq-x64-v120-mt-sgd-4_2_2_0.lib" />

        <!-- x64 Static link time code generation libraries (.ltcg.lib) -->
        <file src="..\..\..\bin\Win32\Release\v120\ltcg\czmq.lib" target="build\native\bin\czmq-x64-v120-mt-s-4_2_2_0.ltcg.lib" />
        <file src="..\..\..\bin\Win32\Debug\v120\ltcg\czmq.lib" target="build\native\bin\czmq-x64-v120-mt-sgd-4_2_2_0.ltcg.lib" />
    </files>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
</package>
