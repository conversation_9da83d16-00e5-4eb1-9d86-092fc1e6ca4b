/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#ifndef QML_ZHTTP_SERVER_CONNECTION_H
#define QML_ZHTTP_SERVER_CONNECTION_H

#include <QtQml>

#include <czmq.h>
#include "qml_czmq_plugin.h"


class QmlZhttpServerConnection : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isNULL READ isNULL)

public:
    zhttp_server_connection_t *self;

    QmlZhttpServerConnection() { self = NULL; }
    bool isNULL() { return self == NULL; }

    static QObject* qmlAttachedProperties(QObject* object); // defined in QmlZhttpServerConnection.cpp

public slots:};

class QmlZhttpServerConnectionAttached : public QObject
{
    Q_OBJECT
    QObject* m_attached;

public:
    QmlZhttpServerConnectionAttached (QObject* attached) {
        Q_UNUSED (attached);
    };

public slots:
    //  Self test of this class.
    void test (bool verbose);
};


QML_DECLARE_TYPEINFO(QmlZhttpServerConnection, QML_HAS_ATTACHED_PROPERTIES)

#endif
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
