################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from __future__ import print_function
import os
import sys
from ctypes.util import find_library

import cffi
ffi = cffi.FFI()

try:
    # If LD_LIBRARY_PATH or your OSs equivalent is set, this is the only way to
    # load the library.  If we use find_library below, we get the wrong result.
    if os.name == 'posix':
        if sys.platform == 'darwin':
            libpath = 'libczmq.4.dylib'
        else:
            libpath = 'libczmq.so.4'
    elif os.name == 'nt':
        libpath = 'libczmq.dll'
    lib = ffi.dlopen(libpath)
except OSError:
    libpath = find_library("czmq")
    if not libpath:
        raise ImportError("Unable to find libczmq")
    lib = ffi.dlopen(libpath)

from czmq_cffi.cdefs import czmq_cdefs

for cdef in czmq_cdefs:
   ffi.cdef (cdef)
