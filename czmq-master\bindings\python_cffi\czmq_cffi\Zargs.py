################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class Zargs(object):
    """
    Platform independent command line argument parsing helpers

There are two kind of elements provided by this class
Named parameters, accessed by param_get and param_has methods
  * --named-parameter
  * --parameter with_value
  * -a val
Positional arguments, accessed by zargs_first, zargs_next

It DOES:
* provide easy to use CLASS compatible API for accessing argv
* is platform independent
* provide getopt_long style -- argument, which delimits parameters from arguments
* makes parameters position independent

It does NOT
* change argv
* provide a "declarative" way to define command line interface

In future it SHALL
* hide several formats of command line to one (-Idir, --include=dir,
  --include dir are the same from API pov)
    """

    def __init__(self, argc, argv):
        """
        Create a new zargs from command line arguments.
        """
        p = utils.lib.zargs_new(argc, utils.to_bytes(argv))
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zargs_destroy_py)

    def progname(self):
        """
        Return program name (argv[0])
        """
        return utils.lib.zargs_progname(self._p)

    def arguments(self):
        """
        Return number of positional arguments
        """
        return utils.lib.zargs_arguments(self._p)

    def first(self):
        """
        Return first positional argument or NULL
        """
        return utils.lib.zargs_first(self._p)

    def next(self):
        """
        Return next positional argument or NULL
        """
        return utils.lib.zargs_next(self._p)

    def param_first(self):
        """
        Return first named parameter value, or NULL if there are no named
        parameters, or value for which zargs_param_empty (arg) returns true.
        """
        return utils.lib.zargs_param_first(self._p)

    def param_next(self):
        """
        Return next named parameter value, or NULL if there are no named
        parameters, or value for which zargs_param_empty (arg) returns true.
        """
        return utils.lib.zargs_param_next(self._p)

    def param_name(self):
        """
        Return current parameter name, or NULL if there are no named parameters.
        """
        return utils.lib.zargs_param_name(self._p)

    def get(self, name):
        """
        Return value of named parameter or NULL is it has no value (or was not specified)
        """
        return utils.lib.zargs_get(self._p, utils.to_bytes(name))

    def getx(self, name, *name_args):
        """
        Return value of one of parameter(s) or NULL is it has no value (or was not specified)
        """
        return utils.lib.zargs_getx(self._p, utils.to_bytes(name), *name_args)

    def has(self, name):
        """
        Returns true if named parameter was specified on command line
        """
        return utils.lib.zargs_has(self._p, utils.to_bytes(name))

    def hasx(self, name, *name_args):
        """
        Returns true if named parameter(s) was specified on command line
        """
        return utils.lib.zargs_hasx(self._p, utils.to_bytes(name), *name_args)

    def print_py(self):
        """
        Print an instance of zargs.
        """
        utils.lib.zargs_print(self._p)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zargs_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
