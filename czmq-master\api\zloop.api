<class name = "zloop" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    event-driven reactor

    <callback_type name = "reader_fn">
        Callback function for reactor socket activity
        <argument name = "loop" type = "zloop" />
        <argument name = "reader" type = "zsock" />
        <argument name = "arg" type = "anything" />
        <return type = "integer" />
    </callback_type>

    <callback_type name = "fn">
        Callback function for reactor events (low-level)
        <argument name = "loop" type = "zloop" />
        <argument name = "item" type = "zmq_pollitem" />
        <argument name = "arg" type = "anything" />
        <return type = "integer" />
    </callback_type>

    <callback_type name = "timer_fn">
        Callback for reactor timer events
        <argument name = "loop" type = "zloop" />
        <argument name = "timer_id" type = "integer" />
        <argument name = "arg" type = "anything" />
        <return type = "integer" />
    </callback_type>

    <constructor>
        Create a new zloop reactor
    </constructor>

    <destructor>
        Destroy a reactor
    </destructor>

    <method name = "reader">
        Register socket reader with the reactor. When the reader has messages,
        the reactor will call the handler, passing the arg. Returns 0 if OK, -1
        if there was an error. If you register the same socket more than once,
        each instance will invoke its corresponding handler.
        <argument name = "sock" type = "zsock" />
        <argument name = "handler" type = "zloop_reader_fn" callback = "1" />
        <argument name = "arg" type = "anything" />
        <return type = "integer" />
    </method>

    <method name = "reader end">
        Cancel a socket reader from the reactor. If multiple readers exist for
        same socket, cancels ALL of them.
        <argument name = "sock" type = "zsock" />
    </method>

    <method name = "reader set tolerant">
        Configure a registered reader to ignore errors. If you do not set this,
        then readers that have errors are removed from the reactor silently.
        <argument name = "sock" type = "zsock" />
    </method>

    <method name = "poller">
        Register low-level libzmq pollitem with the reactor. When the pollitem
        is ready, will call the handler, passing the arg. Returns 0 if OK, -1
        if there was an error. If you register the pollitem more than once, each
        instance will invoke its corresponding handler. A pollitem with
        socket=NULL and fd=0 means 'poll on FD zero'.
        <argument name = "item" type = "zmq_pollitem" />
        <argument name = "handler" type = "zloop_fn" callback = "1" />
        <argument name = "arg" type = "anything" />
        <return type = "integer" />
    </method>

    <method name = "poller end">
        Cancel a pollitem from the reactor, specified by socket or FD. If both
        are specified, uses only socket. If multiple poll items exist for same
        socket/FD, cancels ALL of them.
        <argument name = "item" type = "zmq_pollitem" />
    </method>

    <method name = "poller set tolerant">
        Configure a registered poller to ignore errors. If you do not set this,
        then poller that have errors are removed from the reactor silently.
        <argument name = "item" type = "zmq_pollitem" />
    </method>

    <method name = "timer">
        Register a timer that expires after some delay and repeats some number of
        times. At each expiry, will call the handler, passing the arg. To run a
        timer forever, use 0 times. Returns a timer_id that is used to cancel the
        timer in the future. Returns -1 if there was an error.
        <argument name = "delay" type = "size" />
        <argument name = "times" type = "size" />
        <argument name = "handler" type = "zloop_timer_fn" callback = "1" />
        <argument name = "arg" type = "anything" />
        <return type = "integer" />
    </method>

    <method name = "timer end">
        Cancel a specific timer identified by a specific timer_id (as returned by
        zloop_timer).
        <argument name = "timer id" type = "integer" />
        <return type = "integer" />
    </method>

    <method name = "ticket">
        Register a ticket timer. Ticket timers are very fast in the case where
        you use a lot of timers (thousands), and frequently remove and add them.
        The main use case is expiry timers for servers that handle many clients,
        and which reset the expiry timer for each message received from a client.
        Whereas normal timers perform poorly as the number of clients grows, the
        cost of ticket timers is constant, no matter the number of clients. You
        must set the ticket delay using zloop_set_ticket_delay before creating a
        ticket. Returns a handle to the timer that you should use in
        zloop_ticket_reset and zloop_ticket_delete.
        <argument name = "handler" type = "zloop_timer_fn" callback = "1" />
        <argument name = "arg" type = "anything" />
        <return type = "anything" />
    </method>

    <method name = "ticket reset">
        Reset a ticket timer, which moves it to the end of the ticket list and
        resets its execution time. This is a very fast operation.
        <argument name = "handle" type = "anything" />
    </method>

    <method name = "ticket delete">
        Delete a ticket timer. We do not actually delete the ticket here, as
        other code may still refer to the ticket. We mark as deleted, and remove
        later and safely.
        <argument name = "handle" type = "anything" />
    </method>

    <method name = "set ticket delay">
        Set the ticket delay, which applies to all tickets. If you lower the
        delay and there are already tickets created, the results are undefined.
        <argument name = "ticket delay" type = "size" />
    </method>

    <method name = "set max timers">
        Set hard limit on number of timers allowed. Setting more than a small
        number of timers (10-100) can have a dramatic impact on the performance
        of the reactor. For high-volume cases, use ticket timers. If the hard
        limit is reached, the reactor stops creating new timers and logs an
        error.
        <argument name = "max timers" type = "size" />
    </method>

    <method name = "set verbose">
        Set verbose tracing of reactor on/off. The default verbose setting is
        off (false).
        <argument name = "verbose" type = "boolean" />
    </method>

    <method name = "set nonstop" >
        By default the reactor stops if the process receives a SIGINT or SIGTERM
        signal. This makes it impossible to shut-down message based architectures
        like zactors. This method lets you switch off break handling. The default
        nonstop setting is off (false).
        <argument name = "nonstop" type = "boolean" />
    </method>

    <method name = "start">
        Start the reactor. Takes control of the thread and returns when the 0MQ
        context is terminated or the process is interrupted, or any event handler
        returns -1. Event handlers may register new sockets and timers, and
        cancel sockets. Returns 0 if interrupted, -1 if canceled by a handler.
        <return type = "integer" />
    </method>
</class>
