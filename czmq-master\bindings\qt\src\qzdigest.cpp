/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZdigest::QZdigest (zdigest_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Constructor - creates new digest object, which you use to build up a
//  digest by repeatedly calling zdigest_update() on chunks of data.
QZdigest::QZdigest (QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zdigest_new ();
}

///
//  Destroy a digest object
QZdigest::~QZdigest ()
{
    zdigest_destroy (&self);
}

///
//  Add buffer into digest calculation
void QZdigest::update (const byte *buffer, size_t length)
{
    zdigest_update (self, buffer, length);

}

///
//  Return final digest hash data. If built without crypto support,
//  returns NULL.
const byte * QZdigest::data ()
{
    const byte * rv = zdigest_data (self);
    return rv;
}

///
//  Return final digest hash size
size_t QZdigest::size ()
{
    size_t rv = zdigest_size (self);
    return rv;
}

///
//  Return digest as printable hex string; caller should not modify nor
//  free this string. After calling this, you may not use zdigest_update()
//  on the same digest. If built without crypto support, returns NULL.
const QString QZdigest::string ()
{
    const QString rv = QString (zdigest_string (self));
    return rv;
}

///
//  Self test of this class.
void QZdigest::test (bool verbose)
{
    zdigest_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
