/*  =========================================================================
    zclock - millisecond clocks and delays

    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    =========================================================================
*/

#ifndef __ZCLOCK_H_INCLUDED__
#define __ZCLOCK_H_INCLUDED__

#ifdef __cplusplus
extern "C" {
#endif

//  @warning THE FOLLOWING @INTERFACE BLOCK IS AUTO-GENERATED BY ZPROJECT
//  @warning Please edit the model at "api/zclock.api" to make changes.
//  @interface
//  This is a stable class, and may not change except for emergencies. It
//  is provided in stable builds.
//  Sleep for a number of milliseconds
CZMQ_EXPORT void
    zclock_sleep (int msecs);

//  Return current system clock as milliseconds. Note that this clock can
//  jump backwards (if the system clock is changed) so is unsafe to use for
//  timers and time offsets. Use zclock_mono for that instead.
CZMQ_EXPORT int64_t
    zclock_time (void);

//  Return current monotonic clock in milliseconds. Use this when you compute
//  time offsets. The monotonic clock is not affected by system changes and
//  so will never be reset backwards, unlike a system clock.
CZMQ_EXPORT int64_t
    zclock_mono (void);

//  Return current monotonic clock in microseconds. Use this when you compute
//  time offsets. The monotonic clock is not affected by system changes and
//  so will never be reset backwards, unlike a system clock.
CZMQ_EXPORT int64_t
    zclock_usecs (void);

//  Return formatted date/time as fresh string. Free using zstr_free().
//  Caller owns return value and must destroy it when done.
CZMQ_EXPORT char *
    zclock_timestr (void);

//  Self test of this class.
CZMQ_EXPORT void
    zclock_test (bool verbose);

//  @end


//  DEPRECATED in favor of zsys logging, see issue #519
//  Print formatted string to stdout, prefixed by date/time and
//  terminated with a newline.
CZMQ_EXPORT void
    zclock_log (const char *format, ...);

//  Compiler hints
CZMQ_EXPORT void zclock_log (const char *format, ...) CHECK_PRINTF (1);

#ifdef __cplusplus
}
#endif

#endif
