/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZhttpRequest.h"


///
//  Receive a new request from zhttp_server.
//  Return the underlying connection if successful, to be used when calling zhttp_response_send.
void *QmlZhttpRequest::recv (QmlZsock *sock) {
    return zhttp_request_recv (self, sock->self);
};

///
//  Send a request to zhttp_client.
//  Url and the request path will be concatenated.
//  This behavior is useful for url rewrite and reverse proxy.
//
//  Send also allow two user provided arguments which will be returned with the response.
//  The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an arg.
int QmlZhttpRequest::send (QmlZhttpClient *client, int timeout, void *arg, void *arg2) {
    return zhttp_request_send (self, client->self, timeout, arg, arg2);
};

///
//  Get the request method
const QString QmlZhttpRequest::method () {
    return QString (zhttp_request_method (self));
};

///
//  Set the request method
void QmlZhttpRequest::setMethod (const QString &method) {
    zhttp_request_set_method (self, method.toUtf8().data());
};

///
//  Get the request url.
//  When receiving a request from http server this is only the path part of the url.
const QString QmlZhttpRequest::url () {
    return QString (zhttp_request_url (self));
};

///
//  Set the request url
//  When sending a request to http client this should be full url.
void QmlZhttpRequest::setUrl (const QString &url) {
    zhttp_request_set_url (self, url.toUtf8().data());
};

///
//  Get the request content type
const QString QmlZhttpRequest::contentType () {
    return QString (zhttp_request_content_type (self));
};

///
//  Set the request content type
void QmlZhttpRequest::setContentType (const QString &contentType) {
    zhttp_request_set_content_type (self, contentType.toUtf8().data());
};

///
//  Get the content length of the request
size_t QmlZhttpRequest::contentLength () {
    return zhttp_request_content_length (self);
};

///
//  Get the headers of the request
QmlZhash *QmlZhttpRequest::headers () {
    QmlZhash *retQ_ = new QmlZhash ();
    retQ_->self = zhttp_request_headers (self);
    return retQ_;
};

///
//  Get the content of the request.
const QString QmlZhttpRequest::content () {
    return QString (zhttp_request_content (self));
};

///
//  Get the content of the request.
QString QmlZhttpRequest::getContent () {
    char *retStr_ = zhttp_request_get_content (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Set the content of the request.
//  Content must by dynamically allocated string.
//  Takes ownership of the content.
void QmlZhttpRequest::setContent (QString content) {
    zhttp_request_set_content (self, content.toUtf8().data());
};

///
//  Set the content of the request..
//  The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
void QmlZhttpRequest::setContentConst (const QString &content) {
    zhttp_request_set_content_const (self, content.toUtf8().data());
};

///
//  Set the content to NULL
void QmlZhttpRequest::resetContent () {
    zhttp_request_reset_content (self);
};

///
//  Set the request username
void QmlZhttpRequest::setUsername (const QString &username) {
    zhttp_request_set_username (self, username.toUtf8().data());
};

///
//  Set the request password
void QmlZhttpRequest::setPassword (const QString &password) {
    zhttp_request_set_password (self, password.toUtf8().data());
};

///
//  Match the path of the request.
//  Support wildcards with '%s' symbol inside the match string.
//  Matching wildcards until the next '/', '?' or '\0'.
//  On successful match the variadic arguments will be filled with the matching strings.
//  On successful match the method is modifying the url field and break it into substrings.
//  If you need to use the url, do it before matching or take a copy.
//
//  User must not free the variadic arguments as they are part of the url.
//
//  To use the percent symbol, just double it, e.g "%%something".
//
//  Example:
//  if (zhttp_request_match (request, "POST", "/send/%s/%s", &name, &id))
bool QmlZhttpRequest::match (const QString &method, const QString &path) {
    return zhttp_request_match (self, method.toUtf8().data(), path.toUtf8().data());
};


QObject* QmlZhttpRequest::qmlAttachedProperties(QObject* object) {
    return new QmlZhttpRequestAttached(object);
}


///
//  Self test of this class.
void QmlZhttpRequestAttached::test (bool verbose) {
    zhttp_request_test (verbose);
};

///
//  Create a new http request.
QmlZhttpRequest *QmlZhttpRequestAttached::construct () {
    QmlZhttpRequest *qmlSelf = new QmlZhttpRequest ();
    qmlSelf->self = zhttp_request_new ();
    return qmlSelf;
};

///
//  Destroy an http request.
void QmlZhttpRequestAttached::destruct (QmlZhttpRequest *qmlSelf) {
    zhttp_request_destroy (&qmlSelf->self);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
