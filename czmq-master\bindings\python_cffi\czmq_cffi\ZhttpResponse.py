################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class ZhttpResponse(object):
    """
    Http response that can be received from zhttp_client or sent to zhttp_server.
Class can be reused between send & recv calls.
Headers and Content is being destroyed after every send call.
    """

    def __init__(self):
        """
        Create a new zhttp_response.
        """
        p = utils.lib.zhttp_response_new()
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zhttp_response_destroy_py)

    def send(self, sock, connection):
        """
        Send a response to a request.
        Returns 0 if successful and -1 otherwise.
        """
        return utils.lib.zhttp_response_send(self._p, sock._p, connection._p)

    def recv(self, client, arg, arg2):
        """
        Receive a response from zhttp_client.
        On success return 0, -1 otherwise.

        Recv returns the two user arguments which was provided with the request.
        The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an argument.
        """
        return utils.lib.zhttp_response_recv(self._p, client._p, arg._p, arg2._p)

    def content_type(self):
        """
        Get the response content type
        """
        return utils.lib.zhttp_response_content_type(self._p)

    def set_content_type(self, value):
        """
        Set the content type of the response.
        """
        utils.lib.zhttp_response_set_content_type(self._p, utils.to_bytes(value))

    def status_code(self):
        """
        Get the status code of the response.
        """
        return utils.lib.zhttp_response_status_code(self._p)

    def set_status_code(self, status_code):
        """
        Set the status code of the response.
        """
        utils.lib.zhttp_response_set_status_code(self._p, status_code)

    def headers(self):
        """
        Get the headers of the response.
        """
        return utils.lib.zhttp_response_headers(self._p)

    def content_length(self):
        """
        Get the content length of the response
        """
        return utils.lib.zhttp_response_content_length(self._p)

    def content(self):
        """
        Get the content of the response.
        """
        return utils.lib.zhttp_response_content(self._p)

    def get_content(self):
        """
        Get the content of the response.
        """
        return utils.lib.zhttp_response_get_content(self._p)

    def set_content(self, content):
        """
        Set the content of the response.
        Content must by dynamically allocated string.
        Takes ownership of the content.
        """
        utils.lib.zhttp_response_set_content(self._p, utils.to_bytes(content))

    def set_content_const(self, content):
        """
        Set the content of the response.
        The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
        """
        utils.lib.zhttp_response_set_content_const(self._p, utils.to_bytes(content))

    def reset_content(self):
        """
        Set the content to NULL
        """
        utils.lib.zhttp_response_reset_content(self._p)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zhttp_response_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
