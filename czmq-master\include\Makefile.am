################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
include_HEADERS = \
    czmq_prelude.h \
    czmq.h \
    zactor.h \
    zarmour.h \
    zcert.h \
    zcertstore.h \
    zchunk.h \
    zclock.h \
    zconfig.h \
    zdigest.h \
    zdir.h \
    zdir_patch.h \
    zfile.h \
    zframe.h \
    zhash.h \
    zhashx.h \
    ziflist.h \
    zlist.h \
    zlistx.h \
    zloop.h \
    zmsg.h \
    zpoller.h \
    zsock.h \
    zstr.h \
    zsys.h \
    zuuid.h \
    zauth.h \
    zbeacon.h \
    zgossip.h \
    zmonitor.h \
    zproxy.h \
    zrex.h \
    czmq_library.h

if ENABLE_DRAFTS
include_HEADERS += \
    zargs.h \
    zproc.h \
    ztimerset.h \
    ztrie.h \
    zhttp_client.h \
    zhttp_server.h \
    zhttp_server_options.h \
    zhttp_request.h \
    zhttp_response.h \
    zosc.h

endif

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
