<class name = "zactor" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    provides a simple actor framework

    <callback_type name = "fn">
        Actors get a pipe and arguments from caller
        <argument name = "pipe" type = "zsock" />
        <argument name = "args" type = "anything" />
    </callback_type>

    <callback_type name = "destructor_fn" state = "draft">
        Function to be called on zactor_destroy. Default behavior is to send zmsg_t with string "$TERM" in a first frame.

        An example - to send $KTHXBAI string

            if (zstr_send (self, "$KTHXBAI") == 0)
                zsock_wait (self);
        <argument name = "self" type = "zactor" />
    </callback_type>

    <constructor>
        Create a new actor passing arbitrary arguments reference.
        <argument name = "task" type = "zactor_fn" callback = "1" />
        <argument name = "args" type = "anything" />
    </constructor>

    <destructor>
        Destroy an actor.
    </destructor>

    <method name = "send">
        Send a zmsg message to the actor, take ownership of the message
        and destroy when it has been sent.
        <argument name = "msg_p" type = "zmsg" by_reference = "1" />
        <return type = "integer" />
    </method>

    <method name = "recv">
        Receive a zmsg message from the actor. Returns NULL if the actor
        was interrupted before the message could be received, or if there
        was a timeout on the actor.
        <return type = "zmsg" fresh = "1" />
    </method>

    <method name = "is" singleton = "1">
        Probe the supplied object, and report if it looks like a zactor_t.
        <argument name = "self" type = "anything" />
        <return type = "boolean" />
    </method>

    <method name = "resolve" singleton = "1">
        Probe the supplied reference. If it looks like a zactor_t instance,
        return the underlying libzmq actor handle; else if it looks like
        a libzmq actor handle, return the supplied value.
        <argument name = "self" type = "anything" />
        <return type = "anything" />
    </method>

    <method name = "sock">
        Return the actor's zsock handle. Use this when you absolutely need
        to work with the zsock instance rather than the actor.
        <return type = "zsock" />
    </method>

    <method name = "set destructor" state = "draft">
        Change default destructor by custom function. Actor MUST be able to handle new message instead of default $TERM.
        <argument name = "destructor" type = "zactor_destructor_fn" callback="1" />
    </method>
</class>
