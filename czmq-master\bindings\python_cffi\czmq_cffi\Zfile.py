################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class Zfile(object):
    """
    helper functions for working with files.
    """

    def __init__(self, path, name):
        """
        If file exists, populates properties. CZMQ supports portable symbolic
        links, which are files with the extension ".ln". A symbolic link is a
        text file containing one line, the filename of a target file. Reading
        data from the symbolic link actually reads from the target file. Path
        may be NULL, in which case it is not used.
        """
        p = utils.lib.zfile_new(utils.to_bytes(path), utils.to_bytes(name))
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zfile_destroy_py)

    @staticmethod
    def tmp():
        """
        Create new temporary file for writing via tmpfile. File is automatically
        deleted on destroy
        """
        return utils.lib.zfile_tmp()

    def dup(self):
        """
        Duplicate a file item, returns a newly constructed item. If the file
        is null, or memory was exhausted, returns null.
        """
        return utils.lib.zfile_dup(self._p)

    def filename(self, path):
        """
        Return file name, remove path if provided
        """
        return utils.lib.zfile_filename(self._p, utils.to_bytes(path))

    def restat(self):
        """
        Refresh file properties from disk; this is not done automatically
        on access methods, otherwise it is not possible to compare directory
        snapshots.
        """
        utils.lib.zfile_restat(self._p)

    def modified(self):
        """
        Return when the file was last modified. If you want this to reflect the
        current situation, call zfile_restat before checking this property.
        """
        return utils.lib.zfile_modified(self._p)

    def cursize(self):
        """
        Return the last-known size of the file. If you want this to reflect the
        current situation, call zfile_restat before checking this property.
        """
        return utils.lib.zfile_cursize(self._p)

    def is_directory(self):
        """
        Return true if the file is a directory. If you want this to reflect
        any external changes, call zfile_restat before checking this property.
        """
        return utils.lib.zfile_is_directory(self._p)

    def is_regular(self):
        """
        Return true if the file is a regular file. If you want this to reflect
        any external changes, call zfile_restat before checking this property.
        """
        return utils.lib.zfile_is_regular(self._p)

    def is_readable(self):
        """
        Return true if the file is readable by this process. If you want this to
        reflect any external changes, call zfile_restat before checking this
        property.
        """
        return utils.lib.zfile_is_readable(self._p)

    def is_writeable(self):
        """
        Return true if the file is writeable by this process. If you want this
        to reflect any external changes, call zfile_restat before checking this
        property.
        """
        return utils.lib.zfile_is_writeable(self._p)

    def is_stable(self):
        """
        Check if file has stopped changing and can be safely processed.
        Updates the file statistics from disk at every call.
        """
        return utils.lib.zfile_is_stable(self._p)

    def has_changed(self):
        """
        Return true if the file was changed on disk since the zfile_t object
        was created, or the last zfile_restat() call made on it.
        """
        return utils.lib.zfile_has_changed(self._p)

    def remove(self):
        """
        Remove the file from disk
        """
        utils.lib.zfile_remove(self._p)

    def input(self):
        """
        Open file for reading
        Returns 0 if OK, -1 if not found or not accessible
        """
        return utils.lib.zfile_input(self._p)

    def output(self):
        """
        Open file for writing, creating directory if needed
        File is created if necessary; chunks can be written to file at any
        location. Returns 0 if OK, -1 if error.
        """
        return utils.lib.zfile_output(self._p)

    def read(self, bytes, offset):
        """
        Read chunk from file at specified position. If this was the last chunk,
        sets the eof property. Returns a null chunk in case of error.
        """
        return utils.lib.zfile_read(self._p, bytes, offset)

    def eof(self):
        """
        Returns true if zfile_read() just read the last chunk in the file.
        """
        return utils.lib.zfile_eof(self._p)

    def write(self, chunk, offset):
        """
        Write chunk to file at specified position
        Return 0 if OK, else -1
        """
        return utils.lib.zfile_write(self._p, chunk._p, offset)

    def readln(self):
        """
        Read next line of text from file. Returns a pointer to the text line,
        or NULL if there was nothing more to read from the file.
        """
        return utils.lib.zfile_readln(self._p)

    def close(self):
        """
        Close file, if open
        """
        utils.lib.zfile_close(self._p)

    def handle(self):
        """
        Return file handle, if opened
        """
        return utils.lib.zfile_handle(self._p)

    def digest(self):
        """
        Calculate SHA1 digest for file, using zdigest class.
        """
        return utils.lib.zfile_digest(self._p)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zfile_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
