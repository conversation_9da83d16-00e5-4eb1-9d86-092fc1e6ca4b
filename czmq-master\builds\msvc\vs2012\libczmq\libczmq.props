<?xml version="1.0" encoding="utf-8"?>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup Label="Globals">
    <_PropertySheetDisplayName>CZMQ Common Settings</_PropertySheetDisplayName>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <RunCodeAnalysis>false</RunCodeAnalysis>
  </PropertyGroup>

  <!-- Configuration -->
  <ItemDefinitionGroup>
    <PreBuildEvent>
      <Command>copy "$(BuildRoot)platform.h" "$(RepoRoot)include\"</Command>
    </PreBuildEvent>
    <ClCompile>
      <AdditionalIncludeDirectories>$(RepoRoot)include\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <CompileAs>CompileAsCpp</CompileAs>
      <DisableSpecificWarnings>%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <EnablePREfast>false</EnablePREfast>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;BASE_THREADSAFE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(ConfigurationType)' == 'StaticLibrary'">CZMQ_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessorDefinitions Condition="'$(ConfigurationType)' == 'DynamicLibrary'">CZMQ_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>Rpcrt4.lib;Ws2_32.lib;Iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>

  <!-- Dependencies -->
  <ImportGroup Label="PropertySheets">
    <Import Project="$(SolutionDir)libzmq.import.props" />
    <Import Project="$(SolutionDir)uuid.import.props" Condition="'$(HAVE_UUID)'=='1'" />
    <Import Project="$(SolutionDir)systemd.import.props" Condition="'$(HAVE_SYSTEMD)'=='1'" />
    <Import Project="$(SolutionDir)lz4.import.props" Condition="'$(HAVE_LZ4)'=='1'" />
    <Import Project="$(SolutionDir)libcurl.import.props" Condition="'$(HAVE_LIBCURL)'=='1'" />
    <Import Project="$(SolutionDir)nss.import.props" Condition="'$(HAVE_NSS)'=='1'" />
    <Import Project="$(SolutionDir)libmicrohttpd.import.props" Condition="'$(HAVE_LIBMICROHTTPD)'=='1'" />
  </ImportGroup>

  <PropertyGroup Condition="'$(DefaultLinkage)' == 'dynamic'">
    <Linkage-libzmq>dynamic</Linkage-libzmq>
    <Linkage-uuid Condition="'$(HAVE_UUID)'=='1'">dynamic</Linkage-uuid>
    <Linkage-systemd Condition="'$(HAVE_SYSTEMD)'=='1'">dynamic</Linkage-systemd>
    <Linkage-lz4 Condition="'$(HAVE_LZ4)'=='1'">dynamic</Linkage-lz4>
    <Linkage-libcurl Condition="'$(HAVE_LIBCURL)'=='1'">dynamic</Linkage-libcurl>
    <Linkage-nss Condition="'$(HAVE_NSS)'=='1'">dynamic</Linkage-nss>
    <Linkage-libmicrohttpd Condition="'$(HAVE_LIBMICROHTTPD)'=='1'">dynamic</Linkage-libmicrohttpd>
  </PropertyGroup>

  <PropertyGroup Condition="'$(DefaultLinkage)' == 'ltcg'">
    <Linkage-libzmq>ltcg</Linkage-libzmq>
    <Linkage-uuid Condition="'$(HAVE_UUID)'=='1'">ltcg</Linkage-uuid>
    <Linkage-systemd Condition="'$(HAVE_SYSTEMD)'=='1'">ltcg</Linkage-systemd>
    <Linkage-lz4 Condition="'$(HAVE_LZ4)'=='1'">ltcg</Linkage-lz4>
    <Linkage-libcurl Condition="'$(HAVE_LIBCURL)'=='1'">ltcg</Linkage-libcurl>
    <Linkage-nss Condition="'$(HAVE_NSS)'=='1'">ltcg</Linkage-nss>
    <Linkage-libmicrohttpd Condition="'$(HAVE_LIBMICROHTTPD)'=='1'">ltcg</Linkage-libmicrohttpd>
  </PropertyGroup>

  <PropertyGroup Condition="'$(DefaultLinkage)' == 'static'">
    <Linkage-libzmq>static</Linkage-libzmq>
    <Linkage-uuid Condition="'$(HAVE_UUID)'=='1'">static</Linkage-uuid>
    <Linkage-systemd Condition="'$(HAVE_SYSTEMD)'=='1'">static</Linkage-systemd>
    <Linkage-lz4 Condition="'$(HAVE_LZ4)'=='1'">static</Linkage-lz4>
    <Linkage-libcurl Condition="'$(HAVE_LIBCURL)'=='1'">static</Linkage-libcurl>
    <Linkage-nss Condition="'$(HAVE_NSS)'=='1'">static</Linkage-nss>
    <Linkage-libmicrohttpd Condition="'$(HAVE_LIBMICROHTTPD)'=='1'">static</Linkage-libmicrohttpd>
  </PropertyGroup>

  <!-- Messages -->
  <Target Name="CustomInfo" BeforeTargets="PrepareForBuild">
    <Message Text="Will copy $(BuildRoot)platform.h -&gt; $(RepoRoot)include\platform.h" Importance="high"/>
  </Target>

  <Target Name="LinkageInfo" BeforeTargets="PrepareForBuild">
    <Message Text="Linkage-libzmq: $(Linkage-libzmq)" Importance="high" />
    <Message Text="Linkage-uuid: $(Linkage-uuid)" Importance="high" Condition="'$(HAVE_UUID)'=='1'" />
    <Message Text="Linkage-systemd: $(Linkage-systemd)" Importance="high" Condition="'$(HAVE_SYSTEMD)'=='1'" />
    <Message Text="Linkage-lz4: $(Linkage-lz4)" Importance="high" Condition="'$(HAVE_LZ4)'=='1'" />
    <Message Text="Linkage-libcurl: $(Linkage-libcurl)" Importance="high" Condition="'$(HAVE_LIBCURL)'=='1'" />
    <Message Text="Linkage-nss: $(Linkage-nss)" Importance="high" Condition="'$(HAVE_NSS)'=='1'" />
    <Message Text="Linkage-libmicrohttpd: $(Linkage-libmicrohttpd)" Importance="high" Condition="'$(HAVE_LIBMICROHTTPD)'=='1'" />
  </Target>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
</Project>
