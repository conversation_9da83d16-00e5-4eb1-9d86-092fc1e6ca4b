/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#include <stdio.h>
#include <stdlib.h>
#include <jni.h>
#include "czmq.h"
#include "org_zeromq_czmq_ZhttpServer.h"

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_ZhttpServer__1_1new (JNIEnv *env, jclass c, jlong options)
{
    //  Disable CZMQ signal handling; allow Java to deal with it
    zsys_handler_set (NULL);
    jlong new_ = (jlong) (intptr_t) zhttp_server_new ((zhttp_server_options_t *) (intptr_t) options);
    return new_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpServer__1_1destroy (JNIEnv *env, jclass c, jlong self)
{
    zhttp_server_destroy ((zhttp_server_t **) &self);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_ZhttpServer__1_1port (JNIEnv *env, jclass c, jlong self)
{
    jint port_ = (jint) zhttp_server_port ((zhttp_server_t *) (intptr_t) self);
    return port_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_ZhttpServer__1_1test (JNIEnv *env, jclass c, jboolean verbose)
{
    zhttp_server_test ((bool) verbose);
}

