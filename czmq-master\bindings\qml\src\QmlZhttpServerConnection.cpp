/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZhttpServerConnection.h"



QObject* QmlZhttpServerConnection::qmlAttachedProperties(QObject* object) {
    return new QmlZhttpServerConnectionAttached(object);
}


///
//  Self test of this class.
void QmlZhttpServerConnectionAttached::test (bool verbose) {
    zhttp_server_connection_test (verbose);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
