#! /usr/bin/python
#
#   wdput - Send one Wikidot page to api.zero.mq
#   Part of the ztools/apisite toolkit. Install into any directory on PATH.
#
#   Author: <PERSON> <<EMAIL>>
#   License: public domain
#
#   syntax: wdput sitename category page title
#
#   Wikidot text must be in page + '.wd'
#
#   Supply your Wikidot user name as an environment variable APISITE_USER
#   Supply your Wikidot API key as an environment variable APISITE_KEY

#   Get script arguments
import sys
sitename = sys.argv [1]
category = sys.argv [2]
name     = sys.argv [3]
title    = sys.argv [4]

#   Get authentication credentials
import os
user = os.environ ['APISITE_USER']
key  = os.environ ['APISITE_KEY']

#   Create XML/RPC connection to Wikidot
from xmlrpclib import ServerProxy
server = ServerProxy ('https://' + user + ':' + key + '@www.wikidot.com/xml-rpc-api.php')

#   Send page content (create or update)
file = open (name + ".wd", "r")
content = ""
for line in file:
    content = content + line

print " - " + category + ":" + name
server.pages.save_one ({
    "site": sitename,
    "page": category + ":" + name,
    "title": title,
    "content": content})
