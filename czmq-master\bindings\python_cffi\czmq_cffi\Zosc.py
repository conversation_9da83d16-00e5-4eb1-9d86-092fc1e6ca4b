################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class Zosc(object):
    """
    Create and decode Open Sound Control messages. (OSC)

OSC is a serialisation format (and usually transported over UDP) which is
supported by many applications and appliances. It is a de facto protocol
for networking sound synthesizers, computers, and other multimedia devices
for purposes such as musical performance or show control. It is also often
used for rapid prototyping purposes due to the support by many applications
and frameworks in this field. With ZeroMQ's DGRAM sockets it is possible
to use ZeroMQ to send and receive OSC messages which can be understood by
any device supporting OSC.

Example creating an OSC message:

    zosc_t* conm = zosc_create("/someaddress", "iihfdscF",
                        1, 2, 3, 3.14, 6.283185307179586, "greetings", 'q');

Decoding a message:

    int rc = zosc_retr(oscmsg, "iihfdscF", &intx, &inty, &intz, &floatz,
                        &doublez, &strings, &charq, &someBool);

See the class's test method for more examples how to use the class.
    """

    def __init__(self, address):
        """
        Create a new empty OSC message with the specified address string.
        """
        p = utils.lib.zosc_new(utils.to_bytes(address))
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zosc_destroy_py)

    @staticmethod
    def fromframe(frame):
        """
        Create a new OSC message from the specified zframe. Takes ownership of
        the zframe.
        """
        return utils.lib.zosc_fromframe(frame._p)

    @staticmethod
    def frommem(data, size):
        """
        Create a new zosc message from memory. Take ownership of the memory
        and calling free on the data after construction.
        """
        return utils.lib.zosc_frommem(data, size)

    @staticmethod
    def fromstring(oscstring):
        """
        Create a new zosc message from a string. This the same syntax as
        zosc_create but written as a single line string.
        """
        return utils.lib.zosc_fromstring(utils.to_bytes(oscstring))

    @staticmethod
    def create(address, format, *format_args):
        """
        Create a new zosc message from the given format and arguments.
        The format type tags are as follows:
          i - 32bit integer
          h - 64bit integer
          f - 32bit floating point number (IEEE)
          d - 64bit (double) floating point number
          s - string (NULL terminated)
          t = timetag: an OSC timetag in NTP format (uint64_t)
          S - symbol
          c - char
          m - 4 byte midi packet (8 digits hexadecimal)
          T - TRUE (no value required)
          F - FALSE (no value required)
          N - NIL (no value required)
          I - Impulse (for triggers) or INFINITUM (no value required)
          b - binary blob
        """
        return utils.lib.zosc_create(utils.to_bytes(address), utils.to_bytes(format), *format_args)

    def size(self):
        """
        Return chunk data size
        """
        return utils.lib.zosc_size(self._p)

    def data(self):
        """
        Return OSC chunk data. Caller does not own the data!
        """
        return utils.lib.zosc_data(self._p)

    def address(self):
        """
        Return the OSC address string
        """
        return utils.lib.zosc_address(self._p)

    def format(self):
        """
        Return the OSC format of the message.
          i - 32bit integer
          h - 64bit integer
          f - 32bit floating point number (IEEE)
          d - 64bit (double) floating point number
          s - string (NULL terminated)
          t = timetag: an OSC timetag in NTP format (uint64_t)
          S - symbol
          c - char
          m - 4 byte midi packet (8 digits hexadecimal)
          T - TRUE (no value required)
          F - FALSE (no value required)
          N - NIL (no value required)
          I - Impulse (for triggers) or INFINITUM (no value required)
          b - binary blob
        """
        return utils.lib.zosc_format(self._p)

    def append(self, format, *format_args):
        """
        Append data to the osc message. The format describes the data that
        needs to be appended in the message. This essentially relocates all
        data!
        The format type tags are as follows:
          i - 32bit integer
          h - 64bit integer
          f - 32bit floating point number (IEEE)
          d - 64bit (double) floating point number
          s - string (NULL terminated)
          t = timetag: an OSC timetag in NTP format (uint64_t)
          S - symbol
          c - char
          m - 4 byte midi packet (8 digits hexadecimal)
          T - TRUE (no value required)
          F - FALSE (no value required)
          N - NIL (no value required)
          I - Impulse (for triggers) or INFINITUM (no value required)
          b - binary blob
        """
        return utils.lib.zosc_append(self._p, utils.to_bytes(format), *format_args)

    def retr(self, format, *format_args):
        """
        Retrieve the values provided by the given format. Note that zosc_retr
        creates the objects and the caller must destroy them when finished.
        The supplied pointers do not need to be initialized. Returns 0 if
        successful, or -1 if it failed to retrieve a value in which case the
        pointers are not modified. If an argument pointer is NULL is skips the
        value. See the format method for a detailed list op type tags for the
        format string.
        """
        return utils.lib.zosc_retr(self._p, utils.to_bytes(format), *format_args)

    def dup(self):
        """
        Create copy of the message, as new chunk object. Returns a fresh zosc_t
        object, or null if there was not enough heap memory. If chunk is null,
        returns null.
        """
        return utils.lib.zosc_dup(self._p)

    def pack(self):
        """
        Transform zosc into a zframe that can be sent in a message.
        """
        return utils.lib.zosc_pack(self._p)

    @staticmethod
    def packx(self_p):
        """
        Transform zosc into a zframe that can be sent in a message.
        Take ownership of the chunk.
        """
        return utils.lib.zosc_packx(utils.ffi.new("zosc_t **", self_p._p))

    @staticmethod
    def unpack(frame):
        """
        Transform a zframe into a zosc.
        """
        return utils.lib.zosc_unpack(frame._p)

    def dump(self):
        """
        Return a string describing the the OSC message. The returned string must be freed by the caller.
        """
        return utils.lib.zosc_dump(self._p)

    def print_py(self):
        """
        Dump OSC message to stdout, for debugging and tracing.
        """
        utils.lib.zosc_print(self._p)

    @staticmethod
    def is_py(self):
        """
        Probe the supplied object, and report if it looks like a zosc_t.
        """
        return utils.lib.zosc_is(self._p)

    def first(self, type):
        """
        Return a pointer to the item at the head of the OSC data.
        Sets the given char argument to the type tag of the data.
        If the message is empty, returns NULL and the sets the
        given char to NULL.
        """
        return utils.lib.zosc_first(self._p, utils.ffi.new("char_t **", type._p))

    def next(self, type):
        """
        Return the next item of the OSC message. If the list is empty, returns
        NULL. To move to the start of the OSC message call zosc_first ().
        """
        return utils.lib.zosc_next(self._p, utils.ffi.new("char_t **", type._p))

    def last(self, type):
        """
        Return a pointer to the item at the tail of the OSC message.
        Sets the given char argument to the type tag of the data. If
        the message is empty, returns NULL.
        """
        return utils.lib.zosc_last(self._p, utils.ffi.new("char_t **", type._p))

    def pop_int32(self, val):
        """
        Set the provided 32 bit integer from value at the current cursor position in the message.
        If the type tag at the current position does not correspond it will fail and
        return -1. Returns 0 on success.
        """
        return utils.lib.zosc_pop_int32(self._p, val)

    def pop_int64(self, val):
        """
        Set the provided 64 bit integer from the value at the current cursor position in the message.
        If the type tag at the current position does not correspond it will fail and
        return -1. Returns 0 on success.
        """
        return utils.lib.zosc_pop_int64(self._p, utils.ffi.new("msecs_t **", val._p))

    def pop_float(self, val):
        """
        Set the provided float from the value at the current cursor position in the message.
        If the type tag at the current position does not correspond it will fail and
        return -1. Returns 0 on success.
        """
        return utils.lib.zosc_pop_float(self._p, val)

    def pop_double(self, val):
        """
        Set the provided double from the value at the current cursor position in the message.
        If the type tag at the current position does not correspond it will fail and
        return -1. Returns 0 on success.
        """
        return utils.lib.zosc_pop_double(self._p, val)

    def pop_string(self, val):
        """
        Set the provided string from the value at the current cursor position in the message.
        If the type tag at the current position does not correspond it will fail and
        return -1. Returns 0 on success. Caller owns the string!
        """
        return utils.lib.zosc_pop_string(self._p, utils.to_bytes(val))

    def pop_char(self, val):
        """
        Set the provided char from the value at the current cursor position in the message.
        If the type tag at the current position does not correspond it will fail and
        return -1. Returns 0 on success.
        """
        return utils.lib.zosc_pop_char(self._p, utils.ffi.new("char_t **", val._p))

    def pop_bool(self, val):
        """
        Set the provided boolean from the type tag in the message. Booleans are not represented
        in the data in the message, only in the type tag. If the type tag at the current
        position does not correspond it will fail and return -1. Returns 0 on success.
        """
        return utils.lib.zosc_pop_bool(self._p, val)

    def pop_midi(self, val):
        """
        Set the provided 4 bytes (unsigned 32bit int) from the value at the current
        cursor position in the message. If the type tag at the current position does
        not correspond it will fail and return -1. Returns 0 on success.
        """
        return utils.lib.zosc_pop_midi(self._p, val)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zosc_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
