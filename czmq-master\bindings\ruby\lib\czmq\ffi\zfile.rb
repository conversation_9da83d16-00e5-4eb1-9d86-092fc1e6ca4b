################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

module CZMQ
  module FFI

    # helper functions for working with files.
    # @note This class is 100% generated using zproject.
    class Zfile
      # Raised when one tries to use an instance of {Zfile} after
      # the internal pointer to the native object has been nullified.
      class DestroyedError < RuntimeError; end

      # Boilerplate for self pointer, initializer, and finalizer
      class << self
        alias :__new :new
      end
      # Attaches the pointer _ptr_ to this instance and defines a finalizer for
      # it if necessary.
      # @param ptr [::FFI::Pointer]
      # @param finalize [Boolean]
      def initialize(ptr, finalize = true)
        @ptr = ptr
        if @ptr.null?
          @ptr = nil # Remove null pointers so we don't have to test for them.
        elsif finalize
          @finalizer = self.class.create_finalizer_for @ptr
          ObjectSpace.define_finalizer self, @finalizer
        end
      end
      # @param ptr [::FFI::Pointer]
      # @return [Proc]
      def self.create_finalizer_for(ptr)
        ptr_ptr = ::FFI::MemoryPointer.new :pointer

        Proc.new do
          ptr_ptr.write_pointer ptr
          ::CZMQ::FFI.zfile_destroy ptr_ptr
        end
      end
      # @return [Boolean]
      def null?
        !@ptr or @ptr.null?
      end
      # Return internal pointer
      # @return [::FFI::Pointer]
      def __ptr
        raise DestroyedError unless @ptr
        @ptr
      end
      # So external Libraries can just pass the Object to a FFI function which expects a :pointer
      alias_method :to_ptr, :__ptr
      # Nullify internal pointer and return pointer pointer.
      # @note This detaches the current instance from the native object
      #   and thus makes it unusable.
      # @return [::FFI::MemoryPointer] the pointer pointing to a pointer
      #   pointing to the native object
      def __ptr_give_ref
        raise DestroyedError unless @ptr
        ptr_ptr = ::FFI::MemoryPointer.new :pointer
        ptr_ptr.write_pointer @ptr
        __undef_finalizer if @finalizer
        @ptr = nil
        ptr_ptr
      end
      # Undefines the finalizer for this object.
      # @note Only use this if you need to and can guarantee that the native
      #   object will be freed by other means.
      # @return [void]
      def __undef_finalizer
        ObjectSpace.undefine_finalizer self
        @finalizer = nil
      end

      # If file exists, populates properties. CZMQ supports portable symbolic
      # links, which are files with the extension ".ln". A symbolic link is a
      # text file containing one line, the filename of a target file. Reading
      # data from the symbolic link actually reads from the target file. Path
      # may be NULL, in which case it is not used.
      # @param path [String, #to_s, nil]
      # @param name [String, #to_s, nil]
      # @return [CZMQ::Zfile]
      def self.new(path, name)
        ptr = ::CZMQ::FFI.zfile_new(path, name)
        __new ptr
      end

      # Create new temporary file for writing via tmpfile. File is automatically
      # deleted on destroy
      # @return [CZMQ::Zfile]
      def self.tmp()
        ptr = ::CZMQ::FFI.zfile_tmp()
        __new ptr
      end

      # Destroy a file item
      #
      # @return [void]
      def destroy()
        return unless @ptr
        self_p = __ptr_give_ref
        result = ::CZMQ::FFI.zfile_destroy(self_p)
        result
      end

      # Duplicate a file item, returns a newly constructed item. If the file
      # is null, or memory was exhausted, returns null.
      #
      # @return [Zfile]
      def dup()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_dup(self_p)
        result = Zfile.__new result, true
        result
      end

      # Return file name, remove path if provided
      #
      # @param path [String, #to_s, nil]
      # @return [String]
      def filename(path)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_filename(self_p, path)
        result
      end

      # Refresh file properties from disk; this is not done automatically
      # on access methods, otherwise it is not possible to compare directory
      # snapshots.
      #
      # @return [void]
      def restat()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_restat(self_p)
        result
      end

      # Return when the file was last modified. If you want this to reflect the
      # current situation, call zfile_restat before checking this property.
      #
      # @return [::FFI::Pointer]
      def modified()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_modified(self_p)
        result
      end

      # Return the last-known size of the file. If you want this to reflect the
      # current situation, call zfile_restat before checking this property.
      #
      # @return [::FFI::Pointer]
      def cursize()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_cursize(self_p)
        result
      end

      # Return true if the file is a directory. If you want this to reflect
      # any external changes, call zfile_restat before checking this property.
      #
      # @return [Boolean]
      def is_directory()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_is_directory(self_p)
        result
      end

      # Return true if the file is a regular file. If you want this to reflect
      # any external changes, call zfile_restat before checking this property.
      #
      # @return [Boolean]
      def is_regular()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_is_regular(self_p)
        result
      end

      # Return true if the file is readable by this process. If you want this to
      # reflect any external changes, call zfile_restat before checking this
      # property.
      #
      # @return [Boolean]
      def is_readable()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_is_readable(self_p)
        result
      end

      # Return true if the file is writeable by this process. If you want this
      # to reflect any external changes, call zfile_restat before checking this
      # property.
      #
      # @return [Boolean]
      def is_writeable()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_is_writeable(self_p)
        result
      end

      # Check if file has stopped changing and can be safely processed.
      # Updates the file statistics from disk at every call.
      #
      # @return [Boolean]
      def is_stable()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_is_stable(self_p)
        result
      end

      # Return true if the file was changed on disk since the zfile_t object
      # was created, or the last zfile_restat() call made on it.
      #
      # @return [Boolean]
      def has_changed()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_has_changed(self_p)
        result
      end

      # Remove the file from disk
      #
      # @return [void]
      def remove()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_remove(self_p)
        result
      end

      # Open file for reading
      # Returns 0 if OK, -1 if not found or not accessible
      #
      # @return [Integer]
      def input()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_input(self_p)
        result
      end

      # Open file for writing, creating directory if needed
      # File is created if necessary; chunks can be written to file at any
      # location. Returns 0 if OK, -1 if error.
      #
      # @return [Integer]
      def output()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_output(self_p)
        result
      end

      # Read chunk from file at specified position. If this was the last chunk,
      # sets the eof property. Returns a null chunk in case of error.
      #
      # @param bytes [Integer, #to_int, #to_i]
      # @param offset [::FFI::Pointer, #to_ptr]
      # @return [Zchunk]
      def read(bytes, offset)
        raise DestroyedError unless @ptr
        self_p = @ptr
        bytes = Integer(bytes)
        result = ::CZMQ::FFI.zfile_read(self_p, bytes, offset)
        result = Zchunk.__new result, true
        result
      end

      # Returns true if zfile_read() just read the last chunk in the file.
      #
      # @return [Boolean]
      def eof()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_eof(self_p)
        result
      end

      # Write chunk to file at specified position
      # Return 0 if OK, else -1
      #
      # @param chunk [Zchunk, #__ptr]
      # @param offset [::FFI::Pointer, #to_ptr]
      # @return [Integer]
      def write(chunk, offset)
        raise DestroyedError unless @ptr
        self_p = @ptr
        chunk = chunk.__ptr if chunk
        result = ::CZMQ::FFI.zfile_write(self_p, chunk, offset)
        result
      end

      # Read next line of text from file. Returns a pointer to the text line,
      # or NULL if there was nothing more to read from the file.
      #
      # @return [String]
      def readln()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_readln(self_p)
        result
      end

      # Close file, if open
      #
      # @return [void]
      def close()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_close(self_p)
        result
      end

      # Return file handle, if opened
      #
      # @return [::FFI::Pointer]
      def handle()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_handle(self_p)
        result
      end

      # Calculate SHA1 digest for file, using zdigest class.
      #
      # @return [String]
      def digest()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zfile_digest(self_p)
        result
      end

      # Self test of this class.
      #
      # @param verbose [Boolean]
      # @return [void]
      def self.test(verbose)
        verbose = !(0==verbose||!verbose) # boolean
        result = ::CZMQ::FFI.zfile_test(verbose)
        result
      end
    end
  end
end

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
