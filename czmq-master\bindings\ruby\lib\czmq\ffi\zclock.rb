################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

module CZMQ
  module FFI

    # millisecond clocks and delays
    # @note This class is 100% generated using zproject.
    class Zclock
      # Raised when one tries to use an instance of {Zclock} after
      # the internal pointer to the native object has been nullified.
      class DestroyedError < RuntimeError; end

      # Boilerplate for self pointer, initializer, and finalizer
      class << self
        alias :__new :new
      end
      # Attaches the pointer _ptr_ to this instance and defines a finalizer for
      # it if necessary.
      # @param ptr [::FFI::Pointer]
      # @param finalize [Boolean]
      def initialize(ptr, finalize = true)
        @ptr = ptr
        if @ptr.null?
          @ptr = nil # Remove null pointers so we don't have to test for them.
        elsif finalize
          @finalizer = self.class.create_finalizer_for @ptr
          ObjectSpace.define_finalizer self, @finalizer
        end
      end
      # @return [Proc]
      def self.create_finalizer_for(ptr)
        Proc.new do
          "WARNING: "\
          "Objects of type #{self} cannot be destroyed implicitly. "\
          "Please call the correct destroy method with the relevant arguments."
        end
      end
      # @return [Boolean]
      def null?
        !@ptr or @ptr.null?
      end
      # Return internal pointer
      # @return [::FFI::Pointer]
      def __ptr
        raise DestroyedError unless @ptr
        @ptr
      end
      # So external Libraries can just pass the Object to a FFI function which expects a :pointer
      alias_method :to_ptr, :__ptr
      # Nullify internal pointer and return pointer pointer.
      # @note This detaches the current instance from the native object
      #   and thus makes it unusable.
      # @return [::FFI::MemoryPointer] the pointer pointing to a pointer
      #   pointing to the native object
      def __ptr_give_ref
        raise DestroyedError unless @ptr
        ptr_ptr = ::FFI::MemoryPointer.new :pointer
        ptr_ptr.write_pointer @ptr
        __undef_finalizer if @finalizer
        @ptr = nil
        ptr_ptr
      end
      # Undefines the finalizer for this object.
      # @note Only use this if you need to and can guarantee that the native
      #   object will be freed by other means.
      # @return [void]
      def __undef_finalizer
        ObjectSpace.undefine_finalizer self
        @finalizer = nil
      end

      # Sleep for a number of milliseconds
      #
      # @param msecs [Integer, #to_int, #to_i]
      # @return [void]
      def self.sleep(msecs)
        msecs = Integer(msecs)
        result = ::CZMQ::FFI.zclock_sleep(msecs)
        result
      end

      # Return current system clock as milliseconds. Note that this clock can
      # jump backwards (if the system clock is changed) so is unsafe to use for
      # timers and time offsets. Use zclock_mono for that instead.
      #
      # @return [::FFI::Pointer]
      def self.time()
        result = ::CZMQ::FFI.zclock_time()
        result
      end

      # Return current monotonic clock in milliseconds. Use this when you compute
      # time offsets. The monotonic clock is not affected by system changes and
      # so will never be reset backwards, unlike a system clock.
      #
      # @return [::FFI::Pointer]
      def self.mono()
        result = ::CZMQ::FFI.zclock_mono()
        result
      end

      # Return current monotonic clock in microseconds. Use this when you compute
      # time offsets. The monotonic clock is not affected by system changes and
      # so will never be reset backwards, unlike a system clock.
      #
      # @return [::FFI::Pointer]
      def self.usecs()
        result = ::CZMQ::FFI.zclock_usecs()
        result
      end

      # Return formatted date/time as fresh string. Free using zstr_free().
      #
      # @return [::FFI::AutoPointer]
      def self.timestr()
        result = ::CZMQ::FFI.zclock_timestr()
        result = ::FFI::AutoPointer.new(result, LibC.method(:free))
        result
      end

      # Self test of this class.
      #
      # @param verbose [Boolean]
      # @return [void]
      def self.test(verbose)
        verbose = !(0==verbose||!verbose) # boolean
        result = ::CZMQ::FFI.zclock_test(verbose)
        result
      end
    end
  end
end

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
