<class name = "zpoller" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    event-driven reactor

    <constructor>
        Create new poller, specifying zero or more readers. The list of
        readers ends in a NULL. Each reader can be a zsock_t instance, a
        zactor_t instance, a libzmq socket (void *), or a file handle.
        <argument name = "reader" type = "sockish" variadic = "1" />
    </constructor>

    <destructor>
        Destroy a poller
    </destructor>

    <method name = "add">
        Add a reader to be polled. Returns 0 if OK, -1 on failure. The reader may
        be a libzmq void * socket, a zsock_t instance, a zactor_t instance or a
        file handle.
        <argument name = "reader" type = "sockish" />
        <return type = "integer" />
    </method>

    <method name = "remove">
        Remove a reader from the poller; returns 0 if OK, -1 on failure. The reader
        must have been passed during construction, or in an zpoller_add () call.
        <argument name = "reader" type = "anything" />
        <return type = "integer" />
    </method>

    <method name = "set nonstop" >
        By default the poller stops if the process receives a SIGINT or SIGTERM
        signal. This makes it impossible to shut-down message based architectures
        like zactors. This method lets you switch off break handling. The default
        nonstop setting is off (false).
        <argument name = "nonstop" type = "boolean" />
    </method>

    <method name = "wait">
        Poll the registered readers for I/O, return first reader that has input.
        The reader will be a libzmq void * socket, a zsock_t, a zactor_t
        instance or a file handle as specified in zpoller_new/zpoller_add. The 
        timeout should be zero or greater, or -1 to wait indefinitely. Socket 
        priority is defined by their order in the poll list. If you need a 
        balanced poll, use the low level zmq_poll method directly. If the poll 
        call was interrupted (SIGINT), or the ZMQ context was destroyed, or the 
        timeout expired, returns NULL. You can test the actual exit condition by 
        calling zpoller_expired () and zpoller_terminated (). The timeout is in 
        msec.
        <argument name = "timeout" type = "integer" />
        <return type = "sockish" />
    </method>

    <method name = "expired">
        Return true if the last zpoller_wait () call ended because the timeout
        expired, without any error.
        <return type = "boolean" />
    </method>

    <method name = "terminated">
        Return true if the last zpoller_wait () call ended because the process
        was interrupted, or the parent context was destroyed.
        <return type = "boolean" />
    </method>
</class>
