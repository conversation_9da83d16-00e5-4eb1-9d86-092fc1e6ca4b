/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZhttpServerOptions.h"


///
//  Get the server listening port.
int QmlZhttpServerOptions::port () {
    return zhttp_server_options_port (self);
};

///
//  Set the server listening port
void QmlZhttpServerOptions::setPort (int port) {
    zhttp_server_options_set_port (self, port);
};

///
//  Get the address sockets should connect to in order to receive requests.
const QString QmlZhttpServerOptions::backendAddress () {
    return QString (zhttp_server_options_backend_address (self));
};

///
//  Set the address sockets should connect to in order to receive requests.
void QmlZhttpServerOptions::setBackendAddress (const QString &address) {
    zhttp_server_options_set_backend_address (self, address.toUtf8().data());
};


QObject* QmlZhttpServerOptions::qmlAttachedProperties(QObject* object) {
    return new QmlZhttpServerOptionsAttached(object);
}


///
//  Self test of this class.
void QmlZhttpServerOptionsAttached::test (bool verbose) {
    zhttp_server_options_test (verbose);
};

///
//  Create a new zhttp_server_options.
QmlZhttpServerOptions *QmlZhttpServerOptionsAttached::construct () {
    QmlZhttpServerOptions *qmlSelf = new QmlZhttpServerOptions ();
    qmlSelf->self = zhttp_server_options_new ();
    return qmlSelf;
};

///
//  Create options from config tree.
QmlZhttpServerOptions *QmlZhttpServerOptionsAttached::fromConfig (QmlZconfig *config) {
    QmlZhttpServerOptions *qmlSelf = new QmlZhttpServerOptions ();
    qmlSelf->self = zhttp_server_options_from_config (config->self);
    return qmlSelf;
};

///
//  Destroy the zhttp_server_options.
void QmlZhttpServerOptionsAttached::destruct (QmlZhttpServerOptions *qmlSelf) {
    zhttp_server_options_destroy (&qmlSelf->self);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
