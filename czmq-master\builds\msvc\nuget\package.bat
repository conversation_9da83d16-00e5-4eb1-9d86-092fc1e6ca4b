@if "%ECHOON%" == "" (@echo off) else (@echo %ECHOON%)&:: set ECHOON=on if you want to debug this script
@::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
@::  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  ::
@::  Read the zproject/README.md for information about making permanent changes. ::
@::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
ECHO Started nuget packaging build.
ECHO.
REM http://nuget.codeplex.com/releases
nuget pack package.nuspec -verbosity detailed
ECHO.
ECHO NOTE: Ignore warnings not applicable to native code: "Issue: Assembly outside lib folder."
ECHO.
ECHO Completed nuget packaging build. The package is in the following folder:
CD
