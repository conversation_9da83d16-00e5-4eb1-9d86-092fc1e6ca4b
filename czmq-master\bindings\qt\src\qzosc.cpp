/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZosc::QZosc (zosc_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Create a new empty OSC message with the specified address string.
QZosc::QZosc (const QString &address, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zosc_new (address.toUtf8().data());
}

///
//  Create a new OSC message from the specified zframe. Takes ownership of
//  the zframe.
QZosc* QZosc::fromframe (QZframe *frame, QObject *qObjParent)
{
    return new QZosc (zosc_fromframe (frame->self), qObjParent);
}

///
//  Create a new zosc message from memory. Take ownership of the memory
//  and calling free on the data after construction.
QZosc* QZosc::frommem (char *data, size_t size, QObject *qObjParent)
{
    return new QZosc (zosc_frommem (data, size), qObjParent);
}

///
//  Create a new zosc message from a string. This the same syntax as
//  zosc_create but written as a single line string.
QZosc* QZosc::fromstring (const QString &oscstring, QObject *qObjParent)
{
    return new QZosc (zosc_fromstring (oscstring.toUtf8().data()), qObjParent);
}

///
//  Destroy an OSC message
QZosc::~QZosc ()
{
    zosc_destroy (&self);
}

///
//  Return chunk data size
size_t QZosc::size ()
{
    size_t rv = zosc_size (self);
    return rv;
}

///
//  Return OSC chunk data. Caller does not own the data!
byte * QZosc::data ()
{
    byte * rv = zosc_data (self);
    return rv;
}

///
//  Return the OSC address string
const QString QZosc::address ()
{
    const QString rv = QString (zosc_address (self));
    return rv;
}

///
//  Return the OSC format of the message.
//    i - 32bit integer
//    h - 64bit integer
//    f - 32bit floating point number (IEEE)
//    d - 64bit (double) floating point number
//    s - string (NULL terminated)
//    t = timetag: an OSC timetag in NTP format (uint64_t)
//    S - symbol
//    c - char
//    m - 4 byte midi packet (8 digits hexadecimal)
//    T - TRUE (no value required)
//    F - FALSE (no value required)
//    N - NIL (no value required)
//    I - Impulse (for triggers) or INFINITUM (no value required)
//    b - binary blob
const QString QZosc::format ()
{
    const QString rv = QString (zosc_format (self));
    return rv;
}

///
//  Create copy of the message, as new chunk object. Returns a fresh zosc_t
//  object, or null if there was not enough heap memory. If chunk is null,
//  returns null.
QZosc * QZosc::dup ()
{
    QZosc *rv = new QZosc (zosc_dup (self));
    return rv;
}

///
//  Transform zosc into a zframe that can be sent in a message.
QZframe * QZosc::pack ()
{
    QZframe *rv = new QZframe (zosc_pack (self));
    return rv;
}

///
//  Transform zosc into a zframe that can be sent in a message.
//  Take ownership of the chunk.
QZframe * QZosc::packx ()
{
    QZframe *rv = new QZframe (zosc_packx (&self));
    return rv;
}

///
//  Transform a zframe into a zosc.
QZosc * QZosc::unpack (QZframe *frame)
{
    QZosc *rv = new QZosc (zosc_unpack (frame->self));
    return rv;
}

///
//  Return a string describing the the OSC message. The returned string must be freed by the caller.
QString QZosc::dump ()
{
    char *retStr_ = zosc_dump (self);
    QString rv = QString (retStr_);
    zstr_free (&retStr_);
    return rv;
}

///
//  Dump OSC message to stdout, for debugging and tracing.
void QZosc::print ()
{
    zosc_print (self);

}

///
//  Probe the supplied object, and report if it looks like a zosc_t.
bool QZosc::is (void *self)
{
    bool rv = zosc_is (self);
    return rv;
}

///
//  Return a pointer to the item at the head of the OSC data.
//  Sets the given char argument to the type tag of the data.
//  If the message is empty, returns NULL and the sets the
//  given char to NULL.
const void * QZosc::first (char *type)
{
    const void * rv = zosc_first (self, type);
    return rv;
}

///
//  Return the next item of the OSC message. If the list is empty, returns
//  NULL. To move to the start of the OSC message call zosc_first ().
const void * QZosc::next (char *type)
{
    const void * rv = zosc_next (self, type);
    return rv;
}

///
//  Return a pointer to the item at the tail of the OSC message.
//  Sets the given char argument to the type tag of the data. If
//  the message is empty, returns NULL.
const void * QZosc::last (char *type)
{
    const void * rv = zosc_last (self, type);
    return rv;
}

///
//  Set the provided 32 bit integer from value at the current cursor position in the message.
//  If the type tag at the current position does not correspond it will fail and
//  return -1. Returns 0 on success.
int QZosc::popInt32 (int *val)
{
    int rv = zosc_pop_int32 (self, val);
    return rv;
}

///
//  Set the provided 64 bit integer from the value at the current cursor position in the message.
//  If the type tag at the current position does not correspond it will fail and
//  return -1. Returns 0 on success.
int QZosc::popInt64 (int64_t *val)
{
    int rv = zosc_pop_int64 (self, val);
    return rv;
}

///
//  Set the provided float from the value at the current cursor position in the message.
//  If the type tag at the current position does not correspond it will fail and
//  return -1. Returns 0 on success.
int QZosc::popFloat (float *val)
{
    int rv = zosc_pop_float (self, val);
    return rv;
}

///
//  Set the provided double from the value at the current cursor position in the message.
//  If the type tag at the current position does not correspond it will fail and
//  return -1. Returns 0 on success.
int QZosc::popDouble (double *val)
{
    int rv = zosc_pop_double (self, val);
    return rv;
}

///
//  Set the provided char from the value at the current cursor position in the message.
//  If the type tag at the current position does not correspond it will fail and
//  return -1. Returns 0 on success.
int QZosc::popChar (char *val)
{
    int rv = zosc_pop_char (self, val);
    return rv;
}

///
//  Set the provided boolean from the type tag in the message. Booleans are not represented
//  in the data in the message, only in the type tag. If the type tag at the current
//  position does not correspond it will fail and return -1. Returns 0 on success.
int QZosc::popBool (bool *val)
{
    int rv = zosc_pop_bool (self, val);
    return rv;
}

///
//  Set the provided 4 bytes (unsigned 32bit int) from the value at the current
//  cursor position in the message. If the type tag at the current position does
//  not correspond it will fail and return -1. Returns 0 on success.
int QZosc::popMidi (quint32 val)
{
    int rv = zosc_pop_midi (self, (uint32_t *) val);
    return rv;
}

///
//  Self test of this class.
void QZosc::test (bool verbose)
{
    zosc_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
