/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZarmour.h"


///
//  Encode a stream of bytes into an armoured string. Returns the armoured
//  string, or NULL if there was insufficient memory available to allocate
//  a new string.
QString QmlZarmour::encode (const byte *data, size_t size) {
    char *retStr_ = zarmour_encode (self, data, size);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Decode an armoured string into a chunk. The decoded output is
//  null-terminated, so it may be treated as a string, if that's what
//  it was prior to encoding.
QmlZchunk *QmlZarmour::decode (const QString &data) {
    QmlZchunk *retQ_ = new QmlZchunk ();
    retQ_->self = zarmour_decode (self, data.toUtf8().data());
    return retQ_;
};

///
//  Get the mode property.
int QmlZarmour::mode () {
    return zarmour_mode (self);
};

///
//  Get printable string for mode.
const QString QmlZarmour::modeStr () {
    return QString (zarmour_mode_str (self));
};

///
//  Set the mode property.
void QmlZarmour::setMode (int mode) {
    zarmour_set_mode (self, mode);
};

///
//  Return true if padding is turned on.
bool QmlZarmour::pad () {
    return zarmour_pad (self);
};

///
//  Turn padding on or off. Default is on.
void QmlZarmour::setPad (bool pad) {
    zarmour_set_pad (self, pad);
};

///
//  Get the padding character.
char QmlZarmour::padChar () {
    return zarmour_pad_char (self);
};

///
//  Set the padding character.
void QmlZarmour::setPadChar (char padChar) {
    zarmour_set_pad_char (self, padChar);
};

///
//  Return if splitting output into lines is turned on. Default is off.
bool QmlZarmour::lineBreaks () {
    return zarmour_line_breaks (self);
};

///
//  Turn splitting output into lines on or off.
void QmlZarmour::setLineBreaks (bool lineBreaks) {
    zarmour_set_line_breaks (self, lineBreaks);
};

///
//  Get the line length used for splitting lines.
size_t QmlZarmour::lineLength () {
    return zarmour_line_length (self);
};

///
//  Set the line length used for splitting lines.
void QmlZarmour::setLineLength (size_t lineLength) {
    zarmour_set_line_length (self, lineLength);
};

///
//  Print properties of object
void QmlZarmour::print () {
    zarmour_print (self);
};


QObject* QmlZarmour::qmlAttachedProperties(QObject* object) {
    return new QmlZarmourAttached(object);
}


///
//  Self test of this class.
void QmlZarmourAttached::test (bool verbose) {
    zarmour_test (verbose);
};

///
//  Create a new zarmour
QmlZarmour *QmlZarmourAttached::construct () {
    QmlZarmour *qmlSelf = new QmlZarmour ();
    qmlSelf->self = zarmour_new ();
    return qmlSelf;
};

///
//  Destroy the zarmour
void QmlZarmourAttached::destruct (QmlZarmour *qmlSelf) {
    zarmour_destroy (&qmlSelf->self);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
