/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZdirPatch::QZdirPatch (zdir_patch_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Create new patch
QZdirPatch::QZdirPatch (const QString &path, QZfile *file, int op, const QString &alias, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zdir_patch_new (path.toUtf8().data(), file->self, op, alias.toUtf8().data());
}

///
//  Destroy a patch
QZdirPatch::~QZdirPatch ()
{
    zdir_patch_destroy (&self);
}

///
//  Create copy of a patch. If the patch is null, or memory was exhausted,
//  returns null.
QZdirPatch * QZdirPatch::dup ()
{
    QZdirPatch *rv = new QZdirPatch (zdir_patch_dup (self));
    return rv;
}

///
//  Return patch file directory path
const QString QZdirPatch::path ()
{
    const QString rv = QString (zdir_patch_path (self));
    return rv;
}

///
//  Return patch file item
QZfile * QZdirPatch::file ()
{
    QZfile *rv = new QZfile (zdir_patch_file (self));
    return rv;
}

///
//  Return operation
int QZdirPatch::op ()
{
    int rv = zdir_patch_op (self);
    return rv;
}

///
//  Return patch virtual file path
const QString QZdirPatch::vpath ()
{
    const QString rv = QString (zdir_patch_vpath (self));
    return rv;
}

///
//  Calculate hash digest for file (create only)
void QZdirPatch::digestSet ()
{
    zdir_patch_digest_set (self);

}

///
//  Return hash digest for patch file
const QString QZdirPatch::digest ()
{
    const QString rv = QString (zdir_patch_digest (self));
    return rv;
}

///
//  Self test of this class.
void QZdirPatch::test (bool verbose)
{
    zdir_patch_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
