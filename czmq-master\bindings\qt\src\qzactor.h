/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#ifndef Q_ZACTOR_H
#define Q_ZACTOR_H

#include "qczmq.h"

class QT_CZMQ_EXPORT QZactor : public QObject
{
    Q_OBJECT
public:

    //  Copy-construct to return the proper wrapped c types
    QZactor (zactor_t *self, QObject *qObjParent = 0);

    //  Create a new actor passing arbitrary arguments reference.
    explicit QZactor (zactor_fn task, void *args, QObject *qObjParent = 0);

    //  Destroy an actor.
    ~QZactor ();

    //  Send a zmsg message to the actor, take ownership of the message
    //  and destroy when it has been sent.
    int send (QZmsg *msgP);

    //  Receive a zmsg message from the actor. Returns NULL if the actor
    //  was interrupted before the message could be received, or if there
    //  was a timeout on the actor.
    QZmsg * recv ();

    //  Probe the supplied object, and report if it looks like a zactor_t.
    static bool is (void *self);

    //  Probe the supplied reference. If it looks like a zactor_t instance,
    //  return the underlying libzmq actor handle; else if it looks like
    //  a libzmq actor handle, return the supplied value.
    static void * resolve (void *self);

    //  Return the actor's zsock handle. Use this when you absolutely need
    //  to work with the zsock instance rather than the actor.
    QZsock * sock ();

    //  Change default destructor by custom function. Actor MUST be able to handle new message instead of default $TERM.
    void setDestructor (zactor_destructor_fn destructor);

    //  Self test of this class.
    static void test (bool verbose);

    zactor_t *self;
};
#endif //  Q_ZACTOR_H
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
