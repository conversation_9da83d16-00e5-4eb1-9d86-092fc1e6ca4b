/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#ifndef QML_ZDIGEST_H
#define QML_ZDIGEST_H

#include <QtQml>

#include <czmq.h>
#include "qml_czmq_plugin.h"


class QmlZdigest : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isNULL READ isNULL)

public:
    zdigest_t *self;

    QmlZdigest() { self = NULL; }
    bool isNULL() { return self == NULL; }

    static QObject* qmlAttachedProperties(QObject* object); // defined in QmlZdigest.cpp

public slots:
    //  Add buffer into digest calculation
    void update (const byte *buffer, size_t length);

    //  Return final digest hash data. If built without crypto support,
    //  returns NULL.
    const byte *data ();

    //  Return final digest hash size
    size_t size ();

    //  Return digest as printable hex string; caller should not modify nor
    //  free this string. After calling this, you may not use zdigest_update()
    //  on the same digest. If built without crypto support, returns NULL.
    const QString string ();
};

class QmlZdigestAttached : public QObject
{
    Q_OBJECT
    QObject* m_attached;

public:
    QmlZdigestAttached (QObject* attached) {
        Q_UNUSED (attached);
    };

public slots:
    //  Self test of this class.
    void test (bool verbose);

    //  Constructor - creates new digest object, which you use to build up a
    //  digest by repeatedly calling zdigest_update() on chunks of data.
    QmlZdigest *construct ();

    //  Destroy a digest object
    void destruct (QmlZdigest *qmlSelf);
};


QML_DECLARE_TYPEINFO(QmlZdigest, QML_HAS_ATTACHED_PROPERTIES)

#endif
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
