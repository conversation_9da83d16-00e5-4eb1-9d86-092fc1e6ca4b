#include "sys_utl.h"
#include "es_strategy.h"
#include "ace/OS.h"
#include "rdbop.h"
#include "fdc/datainf.h"
#include "fdc/fdcdef.h"
#include "eventtype.h"
#include "CJsonObject.h"
/*********/
#include <fcntl.h>
#include <unistd.h>
#include <sys/stat.h>
#include <stdio.h>
#include "nlohmann/json.hpp"
/*********/

using namespace ECON;
using namespace FDC;
using namespace std;
using namespace neb;

//云端策略下发通道对应的数据组号
static int g_CMD_RTU_NO[] = {1};

//PCS 数据组号集合
static const int g_PCS_RTU_NO[] = {0, 20, 30, 40, 50};

//PCS 放电有功功率设置 遥测点号集合
static const int g_PCS_FHP_YCNO[] = {154, 154, 154, 154, 154};
//PCS 放电有功功率设置 遥测点号集合
static const int g_PCS_CHP_YCNO[] = {155, 155, 155, 155, 155};
//PCS 产品运行模式设置 遥测点号集合
static const int g_PCS_R_YCNO[] = {146, 146, 146, 146, 146};
//PCS PQ工作模式设置 遥测点号集合
static const int g_PCS_PQM_YCNO[] = {151, 151, 151, 151, 151};
//PCS 无功功率设置 遥测点号集合
static const int g_PCS_HQ_YCNO[] = {131, 131, 131, 131, 131};
//PCS 交流有功功率 遥测点号集合
static const int g_PCS_P_YCNO[] = {11, 11, 11, 11, 11};
//PCS 交流无功功率 遥测点号集合
static const int g_PCS_Q_YCNO[] = {12, 12, 12, 12, 12};

//PCS 故障 遥信点号集合
static const int g_PCS_F_YXNO[] = {6, 6, 6, 6, 6};
//PCS 运行 遥信点号集合
static const int g_PCS_ON_YXNO[] = {14, 14, 14, 14, 14};
//PCS 停机 遥信点号集合
static const int g_PCS_HALT_YXNO[] = {0, 0, 0, 0, 0};
//PCS 待机 遥信点号集合
static const int g_PCS_STDY_YXNO[] = {2, 2, 2, 2, 2};
//PCS 放电 遥信点号集合
static const int g_PCS_FD_YXNO[] = {80, 80, 80, 80, 80};
//PCS 充电 遥信点号集合
static const int g_PCS_CD_YXNO[] = {81, 81, 81, 81, 81};

//PCS 开机 遥调点号集合
static const int g_PCS_SW_YTNO[] = {0, 0, 0, 0, 0};
//PCS 放电有功功率设置 遥调点号集合
static const int g_PCS_FHP_YTNO[] = {10, 10, 10, 10, 10};
//PCS 充电有功功率设置 遥调点号集合
static const int g_PCS_CHP_YTNO[] = {11, 11, 11, 11, 11};
//PCS 产品运行模式设置 遥调点号集合
static const int g_PCS_R_YTNO[] = {7, 7, 7, 7, 7};
//PCS PQ工作模式设置 遥调点号集合
static const int g_PCS_PQ_YTNO[] = {9, 9, 9, 9, 9};
//PCS 无功功率设置 遥调点号集合
static const int g_PCS_Q_YTNO[] = {1, 1, 1, 1, 1};

//--------------------------------------------
//BMS 数据组号集合
static const int g_BMS_RTU_NO[] = {2, 22, 32, 42, 52};
//BMS SOC 遥测点号集合
static const int g_BMS_SOC_YCNO[] = {0, 0, 0, 0, 0};
//BMS 允许最大放电功率 遥测点号集合
static const int g_BMS_PF_YCNO[] = {39, 39, 39, 39, 39};
//BMS 允许最大充电功率 遥测点号集合
static const int g_BMS_PC_YCNO[] = {41, 41, 41, 41, 41};
//BMS 单体最高电压 遥测点号集合
static const int g_BMS_UH_YCNO[] = {7, 7, 7, 7, 7};
//BMS 单体最低电压 遥测点号集合
static const int g_BMS_UL_YCNO[] = {8, 8, 8, 8, 8};
//BMS 单体最高温度 遥测点号集合
static const int g_BMS_TH_YCNO[] = {9, 9, 9, 9, 9};
//BMS 单体最低温度 遥测点号集合
static const int g_BMS_TL_YCNO[] = {10, 10, 10, 10, 10};


//禁止充放
//BMS 故障 遥信点号集合
static const int g_BMS_F_YXNO[] = {130, 130, 130, 130, 130};
//BMS 单体压差大2 遥信点号集合
static const int g_BMS_UDH2_YXNO[] = {26, 26, 26, 26, 26};
//BMS 单体温度高2 遥信点号集合
static const int g_BMS_TH2_YXNO[] = {23, 23, 23, 23, 23};
//BMS 绝缘低2 遥信点号集合
static const int g_BMS_RL2_YXNO[] = {60, 60};
//BMS 极柱温度高2 遥信点号集合
static const int g_BMS_JH2_YXNO[] = {56, 56};
//BMS 温差大2 遥信点号集合
static const int g_BMS_TDH2_YXNO[] = {59, 59};
//BMS 绝缘低3 遥信点号集合
static const int g_BMS_RL3_YXNO[] = {81, 81};
//BMS 极柱温度高3 遥信点号集合
static const int g_BMS_JH3_YXNO[] = {77, 77};
//BMS 单体压差大3 遥信点号集合
static const int g_BMS_UDH3_YXNO[] = {74, 74};
//BMS 温差大3 遥信点号集合
static const int g_BMS_TDH3_YXNO[] = {80, 80};
//BMS 单体电压高3 遥信点号集合
static const int g_BMS_UH3_YXNO[] = {72, 72};
//BMS 充电电流大3 遥信点号集合
static const int g_BMS_CIH3_YXNO[] = {78, 78};
//BMS 总压高3 遥信点号集合
static const int g_BMS_ZUH3_YXNO[] = {82, 82};
//BMS 充电温度高3 遥信点号集合
static const int g_BMS_CTH3_YXNO[] = {84, 84};
//BMS 充电温度低3 遥信点号集合
static const int g_BMS_CTL3_YXNO[] = {85, 85};
//BMS 单体电压低3 遥信点号集合
static const int g_BMS_UL3_YXNO[] = {73, 73};
//BMS 放电电流大3 遥信点号集合
static const int g_BMS_FIH3_YXNO[] = {79, 79};
//BMS 放电温度高3 遥信点号集合
static const int g_BMS_FTH3_YXNO[] = {70, 70};
//BMS 放电温度低3 遥信点号集合
static const int g_BMS_FTL3_YXNO[] = {71, 71};
//BMS 总压低3 遥信点号集合
static const int g_BMS_ZUL3_YXNO[] = {83, 83};

//禁止充电
//BMS 禁充 遥信点号集合
static const int g_BMS_JC_YXNO[] = {128, 128, 128, 128, 128};
//BMS 单体电压高2 遥信点号集合
static const int g_BMS_UH2_YXNO[] = {20, 20, 20, 20, 20};
//BMS 模组电压高2 遥信点号集合
static const int g_BMS_PUH2_YXNO[] = {47, 47, 47, 47, 47};
//BMS 充电电流大2 遥信点号集合
static const int g_BMS_CIH2_YXNO[] = {57, 57};
//BMS 总压高2 遥信点号集合
static const int g_BMS_ZUH2_YXNO[] = {61, 61};

//禁止放电
//BMS 禁放 遥信点号集合
static const int g_BMS_JF_YXNO[] = {129, 129, 129, 129, 129};
//BMS 单体电压低2 遥信点号集合
static const int g_BMS_UL2_YXNO[] = {21, 21, 21, 21, 21};
//BMS 模组电压低2 遥信点号集合
static const int g_BMS_PUL2_YXNO[] = {48, 48, 48, 48, 48};
//BMS 放电电流大2 遥信点号集合
static const int g_BMS_FIH2_YXNO[] = {58, 58};
//BMS 总压低2 遥信点号集合
static const int g_BMS_ZUL2_YXNO[] = {62, 62};

//BMS 风扇1状态 遥信点号集合
static const int g_BMS_FAN_YXNO[] = {64, 64, 64, 64, 64};

//BMS 风扇启停 遥信点号集合
static const int g_BMS_FAN_YTNO[] = {1, 1, 1, 1, 1};

//--------------------------------------------
//计量表 数据组号集合
static const int g_M_RTU_NO[] = {7, 27, 37, 47, 57};
//计量表 正向总电能(充电)
static const int g_M_EC_YCNO[] = {18, 18, 18, 18, 18};
//计量表 反向总电能(放电)
static const int g_M_EF_YCNO[] = {23, 23, 23, 23, 23};

//------------------------------------------------------------
//IO 数据组号集合
static int g_IO_RTU_NO[] = {10, 28, 38, 48, 58};
//IO 温感报警
static int g_IO_HOT_YXNO[] = {0, 0, 0, 0, 0};
//IO 烟感报警
static int g_IO_SMOKE_YXNO[] = {1, 1, 1, 1, 1};
//IO 急停信号
static int g_IO_STOP_YXNO[] = {2, 2, 2, 2, 2};
//IO 运行开出信号反馈 1运行
static int g_IO_RUN_YXNO[] = {16, 9, 9, 9, 9};
//IO 故障开出信号反馈 1故障
static int g_IO_F_YXNO[] = {17, 10, 10, 10, 10};

//IO 运行灯 0灭,1亮 遥控
static int g_IO_RUN_YKNO[] = {0, 1, 1, 1, 1};
//IO 故障灯 0灭,1亮 遥控
static int g_IO_F_YKNO[] = {1, 2, 2, 2, 2};

//--------------------------------------------
//IO自身 数据组号集合
static int g_DIO_RTU_NO = 10;
//IO自身 DI遥信
static int g_DIO_DI_YXNO[] = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15};
//IO自身 DO遥信
static int g_DIO_DO_YXNO[] = {16, 17, 18, 19, 20};
//IO自身 DO遥控
static int g_DIO_DO_YKNO[] = {0, 1, 2, 3, 4};

//--------------------------------------------
//温控(液冷/空调) 数据组号集合
static const int g_TC_RTU_NO[] = {4, 24, 34, 44, 54};
//温控(液冷/空调) 遥信
static const int g_TC_YX_YXNO[] = {0, 0, 0, 0, 0};

//迈凯水冷 电芯最高温度 遥测点号集合
static const int g_TC_TSH_YCNO[] = {17, 17, 17, 17, 17};
//迈凯水冷 电芯最低温度 遥测点号集合
static const int g_TC_TSL_YCNO[] = {18, 18, 18, 18, 18};
//迈凯水冷 充放电状态 遥测点号集合
static const int g_TC_SCF_YCNO[] = {19, 19, 19, 19, 19};

//迈凯水冷 电芯最高温度 遥调点号集合
static const int g_TC_TSH_YTNO[] = {4, 4, 4, 4, 4};
//迈凯水冷 电芯最低温度 遥调点号集合
static const int g_TC_TSL_YTNO[] = {5, 5, 5, 5, 5};
//迈凯水冷 充放电状态 遥调点号集合
static const int g_TC_SCF_YTNO[] = {6, 6, 6, 6, 6};

//TCL空调 开关机 遥控点号集合
static const int g_TC_SW_YKNO[] = {0, 0, 0, 0, 0};


//----------------------------------------------------------------------------
CES_STRATEGY::CES_STRATEGY():
	m_strategyFactory(nullptr)
{
	m_pLink = NULL;
	m_pLinkInfo = NULL;
	m_pRoute = NULL;	
	m_pRouteInfo = NULL;
	m_pGroup = NULL;

	m_bCanNextRoute = FALSE;
	m_pcs_num = 1;
	m_soc_min = 5;
	m_soc_max = 95;
	m_pcs_power = 100;
	m_pcs_half = 50;
	m_anti_rtuno = 9;
	m_anti_power_yc = 45;
	m_res_power = 5;
	m_auto_fan = 0;
	m_third_enable = 0;
	m_meter_rtuno = 7;
	m_meter_ez_yc = 18;
	m_meter_ef_yc = 23;
	m_self_io = 1;
	m_mkls_enable = 0;
	m_io_mode = 0;
	m_soc_adj = 0;
	m_palloc_mode = 0;
	m_u_min = 2800;
	m_u_max = 3550;
	m_u_enable = 0;

	m_curHour = -1;
	m_curSec  = -1;
	memset(m_loopCnt, 0, sizeof(m_loopCnt));

	m_powerSet = 0;
}
CES_STRATEGY::~CES_STRATEGY()
{
}
//是否打开
bool	CES_STRATEGY::isOpen()	const
{
	if (!m_pLink)
		return false;

	return true;
}
//打开规约
bool	CES_STRATEGY::open()
{
	ACE_OS::sleep(1);//延时1s 在open失败、重复调用时 不至于占用cpu
	printlog(LOGE_ES_STRATEGY_BASE + m_link,"<ES_STRATEGY,LinkNo:%d> openning", m_link);
	m_pLink = m_commInf.link( m_link );
	if( !m_pLink ) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"取链路%d的链路信息失败,关闭规约",m_link);
		close();
		return false;
	}

	m_pLinkInfo = m_commInf.linkInfo( m_link );
	if ( !m_pLinkInfo ) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"取链路%d的链路相关信息失败,关闭规约",m_link);
		close();
		return false;
	}
	if (m_pLinkInfo->routeNum<=0) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"链路%d无关联路径,打开规约失败",m_link);
		close();
		return false;
	}
	m_vecStrategy.clear();
	for(int i = 0; i < PCC_NUM; i++) {
		SingleStrategy *sig = new SingleStrategy();
		m_vecStrategy.push_back(sig);
		m_vecStrategy[i]->init();
		m_vecStrategy[i]->m_rtuno = g_CMD_RTU_NO[i];
		// m_vecStrategy[i]->init();
		// m_vecStrategy[i]->m_rtuno = g_CMD_RTU_NO[i];
	}
	
	memset(m_needModStopRun, 0, sizeof(m_needModStopRun));
	memset(m_needModStopUncharge, 0, sizeof(m_needModStopUncharge));
	memset(m_needModStopCharge, 0, sizeof(m_needModStopCharge));
	memset(m_needFaltReset, 0, sizeof(m_needFaltReset));
	
	memset(m_fPCSModSetCHP, 0, sizeof(m_fPCSModSetCHP));
	memset(m_fPCSModSetFHP, 0, sizeof(m_fPCSModSetFHP));
	memset(m_PCSMod_cf, 0, sizeof(m_PCSMod_cf));

	memset(m_PCSMod_jcf, 0, sizeof(m_PCSMod_jcf));
	memset(m_PCSMod_R, 0, sizeof(m_PCSMod_R));
	memset(m_PCSMod_HQ, 0, sizeof(m_PCSMod_HQ));
	memset(m_PCSMod_pqm, 0, sizeof(m_PCSMod_pqm));
	memset(m_fPCS_P, 0, sizeof(m_fPCS_P));
	memset(m_fPCS_Q, 0, sizeof(m_fPCS_Q));

	memset(m_PCS_F, 0, sizeof(m_PCS_F));
	memset(m_PCS_ON, 0, sizeof(m_PCS_ON));
	memset(m_PCS_HALT, 0, sizeof(m_PCS_HALT));
	memset(m_PCS_STDY, 0, sizeof(m_PCS_STDY));
	memset(m_PCS_CD, 0, sizeof(m_PCS_CD));
	memset(m_PCS_FD, 0, sizeof(m_PCS_FD));

	memset(m_PCS_Offline, 0, sizeof(m_PCS_Offline));
	memset(m_TC_Offline, 1, sizeof(m_TC_Offline));
	memset(m_TC_run, 0, sizeof(m_TC_run));
	
	memset(m_iBMS_FanWaitOn, 0, sizeof(m_iBMS_FanWaitOn));
	memset(m_iBMS_FanWaitOff, 0, sizeof(m_iBMS_FanWaitOff));

	memset(m_fBMS_cuSOC, 0, sizeof(m_fBMS_cuSOC));
	memset(m_fBMS_cuU, 0, sizeof(m_fBMS_cuU));
	memset(m_fBMS_cuPC, 0, sizeof(m_fBMS_cuPC));
	memset(m_fBMS_cuPF, 0, sizeof(m_fBMS_cuPF));
	memset(m_fBMS_cuUH, 0, sizeof(m_fBMS_cuUH));
	memset(m_fBMS_cuUL, 0, sizeof(m_fBMS_cuUL));
	memset(m_fBMS_cuTH, 0, sizeof(m_fBMS_cuTH));
	memset(m_fBMS_cuTL, 0, sizeof(m_fBMS_cuTL));

	memset(m_BAMS_Fault, 0, sizeof(m_BAMS_Fault));
	memset(m_BMS_Fault, 0, sizeof(m_BMS_Fault));
	memset(m_BMS_JC, 0, sizeof(m_BMS_JC));
	memset(m_BMS_JF, 0, sizeof(m_BMS_JF));
	memset(m_BMS_Fan, 0, sizeof(m_BMS_Fan));
	
	memset(m_BMS_RL2, 0, sizeof(m_BMS_RL2));
	memset(m_BMS_JH2, 0, sizeof(m_BMS_JH2));
	memset(m_BMS_UDH2, 0, sizeof(m_BMS_UDH2));
	memset(m_BMS_TDH2, 0, sizeof(m_BMS_TDH2));

	memset(m_BMS_RL3, 0, sizeof(m_BMS_RL3));
	memset(m_BMS_JH3, 0, sizeof(m_BMS_JH3));
	memset(m_BMS_UDH3, 0, sizeof(m_BMS_UDH3));
	memset(m_BMS_TDH3, 0, sizeof(m_BMS_TDH3));
	memset(m_BMS_UH3, 0, sizeof(m_BMS_UH3));
	memset(m_BMS_CIH3, 0, sizeof(m_BMS_CIH3));
	memset(m_BMS_ZUH3, 0, sizeof(m_BMS_ZUH3));
	memset(m_BMS_CTH3, 0, sizeof(m_BMS_CTH3));
	memset(m_BMS_CTL3, 0, sizeof(m_BMS_CTL3));
	memset(m_BMS_UL3, 0, sizeof(m_BMS_UL3));
	memset(m_BMS_FIH3, 0, sizeof(m_BMS_FIH3));
	memset(m_BMS_FTH3, 0, sizeof(m_BMS_FTH3));
	memset(m_BMS_FTL3, 0, sizeof(m_BMS_FTL3));
	memset(m_BMS_ZUL3, 0, sizeof(m_BMS_ZUL3));
	
	memset(m_BMS_UH2, 0, sizeof(m_BMS_UH2));
	memset(m_BMS_CIH2, 0, sizeof(m_BMS_CIH2));
	memset(m_BMS_PUH2, 0, sizeof(m_BMS_PUH2));
	memset(m_BMS_ZUH2, 0, sizeof(m_BMS_ZUH2));
	memset(m_BMS_CTH2, 0, sizeof(m_BMS_CTH2));
	memset(m_BMS_CTL2, 0, sizeof(m_BMS_CTL2));

	memset(m_BMS_UL2, 0, sizeof(m_BMS_UL2));
	memset(m_BMS_FIH2, 0, sizeof(m_BMS_FIH2));
	memset(m_BMS_TH2, 0, sizeof(m_BMS_TH2));
	memset(m_BMS_ZUL2, 0, sizeof(m_BMS_ZUL2));
	memset(m_BMS_PUL2, 0, sizeof(m_BMS_PUL2));
	
	memset(m_BMS_UL1, 0, sizeof(m_BMS_UL1));
	memset(m_halfC, 0, sizeof(m_halfC));
	memset(m_halfF, 0, sizeof(m_halfF));

	memset(m_M_EC, 0, sizeof(m_M_EC));
	memset(m_M_EF, 0, sizeof(m_M_EF));

	memset(m_TC_TSH, 0, sizeof(m_TC_TSH));
	memset(m_TC_TSL, 0, sizeof(m_TC_TSL));
	memset(m_TC_SCF, 0, sizeof(m_TC_SCF));	

	memset(m_KT_F, 0, sizeof(m_KT_F));

	memset(m_IO_hot, 0, sizeof(m_IO_hot));
	memset(m_IO_smoke, 0, sizeof(m_IO_smoke));
	memset(m_IO_stop, 0, sizeof(m_IO_stop));
	memset(m_IO_run, 0, sizeof(m_IO_run));
	memset(m_IO_F, 0, sizeof(m_IO_F));

	m_KT_curmin = 0;

	setCurRouteBySend();
	if ( !routeInit() ) {
		printlog((LOGE_ES_STRATEGY_BASE + m_link),"<ES_STRATEGY,LinkNo:%d> routeInit failed! m_loopRoute=%d,m_curRoute=%d", m_link,m_loopRoute,m_curRoute);
		close();
		return false;
	}

	readStaticConfig();

	if ( !readFeature() ) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"ES_STRATEGY,LinkNo %d,read feature config error!",m_link);
		close();
		return false;
	}

	// clearYkInfo();
	// clearYtInfo();
	// getYkInfo();
	// getYtInfo();

	printlog((LOGE_ES_STRATEGY_BASE + m_link),"<ES_STRATEGY,LinkNo:%d> sleep 7s first", m_link);
	ACE_OS::sleep(5);//延时10s 等待其它链路启动
	GetP();
	ACE_OS::sleep(1);
	GetP();
	ACE_OS::sleep(1);
	GetP();
	if(!readZeroEcf()) {
		saveZeroEcf();
	}
	printlog((LOGE_ES_STRATEGY_BASE + m_link),"<ES_STRATEGY,LinkNo:%d> open sucess!", m_link);
	return	true;
}

//关闭规约
void	CES_STRATEGY::close()
{
	printlog(LOGE_ES_STRATEGY_BASE + m_link,"<ES_STRATEGY,LinkNo:%d> closed!", m_link);
	m_pLink = NULL;
	m_pLinkInfo = NULL;
	m_pRoute = NULL;	
	m_pRouteInfo = NULL;
	m_pGroup = NULL;
	
	for(int i = 0; i < PCC_NUM; i++) {
		// m_vecStrategy[i]->init();
		m_vecStrategy[i]->init();
		delete m_vecStrategy[i];
	}
	m_vecStrategy.clear();
	// clearYkInfo();
	// clearYtInfo();
}
//主处理过程
void	CES_STRATEGY::run()
{
	m_ctrlInf.remove();//清理过期命令
	CheckStatus();
	Ctrl_YKYT();
	execNorm();//实施模块停机操作
	autoFan();
	autoTC();

	execStrategy();

	ACE_OS::sleep(1);//延时1s
	
	if (m_pRouteInfo)
		m_pRouteInfo->lastDataOkTime = (hUInt32)time(0);
}

bool	CES_STRATEGY::routeInit()
{
	if(m_loopRoute<0||m_loopRoute>FDC_ROUTE_IN_LINK) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"m_loopRoute err,m_curRoute=%d ",m_loopRoute);
		return false;
	}

	m_pRoute = m_commInf.route(m_curRoute);
	if (!m_pRoute) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"m_pRoute is null,m_curRoute=%d ",m_curRoute);
		return false;
	}
	m_pRouteInfo = m_commInf.routeInfo(m_curRoute);
	if (!m_pRouteInfo) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"m_pRouteInfo is null,m_curRoute=%d ",m_curRoute);
		return false;
	}
	m_pGroup =m_commInf.group(m_pRoute->group);
	if (!m_pGroup) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"m_pGroup is null,group=%d",m_pRoute->group);
		return false;
	}

	return true;
}
bool	CES_STRATEGY::readFeature()
{
	if(reloadConfig())
	{
		return true;
	}
	return false;
	if(loadLocalEs()) {
		return true;
	}
	if(!featureFromdb()) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,
			"装载 ES_STRATEGY规约特征表 失败 , LinkNo = %d , 特征名 = %s , 返回失败",
			m_link, m_pLink->protoFeature);
		return false;
	}
	return true;
}
bool	CES_STRATEGY::featureFromdb()
{
	if(m_pLink->protoFeature[0] == 0)
		return false;

	if(0 != strcmp(m_pLink->protoFeature, "fdc_strategy_ex")) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link, "LinkNo = %d, 特征表 %s 不匹配", m_link, m_pLink->protoFeature);
		return false;
	}

	char sqlstr[80] = {0};
	sprintf(sqlstr,"select * from fdc_strategy_ex order by f_rtuno,f_type,f_no");

	CRdbOp rdbop(CRdbOp::Direct);
	CDataset ds;
	int ret = rdbop.exec(sqlstr, ds);
	printlog(LOGE_ES_STRATEGY_BASE + m_link, "ret=%d SQL:%s", ret, sqlstr);
	if ( ret < 0 ) {
		return false;
	}
	
	int rtuno = 0;
	int idx = 0;
	int t_no;
	hUChar t_type;
	ES_TIMEEXEC te;

	for(int i=0; i<ret; i++) {
		rtuno = ds.field( i,"f_rtuno").value().toInt32();
		idx = findByRtu(rtuno);
		if (idx < 0) {
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "not found rtuno=%d", rtuno);
			continue;
		}

		t_type = ds.field( i,"f_type").value().toInt8();
		switch(t_type) {
			case STRATEGY_TYPE_DEMAND: {//需量控制
				ES_STRATEGY_DEMAND::MonDemand md;
				md.mon = 0;
				//需量控制远程逻辑暂时维持原版 等远程策略修改到文件中配置时再去统一开发  只修改默认使用vector中第一个
				m_vecStrategy[idx]->m_demand.demandVal.clear();
				m_vecStrategy[idx]->m_demand.demandVal.push_back(md);
				t_no = ds.field( i,"f_no").value().toInt8();
				if( t_no == 0 ) {
					m_vecStrategy[idx]->m_demand.useflag = ds.field( i,"f_enable").value().toInt8();
					m_vecStrategy[idx]->m_demand.demandVal[0].demandVal = ds.field( i,"f_value").value().toFloat();
				}
				else if( t_no ==1 ) {
					m_vecStrategy[idx]->m_demand.upper = ds.field( i,"f_value").value().toFloat();
					m_vecStrategy[idx]->m_demand.upVal = m_vecStrategy[idx]->m_demand.demandVal[0].demandVal *m_vecStrategy[idx]->m_demand.upper*0.01;
				}
				else if( t_no ==2 )
					m_vecStrategy[idx]->m_demand.unchargeVal = ds.field( i,"f_value").value().toFloat();
				
				break;
			}
			
			case STRATEGY_TYPE_BACKUPPOWER:{//后备电源
				t_no = ds.field( i,"f_no").value().toInt8();
				if( t_no ==0 ) {
					m_vecStrategy[idx]->m_bkpower.useflag = ds.field( i,"f_enable").value().toInt8();
					m_vecStrategy[idx]->m_bkpower.lowSOC = ds.field( i,"f_value").value().toFloat();
				}
				break;
			}

			case STRATEGY_TYPE_REVERSEPOWER:{//反功率倒送
				t_no = ds.field( i,"f_no").value().toInt8();
				if( t_no ==0 ) {
					m_vecStrategy[idx]->m_repower.useflag = ds.field( i,"f_enable").value().toInt8();
					m_vecStrategy[idx]->m_repower.lowVal = ds.field( i,"f_value").value().toFloat();
				}
				break;
			}
			
			case STRATEGY_TYPE_PLS:{//移峰填谷
				t_no = ds.field( i,"f_no").value().toInt32();
				if( t_no ==0 ) {
					m_vecStrategy[idx]->m_pls.useflag = ds.field( i,"f_enable").value().toInt8();
					m_vecStrategy[idx]->m_pls.m_listTimeTask.clear();
					m_vecStrategy[idx]->m_firstRunPls = true;
					m_vecStrategy[idx]->m_curMin = 0;//能立即响应新策略
				}
				te.min = ds.field( i,"f_min").value().toInt16();
				te.powerVal = ds.field( i,"f_value").value().toFloat();
				m_vecStrategy[idx]->m_pls.m_listTimeTask.push_back(te);
				//printlog(LOGE_ES_STRATEGY_BASE + m_link, "i=%d min=%hd, value=%1.f", i, te.min, te.powerVal);
				break;
			}

			default:
				break;
		}
	}

	//补上24点整的
	if(m_vecStrategy[idx]->m_firstRunPls && m_vecStrategy[idx]->m_pls.m_listTimeTask.size() > 1) {
		te.min = 1440;
		te.powerVal = m_vecStrategy[idx]->m_pls.m_listTimeTask.front().powerVal;
		m_vecStrategy[idx]->m_pls.m_listTimeTask.push_back(te);
	}
	printlog(LOGE_ES_STRATEGY_BASE + m_link,
		"装载 ES_STRATEGY规约特征表 %s 成功 , LinkNo = %d ,相应配置记录数为%d！",m_pLink->protoFeature,m_link,ret);
	return true;
}

void CES_STRATEGY::clearYkInfo()
{	
	m_itCurYk = m_YkInfoMap.begin();
	for (; m_itCurYk!=m_YkInfoMap.end(); m_itCurYk++) {
		if(m_itCurYk->second)
			free(m_itCurYk->second);
	}
	m_YkInfoMap.clear();
}
void CES_STRATEGY::clearYtInfo()
{	
	m_itCurYt = m_YtInfoMap.begin();
	for (; m_itCurYt!=m_YtInfoMap.end(); m_itCurYt++) {
		if(m_itCurYt->second)
			free(m_itCurYt->second);
	}
	m_YtInfoMap.clear();
}
bool CES_STRATEGY::getYkInfo()
{	
	CRdbOp rdbop(CRdbOp::Direct);
	char sqlstr[80] = {0};
	sprintf(sqlstr,"select * from fdc_yk where f_linkno=%d order by f_ykno", m_link);

	CDataset ds;
	int retNum = rdbop.exec( sqlstr,ds);
	if ( retNum < 0 ) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"SQL failed:%s",sqlstr);
		return false;
	}
	if ( retNum == 0 ) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"SQL ret=0:%s",sqlstr);
		return false;
	}
	
	bool bRet;
	int mapKey;
	for(int i=0; i<retNum; i++) {
		MODBUS_YK_INFO *pYkInfo = (MODBUS_YK_INFO *)malloc(sizeof(MODBUS_YK_INFO));
		if(!pYkInfo) return false;

		mapKey = 1000*ds.field( i,"f_rtuno").value().toInt32();
		pYkInfo->ykNo = ds.field( i,"f_ykno").value().toInt32();
		mapKey += pYkInfo->ykNo;
		pYkInfo->funCode = ds.field( i,"f_funcode").value().toInt8();
		pYkInfo->dataType = ds.field( i,"f_datatype").value().toInt8();
		pYkInfo->addr = ds.field( i,"f_addr").value().toUInt16();
		pYkInfo->state = 0;

		//如果insert成功，返回插入的pair
		bRet=m_YkInfoMap.insert(make_pair(mapKey,pYkInfo)).second;
		if (!bRet) free(pYkInfo);
	}
	printlog(LOGE_ES_STRATEGY_BASE + m_link,"load Yk info from fdc_yk num=%d", retNum);
	return true;
}
bool CES_STRATEGY::getYtInfo()
{
	CRdbOp rdbop(CRdbOp::Direct);
	char sqlstr[80] = {0};
	sprintf(sqlstr,"select * from fdc_yt where f_linkno=%d order by f_rtuno,f_ytno", m_link);

	CDataset ds;
	int retNum = rdbop.exec( sqlstr,ds);
	if ( retNum < 0 ) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"SQL failed:%s",sqlstr);
		return false;
	}
	if ( retNum == 0 ) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"SQL ret=0:%s",sqlstr);
		return false;
	}

	bool bRet;
	int mapKey;
	for(int i=0; i<retNum; i++) {
		MODBUS_YT_INFO *pYtInfo = (MODBUS_YT_INFO *)malloc(sizeof(MODBUS_YT_INFO));
		mapKey = 1000*ds.field( i,"f_rtuno").value().toInt32();
		pYtInfo->ytNo = ds.field( i,"f_ytno").value().toInt32();
		mapKey += pYtInfo->ytNo;
		pYtInfo->funCode = ds.field( i,"f_funcode").value().toInt8();
		pYtInfo->dataType = ds.field( i,"f_datatype").value().toInt8();
		pYtInfo->addr = ds.field( i,"f_addr").value().toUInt16();
		pYtInfo->coefA = ds.field( i,"f_coefa").value().toDouble();
		pYtInfo->coefB = ds.field( i,"f_coefb").value().toDouble();
		pYtInfo->targValue = 0;
		
		//如果insert成功，返回插入的pair
		bRet=m_YtInfoMap.insert(make_pair(mapKey,pYtInfo)).second;
		if (!bRet) free(pYtInfo);
	}
	printlog(LOGE_ES_STRATEGY_BASE + m_link,"load Yt info from fdc_yt num=%d", retNum);
	return true;
}

void CES_STRATEGY::calc()
{
	//---- YC -----
	//0 总有功功率
	// wrYc(0, m_vecStrategy[0]->m_fPCSP);
	wrYc(0, m_vecStrategy[0]->m_fPCSP);


	//1 总无功功率
	wrYc(1, m_vecStrategy[0]->m_fPCSQ);
	
	//2 恒功率设置-回显
	wrYc(2, m_powerSet);

	//5 累计充/放电量
	float mc = 0;
	float mf = 0;
	for(int t = 0; t < m_pcs_num; t++) {
		mc += m_M_EC[t];
		mf += m_M_EF[t];
	}
	wrYc(5, mc);
	wrYc(6, mf);

	if(m_vecStrategy[0]->m_Ec > m_vecStrategy[0]->m_Ezc) {
		m_vecStrategy[0]->m_Edc = m_vecStrategy[0]->m_Ec - m_vecStrategy[0]->m_Ezc;
		wrYc(7, m_vecStrategy[0]->m_Edc);//今日充电量
	}
	if(m_vecStrategy[0]->m_Ef > m_vecStrategy[0]->m_Ezf) {
		m_vecStrategy[0]->m_Edf = m_vecStrategy[0]->m_Ef - m_vecStrategy[0]->m_Ezf;
		wrYc(8, m_vecStrategy[0]->m_Edf);//今日放电量
	}

	wrYc(9, m_vecStrategy[0]->m_SOC);

	//---- YX ----
	//0 恒功率放电
	bool sta = false;
	for(int t = 0; t < m_pcs_num; t++) {
		sta |= m_PCS_FD[t];
	}
	wrYx(0, sta);
	
	//1 恒功率充电
	if(sta) {
		wrYx(1, false);
	}
	else {
		sta = false;
		for(int t = 0; t < m_pcs_num; t++) {
			sta |= m_PCS_CD[t];
		}
		wrYx(1, sta);
	}

	//2 停机
	sta = true;
	for(int t = 0; t < m_pcs_num; t++) {
		sta &= m_PCS_HALT[t];
	}
	wrYx(2, sta);

	//3 故障
	sta = false;
	for(int t = 0; t < m_pcs_num; t++) {
		sta |= m_PCS_F[t];
	}
	wrYx(3, sta);

	//4 运行
	sta = false;
	for(int t = 0; t < m_pcs_num; t++) {
		sta |= m_PCS_ON[t];
	}
	wrYx(4, sta);

	//5 急停信号


	//6 消防告警
	//7 超温告警


	//11-20 温控通讯中断
	for(int t = 0; t < m_pcs_num; t++) {
		wrYx(11+t, m_TC_Offline[t]);
	}

}

bool CES_STRATEGY::Ctrl_YKYT()
{
	hUChar pCmdBuf[FDC_CTRL_LEN] = {0};
	int len = m_ctrlInf.get(m_pRoute->group, pCmdBuf, FDC_CTRL_LEN);
	if( len < 1) return false;
		
	ctrl_head head;
	memcpy(&head, pCmdBuf, sizeof(ctrl_head));
	
	//遥调
	if(head.type == CTRL_PRO_YT) {	
		ctrl_pro_setpoint yt;
		memset(&yt, 0, sizeof(yt));
		memcpy(&yt, (ctrl_pro_setpoint*)(pCmdBuf + sizeof(ctrl_head)), sizeof(ctrl_pro_setpoint));
		int no = yt.ctrlNo.hostValue();
		float fval = yt.floatValue.hostValue();
		printlog(LOGE_ES_STRATEGY_BASE + m_link, "收到遥调YT命令: no=%d, setval=%.2f", no, fval);

		FDC_YX_DATA *pYx = NULL;

		if(no == 0) {//总开关机命令
			//wrYc(YC_33, fval);
			//saveTodb(no, yx.val);
		}
		else if(no == 1) {//恒功率设定
			//wrYc(2, fval);
			m_powerSet = fval;
		}
		return true;
	}

	//遥控
	else if(head.type == CTRL_PRO_YK) {
		ctrl_pro_yk yk;
		memset(&yk, 0, sizeof(yk));
		memcpy(&yk, (ctrl_pro_yk*)(pCmdBuf + sizeof(ctrl_head)), sizeof(ctrl_pro_yk));
		int no = yk.ctrlNo.hostValue();

		printlog(LOGE_ES_STRATEGY_BASE + m_link, "收到遥遥控YK命令: no=%d, ctrlstate=%d", no, yk.ctrlState);
		return true;
	}

	return false;
}

void	CES_STRATEGY::CheckStatus()
{
	CheckLoadFlag();
	CheckBMSFlag();
	CheckPCSFlag();
	CheckIO();
	GetP();
	calc();

	int t = 0;
	int m = 0;
	for (int k = 0; k < m_pcs_num; k++, t++) {
		m = t;//模块下BMU起始序号

		if(m_IO_stop[t]) {
			m_needModStopRun[t] = true; 		//PCS停机标志 真
			m_needModStopUncharge[t] = true;	//PCS禁止放电标志 真
			m_needModStopCharge[t] = true;		//PCS禁止充电标志 真
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "急停按下 置PCS%d 0功率,关机", k+1);
			continue;
		}

		//模块下任意一簇故障 该模块停机
		if(m_BMS_Fault[m] || m_PCS_F[t]) {
			m_needModStopRun[t] = true;
			m_needModStopUncharge[t] = true;
			m_needModStopCharge[t] = true;
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "found 簇%d 故障(%hhu), pcs%d 故障(%hhu)"
				, m+1, m_BMS_Fault[m], t+1, m_PCS_F[t]);
			m_PCSMod_jcf[t] = 0;
			continue;
		}
		
		//模块下任意一簇通讯异常时 该模块停机
		if(m_fBMS_cuSOC[m] < -0.1f) {
			m_needModStopRun[t] = true; 		//PCS停机标志 真
			m_needModStopUncharge[t] = true;	//PCS禁止放电标志 真
			m_needModStopCharge[t] = true;		//PCS禁止充电标志 真
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "found 簇%d 通讯异常", m+1);
			m_PCSMod_jcf[t] = 0;
			continue;
		}

		//模块下任意一簇 同时禁充禁放 该模块停机
		if(m_BMS_JC[m] && m_BMS_JF[m]) {
			m_needModStopRun[t] = true; 		//PCS停机标志 真
			m_needModStopUncharge[t] = true;	//PCS禁止放电标志 真
			m_needModStopCharge[t] = true;		//PCS禁止充电标志 真
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "found 簇%d 禁充禁放", m+1);
			m_PCSMod_jcf[t] = 0;
			continue;
		}

		m_needModStopRun[t] = false;

		//模块下任意一簇 三级告警 该模块功率置零
		// if(m_BMS_RL3[m] || m_BMS_JH3[m] || m_BMS_UDH3[m] || m_BMS_TDH3[m] 
		// 	|| m_BMS_UH3[m] || m_BMS_CIH3[m] || m_BMS_ZUH3[m] || m_BMS_CTH3[m] || m_BMS_CTL3[m]
		// 	|| m_BMS_UL3[m] || m_BMS_FIH3[m] || m_BMS_FTH3[m] || m_BMS_FTL3[m] || m_BMS_ZUL3[m]) {
		// 	m_needModStopUncharge[t] = true;	//PCS禁止放电标志 真
		// 	m_needModStopCharge[t] = true;		//PCS禁止充电标志 真
		// 	printlog(LOGE_ES_STRATEGY_BASE + m_link, "found 簇%d 三级告警", m+1);
		// 	continue;
		// }

		//模块下任意一簇 二级告警 该模块功率置零
		if(m_BMS_UDH2[m] || m_BMS_TH2[m]) {
			m_needModStopUncharge[t] = true;	//PCS禁止放电标志 真
			m_needModStopCharge[t] = true;		//PCS禁止充电标志 真
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "found 簇%d 二级告警", m+1);
			continue;
		}

		m_needModStopUncharge[t] = false;
		m_needModStopCharge[t] = false;

		//模块下任意一簇 禁止充电
		if (m_BMS_JC[m]	|| m_BMS_UH2[m] || m_BMS_PUH2[m]) {
			m_needModStopCharge[t] = true;
			m_PCSMod_jcf[t] = MOD_CFSTA_C_FLAG;	//禁止充电标志
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "found 簇%d 禁充", m+1);
			continue;
		}

		//模块下任意一簇 禁止放电
		if (m_BMS_JF[t]	|| m_BMS_UL2[m] || m_BMS_PUL2[m]) {
			m_needModStopUncharge[t] = true;
			m_PCSMod_jcf[t] = MOD_CFSTA_F_FLAG;	//禁止放电标志
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "found 簇%d 禁放", m+1);
			continue;
		}

		//禁止充电标志未清除
		if(m_PCSMod_jcf[t] & MOD_CFSTA_C_FLAG) {
			m_needModStopCharge[t] = true;
			printlog(LOGE_ES_STRATEGY_RX + m_link, "簇%d 禁充标志未清除", t+1);
			continue;
		}

		//禁止放电标志未清除
		if(m_PCSMod_jcf[t] & MOD_CFSTA_F_FLAG) {
			m_needModStopUncharge[t] = true;
			printlog(LOGE_ES_STRATEGY_RX + m_link, "簇%d 禁放标志未清除", t+1);
			continue;
		}
		
		//降50%功率
		// if (m_BMS_RL1[m] || m_BMS_TH1[m] ) {
		// 	m_halfC[t] = true;
		// 	m_halfF[t] = true;
		// 	printlog(LOGE_ES_STRATEGY_BASE + m_link, "found 模块%d 降功率", t+1);
		// 	continue;
		// }
		//降50%功率(放电)
		// if ( m_BMS_UL1[m] || m_BMS_FIH1[m] ) {
		// 	m_halfC[t] = false;
		// 	m_halfF[t] = true;
		// 	printlog(LOGE_ES_STRATEGY_BASE + m_link, "found 模块%d 降放电功率", t+1);
		// 	continue;
		// }
		//降50%功率(充电)
		// if (m_BMS_UH1[m] || m_BMS_CIH1[m] ) {
		// 	m_halfC[t] = true;
		// 	m_halfF[t] = false;
		// 	printlog(LOGE_ES_STRATEGY_BASE + m_link, "found 模块%d 降充电功率", t+1);
		// 	continue;
		// }

		// m_halfC[t] = false;
		// m_halfF[t] = false;
	}
}

void	CES_STRATEGY::CheckLoadFlag()
{
	FDC_SYSTEM_INFO* pSystemInfo = m_commInf.systemInfo();
	if (pSystemInfo->loadParaFlag & 0x10000) {
		//收到web装载策略标志时,从文件重载策略
		pSystemInfo->loadParaFlag &= ~0x10000;
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"file装载策略库");

		bool before_flag[PCC_NUM];
		for(int i = 0; i < PCC_NUM; i++) {//记录装载前的状态
			before_flag[i] = m_vecStrategy[i]->m_pls.useflag;
		}

		// loadLocalEs();
		reloadConfig();

		for(int i = 0; i < PCC_NUM; i++) {//有变更且失能 则主动停机标志为真
			if(before_flag[i] != m_vecStrategy[i]->m_pls.useflag && m_vecStrategy[i]->m_pls.useflag == 0) {
				m_vecStrategy[i]->m_stopall = true;
			}
		}
	}
	else if(pSystemInfo->loadParaFlag & 0x20000)
	{
		//收到web设定切换当前策略远程就地执行类型
		pSystemInfo->loadParaFlag &= ~0x20000;
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"file切换远程就地执行方式");

		//todo 
		// 1 读配置文件
		// 2 清空当前策略m_vecStrategy[n] 中的内容
		// 3 根据当前配置文件中的类型切换m_strategyFactory
		// 4 根据m_strategyFactory 重新生成m_vecStrategy[n]
		reloadConfig();
	}

	else if (pSystemInfo->loadParaFlag & LOADPARA_STRATEGY) {
		//收到装载策略标志时,重载策略
		pSystemInfo->loadParaFlag &= ~LOADPARA_STRATEGY;
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"装载策略库");

		bool before_flag[PCC_NUM];
		for(int i = 0; i < PCC_NUM; i++) {//记录装载前的状态
			before_flag[i] = m_vecStrategy[i]->m_pls.useflag;
		}

		featureFromdb();

		for(int i = 0; i < PCC_NUM; i++) {//有变更且失能 则主动停机标志为真
			if(before_flag[i] != m_vecStrategy[i]->m_pls.useflag && m_vecStrategy[i]->m_pls.useflag == 0) {
				m_vecStrategy[i]->m_stopall = true;
			}
		}
	}
	//TODO:处理消息
}
bool	CES_STRATEGY::loadLocalEs()
{
	char * sysRoot = getenv("SYS_ROOT");
	if(sysRoot == NULL) {
		printlog(LOGE_ES_STRATEGY_BASE+m_link, "SYS_ROOT env failed");
		return false;
	}

	char fname[128] = {0};
	sprintf(fname, "%s/ini/strategy.json", sysRoot);
	
	FILE * pf = fopen(fname, "r");
	if(!pf) {
		printlog(LOGE_ES_STRATEGY_BASE+m_link, "fopen [%s] failed", fname);
		return false;
	}

	char * buf = (char *)malloc(1024);
	buf[1023] = 0;
	size_t sz = fread(buf, 1, 1023, pf);
	fclose(pf);
	if(sz < 1) {
		printlog(LOGE_ES_STRATEGY_BASE+m_link, "fread [%s] failed", fname);
		free(buf);
		return false;
	}

	CJsonObject obj;
	if(!obj.Parse(buf)) {
		printlog(LOGE_ES_STRATEGY_BASE+m_link, "json parse failed");
		free(buf);
		return false;
	}
	free(buf);

	SingleStrategy & ss = *m_vecStrategy[0];
	float temp = 0;
	int ival = 0;
	bool isRight = true;
	ES_TIMEEXEC te;

	if(obj.KeyExist("1")) {//需量控制
		CJsonObject & es = obj["1"];
		
		if(es.KeyExist("data")) {
			CJsonObject & data = es["data"];
			
			isRight = true;
			ES_STRATEGY_DEMAND::MonDemand fdemand;
			
			isRight = isRight && data.Get("demandValue", fdemand.demandVal);
			ss.m_demand.demandVal.push_back(fdemand);

			// isRight = isRight && data.Get("demandValue", ss.m_demand.demandVal);
			isRight = isRight && data.Get("overDemandPercent", ss.m_demand.upper);
			//isRight = isRight && data.Get("dischargePower", ss.m_demand.unchargeVal);

			if(isRight && es.Get("enable", ival)) {
				ss.m_demand.useflag = ival;
				ss.m_demand.upVal = ss.m_demand.demandVal.at(0).demandVal * ss.m_demand.upper * 0.01f;
				printlog(LOGE_ES_STRATEGY_BASE+m_link, "加载 1需量控制");
			}
		}		
	}

	if(obj.KeyExist("2")) {//后备电源
		CJsonObject & es = obj["2"];
		
		if(es.KeyExist("data")) {
			CJsonObject & data = es["data"];
			
			isRight = true;
			isRight = isRight && data.Get("batteryReservedCapacity", ss.m_bkpower.lowSOC);

			if(isRight && es.Get("enable", ival)) {
				ss.m_bkpower.useflag = ival;
				printlog(LOGE_ES_STRATEGY_BASE+m_link, "加载 2后备电源");
			}
		}
	}

	if(obj.KeyExist("3")) {//防逆流
		CJsonObject & es = obj["3"];
		
		if(es.KeyExist("data")) {
			CJsonObject & data = es["data"];
			
			isRight = true;
			isRight = isRight && data.Get("inversePowerMinPower", ss.m_repower.lowVal);

			if(isRight && es.Get("enable", ival)) {
				ss.m_repower.useflag = ival;
				printlog(LOGE_ES_STRATEGY_BASE+m_link, "加载 3防逆流");
			}
		}
	}

	if(obj.KeyExist("4")) {//移峰填谷
		CJsonObject & es = obj["4"];
		
		if(es.KeyExist("data")) {
			CJsonObject & data = es["data"];
			
			if(data.IsArray()) {
				isRight = true;
				ss.m_pls.m_listTimeTask.clear();
				
				int cnt = data.GetArraySize();
				for(int i = 0; i < cnt; i++) {
					isRight = isRight && data[i].Get("val", te.powerVal);
					isRight = isRight && data[i].Get("time", te.min);
					ss.m_pls.m_listTimeTask.push_back(te);
				}				

				if(isRight && es.Get("enable", ival)) {
					ss.m_pls.useflag = ival;
					ss.m_firstRunPls = true;
					printlog(LOGE_ES_STRATEGY_BASE+m_link, "加载 4移峰填谷");
				}
			}
		}
	}
	return true;
}

void	CES_STRATEGY::readStaticConfig()
{
	//读取配置文件内预设的PCS个数
	int n = 0;
	float f = 0;
	IniFile	cfgini("ini/strategy.ini");
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "cloud_rtuno", &n)==TRUE && n>0) {
		g_CMD_RTU_NO[0] = n;
	}

	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "pcs_num", &n)==TRUE && n>0) {
		m_pcs_num = n;
	}

	f = 0;
	if(cfgini.ReadKeyFloatValue("ES_STRATEGY", "pcs_power", &f)==TRUE && f>0.001f) {
		m_pcs_power = f;
	}

	f = 0;
	if(cfgini.ReadKeyFloatValue("ES_STRATEGY", "pcs_half", &f)==TRUE && f>0.001f) {
		m_pcs_half = f;
	}
	printlog(LOGE_ES_STRATEGY_BASE + m_link,"ES_STRATEGY, rtuno=%d pcsnum=%d pcs_power=%.1f pcs_half=%.1f"
		, g_CMD_RTU_NO[0], m_pcs_num, m_pcs_power, m_pcs_half);

	f = 0;
	if(cfgini.ReadKeyFloatValue("ES_STRATEGY", "soc_min", &f)==TRUE && f>0.001f) {
		m_soc_min = f;
	}

	f = 0;
	if(cfgini.ReadKeyFloatValue("ES_STRATEGY", "soc_max", &f)==TRUE && f>0.001f) {
		m_soc_max = f;
	}
	printlog(LOGE_ES_STRATEGY_BASE + m_link,"ES_STRATEGY, soc_min=%.1f soc_max=%.1f"
		, m_soc_min, m_soc_max);

	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "anti_rtuno", &n)==TRUE && n>0) {
		m_anti_rtuno = n;
	}

	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "anti_power_yc", &n)==TRUE && n>0) {
		m_anti_power_yc = n;
	}

	f = 0;
	if(cfgini.ReadKeyFloatValue("ES_STRATEGY", "res_power", &f)==TRUE && f>0.001f) {
		m_res_power = f;
	}

	printlog(LOGE_ES_STRATEGY_BASE + m_link,"ES_STRATEGY, anti_rtuno=%d anti_power_yc=%d res_power=%.1f"
		, m_anti_rtuno, m_anti_power_yc, m_res_power);

	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "auto_fan", &n)==TRUE) {
		m_auto_fan = n;
	}

	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "third_enable", &n)==TRUE) {
		m_third_enable = n;
	}

	printlog(LOGE_ES_STRATEGY_BASE + m_link,"ES_STRATEGY, auto_fan=%d third_enable=%d"
		, m_auto_fan, m_third_enable);

	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "meter_rtuno", &n)==TRUE) {
		m_meter_rtuno = n;
	}

	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "meter_ez_yc", &n)==TRUE) {
		m_meter_ez_yc = n;
	}

	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "meter_ef_yc", &n)==TRUE) {
		m_meter_ef_yc = n;
	}
	printlog(LOGE_ES_STRATEGY_BASE + m_link,"ES_STRATEGY, meter_rtuno=%d meter_ez_yc=%d meter_ef_yc=%d"
		, m_meter_rtuno, m_meter_ez_yc, m_meter_ef_yc);
	
	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "self_io", &n)==TRUE) {
		m_self_io = n;
		if(m_self_io != 1) {//切换为io模块1
			g_IO_RTU_NO[0] = 13;
			g_IO_RUN_YXNO[0] = 9;
			g_IO_F_YXNO[0] = 10;
			g_IO_RUN_YKNO[0] = 1;
			g_IO_F_YKNO[0] = 2;
		}
	}
	printlog(LOGE_ES_STRATEGY_BASE + m_link,"ES_STRATEGY, self_io=%d"
		, m_self_io);

	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "mkls_enable", &n)==TRUE) {
		m_mkls_enable = n;
	}
	printlog(LOGE_ES_STRATEGY_BASE + m_link,"ES_STRATEGY, mkls_enable=%d"
		, m_mkls_enable);

	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "io_mode", &n)==TRUE) {
		m_io_mode = n;
	}
	
	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "soc_adj", &n)==TRUE) {
		m_soc_adj = n;
	}

	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "palloc_mode", &n)==TRUE) {
		m_palloc_mode = n;
	}
	printlog(LOGE_ES_STRATEGY_BASE + m_link,"ES_STRATEGY, io_mode=%d, soc_adj=%d, palloc_mode=%d"
		, m_io_mode, m_soc_adj, m_palloc_mode);
	
	f = 0;
	if(cfgini.ReadKeyFloatValue("ES_STRATEGY", "u_min", &f)==TRUE && f>0.001f) {
		m_u_min = f;
	}

	f = 0;
	if(cfgini.ReadKeyFloatValue("ES_STRATEGY", "u_max", &f)==TRUE && f>0.001f) {
		m_u_max = f;
	}

	n = 0;
	if(cfgini.ReadKeyIntValue("ES_STRATEGY", "u_enable", &n)==TRUE) {
		m_u_enable = n;
	}
	printlog(LOGE_ES_STRATEGY_BASE + m_link,"ES_STRATEGY, u_min=%.1f u_max=%.1f u_enable=%d"
		, m_u_min, m_u_max, m_u_enable);
}
bool 	CES_STRATEGY::readZeroEcf()
{
	char * sysRoot = getenv("SYS_ROOT");
	if(sysRoot == NULL) {
		printlog(LOGE_ES_STRATEGY_BASE+m_link, "SYS_ROOT env failed");
		return false;
	}

	char fname[128] = {0};
	sprintf(fname, "%s/ini/ecf.json", sysRoot);
	
	FILE * pf = fopen(fname, "r");
	if(!pf) {
		printlog(LOGE_ES_STRATEGY_BASE+m_link, "fopen [%s] failed", fname);
		return false;
	}

	char * buf = (char *)malloc(256);
	buf[255] = 0;
	size_t sz = fread(buf, 1, 255, pf);
	fclose(pf);
	if(sz < 1) {
		printlog(LOGE_ES_STRATEGY_BASE+m_link, "fread [%s] failed", fname);
		free(buf);
		return false;
	}

	CJsonObject obj;
	if(!obj.Parse(buf)) {
		printlog(LOGE_ES_STRATEGY_BASE+m_link, "json parse failed");
		free(buf);
		return false;
	}
	free(buf);

	SingleStrategy & ss = *m_vecStrategy[0];
	float temp = 0;
	int ival = 0;
	bool isRight = true;

	//零点时刻的累计充/放电量
	obj.Get("Ezc", temp) && (ss.m_Ezc = temp);
	obj.Get("Ezf", temp) && (ss.m_Ezf = temp);
	printlog(LOGE_ES_STRATEGY_BASE+m_link, "Ezc=%.1f, Ezf=%.1f");
}
bool 	CES_STRATEGY::saveZeroEcf()
{
	char * sysRoot = getenv("SYS_ROOT");
	if(sysRoot == NULL) {
		printlog(LOGE_ES_STRATEGY_BASE+m_link, "SYS_ROOT env failed");
		return false;
	}

	char fname[128] = {0};
	sprintf(fname, "%s/ini/ecf.json", sysRoot);
	
	FILE * pf = fopen(fname, "w+");
	if(!pf) {
		printlog(LOGE_ES_STRATEGY_BASE+m_link, "fopen [%s] failed", fname);
		return false;
	}

	SingleStrategy & ss = *m_vecStrategy[0];
	CJsonObject obj;
	obj.Add("Ezc", ss.m_Ec);
	obj.Add("Ezf", ss.m_Ef);

	//当前电量作为零点时刻电量
	ss.m_Ezc = ss.m_Ec;
	ss.m_Ezf = ss.m_Ef;

	string text = obj.ToString();
	size_t sz = fwrite(text.c_str(), 1, text.length(), pf);
	fclose(pf);
	if(sz < 1) {
		printlog(LOGE_ES_STRATEGY_BASE+m_link, "fwrite [%s] failed", fname);
		return false;
	}
	return true;
}
//检查BMS充放电
bool	CES_STRATEGY::CheckBMSFlag()
{	
	GetBMS_SOC();
	//GetBMS_State();
	return true;	
}
bool 	CES_STRATEGY::GetBMS_SOC()
{
	//取各BMS簇的实时值SOC
	m_vecStrategy[0]->m_SOC = 0;
	int t = 0;
	for (int i = 0; i < m_pcs_num; i++, t++) {
		if(getYc(g_BMS_RTU_NO[t], g_BMS_SOC_YCNO[t], m_fBMS_cuSOC[t], t, "BMS SOC")) {
			m_fBMS_cuSOC[t] *= 0.1f;//系数0.1
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "簇%d SOC=%.1f", t+1, m_fBMS_cuSOC[t]);
			m_vecStrategy[0]->m_SOC += m_fBMS_cuSOC[t];
		}
		else {
			m_fBMS_cuSOC[t] = -1;
			continue;
		}

		getYc(g_BMS_RTU_NO[t], g_BMS_PF_YCNO[t], m_fBMS_cuPF[t], t, "BMS PF");
		getYc(g_BMS_RTU_NO[t], g_BMS_PC_YCNO[t], m_fBMS_cuPC[t], t, "BMS PC");
		getYc(g_BMS_RTU_NO[t], g_BMS_UH_YCNO[t], m_fBMS_cuUH[t], t, "BMS UH");
		getYc(g_BMS_RTU_NO[t], g_BMS_UL_YCNO[t], m_fBMS_cuUL[t], t, "BMS UL");
		if(getYc(g_BMS_RTU_NO[t], g_BMS_TH_YCNO[t], m_fBMS_cuTH[t], t, "fBMS TH")) {
			m_fBMS_cuTH[t] = m_fBMS_cuTH[t] * 0.1f - 40.0f;
		}
		if(getYc(g_BMS_RTU_NO[t], g_BMS_TL_YCNO[t], m_fBMS_cuTL[t], t, "fBMS TL")) {
			m_fBMS_cuTL[t] = m_fBMS_cuTL[t] * 0.1f - 40.0f;
		}

		//禁止充放
		getYx(g_BMS_RTU_NO[t], g_BMS_F_YXNO[t], m_BMS_Fault[t], t, "BMS F");//故障停机
		//getYx(g_BMS_RTU_NO[t], g_BMS_RL2_YXNO[t], m_BMS_RL2[t], t, "BMS RL2");//绝缘低2
		getYx(g_BMS_RTU_NO[t], g_BMS_TH2_YXNO[t], m_BMS_TH2[t], t, "BMS TH2");//单体温度高2
		getYx(g_BMS_RTU_NO[t], g_BMS_UDH2_YXNO[t], m_BMS_UDH2[t], t, "BMS UDH2");//单体压差大2
		//getYx(g_BMS_RTU_NO[t], g_BMS_TDH2_YXNO[t], m_BMS_TDH2[t], t, "BMS TDH2");//温差大2
		
		// getYx(g_BMS_RTU_NO[t], g_BMS_RL3_YXNO[t], m_BMS_RL3[t], t, "BMS RL3");//绝缘低3
		// getYx(g_BMS_RTU_NO[t], g_BMS_JH3_YXNO[t], m_BMS_JH3[t], t, "BMS JH3");//极柱温度高3
		// getYx(g_BMS_RTU_NO[t], g_BMS_UDH3_YXNO[t], m_BMS_UDH3[t], t, "BMS UDH3");//单体压差大3
		// getYx(g_BMS_RTU_NO[t], g_BMS_TDH3_YXNO[t], m_BMS_TDH3[t], t, "BMS TDH3");//温差大3
		// getYx(g_BMS_RTU_NO[t], g_BMS_UH3_YXNO[t], m_BMS_UH3[t], t, "BMS UH3");//单体电压高3
		// getYx(g_BMS_RTU_NO[t], g_BMS_CIH3_YXNO[t], m_BMS_CIH3[t], t, "BMS CIH3");//充电电流大3
		// getYx(g_BMS_RTU_NO[t], g_BMS_ZUH3_YXNO[t], m_BMS_ZUH3[t], t, "BMS ZUH3");//总压高3
		// getYx(g_BMS_RTU_NO[t], g_BMS_CTH3_YXNO[t], m_BMS_CTH3[t], t, "BMS CTH3");//充电温度高3
		// getYx(g_BMS_RTU_NO[t], g_BMS_CTL3_YXNO[t], m_BMS_CTL3[t], t, "BMS CTL3");//充电温度低3
		// getYx(g_BMS_RTU_NO[t], g_BMS_UL3_YXNO[t], m_BMS_UL3[t], t, "BMS UL3");//单体电压低3
		// getYx(g_BMS_RTU_NO[t], g_BMS_FIH3_YXNO[t], m_BMS_FIH3[t], t, "BMS FIH3");//放电电流大3
		// getYx(g_BMS_RTU_NO[t], g_BMS_FTH3_YXNO[t], m_BMS_FTH3[t], t, "BMS FTH3");//放电温度高3
		// getYx(g_BMS_RTU_NO[t], g_BMS_FTL3_YXNO[t], m_BMS_FTL3[t], t, "BMS FTL3");//放电温度低3
		// getYx(g_BMS_RTU_NO[t], g_BMS_ZUL3_YXNO[t], m_BMS_ZUL3[t], t, "BMS ZUL3");//总压低3
		
		//禁止充电
		getYx(g_BMS_RTU_NO[t], g_BMS_JC_YXNO[t], m_BMS_JC[t], t, "BMS JC");//禁充
		getYx(g_BMS_RTU_NO[t], g_BMS_UH2_YXNO[t], m_BMS_UH2[t], t, "BMS UH2");//单体电压高2
		getYx(g_BMS_RTU_NO[t], g_BMS_PUH2_YXNO[t], m_BMS_PUH2[t], t, "BMS PUH2");//模组电压高2
		// getYx(g_BMS_RTU_NO[t], g_BMS_CIH2_YXNO[t], m_BMS_CIH2[t], t, "BMS CIH2");//充电电流大2
		// getYx(g_BMS_RTU_NO[t], g_BMS_ZUH2_YXNO[t], m_BMS_ZUH2[t], t, "BMS ZUH2");//总压高2
		// getYx(g_BMS_RTU_NO[t], g_BMS_CTH2_YXNO[t], m_BMS_CTH2[t], t, "BMS CTH2");//充电温度高2
		// getYx(g_BMS_RTU_NO[t], g_BMS_CTL2_YXNO[t], m_BMS_CTL2[t], t, "BMS CTL2");//充电温度低2

		//禁止放电
		getYx(g_BMS_RTU_NO[t], g_BMS_JF_YXNO[t], m_BMS_JF[t], t, "BMS JF");//禁放
		getYx(g_BMS_RTU_NO[t], g_BMS_UL2_YXNO[t], m_BMS_UL2[t], t, "BMS UL2");//单体电压低2
		getYx(g_BMS_RTU_NO[t], g_BMS_PUL2_YXNO[t], m_BMS_PUL2[t], t, "BMS PUL2");//模组电压低2
		// getYx(g_BMS_RTU_NO[t], g_BMS_FIH2_YXNO[t], m_BMS_FIH2[t], t, "BMS FIH2");//放电电流大2
		// getYx(g_BMS_RTU_NO[t], g_BMS_TH2_YXNO[t], m_BMS_TH2[t], t, "BMS TH2");//单体温度高2
		// getYx(g_BMS_RTU_NO[t], g_BMS_ZUL2_YXNO[t], m_BMS_ZUL2[t], t, "BMS ZUL2");//总压低2

		getYx(g_BMS_RTU_NO[t], g_BMS_FAN_YXNO[t], m_BMS_Fan[t], t, "BMS Fan");//风扇状态
	}

	m_vecStrategy[0]->m_SOC /= m_pcs_num;
	
	return true;
}
bool	CES_STRATEGY::GetBMS_State()
{	
	return true;
}

//检测PCS状态
void	CES_STRATEGY::CheckPCSFlag()
{
	GetPCS_Rset();
	GetPCS_MOD_State();
}
bool 	CES_STRATEGY::GetPCS_Rset()
{
	FDC_YC_DATA * pData = NULL;
	int t = 0;
	for (int i = 0; i < m_pcs_num; i++, t++) {
		//产品运行模式
		pData = m_dataInf.ycDataMainRoute(g_PCS_RTU_NO[t], g_PCS_R_YCNO[t]);
		if (isValidYc(pData, t, "PCS R")) {
			m_PCSMod_R[t] = pData->val.hostValue();
		}
		//PQ工作模式设置
		pData = m_dataInf.ycDataMainRoute(g_PCS_RTU_NO[t], g_PCS_PQM_YCNO[t]);
		if (isValidYc(pData, t, "PCS pqm")) {
			m_PCSMod_pqm[t] = pData->val.hostValue();
		}
		//无功功率设置
		pData = m_dataInf.ycDataMainRoute(g_PCS_RTU_NO[t], g_PCS_HQ_YCNO[t]);
		if (isValidYc(pData, t, "PCS HQ")) {
			m_PCSMod_HQ[t] = pData->val.hostValue();
			m_PCS_Offline[t] = false;
		}
		else {
			m_PCS_Offline[t] = true;
		}
	}
	
	return true;
}
bool	CES_STRATEGY::GetPCS_MOD_State()
{
	//取各PCS下各模块 运行状态
	int t = 0;
	for (int i = 0; i < m_pcs_num; i++, t++) {
		getYx(g_PCS_RTU_NO[t], g_PCS_F_YXNO[t], m_PCS_F[t], t, "PCS F");//故障
		getYx(g_PCS_RTU_NO[t], g_PCS_ON_YXNO[t], m_PCS_ON[t], t, "PCS ON");//运行
		getYx(g_PCS_RTU_NO[t], g_PCS_HALT_YXNO[t], m_PCS_HALT[t], t, "PCS HALT");//停机
		getYx(g_PCS_RTU_NO[t], g_PCS_STDY_YXNO[t], m_PCS_STDY[t], t, "PCS STDY");//待机
		getYx(g_PCS_RTU_NO[t], g_PCS_CD_YXNO[t], m_PCS_CD[t], t, "PCS CD");//充电
		getYx(g_PCS_RTU_NO[t], g_PCS_FD_YXNO[t], m_PCS_FD[t], t, "PCS FD");//放电

		getYc(g_M_RTU_NO[t], g_M_EC_YCNO[t], m_M_EC[t], t, "M EC");//正向总电能(充电)
		getYc(g_M_RTU_NO[t], g_M_EF_YCNO[t], m_M_EF[t], t, "M EF");//反向总电能(放电)
	}
	
	return true;	
}
bool	CES_STRATEGY::CheckIO()
{
	bool st = false;
	float fval = 0;
	for(int t = 0; t < m_pcs_num; t++) {
		if(getYx(g_IO_RTU_NO[t], g_IO_HOT_YXNO[t], st, t, "IO hot")) {//温感报警
			m_IO_hot[t] = st;
		}
		if(getYx(g_IO_RTU_NO[t], g_IO_SMOKE_YXNO[t], st, t, "IO smoke")) {//烟感报警
			m_IO_smoke[t] = st;
		}
		if(getYx(g_IO_RTU_NO[t], g_IO_STOP_YXNO[t], st, t, "IO stop")) {//急停信号
			m_IO_stop[t] = st;
		}

		if(getYx(g_IO_RTU_NO[t], g_IO_RUN_YXNO[t], st, t, "IO run")) {//运行
			m_IO_run[t] = st;
		}
		if(getYx(g_IO_RTU_NO[t], g_IO_F_YXNO[t], st, t, "IO F")) {//故障
			m_IO_F[t] = st;
		}

		//温控
		if(getYx(g_TC_RTU_NO[t], g_TC_YX_YXNO[t], st, t, "TC YX")) {
			m_TC_Offline[t] = false;
		}
		else {
			m_TC_Offline[t] = true;//中断
		}
		//TCL空调运行状态
		if(m_io_mode == 2 && getYx(g_TC_RTU_NO[t], g_TC_YX_YXNO[t]+5, st, t, "TC run")) {//运行
			m_TC_run[t] = true;
		}

		if(m_mkls_enable && !m_TC_Offline[t]) {
			if(getYc(g_TC_RTU_NO[t], g_TC_TSH_YCNO[t], m_TC_TSH[t], t, "TC TSH")) {//电芯最高温度
				m_TC_TSH[t] -= 40.0f;
			}
			if(getYc(g_TC_RTU_NO[t], g_TC_TSL_YCNO[t], m_TC_TSL[t], t, "TC TSL")) {//电芯最低温度
				m_TC_TSL[t] -= 40.0f;
			}
			getYc(g_TC_RTU_NO[t], g_TC_SCF_YCNO[t], fval, t, "TC SCF");//充放电状态
			m_TC_SCF[t] = fval;
		}
	}

	//DIO自身
	for(int t = 0; t < 16; t++) {
		if(getYx(g_DIO_RTU_NO, g_DIO_DI_YXNO[t], st, t, "DIO DI")) m_DIO_DI[t] = st;
	}
	for(int t = 0; t < 5; t++) {
		if(getYx(g_DIO_RTU_NO, g_DIO_DO_YXNO[t], st, t, "DIO DO")) m_DIO_DO[t] = st;
	}
	
	return true;
}

//取有功功率 失败false 成功true
bool	CES_STRATEGY::GetP()
{
	//用户进线总有功遥测
	FDC_YC_DATA *pData = m_dataInf.ycDataMainRoute(m_anti_rtuno, m_anti_power_yc);
	if (!isValidYc(pData, 0, "USER P1")) {
		m_vecStrategy[0]->m_getPflag = false;
		m_vecStrategy[0]->m_fTotalP = 0;
	}
	else {
		m_vecStrategy[0]->m_getPflag  = true;
		m_vecStrategy[0]->m_fTotalP = pData->val.hostValue();
	}

	//并网点计量正向/反向总电量
	pData = m_dataInf.ycDataMainRoute(m_meter_rtuno, m_meter_ez_yc);
	if (isValidYc(pData, 0, "Meter ez")) {
		m_vecStrategy[0]->m_Ec = pData->val.hostValue();
	}
	pData = m_dataInf.ycDataMainRoute(m_meter_rtuno, m_meter_ef_yc);
	if (isValidYc(pData, 0, "Meter ef")) {
		m_vecStrategy[0]->m_Ef = pData->val.hostValue();
	}
	
	int t = 0;
	for (int i = 0; i < m_pcs_num; i++, t++) {
		pData = m_dataInf.ycDataMainRoute(g_PCS_RTU_NO[t], g_PCS_FHP_YCNO[t]);
		if (isValidYc(pData, t, "PCS FHP")) {
			m_fPCSModSetFHP[t] = pData->val.hostValue() * 0.1f;
		}

		pData = m_dataInf.ycDataMainRoute(g_PCS_RTU_NO[t], g_PCS_CHP_YCNO[t]);
		if (isValidYc(pData, t, "PCS CHP")) {
			m_fPCSModSetCHP[t] = pData->val.hostValue() * 0.1f;
		}

		pData = m_dataInf.ycDataMainRoute(g_PCS_RTU_NO[t], g_PCS_P_YCNO[t]);
		if (isValidYc(pData, t, "PCS P")) {
			m_fPCS_P[t] = pData->val.hostValue() * 0.1f;
		}

		pData = m_dataInf.ycDataMainRoute(g_PCS_RTU_NO[t], g_PCS_Q_YCNO[t]);
		if (isValidYc(pData, t, "PCS Q")) {
			m_fPCS_Q[t] = pData->val.hostValue() * 0.1f;
		}
	}

	//计算PCS各模块 恒功率模式功率设定值 总和(+放电/-充电)
	t = 0;
	int k_begin = 0;
	int k_end = 0;
	for(int n = 0; n < PCC_NUM; n++) {
		m_vecStrategy[n]->m_fPCSSetCHP = 0; //清零
		m_vecStrategy[n]->m_fPCSSetFHP = 0; //清零
		m_vecStrategy[n]->m_fPCSP = 0;
		m_vecStrategy[n]->m_fPCSQ = 0;

		for(int i = 0; i < m_pcs_num; i++, t++) {
			m_vecStrategy[n]->m_fPCSSetFHP += m_fPCSModSetFHP[t];
			m_vecStrategy[n]->m_fPCSSetCHP += m_fPCSModSetCHP[t];
			m_vecStrategy[n]->m_fPCSP += m_fPCS_P[t];
			m_vecStrategy[n]->m_fPCSQ += m_fPCS_Q[t];
		}
	}

	for(int i=0; i<PCC_NUM; i++) {
		//用户总有功=进线仪表总有功-储能功率
		if(m_vecStrategy[i]->m_fTotalP > 0.1f)
			m_vecStrategy[i]->m_fUserP = m_vecStrategy[i]->m_fTotalP + m_vecStrategy[i]->m_fPCSP;
		else
			m_vecStrategy[i]->m_fUserP = 0.0f;

		//计算三次平均功率(防止跳变引起的误差)
		m_vecStrategy[i]->m_fAvgUserP = m_vecStrategy[i]->calcPav(m_vecStrategy[i]->m_fUserP);

		printlog(LOGE_ES_STRATEGY_RX + m_link
			, "Get P 装置%d 总:%.1f,储能:%.1f,用户负载:%.1f,三次平均负载:%.1f"
			, i+1, m_vecStrategy[i]->m_fTotalP, m_vecStrategy[i]->m_fPCSP
			, m_vecStrategy[i]->m_fUserP, m_vecStrategy[i]->m_fAvgUserP);
	}
	return true;
}
//============================================================================
bool CES_STRATEGY::execNorm()
{	
	if(m_soc_adj == 1) {
		for (int k = 0; k < m_pcs_num; k++) {
			if(((m_u_enable == 1 && m_fBMS_cuUH[k] > m_u_max) || (m_BMS_UH2[k] && !m_BMS_UL2[k]))
				 && m_fBMS_cuSOC[k]>0.01f && m_fBMS_cuSOC[k]<99.0f) {
				if(m_loopCnt[k] > 0) {
					m_loopCnt[k]--;
					continue;
				}
				printlog(LOGE_ES_STRATEGY_BASE + m_link, "簇%d Soc校准至100", k+1);
				setYt(g_BMS_RTU_NO[k], 2, 100.0f);
				m_loopCnt[k]=2;
				continue;
			}
			if(((m_u_enable == 1 && m_fBMS_cuUL[k] < m_u_min) || (!m_BMS_UH2[k] && m_BMS_UL2[k]))
				 && m_fBMS_cuSOC[k]>1.0f) {
				if(m_loopCnt[k] > 0) {
					m_loopCnt[k]--;
					continue;
				}
				printlog(LOGE_ES_STRATEGY_BASE + m_link, "簇%d Soc校准至0", k+1);
				setYt(g_BMS_RTU_NO[k], 2, 0.0f);
				m_loopCnt[k]=2;
				continue;
			}
		}
	}
	switch(m_io_mode) {
	case 0:
		IOMode0();
		break;

	case 1:
		IOMode1();
		break;

	case 2:
		IOMode2();
		break;
	
	case 3:
		IOMode3();
		break;
	
	case 4:
		IOMode4();
		break;

	default:
		break;
	}

	return doCond1();
}
bool CES_STRATEGY::doCond1()
{
	bool bDone = false;
	int t = 0;
	for (int k = 0; k < m_pcs_num; k++, t++) {
		if( (m_needModStopRun[t] && (m_PCS_ON[t] || m_PCS_STDY[t])) ) {
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "doCond 1 成立 置PCS%d 0功率,关机", k+1);
			clearMod(t, t);
			closeMod(t, t);
			bDone = true;
			continue;
		}
		
		if( (m_needModStopCharge[t] && m_PCS_ON[t] && m_PCS_CD[t]) 
			|| (m_needModStopUncharge[t] && m_PCS_ON[t] && m_PCS_FD[t]) ) {
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "doCond 2 成立 置PCS%d 0功率", k+1);
			clearMod(t, t);
			closeMod(t, t);
			bDone = true;
			continue;
		}
	}
	
	if(bDone)
		ACE_OS::sleep(1);
	return bDone;
}
bool CES_STRATEGY::IOMode0()
{
	int t = 0;
	bool st = false;
	for (int k = 0; k < m_pcs_num; k++, t++) {
		//1.PCS断交流 -> 都灭
		if(m_PCS_Offline[t]) {
			if(m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 0);
			if(m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 0);
			continue;
		}
		
		//2.PCS或BMS故障 -> 运行灭,故障亮
		if(m_PCS_F[t] || m_BMS_Fault[t]) {
			if(m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 0);
			if(!m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 1);
			continue;
		}

		//3.PCS运行 -> 运行亮,故障灭
		if(m_PCS_ON[t]) {
			if(!m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 1);
			if(m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 0);
			continue;
		}

		//4.PCS未运行 -> 都灭
		if(!m_PCS_ON[t]) {
			if(m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 0);
			if(m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 0);
			continue;
		}
	}

	return true;
}
bool CES_STRATEGY::IOMode1()
{
	// 说明：
	// 1.运行指示灯：储能系统正常运行时点亮，故障时熄灭。
	// 2.故障指示灯：储能系统故障时点亮，正常运行时熄灭。
	// 3.DI05\DI06\DI07为消防气溶胶喷洒信号，气溶胶喷洒后会闭合。
	// 4.DI08为升压舱变压器超温报警。
	// 5.DI09为升压舱烟温感报警信号。
	// 6.3DO\4DO\5DO为消防扩展信号，用于汇流柜开关脱扣，启动蜂鸣器报警
	
	// 系统启动后：
	// 检测DI09 DI08 DI05 DI06 DI07
	// 当任意输入为1时，系统故障停机，2DO 故障指示灯打开

	// 运行过程中：
	// 当DI09为1时，延时5秒仍保持故障信号，系统故障停机，2DO故障指示灯打开。
	// 当DI08为1时，系统故障停机，2DO故障指示灯打开。
	// 当DI05为1时，#1消防扩展3DO打开，2DO故障指示灯打开。
	// 当DI06为1时，#2消防扩展4DO打开。
	// 当DI07为1时，#3消防扩展5DO打开。
	
	int t = 0;
	bool bothOFF = true;
	bool bFON = false;
	bool bRON = false;
	
	for (int k = 0; k < m_pcs_num; k++, t++) {
		//1.PCS断交流 -> 都灭
		bothOFF = (bothOFF && m_PCS_Offline[t]);
		
		//2.PCS或BMS故障 -> 运行灭,故障亮
		bFON = (bFON || m_PCS_F[t] || m_BMS_Fault[t]);

		//3.PCS运行 -> 运行亮,故障灭
		bRON = (bRON | m_PCS_ON[t]);
	}

	if(m_DIO_DI[4]) {
		if(!m_DIO_DO[2]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[2], 1);
		printlog(LOGE_ES_STRATEGY_BASE + m_link, "IO mode=1 消防1动作");
	}
	else {
		if(m_DIO_DO[2]) {
			setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[2], 0);
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "IO mode=1 消防1复归");
		}
	}

	if(m_DIO_DI[5]) {
		if(!m_DIO_DO[3]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[3], 1);
		printlog(LOGE_ES_STRATEGY_BASE + m_link, "IO mode=1 消防2动作");
	}
	else {
		if(m_DIO_DO[3]) {
			setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[3], 0);
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "IO mode=1 消防2复归");
		}
	}

	if(m_DIO_DI[6]) {
		if(!m_DIO_DO[4]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[4], 1);
		printlog(LOGE_ES_STRATEGY_BASE + m_link, "IO mode=1 消防3动作");
	}
	else {
		if(m_DIO_DO[4]) {
			setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[4], 0);
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "IO mode=1 消防3复归");
		}
	}

	if(m_DIO_DI[4] || m_DIO_DI[5] || m_DIO_DI[6] || m_DIO_DI[7] || m_DIO_DI[8]) {
		bothOFF = false;
		bFON = true;
		bRON = false;
		printlog(LOGE_ES_STRATEGY_BASE + m_link, "IO mode=1 有DI告警信号");
	}

	printlog(LOGE_ES_STRATEGY_BASE + m_link, "IO mode=1 bothOFF=%hhu, bFON=%hhu, bRON=%hhu"
		, bothOFF, bFON, bRON);

	t = 0;
	if(bothOFF) {
		if(m_IO_run[t]) setYk(g_DIO_RTU_NO, g_IO_RUN_YKNO[t], 0);
		if(m_IO_F[t]) setYk(g_DIO_RTU_NO, g_IO_F_YKNO[t], 0);
	}
	else if(bFON) {
		if(m_IO_run[t]) setYk(g_DIO_RTU_NO, g_IO_RUN_YKNO[t], 0);
		if(!m_IO_F[t]) setYk(g_DIO_RTU_NO, g_IO_F_YKNO[t], 1);
	}
	else if(bRON) {
		if(!m_IO_run[t]) setYk(g_DIO_RTU_NO, g_IO_RUN_YKNO[t], 1);
		if(m_IO_F[t]) setYk(g_DIO_RTU_NO, g_IO_F_YKNO[t], 0);
	}
	else {
		if(m_IO_run[t]) setYk(g_DIO_RTU_NO, g_IO_RUN_YKNO[t], 0);
		if(m_IO_F[t]) setYk(g_DIO_RTU_NO, g_IO_F_YKNO[t], 0);
	}
	return true;
}
bool CES_STRATEGY::IOMode2()
{
	int t = 0;
	bool st = false;
	for (int k = 0; k < m_pcs_num; k++, t++) 
	{
		//6.消防灭火装置热启动后反馈信号至IO模块DI3接口，将PCS跳闸，停止空调系统
		if(m_IO_stop[t]) 
		{
			//PCS跳闸
			if(m_PCS_ON[t]) setYt(g_PCS_RTU_NO[t], g_PCS_SW_YTNO[t], 0);
			//空调停机
			if(m_TC_run[t]) setYk(g_TC_RTU_NO[t], g_TC_SW_YKNO[t], 0);
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "IO mode=2 消防灭火装置热启动，PCS跳闸，空调停机");
		}

		//5.感温感烟同时报警 且报警持续10s，将PCS跳闸，停止空调系统，延时后消防装置开始喷洒
		if(g_IO_HOT_YXNO[t] && g_IO_SMOKE_YXNO[t]) 
		{
			ECON_SYS_TIME cur_time;
			GetTime(&cur_time);
			int cursec = cur_time.hour *60 *60 + cur_time.min *60 + cur_time.sec;
			if(m_curSec < 0)
			{
				m_curSec = cursec;
				printlog(LOGE_ES_STRATEGY_BASE + m_link, "IO mode=2 感温感烟同时报警");
			}
			else if(cursec - m_curSec >= 10)
			{
				//PCS跳闸
				if(m_PCS_ON[t]) setYt(g_PCS_RTU_NO[t], g_PCS_SW_YTNO[t], 0);
				//空调停机
				if(m_TC_run[t]) setYk(g_TC_RTU_NO[t], g_TC_SW_YKNO[t], 0);
				//延时后消防装置开始喷洒
				getYx(g_IO_RTU_NO[t], 8, st, t, "IO DO1");
				if(!st) 
				{
					//setYk(g_IO_RTU_NO[t], 0, 1);
					printlog(LOGE_ES_STRATEGY_BASE + m_link, "IO mode=2 感温感烟再次同时报警，PCS跳闸,空调停机,消防喷洒");
				}
				m_curSec = -1;
			}
		}
		//1.PCS断交流 -> 都灭
		if(m_PCS_Offline[t]) 
		{
			if(m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 0);
			if(m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 0);
			continue;
		}
		
		//2.PCS或BMS故障 -> 运行灭,故障亮
		if(m_PCS_F[t] || m_BMS_Fault[t] || m_IO_hot[t] || m_IO_smoke[t]) 
		{
			if(m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 0);
			if(!m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 1);
			sleep(2);
			continue;
		}

		//3.PCS运行 -> 运行亮,故障灭
		if(m_PCS_ON[t]) 
		{
			if(!m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 1);
			if(m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 0);
			continue;
		}

		//4.PCS未运行 -> 都灭
		if(!m_PCS_ON[t]) 
		{
			if(m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 0);
			if(m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 0);
			continue;
		}
		
	}
}		
bool CES_STRATEGY::IOMode3()
{
	// 说明：圣丹努#1
	// 1.运行指示灯：储能系统正常运行时点亮，故障时熄灭。
	// 2.故障指示灯：储能系统故障时点亮，正常运行时熄灭。
	
	int t = 0;
	bool st = false;
	
	do {
		if(t < m_pcs_num) {//簇1
			//1.PCS断交流 -> 都灭
			if(m_PCS_Offline[t]) {
				if(m_DIO_DO[3]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[3], 0);
				if(m_DIO_DO[4]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[4], 0);
				continue;
			}
			
			//2.PCS或BMS故障 -> 运行灭,故障亮
			if(m_PCS_F[t] || m_BMS_Fault[t]) {
				if(!m_DIO_DO[3]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[3], 1);
				if(m_DIO_DO[4]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[4], 0);
				continue;
			}

			//3.PCS运行 -> 运行亮,故障灭
			if(m_PCS_ON[t]) {
				if(m_DIO_DO[3]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[3], 0);
				if(!m_DIO_DO[4]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[4], 1);
				continue;
			}

			//4.PCS未运行 -> 都灭
			if(!m_PCS_ON[t]) {
				if(m_DIO_DO[3]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[3], 0);
				if(m_DIO_DO[4]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[4], 0);
				continue;
			}
		}
	}while(0);

	t = 1;
	do {
		if(t < m_pcs_num) {//簇2
			//1.PCS断交流 -> 都灭
			if(m_PCS_Offline[t]) {
				if(m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 0);
				if(m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 0);
				continue;
			}
			
			//2.PCS或BMS故障 -> 运行灭,故障亮
			if(m_PCS_F[t] || m_BMS_Fault[t]) {
				if(m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 0);
				if(!m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 1);
				continue;
			}

			//3.PCS运行 -> 运行亮,故障灭
			if(m_PCS_ON[t]) {
				if(!m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 1);
				if(m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 0);
				continue;
			}

			//4.PCS未运行 -> 都灭
			if(!m_PCS_ON[t]) {
				if(m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 0);
				if(m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 0);
				continue;
			}
		}
	}while(0);
	return true;
}
bool CES_STRATEGY::IOMode4()
{
	// 说明：圣丹努#2
	// 1.运行指示灯：储能系统正常运行时点亮，故障时熄灭。
	// 2.故障指示灯：储能系统故障时点亮，正常运行时熄灭。
	
	int t = 0;
	bool st = false;
	
	do {
		if(t < m_pcs_num) {//簇1
			//1.PCS断交流 -> 都灭
			if(m_PCS_Offline[t]) {
				if(m_DIO_DO[1]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[1], 0);
				if(m_DIO_DO[0]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[0], 0);
				continue;
			}
			
			//2.PCS或BMS故障 -> 运行灭,故障亮
			if(m_PCS_F[t] || m_BMS_Fault[t]) {
				if(!m_DIO_DO[1]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[1], 1);
				if(m_DIO_DO[0]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[0], 0);
				continue;
			}

			//3.PCS运行 -> 运行亮,故障灭
			if(m_PCS_ON[t]) {
				if(m_DIO_DO[1]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[1], 0);
				if(!m_DIO_DO[0]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[0], 1);
				continue;
			}

			//4.PCS未运行 -> 都灭
			if(!m_PCS_ON[t]) {
				if(m_DIO_DO[1]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[1], 0);
				if(m_DIO_DO[0]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[0], 0);
				continue;
			}
		}
	}while(0);

	t = 1;
	do {
		if(t < m_pcs_num) {//簇2
			//1.PCS断交流 -> 都灭
			if(m_PCS_Offline[t]) {
				if(m_DIO_DO[3]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[3], 0);
				if(m_DIO_DO[4]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[4], 0);
				continue;
			}
			
			//2.PCS或BMS故障 -> 运行灭,故障亮
			if(m_PCS_F[t] || m_BMS_Fault[t]) {
				if(!m_DIO_DO[3]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[3], 1);
				if(m_DIO_DO[4]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[4], 0);
				continue;
			}

			//3.PCS运行 -> 运行亮,故障灭
			if(m_PCS_ON[t]) {
				if(m_DIO_DO[3]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[3], 0);
				if(!m_DIO_DO[4]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[4], 1);
				continue;
			}

			//4.PCS未运行 -> 都灭
			if(!m_PCS_ON[t]) {
				if(m_DIO_DO[3]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[3], 0);
				if(m_DIO_DO[4]) setYk(g_DIO_RTU_NO, g_DIO_DO_YKNO[4], 0);
				continue;
			}
		}
	}while(0);

	t = 2;
	do {
		if(t < m_pcs_num) {//簇3
			//1.PCS断交流 -> 都灭
			if(m_PCS_Offline[t]) {
				if(m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 0);
				if(m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 0);
				continue;
			}
			
			//2.PCS或BMS故障 -> 运行灭,故障亮
			if(m_PCS_F[t] || m_BMS_Fault[t]) {
				if(m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 0);
				if(!m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 1);
				continue;
			}

			//3.PCS运行 -> 运行亮,故障灭
			if(m_PCS_ON[t]) {
				if(!m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 1);
				if(m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 0);
				continue;
			}

			//4.PCS未运行 -> 都灭
			if(!m_PCS_ON[t]) {
				if(m_IO_run[t]) setYk(g_IO_RTU_NO[t], g_IO_RUN_YKNO[t], 0);
				if(m_IO_F[t]) setYk(g_IO_RTU_NO[t], g_IO_F_YKNO[t], 0);
				continue;
			}
		}
	}while(0);
	return true;
}
		

bool CES_STRATEGY::execStrategy()
{	
	ECON_SYS_TIME cur_time;
	GetTime(&cur_time);

	if(m_curHour != cur_time.hour) {
		m_curHour = cur_time.hour;
		if(m_curHour == 0) {//0点以后 保存一次电量
			saveZeroEcf();
		}
	}
	
	int curmin = cur_time.hour *60 + cur_time.min;

	bool done = false;
	for(int i = 0; i < PCC_NUM; i++) {
		//充放策略启用->关闭时,执行停机操作
		if(m_vecStrategy[i]->m_stopall) {
			m_vecStrategy[i]->m_stopall = false;
			clearPPC(i);
			continue;
		}

		execBkpower(i);
		
		//每分钟检查一次充放策略
		if(m_vecStrategy[i]->m_pls.useflag && m_vecStrategy[i]->m_curMin != curmin) {
			m_vecStrategy[i]->m_curMin = curmin;

			printlog(LOGE_ES_STRATEGY_RX + m_link,"移峰填谷%d,当前时间[%02hhu:%02hhu] %d"
				, i+1, cur_time.hour, cur_time.min, curmin);
			execPls(i, curmin);
		}
		checkUpVal(cur_time.mon);
		done = autoUCP(i);
	}

	return true;
}
//策略 后备电源
bool CES_STRATEGY::execBkpower(int idx)
{
	if( !m_vecStrategy[idx]->m_bkpower.useflag )
		return false;
	
	bool done = false;
	int t = 0;
	for (int k = 0; k < m_pcs_num; k++, t++) {
		if( (m_fBMS_cuSOC[t] > -0.1f)//SOC有效
			&& (m_fBMS_cuSOC[t] < m_vecStrategy[idx]->m_bkpower.lowSOC) ) {// 低于后备值
			done = true;

			m_needModStopUncharge[t] = true;
			if(m_PCS_FD[t]) {//处于放电
				if(m_fPCSModSetFHP[t] > 0.1) {//功率值设为0
					setYt(g_PCS_RTU_NO[k], g_PCS_FHP_YTNO[t], 0);//恒功率模式功率设定值 kW
				}
			}

			printlog(LOGE_ES_STRATEGY_BASE + m_link
				, "后备电源%d,当前 簇%d SOC[%.1f]小于设定备用SOC容量[%.1f],停止放电"
				, idx+1, k+1, m_fBMS_cuSOC[t], m_vecStrategy[idx]->m_bkpower.lowSOC);
		}
	}
		
	return done;
}
//策略 移峰填谷
bool CES_STRATEGY::execPls(int idx, int curmin)
{
	ES_STRATEGY_PLS & pls = m_vecStrategy[idx]->m_pls;
	
	list<ES_TIMEEXEC>::iterator it = pls.m_listTimeTask.begin();
	for(; it!=pls.m_listTimeTask.end(); it++) {
		if( (curmin == it->min ) || (m_vecStrategy[idx]->m_firstRunPls && (curmin < it->min) )) {
			if( (curmin != it->min ) && m_vecStrategy[idx]->m_firstRunPls && (it !=pls.m_listTimeTask.begin()) ) {
				it--;//预防程序重启后 前一次任务未被执行到
				printlog(LOGE_ES_STRATEGY_BASE + m_link,"移峰填谷%d,首次执行,选用时间min[%hu],功率值[%.1f]"
					, idx+1, it->min, it->powerVal);
			}
			m_vecStrategy[idx]->m_firstRunPls = false;
			
			/////////////////////////////////////////
			if(it->powerVal > 0.1f ) {//充电策略值为正
				m_vecStrategy[idx]->m_PCSCurCMD = PCS_CMD_CHARGE;
				m_vecStrategy[idx]->m_fTargCP = it->powerVal;
				m_vecStrategy[idx]->m_iUCPCount = 3;//立即执行

				printlog(LOGE_ES_STRATEGY_BASE + m_link, "移峰填谷%d,当前目标充电功率值[%.1f]"
					, idx+1, m_vecStrategy[idx]->m_fTargCP);
				return true;
			}
			/////////////////////////////////////////
			else if(it->powerVal < -0.1f ) {//放电策略值为负
				m_vecStrategy[idx]->m_PCSCurCMD = PCS_CMD_UNCHARGE;
				m_vecStrategy[idx]->m_fTargUnCP = 0 - it->powerVal;
				m_vecStrategy[idx]->m_iUCPCount = 3;//立即执行

				printlog(LOGE_ES_STRATEGY_BASE + m_link, "移峰填谷%d,当前目标放电功率值[%.1f]"
					, idx+1, m_vecStrategy[idx]->m_fTargUnCP);
				return true;
			}
			/////////////////////////////////////////
			else {//不充电也不放电 即停机
				m_vecStrategy[idx]->m_PCSCurCMD = PCS_CMD_STOP;
				m_vecStrategy[idx]->m_iUCPCount = 5;//标志立即停机
				printlog(LOGE_ES_STRATEGY_BASE + m_link, "移峰填谷%d,当前不充电也不放电", idx+1);
				return true;
			}
		}
	}
	return false;
}

//自动调整充/放电功率,不高于设定功率运行
bool CES_STRATEGY::autoUCP(int idx)
{
	SingleStrategy & ss = *m_vecStrategy[idx];
	if( !ss.m_pls.useflag ) {
		thirdCtrl();
		return false;
	}

	float t_fval = 0;

	//放电阶段
	if(ss.m_PCSCurCMD == PCS_CMD_UNCHARGE) {
		if(++ss.m_iUCPCount < 3)
			return false;

		ss.m_iUCPCount = 0;

		//逆功率判断
		if(ss.m_repower.useflag) {
			if(!ss.m_getPflag) {
				printlog(LOGE_ES_STRATEGY_BASE + m_link, "装置%d auto FD 未取得进线有功", idx+1);
				return false;
			}
			printlog(LOGE_ES_STRATEGY_RX + m_link
				, "装置%d auto FD 当前总%.1f,最低停止放电功率%.1f,最大允许放电功率%.1f,实际设定放电功率%.1f"
				, idx+1, ss.m_fTotalP, ss.m_repower.lowVal, ss.m_fTargUnCP, ss.m_fPCSSetFHP);
			
			t_fval = ss.m_fAvgUserP - ss.m_repower.lowVal - m_res_power;

			if(t_fval > ss.m_fTargUnCP) {
				t_fval = ss.m_fTargUnCP;
			}
			else if( t_fval < 0) {
				t_fval = 0;//为负值放电功率设为0
			}
		}
		else {
			t_fval = ss.m_fTargUnCP;
		}

		printlog(LOGE_ES_STRATEGY_BASE + m_link
			, "装置%d auto FD 当前总%.1f,用户负载%.1f,修正放电功率=%.1f"
			, idx+1, ss.m_fTotalP, ss.m_fUserP, t_fval);

		return unCharge(idx, t_fval);
	}

	//充电阶段
	if(ss.m_PCSCurCMD == PCS_CMD_CHARGE) {
		if(++ss.m_iUCPCount < 3)
			return false;
	
		ss.m_iUCPCount = 0;
		
		//需量控制判断
		if(ss.m_demand.useflag) {
			if(!ss.m_getPflag) {
				printlog(LOGE_ES_STRATEGY_BASE + m_link, "装置%d auto CD 未取得进线有功", idx+1);
				return false;
			}
			// add hubo  此处需判断每分钟的需量上限是否修改



			t_fval = ss.m_demand.upVal - ss.m_fAvgUserP - m_res_power;
			
			if(t_fval >= ss.m_fTargCP) t_fval = ss.m_fTargCP;
			else if(t_fval < 1.0f) t_fval = 0;

			printlog(LOGE_ES_STRATEGY_BASE + m_link, "装置%d auto CD 需量控制,修正充电功率=%.1f", idx+1, t_fval);
			return charge(idx, t_fval);
		}
		
		t_fval = ss.m_fTargCP;
		printlog(LOGE_ES_STRATEGY_BASE + m_link, "装置%d,设置满充电功率=%.1f", idx+1, t_fval);
		return charge(idx, t_fval);
	}

	//停机阶段
	if(ss.m_PCSCurCMD == PCS_CMD_STOP) {
		if(++ss.m_iUCPCount < 5)
			return false;
	
		ss.m_iUCPCount = 0;
		closePPC(idx);
		return true;
	}

	return false;
}
//自动启停风机
void CES_STRATEGY::autoFan()
{
	if(m_auto_fan == 0) return;//未启用时,不进行控制

	int t = 0;
	for (int k = 0; k < m_pcs_num; k++, t++) {
		if(m_iBMS_FanWaitOn[t] > 0) m_iBMS_FanWaitOn[t]--;
		if(m_iBMS_FanWaitOff[t] > 0) m_iBMS_FanWaitOff[t]--;
		
		if(m_PCS_ON[t] && !m_BMS_Fan[t]) {
			if(m_iBMS_FanWaitOn[t] == 0) {
				m_iBMS_FanWaitOn[t] = 10;
				setYt(g_BMS_RTU_NO[t], g_BMS_FAN_YTNO[t], 1);//1启动，0停止
				printlog(LOGE_ES_STRATEGY_BASE + m_link, "下发簇%d Fan启动", t+1);
			}
			continue;
		}

		if(m_PCS_HALT[t] && m_BMS_Fan[t]) {
			if(m_iBMS_FanWaitOff[t] == 0) m_iBMS_FanWaitOff[t] = 20;
			if(m_iBMS_FanWaitOff[t] != 1) continue;
			
			setYt(g_BMS_RTU_NO[t], g_BMS_FAN_YTNO[t], 0);//1启动，0停止
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "下发簇%d Fan关闭", t+1);
			continue;
		}
	}
}
void CES_STRATEGY::autoTC()
{
	if(0 == m_mkls_enable) return;

	int t = 0;
	for (int k = 0; k < m_pcs_num; k++, t++) {
		if(m_TC_Offline[t]) continue;

		if(fabs(m_fBMS_cuTH[t] - m_TC_TSH[t]) > 1.0f) {
			setYt(g_TC_RTU_NO[t], g_TC_TSH_YTNO[t], m_fBMS_cuTH[t]);
		}
		if(fabs(m_fBMS_cuTL[t] - m_TC_TSL[t]) > 1.0f) {
			setYt(g_TC_RTU_NO[t], g_TC_TSL_YTNO[t], m_fBMS_cuTL[t]);
		}
		if(m_PCS_ON[t] && (m_TC_SCF[t] != 1)) {
			setYt(g_TC_RTU_NO[t], g_TC_SCF_YTNO[t], 1.0f);
		}
		if(!m_PCS_ON[t] && (m_TC_SCF[t] != 0)) {
			setYt(g_TC_RTU_NO[t], g_TC_SCF_YTNO[t], 0.0f);
		}
	}
	
}
bool CES_STRATEGY::thirdCtrl()
{
	if(m_third_enable == 0) return false;//未启用时 不执行

	if(m_powerSet > 0.1f) {
		return charge(0, m_powerSet);
	}

	if(m_powerSet > -65535.1f && m_powerSet < -65534.9f) {
		closePPC(0);
		return false;
	}
	
	if(m_powerSet < -0.1f) {
		return unCharge(0, 0.0f - m_powerSet);
	}
	
	clearPPC(0);
	return false;
}

/*******************充/放电流程********************
设置整机 产品运行模式设置=1 PQ模式
设置模块 PQ工作模式=0 恒功率模式
设置模块 无功功率值=0
设置模块 恒功率模式功率设定值 kW 负为放电,正为充电
**************************************************/
bool CES_STRATEGY::charge(int idx, float powerVal)
{
	int iflags[AMX] = {0};			// 可充电操作模块标志=1
	int cnt = 0;					// 可充电操作模块数量
	float total_soc = 0;			// 可充电操作模块SOC总和

	int t = 0;
	for (int k = 0; k < m_pcs_num; k++, t++) {
		//充电时 解除禁放标志
		if(m_PCSMod_jcf[t] & MOD_CFSTA_F_FLAG)
			m_PCSMod_jcf[t] &= ~MOD_CFSTA_F_FLAG;

		//跳过禁充的
		if(m_needModStopCharge[t]) continue;

		//充满但未停机的 做停机处理
		if(m_fBMS_cuSOC[t] > m_soc_max) {
			if(m_PCS_CD[t] && m_PCS_ON[t]) {
				printlog(LOGE_ES_STRATEGY_BASE + m_link, "装置%d Charge 簇%d 已充满%.1f,清零", idx+1, t+1, m_fBMS_cuSOC[t]);
				clearMod(k, t);
				closeMod(k, t);
			}
			continue;
		}

		total_soc += (100.0f - m_fBMS_cuSOC[t]);
		iflags[t] = 1;
		cnt++;
	}
	
	if(cnt == 0) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link, "装置%d Charge 无模块可充电", idx+1);
		ACE_OS::sleep(1);//此处防止频繁检测
		return false;
	}

	//此处充电 只负责开机、设置功率
	float fval = (powerVal/cnt);
	if(fval > m_pcs_power)
		fval = m_pcs_power;
	float final_val = fval;
	bool done = false;
	
	t = 0;
	for (int k = 0; k < m_pcs_num; k++, t++) {
		if(0 == iflags[t]) continue;
		
		if(m_palloc_mode == 1) {//SOC分配
			fval = powerVal * (100.0f - m_fBMS_cuSOC[t]) / total_soc;
			if(fval > m_pcs_power) fval = m_pcs_power;
		}

		if( fval > m_fBMS_cuPC[t] ) {//降功率
			final_val = m_fBMS_cuPC[t];
		}
		else {
			final_val = fval;
		}

		printlog(LOGE_ES_STRATEGY_BASE + m_link, "装置%d Charge PCS%d 充电功率限值%.1f", idx+1, k+1, m_fBMS_cuPC[t]);
		
		if(m_PCSMod_pqm[t] != 1) {
			setYt(g_PCS_RTU_NO[k], g_PCS_PQ_YTNO[t], 1);//0=恒放电 1=恒充电
		}

		//与旧值差值超过1才调节
		if(fabs(final_val - m_fPCSModSetCHP[t]) > 0.99f) {
			setYt(g_PCS_RTU_NO[k], g_PCS_CHP_YTNO[t], final_val);	//功率设定值 kW
		}

		if(m_fPCSModSetFHP[t] > 0.1f) {
			setYt(g_PCS_RTU_NO[k], g_PCS_FHP_YTNO[t], 0);	//放电置零
		}

		//当前模块处于停机状态,下发开机命令
		if(m_PCS_HALT[t]) {
			if(m_PCSMod_R[t] != 1) {
				setYt(g_PCS_RTU_NO[k], g_PCS_R_YTNO[t], 1);//1=PQ模式
			}
			
			setYt(g_PCS_RTU_NO[k], g_PCS_SW_YTNO[t], 1); //1开机
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "装置%d Charge 设置PCS%d 开机open", idx+1, k+1);
			done = true;
		}
	}
	if(done) {
		ACE_OS::sleep(1);
	}
	return done;
}
bool CES_STRATEGY::unCharge(int idx, float powerVal)
{
	int iflags[AMX] = {0};	// 可放电操作模块标志=1
	int cnt = 0;			// 可放电操作模块数量
	float total_soc = 0;	// 可充电操作模块SOC总和
	
	int t = 0;
	for (int k = 0; k < m_pcs_num; k++, t++) {
		//解除禁充标志
		if(m_PCSMod_jcf[t] & MOD_CFSTA_C_FLAG)
			m_PCSMod_jcf[t] &= ~MOD_CFSTA_C_FLAG;

		//跳过禁放的
		if(m_needModStopUncharge[t]) continue;

		//放完但未停机的 做机处理
		if(m_fBMS_cuSOC[t] < m_soc_min) {
			if(m_PCS_FD[t] && m_PCS_ON[t]) {
				printlog(LOGE_ES_STRATEGY_BASE + m_link, "装置%d uncharge 簇%d 已放完%.1f,清零", idx+1, t+1, m_fBMS_cuSOC[t]);
				clearMod(k, t);
				closeMod(k, t);
			}
			continue;
		}

		total_soc += m_fBMS_cuSOC[t];
		iflags[t] = 1;
		cnt++;
	}

	if(cnt == 0) {
		printlog(LOGE_ES_STRATEGY_BASE + m_link, "装置%d uncharge 无模块可放电", idx+1);
		ACE_OS::sleep(1);//此处防止频繁检测
		return false;
	}

	//此处放电 只负责开机、设置功率
	float fval = (powerVal/cnt);
	if(fval > m_pcs_power)
		fval = m_pcs_power;
	float final_val = fval;
	bool done = false;
	
	t = 0;
	for (int k = 0; k < m_pcs_num; k++, t++) {
		if(0 == iflags[t]) continue;

		if(m_palloc_mode == 1) {//SOC分配
			fval = powerVal * m_fBMS_cuSOC[t] / total_soc;
			if(fval > m_pcs_power) fval = m_pcs_power;
		}
			
		if( fval > m_fBMS_cuPF[t] ) {//降功率
			final_val = m_fBMS_cuPF[t];
		}
		else {
			final_val = fval;
		}

		printlog(LOGE_ES_STRATEGY_BASE + m_link, "装置%d uncharge PCS%d 放电功率限值%.1f", idx+1, k+1, m_fBMS_cuPF[t]);

		if(m_PCSMod_pqm[t] != 0) {
			setYt(g_PCS_RTU_NO[k], g_PCS_PQ_YTNO[t], 0);//0=恒放电 1=恒充电
		}

		if(fabs(final_val - m_fPCSModSetFHP[t]) > 0.99f) {
			setYt(g_PCS_RTU_NO[k], g_PCS_FHP_YTNO[t], final_val);	//功率设定值 kW
		}

		if(m_fPCSModSetCHP[t] > 0.1f) {
			setYt(g_PCS_RTU_NO[k], g_PCS_CHP_YTNO[t], 0);	//充电置零
		}

		//当前模块处于停机状态,下发开机命令
		if(m_PCS_HALT[t]) {
			if(m_PCSMod_R[t] != 1) {
				setYt(g_PCS_RTU_NO[k], g_PCS_R_YTNO[t], 1);//1=PQ模式
			}

			setYt(g_PCS_RTU_NO[k], g_PCS_SW_YTNO[t], 1);	//1开机
			printlog(LOGE_ES_STRATEGY_BASE + m_link, "装置%d uncharge 设置PCS%d 开机open", idx+1, k+1);
			done = true;
		}
	}
	if(done) {
		ACE_OS::sleep(1);
	}
	return done;
}

//关闭PCS(以装置为单位)
void CES_STRATEGY::closePPC(int idx)
{
	int t = 0;
	for (int k = 0; k < m_pcs_num; k++, t++) {
		clearMod(k, t);

		//当前模块 需停机 或 处于停机状态 (或 处于未知状态,不下发关机命令)
		if(m_needModStopRun[t] || !m_PCS_ON[t])
			continue;

		closeMod(k, t);
	}
}
void CES_STRATEGY::clearPPC(int idx)
{
	int t = 0;
	for (int k = 0; k < m_pcs_num; k++, t++) {
		clearMod(k, t);
	}
}

void CES_STRATEGY::openMod(int k, int t)
{
	//1.当前模块失联,不下发命令
	// if(m_PCSMod_state[t] == MOD_STA_UNNOW)
	// 	return;

	setYt(g_PCS_RTU_NO[k], g_PCS_SW_YTNO[t], 1);//1开机
	printlog(LOGE_ES_STRATEGY_BASE + m_link, "设置PCS%d 开机open", k+1);
}
void CES_STRATEGY::closeMod(int k, int t)
{
	//当前模块失联,不下发命令
	// if(m_PCSMod_state[t] == MOD_STA_UNNOW)
	// 	return;

	setYt(g_PCS_RTU_NO[k], g_PCS_SW_YTNO[t], 0);//1开机，0关机
	printlog(LOGE_ES_STRATEGY_BASE + m_link, "设置PCS%d 关机off", k+1);
}
void CES_STRATEGY::clearMod(int k, int t)
{
	if(m_fPCSModSetCHP[t] > 0.1f || m_fPCSModSetCHP[t] < -0.1f) {
		setYt(g_PCS_RTU_NO[k], g_PCS_CHP_YTNO[t], 0);//充电功率设定 kW
	}
	if(m_fPCSModSetFHP[t] > 0.1f || m_fPCSModSetFHP[t] < -0.1f) {
		setYt(g_PCS_RTU_NO[k], g_PCS_FHP_YTNO[t], 0);//放电功率设定 kW
	}
}

bool CES_STRATEGY::wrYc(int no, float val)
{
	FDC_YC_DATA	data;
	memset(&data, 0, sizeof(FDC_YC_DATA));

	data.val = val;
	m_dataInf.setYc(m_curRoute, no, data);
}
bool CES_STRATEGY::wrYx(int no, hUInt8 val)
{
	FDC_YX_DATA	data;
	memset(&data, 0, sizeof(FDC_YX_DATA));

	data.val = val;
	m_dataInf.setYx(m_curRoute, no, data);
}

bool CES_STRATEGY::setYk(int groupNo,int selNo,hUChar ctrlState)
{
	hUChar cmdBuf[FDC_CTRL_LEN];
	
	ctrl_head *m_head = (ctrl_head *)cmdBuf;
	m_head->type = CTRL_PRO_YK;
	ctrl_pro_yk *m_ctrlyk = (ctrl_pro_yk *)(cmdBuf + sizeof(ctrl_head));
	m_ctrlyk->ctrlNo = selNo;
	m_ctrlyk->ctrlState = ctrlState;
	m_ctrlyk->ctrlType = YK_EXE_CMD;
	m_ctrlyk->groupNo = groupNo;	
	
	bool retflag = m_ctrlInf.add(cmdBuf,sizeof(ctrl_head)+sizeof(ctrl_pro_yk),0);
	printlog(LOGE_ES_STRATEGY_TX + m_link,"TxYK,group[%d],ykno[%d],ival[%d],ret[%d]\n"
		,groupNo,selNo,ctrlState,retflag);
	return	retflag;
}
bool CES_STRATEGY::setYt(int groupNo,int selNo,float ctrlVal)
{
	hUChar cmdBuf[FDC_CTRL_LEN];

	ctrl_head *m_head = (ctrl_head *)cmdBuf;
	m_head->type = CTRL_PRO_YT;
	ctrl_pro_setpoint *m_ctrlyt = (ctrl_pro_setpoint *)(cmdBuf + sizeof(ctrl_head));
	m_ctrlyt->ctrlType = CTRL_PRO_YT;
	m_ctrlyt->groupNo = groupNo;
	m_ctrlyt->ctrlNo = selNo;
	m_ctrlyt->floatValue = ctrlVal;

	bool retflag = m_ctrlInf.add(cmdBuf,sizeof(ctrl_head)+sizeof(ctrl_pro_setpoint),0);
	printlog(LOGE_ES_STRATEGY_TX + m_link,"TxYT,group[%d],ytno[%d],fval[%g],ret[%d]"
		,groupNo,selNo,ctrlVal,retflag);
	return	retflag;
}

bool CES_STRATEGY::isValidYc(FDC_YC_DATA * pData, int devNo, const char * label)
{
	if (pData == NULL) {
		printlog(LOGE_ES_STRATEGY_RX + m_link,"%s[%d] pData is null", label, devNo);
		return false;
	}
	if (pData->quality.hostValue() & QUALITY_BIT_IV) {
		printlog(LOGE_ES_STRATEGY_RX + m_link,"%s[%d] pData is invalid", label, devNo);
		return false;
	}
	if (pData->quality.hostValue() & QUALITY_BIT_NT) {
		printlog(LOGE_ES_STRATEGY_RX + m_link,"%s[%d] pData is dirty", label, devNo);
		return false;
	}
	return true;
}
bool CES_STRATEGY::isValidYx(FDC_YX_DATA * pData, int devNo, const char * label)
{
	if (pData == NULL) {
		printlog(LOGE_ES_STRATEGY_RX + m_link,"%s[%d] pData is null", label, devNo);
		return false;
	}
	if (pData->quality.hostValue() & QUALITY_BIT_IV) {
		printlog(LOGE_ES_STRATEGY_RX + m_link,"%s[%d] pData is invalid", label, devNo);
		return false;
	}
	if (pData->quality.hostValue() & QUALITY_BIT_NT) {
		printlog(LOGE_ES_STRATEGY_RX + m_link,"%s[%d] pData is dirty", label, devNo);
		return false;
	}
	return true;
}

bool CES_STRATEGY::getYc(int grpNo, int no, float & val, int devNo, const char * label)
{
	FDC_YC_DATA * pData = m_dataInf.ycDataMainRoute(grpNo, no);
	if (isValidYc(pData, devNo, label)) {
		val = pData->val.hostValue();
		return true;
	}
	return false;
}
bool CES_STRATEGY::getYx(int grpNo, int no, bool & val, int devNo, const char * label)
{
	FDC_YX_DATA * pData = m_dataInf.yxDataMainRoute(grpNo, no);
	if (isValidYx(pData, devNo, label)) {
		val = pData->val;
		return true;
	}
	return false;
}

int	 CES_STRATEGY::findByRtu(int rtuno)
{
	for(int i = 0; i < PCC_NUM; i++) {
		if(m_vecStrategy[i]->m_rtuno == rtuno) return i;
	}
	return -1;
}

bool ECON::FDC::CES_STRATEGY::reloadConfig()
{
    ES_CTRL_TYPE type;
	readCtrlTypeConfig(type);
	for(int i = 0; i < PCC_NUM; i++) 
	{
		m_vecStrategy[i]->init();
		delete m_vecStrategy[i];
	}
	m_vecStrategy.clear();
	
	printlog(LOGE_ES_STRATEGY_BASE + m_link,"策略加载配置文件");
	if(type == ES_CTRL_TYPE::LOCAL_TYPE)
	{
		//本地策略
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"当前模式为本地策略");

		for(int i = 0; i < PCC_NUM; i++) 
		{
			m_strategyFactory = new localStrategy(g_CMD_RTU_NO[i]);
			m_vecStrategy.push_back(m_strategyFactory->init());
			delete m_strategyFactory;
		}
	}
	else if(type == ES_CTRL_TYPE::REMOTE_TYPE)
	{
		//云端策略
		printlog(LOGE_ES_STRATEGY_BASE + m_link,"当前模式为云端策略");
		for(int i = 0; i < PCC_NUM; i++) 
		{
			SingleStrategy *sig = new SingleStrategy();
			m_vecStrategy.push_back(sig);
			m_vecStrategy[i]->init();
			m_vecStrategy[i]->m_rtuno = g_CMD_RTU_NO[i];
			// m_strategyFactory = new remoteStrategy(g_CMD_RTU_NO[i]);
			// m_vecStrategy.push_back(m_strategyFactory->init());
		}
		featureFromdb();	//暂时都从数据库读
	}
	for(int i = 0; i < PCC_NUM; i++) 
	{
		m_vecStrategy[i]->printDetails();
	}
	
	return true;
}

bool ECON::FDC::CES_STRATEGY::readCtrlTypeConfig(ES_CTRL_TYPE &type)
{
	char *sysRoot = getenv("SYS_ROOT");
	if (sysRoot == NULL)
	{
		// printlog(LOGE_ES_STRATEGY_BASE+m_link, "SYS_ROOT env failed");
		return false;
	}

	char fname[128] = {0};
	sprintf(fname, "%s/ini/share_config.json", sysRoot);

	std::string str;
	int fd;
	fd = ::open(fname, O_RDONLY);
	if (fd == -1)
		return false;
	char readbuff[1024 * 10];
	ssize_t bytesRead = read(fd, readbuff, sizeof(readbuff) - 1);

	if (bytesRead > 0)
	{
		readbuff[bytesRead] = '\0';
		str = std::string(readbuff, bytesRead);
	}
	::close(fd); // 关闭文件描述符
	nlohmann::json configJson;
	configJson =  NLJson::parse(readbuff);
	int t= configJson["controlType"].get<int>();
	type = static_cast<ES_CTRL_TYPE>(t);
	printlog(LOGE_ES_STRATEGY_BASE+m_link, "当前策略控制方式为%s控制",type==0?"[云端]":"[本地]");
    return true;
}

bool ECON::FDC::CES_STRATEGY::checkUpVal(int mon)
{
	for (auto &strategy : m_vecStrategy)
    {
        ES_STRATEGY_DEMAND::MonDemand mid;
        for (auto &dmv : strategy->m_demand.demandVal)
        {
			if(dmv.mon == 0)
			{
				mid.demandVal = dmv.demandVal;
                mid.mon = dmv.mon;
			}
            else if (dmv.mon == mon)
            {
                mid.demandVal = dmv.demandVal;
                mid.mon = dmv.mon;
            }
        }
        strategy->m_demand.upVal = mid.demandVal * strategy->m_demand.upper * 0.01;
    }
    return false;
}

//----------------------------------------------------------------------------
extern "C" ES_STRATEGY_EXPORT ECON::FDC::CProtocol * CreateProtocol()
{
	return ( new CES_STRATEGY() );
}

extern "C" ES_STRATEGY_EXPORT void DestroyProtocol(ECON::FDC::CProtocol *p)
{
	delete p;
}





