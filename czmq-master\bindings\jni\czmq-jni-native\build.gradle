/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

dependencies {
    implementation project(':czmq-jni')
}

//  ------------------------------------------------------------------
//  Build section

tasks.register('copyLibs', Copy) {
    def libraryPaths = []
    if (project.hasProperty('buildPrefix')) {
        if (osdetector.os == 'windows') {
            // DLLs are installed to the bin directory by cmake
            libraryPaths.add("${project.buildPrefix}/bin")
        }
        libraryPaths.add("${project.buildPrefix}/lib")
    }

    def javaLibraryPaths = System.getProperty('java.library.path').split(File.pathSeparator).toList()
    libraryPaths.addAll(javaLibraryPaths)

    libraryPaths.add('/usr/local/lib')
    if (osdetector.os == 'windows') {
        libraryPaths.add("${rootDir}/czmq-jni/build/Release")
    } else {
        libraryPaths.add("${rootDir}/czmq-jni/build")
    }

    def oldStrategy = duplicatesStrategy
    duplicatesStrategy = DuplicatesStrategy.WARN
    libraryPaths.each { path ->
        from path
            include 'libczmqjni.so'
            include 'libczmqjni.dylib'
            include '*czmqjni*.dll'
            include 'libczmq.so'
            include 'libczmq.dylib'
            include '*czmq*.dll'
            include 'libzmq.so'
            include 'libzmq.dylib'
            include '*zmq*.dll'
            include 'uuid.so'
            include 'uuid.dylib'
            include '*uuid*.dll'
            include 'libsystemd.so'
            include 'libsystemd.dylib'
            include '*systemd*.dll'
            include 'liblz4.so'
            include 'liblz4.dylib'
            include '*lz4*.dll'
            include 'libcurl.so'
            include 'libcurl.dylib'
            include '*curl*.dll'
            include 'nss.so'
            include 'nss.dylib'
            include '*nss*.dll'
            include 'libmicrohttpd.so'
            include 'libmicrohttpd.dylib'
            include '*microhttpd*.dll'
        into 'build/natives'
    }
    duplicatesStrategy = oldStrategy
}

jar.archiveBaseName = "czmq-jni-${osdetector.classifier}"
jar.dependsOn copyLibs

jar {
    def arch = osdetector.arch.contains('64') ? '64' : '32'
    from 'build/natives'
        include '*'
    into "natives/${osdetector.os}_${arch}"
}

//  ------------------------------------------------------------------
//  Install and Publish section

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
            artifactId = "czmq-jni-${osdetector.classifier}"
            pom {
                name = "czmq-jni-${osdetector.classifier}"
                description = 'The high-level C binding for 0MQ'
                packaging = 'jar'
                url = 'https://github.com/zeromq/czmq'
                licenses {
                    license {
                        name = 'Mozilla Public License Version 2.0'
                        url = 'https://www.mozilla.org/en-US/MPL/2.0/'
                    }
                }
                scm {
                    connection = 'https://github.com/zeromq/czmq.git'
                    developerConnection = 'https://github.com/zeromq/czmq.git'
                    url = 'https://github.com/zeromq/czmq'
                }
            }
        }
    }
}

artifactoryPublish {
    publications ('mavenJava')
}

bintray {
    user = System.getenv('BINTRAY_USER')
    key = System.getenv('BINTRAY_KEY')
    publications = ['mavenJava']
    publish = true
    override = true
    pkg {
        repo = 'maven'
        name = "czmq-jni-${osdetector.classifier}"
        desc = 'The high-level C binding for 0MQ'
        userOrg = System.getenv('BINTRAY_USER_ORG')
        licenses = ['MPL-2.0']
        websiteUrl = 'https://github.com/zeromq/czmq'
        issueTrackerUrl = 'https://github.com/zeromq/czmq/issues'
        vcsUrl = 'https://github.com/zeromq/czmq.git'
        githubRepo = System.getenv('BINTRAY_USER_ORG') + '/czmq'
        version {
            name = project.version
            vcsTag= project.version
        }
    }
}

//  ------------------------------------------------------------------
//  Cleanup section

clean.doFirst {
    delete fileTree(projectDir) {
        include '*.so'
        include '*.dylib'
    }
}
