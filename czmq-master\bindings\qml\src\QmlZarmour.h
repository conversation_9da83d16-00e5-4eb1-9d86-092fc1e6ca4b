/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#ifndef QML_ZARMOUR_H
#define QML_ZARMOUR_H

#include <QtQml>

#include <czmq.h>
#include "qml_czmq_plugin.h"


class QmlZarmour : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isNULL READ isNULL)

public:
    zarmour_t *self;

    QmlZarmour() { self = NULL; }
    bool isNULL() { return self == NULL; }

    static QObject* qmlAttachedProperties(QObject* object); // defined in QmlZarmour.cpp

public slots:
    //  Encode a stream of bytes into an armoured string. Returns the armoured
    //  string, or NULL if there was insufficient memory available to allocate
    //  a new string.
    QString encode (const byte *data, size_t size);

    //  Decode an armoured string into a chunk. The decoded output is
    //  null-terminated, so it may be treated as a string, if that's what
    //  it was prior to encoding.
    QmlZchunk *decode (const QString &data);

    //  Get the mode property.
    int mode ();

    //  Get printable string for mode.
    const QString modeStr ();

    //  Set the mode property.
    void setMode (int mode);

    //  Return true if padding is turned on.
    bool pad ();

    //  Turn padding on or off. Default is on.
    void setPad (bool pad);

    //  Get the padding character.
    char padChar ();

    //  Set the padding character.
    void setPadChar (char padChar);

    //  Return if splitting output into lines is turned on. Default is off.
    bool lineBreaks ();

    //  Turn splitting output into lines on or off.
    void setLineBreaks (bool lineBreaks);

    //  Get the line length used for splitting lines.
    size_t lineLength ();

    //  Set the line length used for splitting lines.
    void setLineLength (size_t lineLength);

    //  Print properties of object
    void print ();
};

class QmlZarmourAttached : public QObject
{
    Q_OBJECT
    QObject* m_attached;

public:
    QmlZarmourAttached (QObject* attached) {
        Q_UNUSED (attached);
    };

public slots:
    //  Self test of this class.
    void test (bool verbose);

    //  Create a new zarmour
    QmlZarmour *construct ();

    //  Destroy the zarmour
    void destruct (QmlZarmour *qmlSelf);
};


QML_DECLARE_TYPEINFO(QmlZarmour, QML_HAS_ATTACHED_PROPERTIES)

#endif
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
