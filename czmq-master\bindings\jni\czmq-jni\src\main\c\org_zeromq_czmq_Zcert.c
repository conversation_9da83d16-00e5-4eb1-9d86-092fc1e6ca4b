/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#include <stdio.h>
#include <stdlib.h>
#include <jni.h>
#include "czmq.h"
#include "org_zeromq_czmq_Zcert.h"

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zcert__1_1new (JNIEnv *env, jclass c)
{
    //  Disable CZMQ signal handling; allow Java to deal with it
    zsys_handler_set (NULL);
    jlong new_ = (jlong) (intptr_t) zcert_new ();
    return new_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zcert__1_1newFrom (JNIEnv *env, jclass c, jbyteArray public_key, jbyteArray secret_key)
{
    jbyte *public_key_ = (byte *) (*env)->GetByteArrayElements (env, public_key, 0);
    jbyte *secret_key_ = (byte *) (*env)->GetByteArrayElements (env, secret_key, 0);
    jlong new_from_ = (jlong) (intptr_t) zcert_new_from (public_key_, secret_key_);
    (*env)->ReleaseByteArrayElements (env, public_key, (jbyte *) public_key_, 0);
    (*env)->ReleaseByteArrayElements (env, secret_key, (jbyte *) secret_key_, 0);
    return new_from_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zcert__1_1newFromTxt (JNIEnv *env, jclass c, jstring public_txt, jstring secret_txt)
{
    char *public_txt_ = (char *) (*env)->GetStringUTFChars (env, public_txt, NULL);
    char *secret_txt_ = (char *) (*env)->GetStringUTFChars (env, secret_txt, NULL);
    jlong new_from_txt_ = (jlong) (intptr_t) zcert_new_from_txt (public_txt_, secret_txt_);
    (*env)->ReleaseStringUTFChars (env, public_txt, public_txt_);
    (*env)->ReleaseStringUTFChars (env, secret_txt, secret_txt_);
    return new_from_txt_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zcert__1_1load (JNIEnv *env, jclass c, jstring filename)
{
    char *filename_ = (char *) (*env)->GetStringUTFChars (env, filename, NULL);
    jlong load_ = (jlong) (intptr_t) zcert_load (filename_);
    (*env)->ReleaseStringUTFChars (env, filename, filename_);
    return load_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zcert__1_1destroy (JNIEnv *env, jclass c, jlong self)
{
    zcert_destroy ((zcert_t **) &self);
}

JNIEXPORT jbyteArray JNICALL
Java_org_zeromq_czmq_Zcert__1_1publicKey (JNIEnv *env, jclass c, jlong self)
{
    jbyte *public_key_ = (jbyte *) zcert_public_key ((zcert_t *) (intptr_t) self);
    jint return_size_ = (jint) 32;
    jbyteArray return_data_ = (*env)->NewByteArray (env, return_size_);
    (*env)->SetByteArrayRegion (env, return_data_, 0, return_size_, (jbyte *) public_key_);
    return return_data_;
}

JNIEXPORT jbyteArray JNICALL
Java_org_zeromq_czmq_Zcert__1_1secretKey (JNIEnv *env, jclass c, jlong self)
{
    jbyte *secret_key_ = (jbyte *) zcert_secret_key ((zcert_t *) (intptr_t) self);
    jint return_size_ = (jint) 32;
    jbyteArray return_data_ = (*env)->NewByteArray (env, return_size_);
    (*env)->SetByteArrayRegion (env, return_data_, 0, return_size_, (jbyte *) secret_key_);
    return return_data_;
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zcert__1_1publicTxt (JNIEnv *env, jclass c, jlong self)
{
    char *public_txt_ = (char *) zcert_public_txt ((zcert_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, public_txt_);
    return return_string_;
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zcert__1_1secretTxt (JNIEnv *env, jclass c, jlong self)
{
    char *secret_txt_ = (char *) zcert_secret_txt ((zcert_t *) (intptr_t) self);
    jstring return_string_ = (*env)->NewStringUTF (env, secret_txt_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zcert__1_1setMeta (JNIEnv *env, jclass c, jlong self, jstring name, jstring format)
{
    char *name_ = (char *) (*env)->GetStringUTFChars (env, name, NULL);
    char *format_ = (char *) (*env)->GetStringUTFChars (env, format, NULL);
    zcert_set_meta ((zcert_t *) (intptr_t) self, name_, "%s", format_);
    (*env)->ReleaseStringUTFChars (env, name, name_);
    (*env)->ReleaseStringUTFChars (env, format, format_);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zcert__1_1unsetMeta (JNIEnv *env, jclass c, jlong self, jstring name)
{
    char *name_ = (char *) (*env)->GetStringUTFChars (env, name, NULL);
    zcert_unset_meta ((zcert_t *) (intptr_t) self, name_);
    (*env)->ReleaseStringUTFChars (env, name, name_);
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zcert__1_1meta (JNIEnv *env, jclass c, jlong self, jstring name)
{
    char *name_ = (char *) (*env)->GetStringUTFChars (env, name, NULL);
    char *meta_ = (char *) zcert_meta ((zcert_t *) (intptr_t) self, name_);
    jstring return_string_ = (*env)->NewStringUTF (env, meta_);
    (*env)->ReleaseStringUTFChars (env, name, name_);
    return return_string_;
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zcert__1_1metaKeys (JNIEnv *env, jclass c, jlong self)
{
    jlong meta_keys_ = (jlong) (intptr_t) zcert_meta_keys ((zcert_t *) (intptr_t) self);
    return meta_keys_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zcert__1_1save (JNIEnv *env, jclass c, jlong self, jstring filename)
{
    char *filename_ = (char *) (*env)->GetStringUTFChars (env, filename, NULL);
    jint save_ = (jint) zcert_save ((zcert_t *) (intptr_t) self, filename_);
    (*env)->ReleaseStringUTFChars (env, filename, filename_);
    return save_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zcert__1_1savePublic (JNIEnv *env, jclass c, jlong self, jstring filename)
{
    char *filename_ = (char *) (*env)->GetStringUTFChars (env, filename, NULL);
    jint save_public_ = (jint) zcert_save_public ((zcert_t *) (intptr_t) self, filename_);
    (*env)->ReleaseStringUTFChars (env, filename, filename_);
    return save_public_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zcert__1_1saveSecret (JNIEnv *env, jclass c, jlong self, jstring filename)
{
    char *filename_ = (char *) (*env)->GetStringUTFChars (env, filename, NULL);
    jint save_secret_ = (jint) zcert_save_secret ((zcert_t *) (intptr_t) self, filename_);
    (*env)->ReleaseStringUTFChars (env, filename, filename_);
    return save_secret_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zcert__1_1apply (JNIEnv *env, jclass c, jlong self, jlong socket)
{
    zcert_apply ((zcert_t *) (intptr_t) self, (void *) (intptr_t) socket);
}

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zcert__1_1dup (JNIEnv *env, jclass c, jlong self)
{
    jlong dup_ = (jlong) (intptr_t) zcert_dup ((zcert_t *) (intptr_t) self);
    return dup_;
}

JNIEXPORT jboolean JNICALL
Java_org_zeromq_czmq_Zcert__1_1eq (JNIEnv *env, jclass c, jlong self, jlong compare)
{
    jboolean eq_ = (jboolean) zcert_eq ((zcert_t *) (intptr_t) self, (zcert_t *) (intptr_t) compare);
    return eq_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zcert__1_1print (JNIEnv *env, jclass c, jlong self)
{
    zcert_print ((zcert_t *) (intptr_t) self);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zcert__1_1test (JNIEnv *env, jclass c, jboolean verbose)
{
    zcert_test ((bool) verbose);
}

