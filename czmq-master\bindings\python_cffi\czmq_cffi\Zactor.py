################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class Zactor(object):
    """
    provides a simple actor framework
    """

    def __init__(self, task, args):
        """
        Create a new actor passing arbitrary arguments reference.
        """
        p = utils.lib.zactor_new(task, args._p)
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zactor_destroy_py)

    def send(self, msg_p):
        """
        Send a zmsg message to the actor, take ownership of the message
        and destroy when it has been sent.
        """
        return utils.lib.zactor_send(self._p, utils.ffi.new("zmsg_t **", msg_p._p))

    def recv(self):
        """
        Receive a zmsg message from the actor. Returns NULL if the actor
        was interrupted before the message could be received, or if there
        was a timeout on the actor.
        """
        return utils.lib.zactor_recv(self._p)

    @staticmethod
    def is_py(self):
        """
        Probe the supplied object, and report if it looks like a zactor_t.
        """
        return utils.lib.zactor_is(self._p)

    @staticmethod
    def resolve(self):
        """
        Probe the supplied reference. If it looks like a zactor_t instance,
        return the underlying libzmq actor handle; else if it looks like
        a libzmq actor handle, return the supplied value.
        """
        return utils.lib.zactor_resolve(self._p)

    def sock(self):
        """
        Return the actor's zsock handle. Use this when you absolutely need
        to work with the zsock instance rather than the actor.
        """
        return utils.lib.zactor_sock(self._p)

    def set_destructor(self, destructor):
        """
        Change default destructor by custom function. Actor MUST be able to handle new message instead of default $TERM.
        """
        utils.lib.zactor_set_destructor(self._p, destructor)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zactor_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
