/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#ifndef Q_ZHTTP_CLIENT_H
#define Q_ZHTTP_CLIENT_H

#include "qczmq.h"

class QT_CZMQ_EXPORT QZhttpClient : public QObject
{
    Q_OBJECT
public:

    //  Copy-construct to return the proper wrapped c types
    QZhttpClient (zhttp_client_t *self, QObject *qObjParent = 0);

    //  Create a new http client
    explicit QZhttpClient (bool verbose, QObject *qObjParent = 0);

    //  Destroy an http client
    ~QZhttpClient ();

    //  Self test of this class.
    static void test (bool verbose);

    zhttp_client_t *self;
};
#endif //  Q_ZHTTP_CLIENT_H
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
