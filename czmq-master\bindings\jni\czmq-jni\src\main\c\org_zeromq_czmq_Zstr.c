/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#include <stdio.h>
#include <stdlib.h>
#include <jni.h>
#include "czmq.h"
#include "org_zeromq_czmq_Zstr.h"

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zstr__1_1recv (JNIEnv *env, jclass c, jlong source)
{
    char *recv_ = (char *) zstr_recv ((void *) (intptr_t) source);
    jstring return_string_ = (*env)->NewStringUTF (env, recv_);
    zstr_free (&recv_);
    return return_string_;
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zstr__1_1recvCompress (JNIEnv *env, jclass c, jlong source)
{
    char *recv_compress_ = (char *) zstr_recv_compress ((void *) (intptr_t) source);
    jstring return_string_ = (*env)->NewStringUTF (env, recv_compress_);
    zstr_free (&recv_compress_);
    return return_string_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zstr__1_1send (JNIEnv *env, jclass c, jlong dest, jstring string)
{
    char *string_ = (char *) (*env)->GetStringUTFChars (env, string, NULL);
    jint send_ = (jint) zstr_send ((void *) (intptr_t) dest, string_);
    (*env)->ReleaseStringUTFChars (env, string, string_);
    return send_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zstr__1_1sendm (JNIEnv *env, jclass c, jlong dest, jstring string)
{
    char *string_ = (char *) (*env)->GetStringUTFChars (env, string, NULL);
    jint sendm_ = (jint) zstr_sendm ((void *) (intptr_t) dest, string_);
    (*env)->ReleaseStringUTFChars (env, string, string_);
    return sendm_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zstr__1_1sendf (JNIEnv *env, jclass c, jlong dest, jstring format)
{
    char *format_ = (char *) (*env)->GetStringUTFChars (env, format, NULL);
    jint sendf_ = (jint) zstr_sendf ((void *) (intptr_t) dest, "%s", format_);
    (*env)->ReleaseStringUTFChars (env, format, format_);
    return sendf_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zstr__1_1sendfm (JNIEnv *env, jclass c, jlong dest, jstring format)
{
    char *format_ = (char *) (*env)->GetStringUTFChars (env, format, NULL);
    jint sendfm_ = (jint) zstr_sendfm ((void *) (intptr_t) dest, "%s", format_);
    (*env)->ReleaseStringUTFChars (env, format, format_);
    return sendfm_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zstr__1_1sendx (JNIEnv *env, jclass c, jlong dest, jstring string)
{
    char *string_ = (char *) (*env)->GetStringUTFChars (env, string, NULL);
    jint sendx_ = (jint) zstr_sendx ((void *) (intptr_t) dest, string_, NULL);
    (*env)->ReleaseStringUTFChars (env, string, string_);
    return sendx_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zstr__1_1sendCompress (JNIEnv *env, jclass c, jlong dest, jstring string)
{
    char *string_ = (char *) (*env)->GetStringUTFChars (env, string, NULL);
    jint send_compress_ = (jint) zstr_send_compress ((void *) (intptr_t) dest, string_);
    (*env)->ReleaseStringUTFChars (env, string, string_);
    return send_compress_;
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zstr__1_1sendmCompress (JNIEnv *env, jclass c, jlong dest, jstring string)
{
    char *string_ = (char *) (*env)->GetStringUTFChars (env, string, NULL);
    jint sendm_compress_ = (jint) zstr_sendm_compress ((void *) (intptr_t) dest, string_);
    (*env)->ReleaseStringUTFChars (env, string, string_);
    return sendm_compress_;
}

JNIEXPORT jstring JNICALL
Java_org_zeromq_czmq_Zstr__1_1str (JNIEnv *env, jclass c, jlong source)
{
    char *str_ = (char *) zstr_str ((void *) (intptr_t) source);
    jstring return_string_ = (*env)->NewStringUTF (env, str_);
    zstr_free (&str_);
    return return_string_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zstr__1_1test (JNIEnv *env, jclass c, jboolean verbose)
{
    zstr_test ((bool) verbose);
}

