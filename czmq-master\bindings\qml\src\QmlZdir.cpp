/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZdir.h"


///
//  Return directory path
const QString QmlZdir::path () {
    return QString (zdir_path (self));
};

///
//  Return last modification time for directory.
time_t QmlZdir::modified () {
    return zdir_modified (self);
};

///
//  Return total hierarchy size, in bytes of data contained in all files
//  in the directory tree.
off_t QmlZdir::cursize () {
    return zdir_cursize (self);
};

///
//  Return directory count
size_t QmlZdir::count () {
    return zdir_count (self);
};

///
//  Returns a sorted list of zfile objects; Each entry in the list is a pointer
//  to a zfile_t item already allocated in the zdir tree. Do not destroy the
//  original zdir tree until you are done with this list.
QmlZlist *QmlZdir::list () {
    QmlZlist *retQ_ = new QmlZlist ();
    retQ_->self = zdir_list (self);
    return retQ_;
};

///
//  Returns a sorted list of char*; Each entry in the list is a path of a file
//  or directory contained in self.
QmlZlist *QmlZdir::listPaths () {
    QmlZlist *retQ_ = new QmlZlist ();
    retQ_->self = zdir_list_paths (self);
    return retQ_;
};

///
//  Remove directory, optionally including all files that it contains, at
//  all levels. If force is false, will only remove the directory if empty.
//  If force is true, will remove all files and all subdirectories.
void QmlZdir::remove (bool force) {
    zdir_remove (self, force);
};

///
//  Return full contents of directory as a zdir_patch list.
QmlZlist *QmlZdir::resync (const QString &alias) {
    QmlZlist *retQ_ = new QmlZlist ();
    retQ_->self = zdir_resync (self, alias.toUtf8().data());
    return retQ_;
};

///
//  Load directory cache; returns a hash table containing the SHA-1 digests
//  of every file in the tree. The cache is saved between runs in .cache.
QmlZhash *QmlZdir::cache () {
    QmlZhash *retQ_ = new QmlZhash ();
    retQ_->self = zdir_cache (self);
    return retQ_;
};

///
//  Print contents of directory to open stream
void QmlZdir::fprint (FILE *file, int indent) {
    zdir_fprint (self, file, indent);
};

///
//  Print contents of directory to stdout
void QmlZdir::print (int indent) {
    zdir_print (self, indent);
};


QObject* QmlZdir::qmlAttachedProperties(QObject* object) {
    return new QmlZdirAttached(object);
}


///
//  Calculate differences between two versions of a directory tree.
//  Returns a list of zdir_patch_t patches. Either older or newer may
//  be null, indicating the directory is empty/absent. If alias is set,
//  generates virtual filename (minus path, plus alias).
QmlZlist *QmlZdirAttached::diff (QmlZdir *older, QmlZdir *newer, const QString &alias) {
    QmlZlist *retQ_ = new QmlZlist ();
    retQ_->self = zdir_diff (older->self, newer->self, alias.toUtf8().data());
    return retQ_;
};

///
//  Create a new zdir_watch actor instance:
//
//      zactor_t *watch = zactor_new (zdir_watch, NULL);
//
//  Destroy zdir_watch instance:
//
//      zactor_destroy (&watch);
//
//  Enable verbose logging of commands and activity:
//
//      zstr_send (watch, "VERBOSE");
//
//  Subscribe to changes to a directory path:
//
//      zsock_send (watch, "ss", "SUBSCRIBE", "directory_path");
//
//  Unsubscribe from changes to a directory path:
//
//      zsock_send (watch, "ss", "UNSUBSCRIBE", "directory_path");
//
//  Receive directory changes:
//      zsock_recv (watch, "sp", &path, &patches);
//
//      // Delete the received data.
//      free (path);
//      zlist_destroy (&patches);
void QmlZdirAttached::watch (QmlZsock *pipe, void *unused) {
    zdir_watch (pipe->self, unused);
};

///
//  Self test of this class.
void QmlZdirAttached::test (bool verbose) {
    zdir_test (verbose);
};

///
//  Create a new directory item that loads in the full tree of the specified
//  path, optionally located under some parent path. If parent is "-", then
//  loads only the top-level directory, and does not use parent as a path.
QmlZdir *QmlZdirAttached::construct (const QString &path, const QString &parent) {
    QmlZdir *qmlSelf = new QmlZdir ();
    qmlSelf->self = zdir_new (path.toUtf8().data(), parent.toUtf8().data());
    return qmlSelf;
};

///
//  Destroy a directory tree and all children it contains.
void QmlZdirAttached::destruct (QmlZdir *qmlSelf) {
    zdir_destroy (&qmlSelf->self);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
