<class name = "zstr" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    sending and receiving strings

    <method name = "recv" singleton = "1">
        Receive C string from socket. Caller must free returned string using
        zstr_free(). Returns NULL if the context is being terminated or the
        process was interrupted.
        <argument name = "source" type = "sockish" />
        <return type = "string" fresh = "1" />
    </method>

    <method name = "recvx" singleton = "1">
        Receive a series of strings (until NULL) from multipart data.
        Each string is allocated and filled with string data; if there
        are not enough frames, unallocated strings are set to NULL.
        Returns -1 if the message could not be read, else returns the
        number of strings filled, zero or more. Free each returned string
        using zstr_free(). If not enough strings are provided, remaining
        multipart frames in the message are dropped.
        <argument name = "source" type = "sockish" />
        <argument name = "string_p" type = "string" by_reference = "1" variadic = "1" />
        <return type = "integer" />
    </method>

    <method name = "recv compress" singleton = "1" state = "draft">
        De-compress and receive C string from socket, received as a message
        with two frames: size of the uncompressed string, and the string itself.
        Caller must free returned string using zstr_free(). Returns NULL if the
        context is being terminated or the process was interrupted.
        <argument name = "source" type = "sockish" />
        <return type = "string" fresh = "1" />
    </method>

    <method name = "send" singleton = "1">
        Send a C string to a socket, as a frame. The string is sent without
        trailing null byte; to read this you can use zstr_recv, or a similar
        method that adds a null terminator on the received string. String
        may be NULL, which is sent as "".
        <argument name = "dest" type = "sockish" />
        <argument name = "string" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "sendm" singleton = "1">
        Send a C string to a socket, as zstr_send(), with a MORE flag, so that
        you can send further strings in the same multi-part message.
        <argument name = "dest" type = "sockish" />
        <argument name = "string" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "sendf" singleton = "1">
        Send a formatted string to a socket. Note that you should NOT use
        user-supplied strings in the format (they may contain '%' which
        will create security holes).
        <argument name = "dest" type = "sockish" />
        <argument name = "format" type = "format" />
        <return type = "integer" />
    </method>

    <method  name = "sendfm" singleton = "1">
        Send a formatted string to a socket, as for zstr_sendf(), with a
        MORE flag, so that you can send further strings in the same multi-part
        message.
        <argument name = "dest" type = "sockish" />
        <argument name = "format" type = "format" />
        <return type = "integer" />
    </method>

    <method name = "sendx" singleton = "1">
        Send a series of strings (until NULL) as multipart data
        Returns 0 if the strings could be sent OK, or -1 on error.
        <argument name = "dest" type = "sockish" />
        <argument name = "string" type = "string" variadic = "1" />
        <return type = "integer" />
    </method>

    <method name = "send compress" singleton = "1" state = "draft">
        Compress and send a C string to a socket, as a message with two frames:
        size of the uncompressed string, and the string itself. The string is
        sent without trailing null byte; to read this you can use
        zstr_recv_compress, or a similar method that de-compresses and adds a
        null terminator on the received string.
        <argument name = "dest" type = "sockish" />
        <argument name = "string" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "sendm compress" singleton = "1" state = "draft">
        Compress and send a C string to a socket, as zstr_send_compress(),
        with a MORE flag, so that you can send further strings in the same
        multi-part message.
        <argument name = "dest" type = "sockish" />
        <argument name = "string" type = "string" />
        <return type = "integer" />
    </method>

    <method name = "str" singleton = "1" state = "draft" >
        Accepts a void pointer and returns a fresh character string. If source
        is null, returns an empty string.
        <argument name = "source" type = "sockish" />
        <return type = "string" fresh = "1" />
    </method>

    <method name = "free" singleton = "1">
        Free a provided string, and nullify the parent pointer. Safe to call on
        a null pointer.
        <argument name = "string_p" type = "string" by_reference = "1" />
    </method>
</class>
