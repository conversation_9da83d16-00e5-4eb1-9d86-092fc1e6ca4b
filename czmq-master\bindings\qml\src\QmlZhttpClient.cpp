/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZhttpClient.h"



QObject* QmlZhttpClient::qmlAttachedProperties(QObject* object) {
    return new QmlZhttpClientAttached(object);
}


///
//  Self test of this class.
void QmlZhttpClientAttached::test (bool verbose) {
    zhttp_client_test (verbose);
};

///
//  Create a new http client
QmlZhttpClient *QmlZhttpClientAttached::construct (bool verbose) {
    QmlZhttpClient *qmlSelf = new QmlZhttpClient ();
    qmlSelf->self = zhttp_client_new (verbose);
    return qmlSelf;
};

///
//  Destroy an http client
void QmlZhttpClientAttached::destruct (QmlZhttpClient *qmlSelf) {
    zhttp_client_destroy (&qmlSelf->self);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
