################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################

module CZMQ
  module FFI

    # Platform independent command line argument parsing helpers
    #
    # There are two kind of elements provided by this class
    # Named parameters, accessed by param_get and param_has methods
    #   * --named-parameter
    #   * --parameter with_value
    #   * -a val
    # Positional arguments, accessed by zargs_first, zargs_next
    #
    # It DOES:
    # * provide easy to use CLASS compatible API for accessing argv
    # * is platform independent
    # * provide getopt_long style -- argument, which delimits parameters from arguments
    # * makes parameters position independent
    #
    # It does NOT
    # * change argv
    # * provide a "declarative" way to define command line interface
    #
    # In future it SHALL
    # * hide several formats of command line to one (-Idir, --include=dir,
    #   --include dir are the same from API pov)
    # @note This class is 100% generated using zproject.
    class Zargs
      # Raised when one tries to use an instance of {Zargs} after
      # the internal pointer to the native object has been nullified.
      class DestroyedError < RuntimeError; end

      # Boilerplate for self pointer, initializer, and finalizer
      class << self
        alias :__new :new
      end
      # Attaches the pointer _ptr_ to this instance and defines a finalizer for
      # it if necessary.
      # @param ptr [::FFI::Pointer]
      # @param finalize [Boolean]
      def initialize(ptr, finalize = true)
        @ptr = ptr
        if @ptr.null?
          @ptr = nil # Remove null pointers so we don't have to test for them.
        elsif finalize
          @finalizer = self.class.create_finalizer_for @ptr
          ObjectSpace.define_finalizer self, @finalizer
        end
      end
      # @param ptr [::FFI::Pointer]
      # @return [Proc]
      def self.create_finalizer_for(ptr)
        ptr_ptr = ::FFI::MemoryPointer.new :pointer

        Proc.new do
          ptr_ptr.write_pointer ptr
          ::CZMQ::FFI.zargs_destroy ptr_ptr
        end
      end
      # @return [Boolean]
      def null?
        !@ptr or @ptr.null?
      end
      # Return internal pointer
      # @return [::FFI::Pointer]
      def __ptr
        raise DestroyedError unless @ptr
        @ptr
      end
      # So external Libraries can just pass the Object to a FFI function which expects a :pointer
      alias_method :to_ptr, :__ptr
      # Nullify internal pointer and return pointer pointer.
      # @note This detaches the current instance from the native object
      #   and thus makes it unusable.
      # @return [::FFI::MemoryPointer] the pointer pointing to a pointer
      #   pointing to the native object
      def __ptr_give_ref
        raise DestroyedError unless @ptr
        ptr_ptr = ::FFI::MemoryPointer.new :pointer
        ptr_ptr.write_pointer @ptr
        __undef_finalizer if @finalizer
        @ptr = nil
        ptr_ptr
      end
      # Undefines the finalizer for this object.
      # @note Only use this if you need to and can guarantee that the native
      #   object will be freed by other means.
      # @return [void]
      def __undef_finalizer
        ObjectSpace.undefine_finalizer self
        @finalizer = nil
      end

      # Create a new zargs from command line arguments.
      # @param argc [Integer, #to_int, #to_i]
      # @param argv [::FFI::Pointer, #to_ptr]
      # @return [CZMQ::Zargs]
      def self.new(argc, argv)
        argc = Integer(argc)
        ptr = ::CZMQ::FFI.zargs_new(argc, argv)
        __new ptr
      end

      # Destroy zargs instance.
      #
      # @return [void]
      def destroy()
        return unless @ptr
        self_p = __ptr_give_ref
        result = ::CZMQ::FFI.zargs_destroy(self_p)
        result
      end

      # Return program name (argv[0])
      #
      # @return [String]
      def progname()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zargs_progname(self_p)
        result
      end

      # Return number of positional arguments
      #
      # @return [Integer]
      def arguments()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zargs_arguments(self_p)
        result
      end

      # Return first positional argument or NULL
      #
      # @return [String]
      def first()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zargs_first(self_p)
        result
      end

      # Return next positional argument or NULL
      #
      # @return [String]
      def next()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zargs_next(self_p)
        result
      end

      # Return first named parameter value, or NULL if there are no named
      # parameters, or value for which zargs_param_empty (arg) returns true.
      #
      # @return [String]
      def param_first()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zargs_param_first(self_p)
        result
      end

      # Return next named parameter value, or NULL if there are no named
      # parameters, or value for which zargs_param_empty (arg) returns true.
      #
      # @return [String]
      def param_next()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zargs_param_next(self_p)
        result
      end

      # Return current parameter name, or NULL if there are no named parameters.
      #
      # @return [String]
      def param_name()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zargs_param_name(self_p)
        result
      end

      # Return value of named parameter or NULL is it has no value (or was not specified)
      #
      # @param name [String, #to_s, nil]
      # @return [String]
      def get(name)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zargs_get(self_p, name)
        result
      end

      # Return value of one of parameter(s) or NULL is it has no value (or was not specified)
      #
      # @param name [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [String]
      def getx(name, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zargs_getx(self_p, name, *args)
        result
      end

      # Returns true if named parameter was specified on command line
      #
      # @param name [String, #to_s, nil]
      # @return [Boolean]
      def has(name)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zargs_has(self_p, name)
        result
      end

      # Returns true if named parameter(s) was specified on command line
      #
      # @param name [String, #to_s, nil]
      # @param args [Array<Object>] see https://github.com/ffi/ffi/wiki/examples#using-varargs
      # @return [Boolean]
      def hasx(name, *args)
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zargs_hasx(self_p, name, *args)
        result
      end

      # Print an instance of zargs.
      #
      # @return [void]
      def print()
        raise DestroyedError unless @ptr
        self_p = @ptr
        result = ::CZMQ::FFI.zargs_print(self_p)
        result
      end

      # Self test of this class.
      #
      # @param verbose [Boolean]
      # @return [void]
      def self.test(verbose)
        verbose = !(0==verbose||!verbose) # boolean
        result = ::CZMQ::FFI.zargs_test(verbose)
        result
      end
    end
  end
end

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
