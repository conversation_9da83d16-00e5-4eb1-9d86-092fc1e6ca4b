<class name = "ztimerset" state = "draft">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    timer set

    <constructor>
        Create new timer set.
    </constructor>

    <destructor>
        Destroy a timer set
    </destructor>

    <callback_type name="fn">
        Callback function for timer event.
        <argument name = "timer_id" type = "integer" />
        <argument name = "arg" type = "anything" />
    </callback_type>

    <method name = "add">
        Add a timer to the set. Returns timer id if OK, -1 on failure.
        <argument name = "interval" type = "size" />
        <argument name = "handler" type = "ztimerset_fn" callback="1" />
        <argument name = "arg" type = "anything" />
        <return type = "integer" />
    </method>

    <method name = "cancel">
        Cancel a timer. Returns 0 if OK, -1 on failure.
        <argument name = "timer id" type = "integer" />
        <return type = "integer" />
    </method>

    <method name = "set interval">
        Set timer interval. Returns 0 if OK, -1 on failure.
        This method is slow, canceling the timer and adding a new one yield better performance.
        <argument name = "timer id" type = "integer" />
        <argument name = "interval" type = "size" />
        <return type = "integer" />
    </method>

    <method name = "reset">
        Reset timer to start interval counting from current time. Returns 0 if OK, -1 on failure.
        This method is slow, canceling the timer and adding a new one yield better performance.
        <argument name = "timer id" type = "integer" />
        <return type = "integer" />
    </method>

    <method name = "timeout">
        Return the time until the next interval.
        Should be used as timeout parameter for the zpoller wait method.
        The timeout is in msec.
        <return type = "integer" />
    </method>

    <method name = "execute">
        Invoke callback function of all timers which their interval has elapsed.
        Should be call after zpoller wait method.
        Returns 0 if OK, -1 on failure.
        <return type = "integer" />
    </method>
</class>
