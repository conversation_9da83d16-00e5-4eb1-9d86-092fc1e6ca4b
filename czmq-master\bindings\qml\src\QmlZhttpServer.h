/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#ifndef QML_ZHTTP_SERVER_H
#define QML_ZHTTP_SERVER_H

#include <QtQml>

#include <czmq.h>
#include "qml_czmq_plugin.h"


class QmlZhttpServer : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isNULL READ isNULL)

public:
    zhttp_server_t *self;

    QmlZhttpServer() { self = NULL; }
    bool isNULL() { return self == NULL; }

    static QObject* qmlAttachedProperties(QObject* object); // defined in QmlZhttpServer.cpp

public slots:
    //  Return the port the server is listening on.
    int port ();
};

class QmlZhttpServerAttached : public QObject
{
    Q_OBJECT
    QObject* m_attached;

public:
    QmlZhttpServerAttached (QObject* attached) {
        Q_UNUSED (attached);
    };

public slots:
    //  Self test of this class.
    void test (bool verbose);

    //  Create a new http server
    QmlZhttpServer *construct (QmlZhttpServerOptions *options);

    //  Destroy an http server
    void destruct (QmlZhttpServer *qmlSelf);
};


QML_DECLARE_TYPEINFO(QmlZhttpServer, QML_HAS_ATTACHED_PROPERTIES)

#endif
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
