################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class Zuuid(object):
    """
    UUID support class
    """

    def __init__(self):
        """
        Create a new UUID object.
        """
        p = utils.lib.zuuid_new()
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zuuid_destroy_py)

    @staticmethod
    def new_from(source):
        """
        Create UUID object from supplied ZUUID_LEN-octet value.
        """
        return utils.lib.zuuid_new_from(source)

    def set(self, source):
        """
        Set UUID to new supplied ZUUID_LEN-octet value.
        """
        utils.lib.zuuid_set(self._p, source)

    def set_str(self, source):
        """
        Set UUID to new supplied string value skipping '-' and '{' '}'
        optional delimiters. Return 0 if OK, else returns -1.
        """
        return utils.lib.zuuid_set_str(self._p, utils.to_bytes(source))

    def data(self):
        """
        Return UUID binary data.
        """
        return utils.lib.zuuid_data(self._p)

    def size(self):
        """
        Return UUID binary size
        """
        return utils.lib.zuuid_size(self._p)

    def str(self):
        """
        Returns UUID as string
        """
        return utils.lib.zuuid_str(self._p)

    def str_canonical(self):
        """
        Return UUID in the canonical string format: 8-4-4-4-12, in lower
        case. Caller does not modify or free returned value. See
        http://en.wikipedia.org/wiki/Universally_unique_identifier
        """
        return utils.lib.zuuid_str_canonical(self._p)

    def export(self, target):
        """
        Store UUID blob in target array
        """
        utils.lib.zuuid_export(self._p, target)

    def eq(self, compare):
        """
        Check if UUID is same as supplied value
        """
        return utils.lib.zuuid_eq(self._p, compare)

    def neq(self, compare):
        """
        Check if UUID is different from supplied value
        """
        return utils.lib.zuuid_neq(self._p, compare)

    def dup(self):
        """
        Make copy of UUID object; if uuid is null, or memory was exhausted,
        returns null.
        """
        return utils.lib.zuuid_dup(self._p)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zuuid_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
