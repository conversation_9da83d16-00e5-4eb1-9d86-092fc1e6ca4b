/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZhttpRequest::QZhttpRequest (zhttp_request_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  Create a new http request.
QZhttpRequest::QZhttpRequest (QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zhttp_request_new ();
}

///
//  Destroy an http request.
QZhttpRequest::~QZhttpRequest ()
{
    zhttp_request_destroy (&self);
}

///
//  Receive a new request from zhttp_server.
//  Return the underlying connection if successful, to be used when calling zhttp_response_send.
void * QZhttpRequest::recv (QZsock *sock)
{
    void * rv = zhttp_request_recv (self, sock->self);
    return rv;
}

///
//  Send a request to zhttp_client.
//  Url and the request path will be concatenated.
//  This behavior is useful for url rewrite and reverse proxy.
//
//  Send also allow two user provided arguments which will be returned with the response.
//  The reason for two, is to be able to pass around the server connection when forwarding requests or both a callback function and an arg.
int QZhttpRequest::send (QZhttpClient *client, int timeout, void *arg, void *arg2)
{
    int rv = zhttp_request_send (self, client->self, timeout, arg, arg2);
    return rv;
}

///
//  Get the request method
const QString QZhttpRequest::method ()
{
    const QString rv = QString (zhttp_request_method (self));
    return rv;
}

///
//  Set the request method
void QZhttpRequest::setMethod (const QString &method)
{
    zhttp_request_set_method (self, method.toUtf8().data());

}

///
//  Get the request url.
//  When receiving a request from http server this is only the path part of the url.
const QString QZhttpRequest::url ()
{
    const QString rv = QString (zhttp_request_url (self));
    return rv;
}

///
//  Set the request url
//  When sending a request to http client this should be full url.
void QZhttpRequest::setUrl (const QString &url)
{
    zhttp_request_set_url (self, url.toUtf8().data());

}

///
//  Get the request content type
const QString QZhttpRequest::contentType ()
{
    const QString rv = QString (zhttp_request_content_type (self));
    return rv;
}

///
//  Set the request content type
void QZhttpRequest::setContentType (const QString &contentType)
{
    zhttp_request_set_content_type (self, contentType.toUtf8().data());

}

///
//  Get the content length of the request
size_t QZhttpRequest::contentLength ()
{
    size_t rv = zhttp_request_content_length (self);
    return rv;
}

///
//  Get the headers of the request
QZhash * QZhttpRequest::headers ()
{
    QZhash *rv = new QZhash (zhttp_request_headers (self));
    return rv;
}

///
//  Get the content of the request.
const QString QZhttpRequest::content ()
{
    const QString rv = QString (zhttp_request_content (self));
    return rv;
}

///
//  Get the content of the request.
QString QZhttpRequest::getContent ()
{
    char *retStr_ = zhttp_request_get_content (self);
    QString rv = QString (retStr_);
    zstr_free (&retStr_);
    return rv;
}

///
//  Set the content of the request..
//  The content is assumed to be constant-memory and will therefore not be copied or deallocated in any way.
void QZhttpRequest::setContentConst (const QString &content)
{
    zhttp_request_set_content_const (self, content.toUtf8().data());

}

///
//  Set the content to NULL
void QZhttpRequest::resetContent ()
{
    zhttp_request_reset_content (self);

}

///
//  Set the request username
void QZhttpRequest::setUsername (const QString &username)
{
    zhttp_request_set_username (self, username.toUtf8().data());

}

///
//  Set the request password
void QZhttpRequest::setPassword (const QString &password)
{
    zhttp_request_set_password (self, password.toUtf8().data());

}

///
//  Self test of this class.
void QZhttpRequest::test (bool verbose)
{
    zhttp_request_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
