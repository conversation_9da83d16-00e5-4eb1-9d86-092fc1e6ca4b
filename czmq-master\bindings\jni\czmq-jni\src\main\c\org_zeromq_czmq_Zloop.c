/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#include <stdio.h>
#include <stdlib.h>
#include <jni.h>
#include "czmq.h"
#include "org_zeromq_czmq_Zloop.h"

JNIEXPORT jlong JNICALL
Java_org_zeromq_czmq_Zloop__1_1new (JNIEnv *env, jclass c)
{
    //  Disable CZMQ signal handling; allow Java to deal with it
    zsys_handler_set (NULL);
    jlong new_ = (jlong) (intptr_t) zloop_new ();
    return new_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zloop__1_1destroy (JNIEnv *env, jclass c, jlong self)
{
    zloop_destroy ((zloop_t **) &self);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zloop__1_1readerEnd (JNIEnv *env, jclass c, jlong self, jlong sock)
{
    zloop_reader_end ((zloop_t *) (intptr_t) self, (zsock_t *) (intptr_t) sock);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zloop__1_1readerSetTolerant (JNIEnv *env, jclass c, jlong self, jlong sock)
{
    zloop_reader_set_tolerant ((zloop_t *) (intptr_t) self, (zsock_t *) (intptr_t) sock);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zloop__1_1timerEnd (JNIEnv *env, jclass c, jlong self, jint timer_id)
{
    jint timer_end_ = (jint) zloop_timer_end ((zloop_t *) (intptr_t) self, (int) timer_id);
    return timer_end_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zloop__1_1ticketReset (JNIEnv *env, jclass c, jlong self, jlong handle)
{
    zloop_ticket_reset ((zloop_t *) (intptr_t) self, (void *) (intptr_t) handle);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zloop__1_1ticketDelete (JNIEnv *env, jclass c, jlong self, jlong handle)
{
    zloop_ticket_delete ((zloop_t *) (intptr_t) self, (void *) (intptr_t) handle);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zloop__1_1setTicketDelay (JNIEnv *env, jclass c, jlong self, jlong ticket_delay)
{
    zloop_set_ticket_delay ((zloop_t *) (intptr_t) self, (size_t) ticket_delay);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zloop__1_1setMaxTimers (JNIEnv *env, jclass c, jlong self, jlong max_timers)
{
    zloop_set_max_timers ((zloop_t *) (intptr_t) self, (size_t) max_timers);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zloop__1_1setVerbose (JNIEnv *env, jclass c, jlong self, jboolean verbose)
{
    zloop_set_verbose ((zloop_t *) (intptr_t) self, (bool) verbose);
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zloop__1_1setNonstop (JNIEnv *env, jclass c, jlong self, jboolean nonstop)
{
    zloop_set_nonstop ((zloop_t *) (intptr_t) self, (bool) nonstop);
}

JNIEXPORT jint JNICALL
Java_org_zeromq_czmq_Zloop__1_1start (JNIEnv *env, jclass c, jlong self)
{
    jint start_ = (jint) zloop_start ((zloop_t *) (intptr_t) self);
    return start_;
}

JNIEXPORT void JNICALL
Java_org_zeromq_czmq_Zloop__1_1test (JNIEnv *env, jclass c, jboolean verbose)
{
    zloop_test ((bool) verbose);
}

