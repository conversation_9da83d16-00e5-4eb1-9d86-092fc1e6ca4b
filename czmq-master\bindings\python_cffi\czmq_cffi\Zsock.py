################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
from . import utils
from . import destructors
libczmq_destructors = destructors.lib

class Zsock(object):
    """
    high-level socket API that hides libzmq contexts and sockets
    """

    def __init__(self, type):
        """
        Create a new socket. Returns the new socket, or NULL if the new socket
        could not be created. Note that the symbol zsock_new (and other
        constructors/destructors for zsock) are redirected to the *_checked
        variant, enabling intelligent socket leak detection. This can have
        performance implications if you use a LOT of sockets. To turn off this
        redirection behaviour, define ZSOCK_NOCHECK.
        """
        p = utils.lib.zsock_new(type)
        if p == utils.ffi.NULL:
            raise MemoryError("Could not allocate person")

        # ffi.gc returns a copy of the cdata object which will have the
        # destructor called when the Python object is GC'd:
        # https://cffi.readthedocs.org/en/latest/using.html#ffi-interface
        self._p = utils.ffi.gc(p, libczmq_destructors.zsock_destroy_py)

    @staticmethod
    def new_pub(endpoint):
        """
        Create a PUB socket. Default action is bind.
        """
        return utils.lib.zsock_new_pub(utils.to_bytes(endpoint))

    @staticmethod
    def new_sub(endpoint, subscribe):
        """
        Create a SUB socket, and optionally subscribe to some prefix string. Default
        action is connect.
        """
        return utils.lib.zsock_new_sub(utils.to_bytes(endpoint), utils.to_bytes(subscribe))

    @staticmethod
    def new_req(endpoint):
        """
        Create a REQ socket. Default action is connect.
        """
        return utils.lib.zsock_new_req(utils.to_bytes(endpoint))

    @staticmethod
    def new_rep(endpoint):
        """
        Create a REP socket. Default action is bind.
        """
        return utils.lib.zsock_new_rep(utils.to_bytes(endpoint))

    @staticmethod
    def new_dealer(endpoint):
        """
        Create a DEALER socket. Default action is connect.
        """
        return utils.lib.zsock_new_dealer(utils.to_bytes(endpoint))

    @staticmethod
    def new_router(endpoint):
        """
        Create a ROUTER socket. Default action is bind.
        """
        return utils.lib.zsock_new_router(utils.to_bytes(endpoint))

    @staticmethod
    def new_push(endpoint):
        """
        Create a PUSH socket. Default action is connect.
        """
        return utils.lib.zsock_new_push(utils.to_bytes(endpoint))

    @staticmethod
    def new_pull(endpoint):
        """
        Create a PULL socket. Default action is bind.
        """
        return utils.lib.zsock_new_pull(utils.to_bytes(endpoint))

    @staticmethod
    def new_xpub(endpoint):
        """
        Create an XPUB socket. Default action is bind.
        """
        return utils.lib.zsock_new_xpub(utils.to_bytes(endpoint))

    @staticmethod
    def new_xsub(endpoint):
        """
        Create an XSUB socket. Default action is connect.
        """
        return utils.lib.zsock_new_xsub(utils.to_bytes(endpoint))

    @staticmethod
    def new_pair(endpoint):
        """
        Create a PAIR socket. Default action is connect.
        """
        return utils.lib.zsock_new_pair(utils.to_bytes(endpoint))

    @staticmethod
    def new_stream(endpoint):
        """
        Create a STREAM socket. Default action is connect.
        """
        return utils.lib.zsock_new_stream(utils.to_bytes(endpoint))

    @staticmethod
    def new_server(endpoint):
        """
        Create a SERVER socket. Default action is bind.
        """
        return utils.lib.zsock_new_server(utils.to_bytes(endpoint))

    @staticmethod
    def new_client(endpoint):
        """
        Create a CLIENT socket. Default action is connect.
        """
        return utils.lib.zsock_new_client(utils.to_bytes(endpoint))

    @staticmethod
    def new_radio(endpoint):
        """
        Create a RADIO socket. Default action is bind.
        """
        return utils.lib.zsock_new_radio(utils.to_bytes(endpoint))

    @staticmethod
    def new_dish(endpoint):
        """
        Create a DISH socket. Default action is connect.
        """
        return utils.lib.zsock_new_dish(utils.to_bytes(endpoint))

    @staticmethod
    def new_gather(endpoint):
        """
        Create a GATHER socket. Default action is bind.
        """
        return utils.lib.zsock_new_gather(utils.to_bytes(endpoint))

    @staticmethod
    def new_scatter(endpoint):
        """
        Create a SCATTER socket. Default action is connect.
        """
        return utils.lib.zsock_new_scatter(utils.to_bytes(endpoint))

    @staticmethod
    def new_dgram(endpoint):
        """
        Create a DGRAM (UDP) socket. Default action is bind.
        The endpoint is a string consisting of a
        'transport'`://` followed by an 'address'. As this is
        a UDP socket the 'transport' has to be 'udp'. The
        'address' specifies the ip address and port to
        bind to. For example:  udp://127.0.0.1:1234
        Note: To send to an endpoint over UDP you have to
        send a message with the destination endpoint address
        as a first message!
        """
        return utils.lib.zsock_new_dgram(utils.to_bytes(endpoint))

    def bind(self, format, *format_args):
        """
        Bind a socket to a formatted endpoint. For tcp:// endpoints, supports
        ephemeral ports, if you specify the port number as "*". By default
        zsock uses the IANA designated range from C000 (49152) to FFFF (65535).
        To override this range, follow the "*" with "[first-last]". Either or
        both first and last may be empty. To bind to a random port within the
        range, use "!" in place of "*".

        Examples:
            tcp://127.0.0.1:*           bind to first free port from C000 up
            tcp://127.0.0.1:!           bind to random port from C000 to FFFF
            tcp://127.0.0.1:*[60000-]   bind to first free port from 60000 up
            tcp://127.0.0.1:![-60000]   bind to random port from C000 to 60000
            tcp://127.0.0.1:![55000-55999]
                                        bind to random port from 55000 to 55999

        On success, returns the actual port number used, for tcp:// endpoints,
        and 0 for other transports. On failure, returns -1. Note that when using
        ephemeral ports, a port may be reused by different services without
        clients being aware. Protocols that run on ephemeral ports should take
        this into account.
        """
        return utils.lib.zsock_bind(self._p, format, *format_args)

    def endpoint(self):
        """
        Returns last bound endpoint, if any.
        """
        return utils.lib.zsock_endpoint(self._p)

    def unbind(self, format, *format_args):
        """
        Unbind a socket from a formatted endpoint.
        Returns 0 if OK, -1 if the endpoint was invalid or the function
        isn't supported.
        """
        return utils.lib.zsock_unbind(self._p, format, *format_args)

    def connect(self, format, *format_args):
        """
        Connect a socket to a formatted endpoint
        Returns 0 if OK, -1 if the endpoint was invalid.
        """
        return utils.lib.zsock_connect(self._p, format, *format_args)

    def disconnect(self, format, *format_args):
        """
        Disconnect a socket from a formatted endpoint
        Returns 0 if OK, -1 if the endpoint was invalid or the function
        isn't supported.
        """
        return utils.lib.zsock_disconnect(self._p, format, *format_args)

    def attach(self, endpoints, serverish):
        """
        Attach a socket to zero or more endpoints. If endpoints is not null,
        parses as list of ZeroMQ endpoints, separated by commas, and prefixed by
        '@' (to bind the socket) or '>' (to connect the socket). Returns 0 if all
        endpoints were valid, or -1 if there was a syntax error. If the endpoint
        does not start with '@' or '>', the serverish argument defines whether
        it is used to bind (serverish = true) or connect (serverish = false).
        """
        return utils.lib.zsock_attach(self._p, utils.to_bytes(endpoints), serverish)

    def type_str(self):
        """
        Returns socket type as printable constant string.
        """
        return utils.lib.zsock_type_str(self._p)

    def send(self, picture, *picture_args):
        """
        Send a 'picture' message to the socket (or actor). The picture is a
        string that defines the type of each frame. This makes it easy to send
        a complex multiframe message in one call. The picture can contain any
        of these characters, each corresponding to one or two arguments:

            i = int (signed)
            1 = uint8_t
            2 = uint16_t
            4 = uint32_t
            8 = uint64_t
            s = char *
            b = byte *, size_t (2 arguments)
            c = zchunk_t *
            f = zframe_t *
            h = zhashx_t *
            l = zlistx_t * (DRAFT)
            U = zuuid_t *
            p = void * (sends the pointer value, only meaningful over inproc)
            m = zmsg_t * (sends all frames in the zmsg)
            z = sends zero-sized frame (0 arguments)
            u = uint (deprecated)

        Note that s, b, c, and f are encoded the same way and the choice is
        offered as a convenience to the sender, which may or may not already
        have data in a zchunk or zframe. Does not change or take ownership of
        any arguments. Returns 0 if successful, -1 if sending failed for any
        reason.
        """
        return utils.lib.zsock_send(self._p, utils.to_bytes(picture), *picture_args)

    def vsend(self, picture, argptr):
        """
        Send a 'picture' message to the socket (or actor). This is a va_list
        version of zsock_send (), so please consult its documentation for the
        details.
        """
        return utils.lib.zsock_vsend(self._p, utils.to_bytes(picture), argptr._p)

    def recv(self, picture, *picture_args):
        """
        Receive a 'picture' message to the socket (or actor). See zsock_send for
        the format and meaning of the picture. Returns the picture elements into
        a series of pointers as provided by the caller:

            i = int * (stores signed integer)
            4 = uint32_t * (stores 32-bit unsigned integer)
            8 = uint64_t * (stores 64-bit unsigned integer)
            s = char ** (allocates new string)
            b = byte **, size_t * (2 arguments) (allocates memory)
            c = zchunk_t ** (creates zchunk)
            f = zframe_t ** (creates zframe)
            U = zuuid_t * (creates a zuuid with the data)
            h = zhashx_t ** (creates zhashx)
            l = zlistx_t ** (creates zlistx) (DRAFT)
            p = void ** (stores pointer)
            m = zmsg_t ** (creates a zmsg with the remaining frames)
            z = null, asserts empty frame (0 arguments)
            u = uint * (stores unsigned integer, deprecated)

        Note that zsock_recv creates the returned objects, and the caller must
        destroy them when finished with them. The supplied pointers do not need
        to be initialized. Returns 0 if successful, or -1 if it failed to recv
        a message, in which case the pointers are not modified. When message
        frames are truncated (a short message), sets return values to zero/null.
        If an argument pointer is NULL, does not store any value (skips it).
        An 'n' picture matches an empty frame; if the message does not match,
        the method will return -1.
        """
        return utils.lib.zsock_recv(self._p, utils.to_bytes(picture), *picture_args)

    def vrecv(self, picture, argptr):
        """
        Receive a 'picture' message from the socket (or actor). This is a
        va_list version of zsock_recv (), so please consult its documentation
        for the details.
        """
        return utils.lib.zsock_vrecv(self._p, utils.to_bytes(picture), argptr._p)

    def bsend(self, picture, *picture_args):
        """
        Send a binary encoded 'picture' message to the socket (or actor). This
        method is similar to zsock_send, except the arguments are encoded in a
        binary format that is compatible with zproto, and is designed to reduce
        memory allocations. The pattern argument is a string that defines the
        type of each argument. Supports these argument types:

         pattern    C type                  zproto type:
            1       uint8_t                 type = "number" size = "1"
            2       uint16_t                type = "number" size = "2"
            4       uint32_t                type = "number" size = "3"
            8       uint64_t                type = "number" size = "4"
            s       char *, 0-255 chars     type = "string"
            S       char *, 0-2^32-1 chars  type = "longstr"
            c       zchunk_t *              type = "chunk"
            f       zframe_t *              type = "frame"
            u       zuuid_t *               type = "uuid"
            m       zmsg_t *                type = "msg"
            p       void *, sends pointer value, only over inproc

        Does not change or take ownership of any arguments. Returns 0 if
        successful, -1 if sending failed for any reason.
        """
        return utils.lib.zsock_bsend(self._p, utils.to_bytes(picture), *picture_args)

    def brecv(self, picture, *picture_args):
        """
        Receive a binary encoded 'picture' message from the socket (or actor).
        This method is similar to zsock_recv, except the arguments are encoded
        in a binary format that is compatible with zproto, and is designed to
        reduce memory allocations. The pattern argument is a string that defines
        the type of each argument. See zsock_bsend for the supported argument
        types. All arguments must be pointers; this call sets them to point to
        values held on a per-socket basis.
        For types 1, 2, 4 and 8 the caller must allocate the memory itself before
        calling zsock_brecv.
        For types S, the caller must free the value once finished with it, as
        zsock_brecv will allocate the buffer.
        For type s, the caller must not free the value as it is stored in a
        local cache for performance purposes.
        For types c, f, u and m the caller must call the appropriate destructor
        depending on the object as zsock_brecv will create new objects.
        For type p the caller must coordinate with the sender, as it is just a
        pointer value being passed.
        """
        return utils.lib.zsock_brecv(self._p, utils.to_bytes(picture), *picture_args)

    def routing_id(self):
        """
        Return socket routing ID if any. This returns 0 if the socket is not
        of type ZMQ_SERVER or if no request was already received on it.
        """
        return utils.lib.zsock_routing_id(self._p)

    def set_routing_id(self, routing_id):
        """
        Set routing ID on socket. The socket MUST be of type ZMQ_SERVER.
        This will be used when sending messages on the socket via the zsock API.
        """
        utils.lib.zsock_set_routing_id(self._p, routing_id)

    def set_unbounded(self):
        """
        Set socket to use unbounded pipes (HWM=0); use this in cases when you are
        totally certain the message volume can fit in memory. This method works
        across all versions of ZeroMQ. Takes a polymorphic socket reference.
        """
        utils.lib.zsock_set_unbounded(self._p)

    def signal(self, status):
        """
        Send a signal over a socket. A signal is a short message carrying a
        success/failure code (by convention, 0 means OK). Signals are encoded
        to be distinguishable from "normal" messages. Accepts a zsock_t or a
        zactor_t argument, and returns 0 if successful, -1 if the signal could
        not be sent. Takes a polymorphic socket reference.
        """
        return utils.lib.zsock_signal(self._p, status)

    def wait(self):
        """
        Wait on a signal. Use this to coordinate between threads, over pipe
        pairs. Blocks until the signal is received. Returns -1 on error, 0 or
        greater on success. Accepts a zsock_t or a zactor_t as argument.
        Takes a polymorphic socket reference.
        """
        return utils.lib.zsock_wait(self._p)

    def flush(self):
        """
        If there is a partial message still waiting on the socket, remove and
        discard it. This is useful when reading partial messages, to get specific
        message types.
        """
        utils.lib.zsock_flush(self._p)

    def join(self, group):
        """
        Join a group for the RADIO-DISH pattern. Call only on ZMQ_DISH.
        Returns 0 if OK, -1 if failed.
        """
        return utils.lib.zsock_join(self._p, utils.to_bytes(group))

    def leave(self, group):
        """
        Leave a group for the RADIO-DISH pattern. Call only on ZMQ_DISH.
        Returns 0 if OK, -1 if failed.
        """
        return utils.lib.zsock_leave(self._p, utils.to_bytes(group))

    @staticmethod
    def is_py(self):
        """
        Probe the supplied object, and report if it looks like a zsock_t.
        Takes a polymorphic socket reference.
        """
        return utils.lib.zsock_is(self._p)

    @staticmethod
    def resolve(self):
        """
        Probe the supplied reference. If it looks like a zsock_t instance, return
        the underlying libzmq socket handle; else if it looks like a file
        descriptor, return NULL; else if it looks like a libzmq socket handle,
        return the supplied value. Takes a polymorphic socket reference.
        """
        return utils.lib.zsock_resolve(self._p)

    def has_in(self):
        """
        Check whether the socket has available message to read.
        """
        return utils.lib.zsock_has_in(self._p)

    def priority(self):
        """
        Get socket option `priority`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_priority(self._p)

    def set_priority(self, priority):
        """
        Set socket option `priority`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_priority(self._p, priority)

    def reconnect_stop(self):
        """
        Get socket option `reconnect_stop`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_reconnect_stop(self._p)

    def set_reconnect_stop(self, reconnect_stop):
        """
        Set socket option `reconnect_stop`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_reconnect_stop(self._p, reconnect_stop)

    def set_only_first_subscribe(self, only_first_subscribe):
        """
        Set socket option `only_first_subscribe`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_only_first_subscribe(self._p, only_first_subscribe)

    def set_hello_msg(self, hello_msg):
        """
        Set socket option `hello_msg`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_hello_msg(self._p, hello_msg._p)

    def set_disconnect_msg(self, disconnect_msg):
        """
        Set socket option `disconnect_msg`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_disconnect_msg(self._p, disconnect_msg._p)

    def set_wss_trust_system(self, wss_trust_system):
        """
        Set socket option `wss_trust_system`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_wss_trust_system(self._p, wss_trust_system)

    def set_wss_hostname(self, wss_hostname):
        """
        Set socket option `wss_hostname`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_wss_hostname(self._p, utils.to_bytes(wss_hostname))

    def set_wss_trust_pem(self, wss_trust_pem):
        """
        Set socket option `wss_trust_pem`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_wss_trust_pem(self._p, utils.to_bytes(wss_trust_pem))

    def set_wss_cert_pem(self, wss_cert_pem):
        """
        Set socket option `wss_cert_pem`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_wss_cert_pem(self._p, utils.to_bytes(wss_cert_pem))

    def set_wss_key_pem(self, wss_key_pem):
        """
        Set socket option `wss_key_pem`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_wss_key_pem(self._p, utils.to_bytes(wss_key_pem))

    def out_batch_size(self):
        """
        Get socket option `out_batch_size`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_out_batch_size(self._p)

    def set_out_batch_size(self, out_batch_size):
        """
        Set socket option `out_batch_size`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_out_batch_size(self._p, out_batch_size)

    def in_batch_size(self):
        """
        Get socket option `in_batch_size`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_in_batch_size(self._p)

    def set_in_batch_size(self, in_batch_size):
        """
        Set socket option `in_batch_size`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_in_batch_size(self._p, in_batch_size)

    def socks_password(self):
        """
        Get socket option `socks_password`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_socks_password(self._p)

    def set_socks_password(self, socks_password):
        """
        Set socket option `socks_password`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_socks_password(self._p, utils.to_bytes(socks_password))

    def socks_username(self):
        """
        Get socket option `socks_username`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_socks_username(self._p)

    def set_socks_username(self, socks_username):
        """
        Set socket option `socks_username`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_socks_username(self._p, utils.to_bytes(socks_username))

    def set_xpub_manual_last_value(self, xpub_manual_last_value):
        """
        Set socket option `xpub_manual_last_value`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_xpub_manual_last_value(self._p, xpub_manual_last_value)

    def router_notify(self):
        """
        Get socket option `router_notify`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_router_notify(self._p)

    def set_router_notify(self, router_notify):
        """
        Set socket option `router_notify`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_router_notify(self._p, router_notify)

    def multicast_loop(self):
        """
        Get socket option `multicast_loop`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_multicast_loop(self._p)

    def set_multicast_loop(self, multicast_loop):
        """
        Set socket option `multicast_loop`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_multicast_loop(self._p, multicast_loop)

    def metadata(self):
        """
        Get socket option `metadata`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_metadata(self._p)

    def set_metadata(self, metadata):
        """
        Set socket option `metadata`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_metadata(self._p, utils.to_bytes(metadata))

    def loopback_fastpath(self):
        """
        Get socket option `loopback_fastpath`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_loopback_fastpath(self._p)

    def set_loopback_fastpath(self, loopback_fastpath):
        """
        Set socket option `loopback_fastpath`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_loopback_fastpath(self._p, loopback_fastpath)

    def zap_enforce_domain(self):
        """
        Get socket option `zap_enforce_domain`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_zap_enforce_domain(self._p)

    def set_zap_enforce_domain(self, zap_enforce_domain):
        """
        Set socket option `zap_enforce_domain`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_zap_enforce_domain(self._p, zap_enforce_domain)

    def gssapi_principal_nametype(self):
        """
        Get socket option `gssapi_principal_nametype`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_gssapi_principal_nametype(self._p)

    def set_gssapi_principal_nametype(self, gssapi_principal_nametype):
        """
        Set socket option `gssapi_principal_nametype`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_gssapi_principal_nametype(self._p, gssapi_principal_nametype)

    def gssapi_service_principal_nametype(self):
        """
        Get socket option `gssapi_service_principal_nametype`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_gssapi_service_principal_nametype(self._p)

    def set_gssapi_service_principal_nametype(self, gssapi_service_principal_nametype):
        """
        Set socket option `gssapi_service_principal_nametype`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_gssapi_service_principal_nametype(self._p, gssapi_service_principal_nametype)

    def bindtodevice(self):
        """
        Get socket option `bindtodevice`.
        Available from libzmq 4.3.0.
        """
        return utils.lib.zsock_bindtodevice(self._p)

    def set_bindtodevice(self, bindtodevice):
        """
        Set socket option `bindtodevice`.
        Available from libzmq 4.3.0.
        """
        utils.lib.zsock_set_bindtodevice(self._p, utils.to_bytes(bindtodevice))

    def heartbeat_ivl(self):
        """
        Get socket option `heartbeat_ivl`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_heartbeat_ivl(self._p)

    def set_heartbeat_ivl(self, heartbeat_ivl):
        """
        Set socket option `heartbeat_ivl`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_heartbeat_ivl(self._p, heartbeat_ivl)

    def heartbeat_ttl(self):
        """
        Get socket option `heartbeat_ttl`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_heartbeat_ttl(self._p)

    def set_heartbeat_ttl(self, heartbeat_ttl):
        """
        Set socket option `heartbeat_ttl`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_heartbeat_ttl(self._p, heartbeat_ttl)

    def heartbeat_timeout(self):
        """
        Get socket option `heartbeat_timeout`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_heartbeat_timeout(self._p)

    def set_heartbeat_timeout(self, heartbeat_timeout):
        """
        Set socket option `heartbeat_timeout`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_heartbeat_timeout(self._p, heartbeat_timeout)

    def use_fd(self):
        """
        Get socket option `use_fd`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_use_fd(self._p)

    def set_use_fd(self, use_fd):
        """
        Set socket option `use_fd`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_use_fd(self._p, use_fd)

    def set_xpub_manual(self, xpub_manual):
        """
        Set socket option `xpub_manual`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_xpub_manual(self._p, xpub_manual)

    def set_xpub_welcome_msg(self, xpub_welcome_msg):
        """
        Set socket option `xpub_welcome_msg`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_xpub_welcome_msg(self._p, utils.to_bytes(xpub_welcome_msg))

    def set_stream_notify(self, stream_notify):
        """
        Set socket option `stream_notify`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_stream_notify(self._p, stream_notify)

    def invert_matching(self):
        """
        Get socket option `invert_matching`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_invert_matching(self._p)

    def set_invert_matching(self, invert_matching):
        """
        Set socket option `invert_matching`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_invert_matching(self._p, invert_matching)

    def set_xpub_verboser(self, xpub_verboser):
        """
        Set socket option `xpub_verboser`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_xpub_verboser(self._p, xpub_verboser)

    def connect_timeout(self):
        """
        Get socket option `connect_timeout`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_connect_timeout(self._p)

    def set_connect_timeout(self, connect_timeout):
        """
        Set socket option `connect_timeout`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_connect_timeout(self._p, connect_timeout)

    def tcp_maxrt(self):
        """
        Get socket option `tcp_maxrt`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_tcp_maxrt(self._p)

    def set_tcp_maxrt(self, tcp_maxrt):
        """
        Set socket option `tcp_maxrt`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_tcp_maxrt(self._p, tcp_maxrt)

    def thread_safe(self):
        """
        Get socket option `thread_safe`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_thread_safe(self._p)

    def multicast_maxtpdu(self):
        """
        Get socket option `multicast_maxtpdu`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_multicast_maxtpdu(self._p)

    def set_multicast_maxtpdu(self, multicast_maxtpdu):
        """
        Set socket option `multicast_maxtpdu`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_multicast_maxtpdu(self._p, multicast_maxtpdu)

    def vmci_buffer_size(self):
        """
        Get socket option `vmci_buffer_size`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_vmci_buffer_size(self._p)

    def set_vmci_buffer_size(self, vmci_buffer_size):
        """
        Set socket option `vmci_buffer_size`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_vmci_buffer_size(self._p, vmci_buffer_size)

    def vmci_buffer_min_size(self):
        """
        Get socket option `vmci_buffer_min_size`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_vmci_buffer_min_size(self._p)

    def set_vmci_buffer_min_size(self, vmci_buffer_min_size):
        """
        Set socket option `vmci_buffer_min_size`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_vmci_buffer_min_size(self._p, vmci_buffer_min_size)

    def vmci_buffer_max_size(self):
        """
        Get socket option `vmci_buffer_max_size`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_vmci_buffer_max_size(self._p)

    def set_vmci_buffer_max_size(self, vmci_buffer_max_size):
        """
        Set socket option `vmci_buffer_max_size`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_vmci_buffer_max_size(self._p, vmci_buffer_max_size)

    def vmci_connect_timeout(self):
        """
        Get socket option `vmci_connect_timeout`.
        Available from libzmq 4.2.0.
        """
        return utils.lib.zsock_vmci_connect_timeout(self._p)

    def set_vmci_connect_timeout(self, vmci_connect_timeout):
        """
        Set socket option `vmci_connect_timeout`.
        Available from libzmq 4.2.0.
        """
        utils.lib.zsock_set_vmci_connect_timeout(self._p, vmci_connect_timeout)

    def tos(self):
        """
        Get socket option `tos`.
        Available from libzmq 4.1.0.
        """
        return utils.lib.zsock_tos(self._p)

    def set_tos(self, tos):
        """
        Set socket option `tos`.
        Available from libzmq 4.1.0.
        """
        utils.lib.zsock_set_tos(self._p, tos)

    def set_router_handover(self, router_handover):
        """
        Set socket option `router_handover`.
        Available from libzmq 4.1.0.
        """
        utils.lib.zsock_set_router_handover(self._p, router_handover)

    def set_connect_rid(self, connect_rid):
        """
        Set socket option `connect_rid`.
        Available from libzmq 4.1.0.
        """
        utils.lib.zsock_set_connect_rid(self._p, utils.to_bytes(connect_rid))

    def set_connect_rid_bin(self, connect_rid):
        """
        Set socket option `connect_rid` from 32-octet binary
        Available from libzmq 4.1.0.
        """
        utils.lib.zsock_set_connect_rid_bin(self._p, connect_rid)

    def handshake_ivl(self):
        """
        Get socket option `handshake_ivl`.
        Available from libzmq 4.1.0.
        """
        return utils.lib.zsock_handshake_ivl(self._p)

    def set_handshake_ivl(self, handshake_ivl):
        """
        Set socket option `handshake_ivl`.
        Available from libzmq 4.1.0.
        """
        utils.lib.zsock_set_handshake_ivl(self._p, handshake_ivl)

    def socks_proxy(self):
        """
        Get socket option `socks_proxy`.
        Available from libzmq 4.1.0.
        """
        return utils.lib.zsock_socks_proxy(self._p)

    def set_socks_proxy(self, socks_proxy):
        """
        Set socket option `socks_proxy`.
        Available from libzmq 4.1.0.
        """
        utils.lib.zsock_set_socks_proxy(self._p, utils.to_bytes(socks_proxy))

    def set_xpub_nodrop(self, xpub_nodrop):
        """
        Set socket option `xpub_nodrop`.
        Available from libzmq 4.1.0.
        """
        utils.lib.zsock_set_xpub_nodrop(self._p, xpub_nodrop)

    def set_router_mandatory(self, router_mandatory):
        """
        Set socket option `router_mandatory`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_router_mandatory(self._p, router_mandatory)

    def set_probe_router(self, probe_router):
        """
        Set socket option `probe_router`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_probe_router(self._p, probe_router)

    def set_req_relaxed(self, req_relaxed):
        """
        Set socket option `req_relaxed`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_req_relaxed(self._p, req_relaxed)

    def set_req_correlate(self, req_correlate):
        """
        Set socket option `req_correlate`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_req_correlate(self._p, req_correlate)

    def set_conflate(self, conflate):
        """
        Set socket option `conflate`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_conflate(self._p, conflate)

    def zap_domain(self):
        """
        Get socket option `zap_domain`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_zap_domain(self._p)

    def set_zap_domain(self, zap_domain):
        """
        Set socket option `zap_domain`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_zap_domain(self._p, utils.to_bytes(zap_domain))

    def mechanism(self):
        """
        Get socket option `mechanism`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_mechanism(self._p)

    def plain_server(self):
        """
        Get socket option `plain_server`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_plain_server(self._p)

    def set_plain_server(self, plain_server):
        """
        Set socket option `plain_server`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_plain_server(self._p, plain_server)

    def plain_username(self):
        """
        Get socket option `plain_username`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_plain_username(self._p)

    def set_plain_username(self, plain_username):
        """
        Set socket option `plain_username`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_plain_username(self._p, utils.to_bytes(plain_username))

    def plain_password(self):
        """
        Get socket option `plain_password`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_plain_password(self._p)

    def set_plain_password(self, plain_password):
        """
        Set socket option `plain_password`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_plain_password(self._p, utils.to_bytes(plain_password))

    def curve_server(self):
        """
        Get socket option `curve_server`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_curve_server(self._p)

    def set_curve_server(self, curve_server):
        """
        Set socket option `curve_server`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_curve_server(self._p, curve_server)

    def curve_publickey(self):
        """
        Get socket option `curve_publickey`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_curve_publickey(self._p)

    def set_curve_publickey(self, curve_publickey):
        """
        Set socket option `curve_publickey`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_curve_publickey(self._p, utils.to_bytes(curve_publickey))

    def set_curve_publickey_bin(self, curve_publickey):
        """
        Set socket option `curve_publickey` from 32-octet binary
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_curve_publickey_bin(self._p, curve_publickey)

    def curve_secretkey(self):
        """
        Get socket option `curve_secretkey`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_curve_secretkey(self._p)

    def set_curve_secretkey(self, curve_secretkey):
        """
        Set socket option `curve_secretkey`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_curve_secretkey(self._p, utils.to_bytes(curve_secretkey))

    def set_curve_secretkey_bin(self, curve_secretkey):
        """
        Set socket option `curve_secretkey` from 32-octet binary
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_curve_secretkey_bin(self._p, curve_secretkey)

    def curve_serverkey(self):
        """
        Get socket option `curve_serverkey`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_curve_serverkey(self._p)

    def set_curve_serverkey(self, curve_serverkey):
        """
        Set socket option `curve_serverkey`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_curve_serverkey(self._p, utils.to_bytes(curve_serverkey))

    def set_curve_serverkey_bin(self, curve_serverkey):
        """
        Set socket option `curve_serverkey` from 32-octet binary
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_curve_serverkey_bin(self._p, curve_serverkey)

    def gssapi_server(self):
        """
        Get socket option `gssapi_server`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_gssapi_server(self._p)

    def set_gssapi_server(self, gssapi_server):
        """
        Set socket option `gssapi_server`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_gssapi_server(self._p, gssapi_server)

    def gssapi_plaintext(self):
        """
        Get socket option `gssapi_plaintext`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_gssapi_plaintext(self._p)

    def set_gssapi_plaintext(self, gssapi_plaintext):
        """
        Set socket option `gssapi_plaintext`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_gssapi_plaintext(self._p, gssapi_plaintext)

    def gssapi_principal(self):
        """
        Get socket option `gssapi_principal`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_gssapi_principal(self._p)

    def set_gssapi_principal(self, gssapi_principal):
        """
        Set socket option `gssapi_principal`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_gssapi_principal(self._p, utils.to_bytes(gssapi_principal))

    def gssapi_service_principal(self):
        """
        Get socket option `gssapi_service_principal`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_gssapi_service_principal(self._p)

    def set_gssapi_service_principal(self, gssapi_service_principal):
        """
        Set socket option `gssapi_service_principal`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_gssapi_service_principal(self._p, utils.to_bytes(gssapi_service_principal))

    def ipv6(self):
        """
        Get socket option `ipv6`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_ipv6(self._p)

    def set_ipv6(self, ipv6):
        """
        Set socket option `ipv6`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_ipv6(self._p, ipv6)

    def immediate(self):
        """
        Get socket option `immediate`.
        Available from libzmq 4.0.0.
        """
        return utils.lib.zsock_immediate(self._p)

    def set_immediate(self, immediate):
        """
        Set socket option `immediate`.
        Available from libzmq 4.0.0.
        """
        utils.lib.zsock_set_immediate(self._p, immediate)

    def sndhwm(self):
        """
        Get socket option `sndhwm`.
        Available from libzmq 3.0.0.
        """
        return utils.lib.zsock_sndhwm(self._p)

    def set_sndhwm(self, sndhwm):
        """
        Set socket option `sndhwm`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_sndhwm(self._p, sndhwm)

    def rcvhwm(self):
        """
        Get socket option `rcvhwm`.
        Available from libzmq 3.0.0.
        """
        return utils.lib.zsock_rcvhwm(self._p)

    def set_rcvhwm(self, rcvhwm):
        """
        Set socket option `rcvhwm`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_rcvhwm(self._p, rcvhwm)

    def maxmsgsize(self):
        """
        Get socket option `maxmsgsize`.
        Available from libzmq 3.0.0.
        """
        return utils.lib.zsock_maxmsgsize(self._p)

    def set_maxmsgsize(self, maxmsgsize):
        """
        Set socket option `maxmsgsize`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_maxmsgsize(self._p, maxmsgsize)

    def multicast_hops(self):
        """
        Get socket option `multicast_hops`.
        Available from libzmq 3.0.0.
        """
        return utils.lib.zsock_multicast_hops(self._p)

    def set_multicast_hops(self, multicast_hops):
        """
        Set socket option `multicast_hops`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_multicast_hops(self._p, multicast_hops)

    def set_xpub_verbose(self, xpub_verbose):
        """
        Set socket option `xpub_verbose`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_xpub_verbose(self._p, xpub_verbose)

    def tcp_keepalive(self):
        """
        Get socket option `tcp_keepalive`.
        Available from libzmq 3.0.0.
        """
        return utils.lib.zsock_tcp_keepalive(self._p)

    def set_tcp_keepalive(self, tcp_keepalive):
        """
        Set socket option `tcp_keepalive`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_tcp_keepalive(self._p, tcp_keepalive)

    def tcp_keepalive_idle(self):
        """
        Get socket option `tcp_keepalive_idle`.
        Available from libzmq 3.0.0.
        """
        return utils.lib.zsock_tcp_keepalive_idle(self._p)

    def set_tcp_keepalive_idle(self, tcp_keepalive_idle):
        """
        Set socket option `tcp_keepalive_idle`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_tcp_keepalive_idle(self._p, tcp_keepalive_idle)

    def tcp_keepalive_cnt(self):
        """
        Get socket option `tcp_keepalive_cnt`.
        Available from libzmq 3.0.0.
        """
        return utils.lib.zsock_tcp_keepalive_cnt(self._p)

    def set_tcp_keepalive_cnt(self, tcp_keepalive_cnt):
        """
        Set socket option `tcp_keepalive_cnt`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_tcp_keepalive_cnt(self._p, tcp_keepalive_cnt)

    def tcp_keepalive_intvl(self):
        """
        Get socket option `tcp_keepalive_intvl`.
        Available from libzmq 3.0.0.
        """
        return utils.lib.zsock_tcp_keepalive_intvl(self._p)

    def set_tcp_keepalive_intvl(self, tcp_keepalive_intvl):
        """
        Set socket option `tcp_keepalive_intvl`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_tcp_keepalive_intvl(self._p, tcp_keepalive_intvl)

    def tcp_accept_filter(self):
        """
        Get socket option `tcp_accept_filter`.
        Available from libzmq 3.0.0.
        """
        return utils.lib.zsock_tcp_accept_filter(self._p)

    def set_tcp_accept_filter(self, tcp_accept_filter):
        """
        Set socket option `tcp_accept_filter`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_tcp_accept_filter(self._p, utils.to_bytes(tcp_accept_filter))

    def last_endpoint(self):
        """
        Get socket option `last_endpoint`.
        Available from libzmq 3.0.0.
        """
        return utils.lib.zsock_last_endpoint(self._p)

    def set_router_raw(self, router_raw):
        """
        Set socket option `router_raw`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_router_raw(self._p, router_raw)

    def ipv4only(self):
        """
        Get socket option `ipv4only`.
        Available from libzmq 3.0.0.
        """
        return utils.lib.zsock_ipv4only(self._p)

    def set_ipv4only(self, ipv4only):
        """
        Set socket option `ipv4only`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_ipv4only(self._p, ipv4only)

    def set_delay_attach_on_connect(self, delay_attach_on_connect):
        """
        Set socket option `delay_attach_on_connect`.
        Available from libzmq 3.0.0.
        """
        utils.lib.zsock_set_delay_attach_on_connect(self._p, delay_attach_on_connect)

    def hwm(self):
        """
        Get socket option `hwm`.
        Available from libzmq 2.0.0 to 3.0.0.
        """
        return utils.lib.zsock_hwm(self._p)

    def set_hwm(self, hwm):
        """
        Set socket option `hwm`.
        Available from libzmq 2.0.0 to 3.0.0.
        """
        utils.lib.zsock_set_hwm(self._p, hwm)

    def swap(self):
        """
        Get socket option `swap`.
        Available from libzmq 2.0.0 to 3.0.0.
        """
        return utils.lib.zsock_swap(self._p)

    def set_swap(self, swap):
        """
        Set socket option `swap`.
        Available from libzmq 2.0.0 to 3.0.0.
        """
        utils.lib.zsock_set_swap(self._p, swap)

    def affinity(self):
        """
        Get socket option `affinity`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_affinity(self._p)

    def set_affinity(self, affinity):
        """
        Set socket option `affinity`.
        Available from libzmq 2.0.0.
        """
        utils.lib.zsock_set_affinity(self._p, affinity)

    def identity(self):
        """
        Get socket option `identity`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_identity(self._p)

    def set_identity(self, identity):
        """
        Set socket option `identity`.
        Available from libzmq 2.0.0.
        """
        utils.lib.zsock_set_identity(self._p, utils.to_bytes(identity))

    def rate(self):
        """
        Get socket option `rate`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_rate(self._p)

    def set_rate(self, rate):
        """
        Set socket option `rate`.
        Available from libzmq 2.0.0.
        """
        utils.lib.zsock_set_rate(self._p, rate)

    def recovery_ivl(self):
        """
        Get socket option `recovery_ivl`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_recovery_ivl(self._p)

    def set_recovery_ivl(self, recovery_ivl):
        """
        Set socket option `recovery_ivl`.
        Available from libzmq 2.0.0.
        """
        utils.lib.zsock_set_recovery_ivl(self._p, recovery_ivl)

    def recovery_ivl_msec(self):
        """
        Get socket option `recovery_ivl_msec`.
        Available from libzmq 2.0.0 to 3.0.0.
        """
        return utils.lib.zsock_recovery_ivl_msec(self._p)

    def set_recovery_ivl_msec(self, recovery_ivl_msec):
        """
        Set socket option `recovery_ivl_msec`.
        Available from libzmq 2.0.0 to 3.0.0.
        """
        utils.lib.zsock_set_recovery_ivl_msec(self._p, recovery_ivl_msec)

    def mcast_loop(self):
        """
        Get socket option `mcast_loop`.
        Available from libzmq 2.0.0 to 3.0.0.
        """
        return utils.lib.zsock_mcast_loop(self._p)

    def set_mcast_loop(self, mcast_loop):
        """
        Set socket option `mcast_loop`.
        Available from libzmq 2.0.0 to 3.0.0.
        """
        utils.lib.zsock_set_mcast_loop(self._p, mcast_loop)

    def rcvtimeo(self):
        """
        Get socket option `rcvtimeo`.
        Available from libzmq 2.2.0.
        """
        return utils.lib.zsock_rcvtimeo(self._p)

    def set_rcvtimeo(self, rcvtimeo):
        """
        Set socket option `rcvtimeo`.
        Available from libzmq 2.2.0.
        """
        utils.lib.zsock_set_rcvtimeo(self._p, rcvtimeo)

    def sndtimeo(self):
        """
        Get socket option `sndtimeo`.
        Available from libzmq 2.2.0.
        """
        return utils.lib.zsock_sndtimeo(self._p)

    def set_sndtimeo(self, sndtimeo):
        """
        Set socket option `sndtimeo`.
        Available from libzmq 2.2.0.
        """
        utils.lib.zsock_set_sndtimeo(self._p, sndtimeo)

    def sndbuf(self):
        """
        Get socket option `sndbuf`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_sndbuf(self._p)

    def set_sndbuf(self, sndbuf):
        """
        Set socket option `sndbuf`.
        Available from libzmq 2.0.0.
        """
        utils.lib.zsock_set_sndbuf(self._p, sndbuf)

    def rcvbuf(self):
        """
        Get socket option `rcvbuf`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_rcvbuf(self._p)

    def set_rcvbuf(self, rcvbuf):
        """
        Set socket option `rcvbuf`.
        Available from libzmq 2.0.0.
        """
        utils.lib.zsock_set_rcvbuf(self._p, rcvbuf)

    def linger(self):
        """
        Get socket option `linger`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_linger(self._p)

    def set_linger(self, linger):
        """
        Set socket option `linger`.
        Available from libzmq 2.0.0.
        """
        utils.lib.zsock_set_linger(self._p, linger)

    def reconnect_ivl(self):
        """
        Get socket option `reconnect_ivl`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_reconnect_ivl(self._p)

    def set_reconnect_ivl(self, reconnect_ivl):
        """
        Set socket option `reconnect_ivl`.
        Available from libzmq 2.0.0.
        """
        utils.lib.zsock_set_reconnect_ivl(self._p, reconnect_ivl)

    def reconnect_ivl_max(self):
        """
        Get socket option `reconnect_ivl_max`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_reconnect_ivl_max(self._p)

    def set_reconnect_ivl_max(self, reconnect_ivl_max):
        """
        Set socket option `reconnect_ivl_max`.
        Available from libzmq 2.0.0.
        """
        utils.lib.zsock_set_reconnect_ivl_max(self._p, reconnect_ivl_max)

    def backlog(self):
        """
        Get socket option `backlog`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_backlog(self._p)

    def set_backlog(self, backlog):
        """
        Set socket option `backlog`.
        Available from libzmq 2.0.0.
        """
        utils.lib.zsock_set_backlog(self._p, backlog)

    def set_subscribe(self, subscribe):
        """
        Set socket option `subscribe`.
        Available from libzmq 2.0.0.
        """
        utils.lib.zsock_set_subscribe(self._p, utils.to_bytes(subscribe))

    def set_unsubscribe(self, unsubscribe):
        """
        Set socket option `unsubscribe`.
        Available from libzmq 2.0.0.
        """
        utils.lib.zsock_set_unsubscribe(self._p, utils.to_bytes(unsubscribe))

    def type(self):
        """
        Get socket option `type`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_type(self._p)

    def rcvmore(self):
        """
        Get socket option `rcvmore`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_rcvmore(self._p)

    def fd(self):
        """
        Get socket option `fd`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_fd(self._p)

    def events(self):
        """
        Get socket option `events`.
        Available from libzmq 2.0.0.
        """
        return utils.lib.zsock_events(self._p)

    @staticmethod
    def test(verbose):
        """
        Self test of this class.
        """
        utils.lib.zsock_test(verbose)

################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
