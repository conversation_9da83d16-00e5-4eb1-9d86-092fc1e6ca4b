/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
#ifndef Q_ZDIR_PATCH_H
#define Q_ZDIR_PATCH_H

#include "qczmq.h"

class QT_CZMQ_EXPORT QZdirPatch : public QObject
{
    Q_OBJECT
public:

    //  Copy-construct to return the proper wrapped c types
    QZdirPatch (zdir_patch_t *self, QObject *qObjParent = 0);

    //  Create new patch
    explicit QZdirPatch (const QString &path, QZfile *file, int op, const QString &alias, QObject *qObjParent = 0);

    //  Destroy a patch
    ~QZdirPatch ();

    //  Create copy of a patch. If the patch is null, or memory was exhausted,
    //  returns null.
    QZdirPatch * dup ();

    //  Return patch file directory path
    const QString path ();

    //  Return patch file item
    QZfile * file ();

    //  Return operation
    int op ();

    //  Return patch virtual file path
    const QString vpath ();

    //  Calculate hash digest for file (create only)
    void digestSet ();

    //  Return hash digest for patch file
    const QString digest ();

    //  Self test of this class.
    static void test (bool verbose);

    zdir_patch_t *self;
};
#endif //  Q_ZDIR_PATCH_H
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
