<?xml version="1.0" encoding="utf-8"?>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="..\..\..\..\src\zactor.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zargs.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zarmour.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zcert.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zcertstore.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zchunk.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zclock.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zconfig.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zdigest.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zdir.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zdir_patch.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zfile.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zframe.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhash.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhashx.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ziflist.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zlist.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zlistx.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zloop.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zmsg.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zpoller.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zproc.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zsock.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zstr.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zsys.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ztimerset.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\ztrie.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zuuid.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhttp_client.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhttp_server.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhttp_server_options.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhttp_request.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zhttp_response.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zosc.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zauth.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zbeacon.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zgossip.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zmonitor.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zproxy.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zrex.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\zgossip_msg.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\src\czmq_private_selftest.c">
      <Filter>src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\include\czmq_library.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\include\czmq_prelude.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\include\czmq.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\src\zsock_option.inc">
      <Filter>src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\src\zgossip_engine.inc">
      <Filter>src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\src\zhash_primes.inc">
      <Filter>src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\src\foreign/sha1/sha1.inc_c">
      <Filter>src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\src\foreign/sha1/sha1.h">
      <Filter>src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\src\foreign/slre/slre.inc_c">
      <Filter>src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\src\foreign/slre/slre.h">
      <Filter>src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\src\foreign/slre/readme.txt">
      <Filter>src</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\platform.h">
      <Filter>src\include</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\resource.h">
      <Filter>resource</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\..\resource.rc">
      <Filter>resource</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="src">
      <UniqueIdentifier>{48f852d3-9723-4499-bf1a-35c0234b8ba9}</UniqueIdentifier>
    </Filter>
    <Filter Include="include">
      <UniqueIdentifier>{95e5d24a-57a2-429a-a1f1-304165f2e3da}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\include">
      <UniqueIdentifier>{d0c837b5-cb58-4b82-b9bf-38727c7b25bd}</UniqueIdentifier>
    </Filter>
    <Filter Include="resource">
      <UniqueIdentifier>{48e93f8c-156c-4379-a901-4b5ad39a4eac}</UniqueIdentifier>
    </Filter>
    <Filter Include="packaging">
      <UniqueIdentifier>{04a473ca-1d88-4e12-9190-8d9cc20efd74}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\nuget\package.bat">
      <Filter>packaging</Filter>
    </None>
    <None Include="..\..\nuget\package.nuspec">
      <Filter>packaging</Filter>
    </None>
    <None Include="..\..\nuget\package.targets">
      <Filter>packaging</Filter>
    </None>
    <None Include="..\..\nuget\package.xml">
      <Filter>packaging</Filter>
    </None>
  </ItemGroup>
<!--
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
-->
</Project>
