/*  =========================================================================
    zargs - Platform independent command line argument parsing helpers

There are two kind of elements provided by this class
foo --named-parameter --parameter with_value positional arguments -a gain-parameter

zargs keeps poision only for arguments, parameters are to be accessed like hash.

It DOES:
* provide easy to use CLASS compatible API for accessing argv
* is platform independent
* provide getopt_long style -- argument, which delimits parameters from arguments
* makes parameters position independent
* hides several formats of command line to one (-Idir, --include=dir,
  --include dir are the same from API pov)

It does NOT
* change argv
* provide a "declarative" way to define command line interface

    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    =========================================================================
*/

#ifndef ZARGS_H_INCLUDED
#define ZARGS_H_INCLUDED

#ifdef __cplusplus
extern "C" {
#endif

//  @warning THE FOLLOWING @INTERFACE BLOCK IS AUTO-GENERATED BY ZPROJECT
//  @warning Please edit the model at "api/zargs.api" to make changes.
//  @interface
//  This is a draft class, and may change without notice. It is disabled in
//  stable builds by default. If you use this in applications, please ask
//  for it to be pushed to stable state. Use --enable-drafts to enable.
#ifdef CZMQ_BUILD_DRAFT_API
//  *** Draft method, for development use, may change without warning ***
//  Create a new zargs from command line arguments.
CZMQ_EXPORT zargs_t *
    zargs_new (int argc, char **argv);

//  *** Draft method, for development use, may change without warning ***
//  Destroy zargs instance.
CZMQ_EXPORT void
    zargs_destroy (zargs_t **self_p);

//  *** Draft method, for development use, may change without warning ***
//  Return program name (argv[0])
CZMQ_EXPORT const char *
    zargs_progname (zargs_t *self);

//  *** Draft method, for development use, may change without warning ***
//  Return number of positional arguments
CZMQ_EXPORT size_t
    zargs_arguments (zargs_t *self);

//  *** Draft method, for development use, may change without warning ***
//  Return first positional argument or NULL
CZMQ_EXPORT const char *
    zargs_first (zargs_t *self);

//  *** Draft method, for development use, may change without warning ***
//  Return next positional argument or NULL
CZMQ_EXPORT const char *
    zargs_next (zargs_t *self);

//  *** Draft method, for development use, may change without warning ***
//  Return first named parameter value, or NULL if there are no named
//  parameters, or value for which zargs_param_empty (arg) returns true.
CZMQ_EXPORT const char *
    zargs_param_first (zargs_t *self);

//  *** Draft method, for development use, may change without warning ***
//  Return next named parameter value, or NULL if there are no named
//  parameters, or value for which zargs_param_empty (arg) returns true.
CZMQ_EXPORT const char *
    zargs_param_next (zargs_t *self);

//  *** Draft method, for development use, may change without warning ***
//  Return current parameter name, or NULL if there are no named parameters.
CZMQ_EXPORT const char *
    zargs_param_name (zargs_t *self);

//  *** Draft method, for development use, may change without warning ***
//  Return value of named parameter or NULL is it has no value (or was not specified)
CZMQ_EXPORT const char *
    zargs_get (zargs_t *self, const char *name);

//  *** Draft method, for development use, may change without warning ***
//  Return value of one of parameter(s) or NULL is it has no value (or was not specified)
CZMQ_EXPORT const char *
    zargs_getx (zargs_t *self, const char *name, ...);

//  *** Draft method, for development use, may change without warning ***
//  Returns true if named parameter was specified on command line
CZMQ_EXPORT bool
    zargs_has (zargs_t *self, const char *name);

//  *** Draft method, for development use, may change without warning ***
//  Returns true if named parameter(s) was specified on command line
CZMQ_EXPORT bool
    zargs_hasx (zargs_t *self, const char *name, ...);

//  *** Draft method, for development use, may change without warning ***
//  Print an instance of zargs.
CZMQ_EXPORT void
    zargs_print (zargs_t *self);

//  *** Draft method, for development use, may change without warning ***
//  Self test of this class.
CZMQ_EXPORT void
    zargs_test (bool verbose);

#endif // CZMQ_BUILD_DRAFT_API
//  @end

#ifdef __cplusplus
}
#endif

#endif
