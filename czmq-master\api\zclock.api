<class name = "zclock" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    millisecond clocks and delays

    <method name = "sleep" singleton = "1">
        Sleep for a number of milliseconds
        <argument name = "msecs" type = "integer" />
    </method>

    <method name = "time" singleton = "1">
        Return current system clock as milliseconds. Note that this clock can
        jump backwards (if the system clock is changed) so is unsafe to use for
        timers and time offsets. Use zclock_mono for that instead.
        <return type = "msecs" />
    </method>

    <method name = "mono" singleton = "1">
        Return current monotonic clock in milliseconds. Use this when you compute
        time offsets. The monotonic clock is not affected by system changes and
        so will never be reset backwards, unlike a system clock.
        <return type = "msecs" />
    </method>

    <method name = "usecs" singleton = "1">
        Return current monotonic clock in microseconds. Use this when you compute
        time offsets. The monotonic clock is not affected by system changes and
        so will never be reset backwards, unlike a system clock.
        <return type = "msecs" />
    </method>

    <method name = "timestr" singleton = "1">
        Return formatted date/time as fresh string. Free using zstr_free().
        <return type = "string" fresh = "1" />
    </method>
</class>
