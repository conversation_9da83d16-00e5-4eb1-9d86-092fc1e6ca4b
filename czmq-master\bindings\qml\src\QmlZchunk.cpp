/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "QmlZchunk.h"


///
//  Resizes chunk max_size as requested; chunk_cur size is set to zero
void QmlZchunk::resize (size_t size) {
    zchunk_resize (self, size);
};

///
//  Return chunk cur size
size_t QmlZchunk::size () {
    return zchunk_size (self);
};

///
//  Return chunk max size
size_t QmlZchunk::maxSize () {
    return zchunk_max_size (self);
};

///
//  Return chunk data
byte *QmlZchunk::data () {
    return zchunk_data (self);
};

///
//  Set chunk data from user-supplied data; truncate if too large. Data may
//  be null. Returns actual size of chunk
size_t QmlZchunk::set (const void *data, size_t size) {
    return zchunk_set (self, data, size);
};

///
//  Fill chunk data from user-supplied octet
size_t QmlZchunk::fill (byte filler, size_t size) {
    return zchunk_fill (self, filler, size);
};

///
//  Append user-supplied data to chunk, return resulting chunk size. If the
//  data would exceeded the available space, it is truncated. If you want to
//  grow the chunk to accommodate new data, use the zchunk_extend method.
size_t QmlZchunk::append (const void *data, size_t size) {
    return zchunk_append (self, data, size);
};

///
//  Append user-supplied data to chunk, return resulting chunk size. If the
//  data would exceeded the available space, the chunk grows in size.
size_t QmlZchunk::extend (const void *data, size_t size) {
    return zchunk_extend (self, data, size);
};

///
//  Copy as much data from 'source' into the chunk as possible; returns the
//  new size of chunk. If all data from 'source' is used, returns exhausted
//  on the source chunk. Source can be consumed as many times as needed until
//  it is exhausted. If source was already exhausted, does not change chunk.
size_t QmlZchunk::consume (QmlZchunk *source) {
    return zchunk_consume (self, source->self);
};

///
//  Returns true if the chunk was exhausted by consume methods, or if the
//  chunk has a size of zero.
bool QmlZchunk::exhausted () {
    return zchunk_exhausted (self);
};

///
//  Write chunk to an open file descriptor
int QmlZchunk::write (FILE *handle) {
    return zchunk_write (self, handle);
};

///
//  Create copy of chunk, as new chunk object. Returns a fresh zchunk_t
//  object, or null if there was not enough heap memory. If chunk is null,
//  returns null.
QmlZchunk *QmlZchunk::dup () {
    QmlZchunk *retQ_ = new QmlZchunk ();
    retQ_->self = zchunk_dup (self);
    return retQ_;
};

///
//  Return chunk data encoded as printable hex string. Caller must free
//  string when finished with it.
QString QmlZchunk::strhex () {
    char *retStr_ = zchunk_strhex (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Return chunk data copied into freshly allocated string
//  Caller must free string when finished with it.
QString QmlZchunk::strdup () {
    char *retStr_ = zchunk_strdup (self);
    QString retQStr_ = QString (retStr_);
    free (retStr_);
    return retQStr_;
};

///
//  Return TRUE if chunk body is equal to string, excluding terminator
bool QmlZchunk::streq (const QString &string) {
    return zchunk_streq (self, string.toUtf8().data());
};

///
//  Transform zchunk into a zframe that can be sent in a message.
QmlZframe *QmlZchunk::pack () {
    QmlZframe *retQ_ = new QmlZframe ();
    retQ_->self = zchunk_pack (self);
    return retQ_;
};

///
//  Calculate SHA1 digest for chunk, using zdigest class.
const QString QmlZchunk::digest () {
    return QString (zchunk_digest (self));
};

///
//  Dump chunk to FILE stream, for debugging and tracing.
void QmlZchunk::fprint (FILE *file) {
    zchunk_fprint (self, file);
};

///
//  Dump message to stderr, for debugging and tracing.
//  See zchunk_fprint for details
void QmlZchunk::print () {
    zchunk_print (self);
};


QObject* QmlZchunk::qmlAttachedProperties(QObject* object) {
    return new QmlZchunkAttached(object);
}


///
//  Read chunk from an open file descriptor
QmlZchunk *QmlZchunkAttached::read (FILE *handle, size_t bytes) {
    QmlZchunk *retQ_ = new QmlZchunk ();
    retQ_->self = zchunk_read (handle, bytes);
    return retQ_;
};

///
//  Try to slurp an entire file into a chunk. Will read up to maxsize of
//  the file. If maxsize is 0, will attempt to read the entire file and
//  fail with an assertion if that cannot fit into memory. Returns a new
//  chunk containing the file data, or NULL if the file could not be read.
QmlZchunk *QmlZchunkAttached::slurp (const QString &filename, size_t maxsize) {
    QmlZchunk *retQ_ = new QmlZchunk ();
    retQ_->self = zchunk_slurp (filename.toUtf8().data(), maxsize);
    return retQ_;
};

///
//  Transform zchunk into a zframe that can be sent in a message.
//  Take ownership of the chunk.
QmlZframe *QmlZchunkAttached::packx (QmlZchunk *selfP) {
    QmlZframe *retQ_ = new QmlZframe ();
    retQ_->self = zchunk_packx (&selfP->self);
    return retQ_;
};

///
//  Transform a zframe into a zchunk.
QmlZchunk *QmlZchunkAttached::unpack (QmlZframe *frame) {
    QmlZchunk *retQ_ = new QmlZchunk ();
    retQ_->self = zchunk_unpack (frame->self);
    return retQ_;
};

///
//  Probe the supplied object, and report if it looks like a zchunk_t.
bool QmlZchunkAttached::is (void *self) {
    return zchunk_is (self);
};

///
//  Self test of this class.
void QmlZchunkAttached::test (bool verbose) {
    zchunk_test (verbose);
};

///
//  Create a new chunk of the specified size. If you specify the data, it
//  is copied into the chunk. If you do not specify the data, the chunk is
//  allocated and left empty, and you can then add data using zchunk_append.
QmlZchunk *QmlZchunkAttached::construct (const void *data, size_t size) {
    QmlZchunk *qmlSelf = new QmlZchunk ();
    qmlSelf->self = zchunk_new (data, size);
    return qmlSelf;
};

///
//  Create a new chunk from memory. Take ownership of the memory and calling the destructor
//  on destroy.
QmlZchunk *QmlZchunkAttached::frommem (void *data, size_t size, zchunk_destructor_fn destructor, void *hint) {
    QmlZchunk *qmlSelf = new QmlZchunk ();
    qmlSelf->self = zchunk_frommem (data, size, destructor, hint);
    return qmlSelf;
};

///
//  Destroy a chunk
void QmlZchunkAttached::destruct (QmlZchunk *qmlSelf) {
    zchunk_destroy (&qmlSelf->self);
};

/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
