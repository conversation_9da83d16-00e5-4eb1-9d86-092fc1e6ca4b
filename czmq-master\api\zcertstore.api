<class name = "zcertstore" state = "stable">
    <!--
    Copyright (c) the Contributors as noted in the AUTHORS file.
    This file is part of CZMQ, the high-level C binding for 0MQ:
    http://czmq.zeromq.org.

    This Source Code Form is subject to the terms of the Mozilla Public
    License, v. 2.0. If a copy of the MPL was not distributed with this
    file, You can obtain one at http://mozilla.org/MPL/2.0/.
    -->
    work with CURVE security certificate stores

    <constructor>
        Create a new certificate store from a disk directory, loading and
        indexing all certificates in that location. The directory itself may be
        absent, and created later, or modified at any time. The certificate store
        is automatically refreshed on any zcertstore_lookup() call. If the
        location is specified as NULL, creates a pure-memory store, which you
        can work with by inserting certificates at runtime.
        <argument name = "location" type = "string" />
    </constructor>

    <destructor>
        Destroy a certificate store object in memory. Does not affect anything
        stored on disk.
    </destructor>

    <callback_type name = "loader" state = "draft">
        Loaders retrieve certificates from an arbitrary source.
        <argument name = "self" type = "zcertstore" />
    </callback_type>

    <callback_type name = "destructor" state = "draft">
        Destructor for loader state.
        <argument name = "self_p" type = "buffer" c_type = "void **" />
    </callback_type>

    <method name = "set_loader" state = "draft">
      Override the default disk loader with a custom loader fn.
      <argument name = "loader" type = "zcertstore_loader" callback = "1" />
      <argument name = "destructor" type = "zcertstore_destructor" callback = "1" />
      <argument name = "state" type = "buffer" c_type = "void *" />
    </method>

    <method name = "lookup">
        Look up certificate by public key, returns zcert_t object if found,
        else returns NULL. The public key is provided in Z85 text format.
        <argument name = "public key" type = "string" />
        <return type = "zcert" mutable = "1" />
    </method>

    <method name = "insert">
        Insert certificate into certificate store in memory. Note that this
        does not save the certificate to disk. To do that, use zcert_save()
        directly on the certificate. Takes ownership of zcert_t object.
        <argument name = "cert p" type = "zcert" by_reference = "1" />
    </method>

    <method name = "empty" state = "draft">
        Empty certificate hashtable. This wrapper exists to be friendly to bindings,
        which don't usually have access to struct internals.
    </method>

    <method name = "print">
        Print list of certificates in store to logging facility
    </method>

    <method name = "certs" state = "draft">
        Return a list of all the certificates in the store.
        The caller takes ownership of the zlistx_t object and is responsible
        for destroying it.  The caller does not take ownership of the zcert_t
        objects.
        <return type = "zlistx" fresh = "1" />
    </method>

    <method name = "state" state = "draft">
        Return the state stored in certstore
        <return type = "anything" />
    </method>

    <method name = "test" singleton = "1">
        Self test of this class
        <argument name = "verbose" type = "boolean" />
    </method>
</class>
