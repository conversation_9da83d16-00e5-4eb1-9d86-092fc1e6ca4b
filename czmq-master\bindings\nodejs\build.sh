#! /bin/bash
#
#   Builds czmq.node package from a fresh git clone
#
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
#
set -e                      #   exit on any error
FORCE=0
VERBOSE=0
QUIET="--quiet"
LOGLEVEL="--loglevel=error"

for ARG in $*; do
    if [ "$ARG" == "--help" -o "$ARG" == "-h" ]; then
        echo "build.sh"
        echo " --help / -h          This help"
        echo " --force / -f         Force full rebuild"
        echo " --verbose / -v       Show build output"
        echo " --xverbose / -x      Extra verbose"
        exit
    elif [ "$ARG" == "--force" -o "$ARG" == "-f" ]; then
        FORCE=1
    elif [ "$ARG" == "--verbose" -o "$ARG" == "-v" ]; then
        VERBOSE=1
        QUIET=""
        LOGLEVEL=""
    elif [ "$ARG" == "--xverbose" -o "$ARG" == "-x" ]; then
        VERBOSE=1
        QUIET=""
        LOGLEVEL="--loglevel=verbose"
        set -x
    fi
done

BUILD_ROOT=`pwd`
cd ../../..
# Note: here we go to parent directory that contains current project,
# so the checkout is nearby, same as expected for usual developer work

#   Check dependent projects
if [ ! -d libzmq ]; then
    echo "I:    cloning https://github.com/zeromq/libzmq.git into `pwd`/libzmq..."
    git clone $QUIET https://github.com/zeromq/libzmq.git || exit
fi
if [ ! -f libzmq/builds/gyp/project.gyp ]; then
    echo "E:    `pwd`/libzmq out of date (builds/gyp/project.gyp missing)" >&2
    exit 1
fi


#   Check Node.js dependencies
cd $BUILD_ROOT
echo "I: checking Node.js dependencies..."

failed=0
set +e
for package in node-ninja bindings nan prebuild; do
    npm list --depth 1 $package > /dev/null 2>&1
    if [ $? -eq 1 ]; then
        npm list --global --depth 1 $package > /dev/null 2>&1
        if [ $? -eq 1 ]; then
            echo "E: $package isn't installed, run 'npm install [-g] $package'"
            failed=1
        fi
    fi
done
test $failed -eq 0 || exit
set -e

#   Calculate how many compiles we can do in parallel
export JOBS=$([[ $(uname) = 'Darwin' ]] \
    && sysctl -n hw.logicalcpu_max \
    || lscpu -p | egrep -v '^#' | wc -l)

#   Build the binding using node-ninja directly, not prebuild
echo "I: building Node.js binding:"
node-ninja configure
node-ninja build
