{"name": "czmq", "version": "0.0.1", "description": "The high-level C binding for 0MQ", "scripts": {"install": "prebuild --install", "test": "echo \"Error: no test specified\" && exit 1", "rebuild": "prebuild --compile", "prebuild": "prebuild --strip --verbose"}, "main": "index", "author": "See AUTHORS", "license": "MPL-2.0", "gypfile": true, "repository": {"type": "git", "url": ""}, "dependencies": {"bindings": "^1.2.1", "nan": "^2.2.0", "node-ninja": "^1.0.1", "prebuild": "^3.0.3"}}