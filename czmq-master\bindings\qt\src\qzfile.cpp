/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/

#include "qczmq.h"

///
//  Copy-construct to return the proper wrapped c types
QZfile::QZfile (zfile_t *self, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = self;
}


///
//  If file exists, populates properties. CZMQ supports portable symbolic
//  links, which are files with the extension ".ln". A symbolic link is a
//  text file containing one line, the filename of a target file. Reading
//  data from the symbolic link actually reads from the target file. Path
//  may be NULL, in which case it is not used.
QZfile::QZfile (const QString &path, const QString &name, QObject *qObjParent) : QObject (qObjParent)
{
    this->self = zfile_new (path.toUtf8().data(), name.toUtf8().data());
}

///
//  Create new temporary file for writing via tmpfile. File is automatically
//  deleted on destroy
QZfile* QZfile::tmp (QObject *qObjParent)
{
    return new QZfile (zfile_tmp (), qObjParent);
}

///
//  Destroy a file item
QZfile::~QZfile ()
{
    zfile_destroy (&self);
}

///
//  Duplicate a file item, returns a newly constructed item. If the file
//  is null, or memory was exhausted, returns null.
QZfile * QZfile::dup ()
{
    QZfile *rv = new QZfile (zfile_dup (self));
    return rv;
}

///
//  Return file name, remove path if provided
const QString QZfile::filename (const QString &path)
{
    const QString rv = QString (zfile_filename (self, path.toUtf8().data()));
    return rv;
}

///
//  Refresh file properties from disk; this is not done automatically
//  on access methods, otherwise it is not possible to compare directory
//  snapshots.
void QZfile::restat ()
{
    zfile_restat (self);

}

///
//  Return when the file was last modified. If you want this to reflect the
//  current situation, call zfile_restat before checking this property.
time_t QZfile::modified ()
{
    time_t rv = zfile_modified (self);
    return rv;
}

///
//  Return the last-known size of the file. If you want this to reflect the
//  current situation, call zfile_restat before checking this property.
off_t QZfile::cursize ()
{
    off_t rv = zfile_cursize (self);
    return rv;
}

///
//  Return true if the file is a directory. If you want this to reflect
//  any external changes, call zfile_restat before checking this property.
bool QZfile::isDirectory ()
{
    bool rv = zfile_is_directory (self);
    return rv;
}

///
//  Return true if the file is a regular file. If you want this to reflect
//  any external changes, call zfile_restat before checking this property.
bool QZfile::isRegular ()
{
    bool rv = zfile_is_regular (self);
    return rv;
}

///
//  Return true if the file is readable by this process. If you want this to
//  reflect any external changes, call zfile_restat before checking this
//  property.
bool QZfile::isReadable ()
{
    bool rv = zfile_is_readable (self);
    return rv;
}

///
//  Return true if the file is writeable by this process. If you want this
//  to reflect any external changes, call zfile_restat before checking this
//  property.
bool QZfile::isWriteable ()
{
    bool rv = zfile_is_writeable (self);
    return rv;
}

///
//  Check if file has stopped changing and can be safely processed.
//  Updates the file statistics from disk at every call.
bool QZfile::isStable ()
{
    bool rv = zfile_is_stable (self);
    return rv;
}

///
//  Return true if the file was changed on disk since the zfile_t object
//  was created, or the last zfile_restat() call made on it.
bool QZfile::hasChanged ()
{
    bool rv = zfile_has_changed (self);
    return rv;
}

///
//  Remove the file from disk
void QZfile::remove ()
{
    zfile_remove (self);

}

///
//  Open file for reading
//  Returns 0 if OK, -1 if not found or not accessible
int QZfile::input ()
{
    int rv = zfile_input (self);
    return rv;
}

///
//  Open file for writing, creating directory if needed
//  File is created if necessary; chunks can be written to file at any
//  location. Returns 0 if OK, -1 if error.
int QZfile::output ()
{
    int rv = zfile_output (self);
    return rv;
}

///
//  Read chunk from file at specified position. If this was the last chunk,
//  sets the eof property. Returns a null chunk in case of error.
QZchunk * QZfile::read (size_t bytes, off_t offset)
{
    QZchunk *rv = new QZchunk (zfile_read (self, bytes, offset));
    return rv;
}

///
//  Returns true if zfile_read() just read the last chunk in the file.
bool QZfile::eof ()
{
    bool rv = zfile_eof (self);
    return rv;
}

///
//  Write chunk to file at specified position
//  Return 0 if OK, else -1
int QZfile::write (QZchunk *chunk, off_t offset)
{
    int rv = zfile_write (self, chunk->self, offset);
    return rv;
}

///
//  Read next line of text from file. Returns a pointer to the text line,
//  or NULL if there was nothing more to read from the file.
const QString QZfile::readln ()
{
    const QString rv = QString (zfile_readln (self));
    return rv;
}

///
//  Close file, if open
void QZfile::close ()
{
    zfile_close (self);

}

///
//  Return file handle, if opened
FILE * QZfile::handle ()
{
    FILE * rv = zfile_handle (self);
    return rv;
}

///
//  Calculate SHA1 digest for file, using zdigest class.
const QString QZfile::digest ()
{
    const QString rv = QString (zfile_digest (self));
    return rv;
}

///
//  Self test of this class.
void QZfile::test (bool verbose)
{
    zfile_test (verbose);

}
/*
################################################################################
#  THIS FILE IS 100% GENERATED BY ZPROJECT; DO NOT EDIT EXCEPT EXPERIMENTALLY  #
#  Read the zproject/README.md for information about making permanent changes. #
################################################################################
*/
